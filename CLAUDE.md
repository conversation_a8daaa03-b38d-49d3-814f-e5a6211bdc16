# Claude: Monorepo Project Configuration

## 0. Core Principles

### Dual-Project Awareness
Consider both Giki AI and Pitch Prep in every decision.


## 1. Memory Loading Strategy

**68.7% reduction achieved** - 14K tokens (was 44K)

### CRITICAL CORE (Always Load)
- @.claude/memory/global/development-quality.md
- @.claude/memory/global/production-code-purity.md
- @.claude/memory/global/development-standards.md
- @.claude/memory/global/QUICK-TODO-DECOMPOSITION.md

### Essential Framework (Always Load)
- @.claude/memory/global/ddc-framework.md
- @.claude/memory/shared/project-architecture.md

### Contextual Loading
#### Workflows & Planning
- @.claude/memory/global/workflows.md
- @.claude/memory/global/enhanced-todo-system.md

#### Tools & Visual Analysis
- @.claude/memory/global/tools.md
- @.claude/memory/global/sdk-verification.md

#### Brand & Design
- @.claude/memory/shared/brand-color-clarification.md

### Project-Specific
#### Giki AI
- @.claude/memory/giki-ai/product-vision-memory.md
- @.claude/memory/giki-ai/debugging-playwright-memory.md
- @.claude/memory/giki-ai/testing-playwright-memory.md

#### Pitch Prep
- @.claude/memory/pitch-prep/pitch-prep-complete.md

## 2. Active Projects

**Giki AI**: Financial categorization, 87%+ accuracy, Port 3000/8000
**Pitch Prep**: Shark Tank prep, <30min reports, Port 3001/8001

## 3. Development Commands

```bash
cd /Users/<USER>/giki-ai-workspace  # MANDATORY
bun run dev              # Pitch Prep
bun run dev:giki        # Giki AI
bun run dev:all         # Both projects
```

## 4. Development Tracker Integration
**Google Sheets**: Strategic project management hierarchy (PROJECT → MILESTONE → EPIC → STORY → TASK → SUBTASK)
**TodoWrite**: Tactical P/I/V decomposition of tasks/subtasks
**GitHub Board #6**: TodoWrite ↔ GitHub Issues sync

## 5. Development Commands
**/develop-mode**: Product development with Context7 research + visual validation

## 6. Tactical Task Queue

Moved to docs/02-IMPLEMENTATION/ACTIVE-TODOS.md under “Tactical Task Queue (Next 2–3 sessions)”.

- Follow Enhanced Todo System and QUICK-TODO-DECOMPOSITION
- Execute P/I/V todos with validation evidence
- Use Development Commands (§3) for startup and testing
