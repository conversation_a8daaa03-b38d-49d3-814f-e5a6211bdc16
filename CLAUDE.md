# Claude: Monorepo Project Configuration

## 0. Core Principles

### Dual-Project Awareness
Consider both Giki AI and Pitch Prep in every decision.


## 1. Memory Loading Strategy

**68.7% reduction achieved** - 14K tokens (was 44K)

### CRITICAL CORE (Always Load)
- @.claude/memory/global/development-quality.md
- @.claude/memory/global/production-code-purity.md
- @.claude/memory/global/development-standards.md
- @.claude/memory/global/QUICK-TODO-DECOMPOSITION.md

### Essential Framework (Always Load)
- @.claude/memory/global/ddc-framework.md
- @.claude/memory/shared/project-architecture.md

### Contextual Loading
#### Workflows & Planning
- @.claude/memory/global/workflows.md
- @.claude/memory/global/enhanced-todo-system.md

#### Tools & Visual Analysis
- @.claude/memory/global/tools.md
- @.claude/memory/global/sdk-verification.md

#### Brand & Design
- @.claude/memory/shared/brand-color-clarification.md

### Project-Specific
#### Giki AI
- @.claude/memory/giki-ai/product-vision-memory.md
- @.claude/memory/giki-ai/debugging-playwright-memory.md
- @.claude/memory/giki-ai/testing-playwright-memory.md

#### Pitch Prep
- @.claude/memory/pitch-prep/pitch-prep-complete.md

## 2. Active Projects

**Giki AI**: Financial categorization, 87%+ accuracy, Port 3000/8000
**Pitch Prep**: Shark Tank prep, <30min reports, Port 3001/8001

## 3. Development Commands

```bash
cd /Users/<USER>/giki-ai-workspace  # MANDATORY
bun run dev              # Pitch Prep
bun run dev:giki        # Giki AI
bun run dev:all         # Both projects
```

## 4. Development Tracker Integration
**Google Sheets**: Strategic project management hierarchy (PROJECT → MILESTONE → EPIC → STORY → TASK → SUBTASK)
**TodoWrite**: Tactical P/I/V decomposition of tasks/subtasks
**GitHub Board #6**: TodoWrite ↔ GitHub Issues sync

## 5. Development Commands
**/develop-mode**: Product development with Context7 research + visual validation

## 6. Claude Tactical Task Queue (Next 2–3 sessions)

Context summary (recent commits):
- service reorg checkpoint: ns (multiple)
- feat: Complete dashboard design validation and fix auth infinite loop
- feat: Complete login page redesign with two-panel layout
- feat: Landing page redesign with prototype design system integration
- feat: Comprehensive template integration testing Phase 1A + isolated template tests

Follow the Enhanced Todo System and QUICK-TODO-DECOMPOSITION. Every TASK/SUBTASK below must be executed with P/I/V todos and immediate validation evidence.

### 6.1 Cross‑Project Coordination
- [COORDINATION] Service reorg completion and verification
  - [TASK: REORG-P] Plan final folder/service map and script impacts (identify start/stop/test scripts to update)
  - [TASK: REORG-I] Apply minimal changes; ensure dev:all, dev:giki, dev:pitch-prep still work
  - [TASK: REORG-V] Validate with: bun run status; start both projects; smoke endpoints; attach logs
- [CAPACITY] Maintain 60/40 split (Giki/Pitch‑Prep) for this block; log effort in worklog

### 6.2 Giki AI — Near‑term priorities (aligns with Product Vision)
- [EPIC] E1: Upload → Categorize → Export happy path hardening
  - [STORY] S1: Auth + App startup stability (post “auth infinite loop” fix)
    - [TASK: S1-P] Define regression criteria: no redirect loops; stored session resume; protected routes
    - [TASK: S1-I] Add Playwright e2e covering login, logout, refresh, deep-link
    - [TASK: S1-V] Run: bun run test:e2e:direct; attach key screenshots and logs
  - [STORY] S2: Dashboard redesign validation lock‑in
    - [TASK: S2-P] Snapshot target states and acceptance checklist from current UI
    - [TASK: S2-I] Add visual regression baselines for key routes (dashboard, reports, upload)
    - [TASK: S2-V] Run: bun run test:e2e:direct; approve/reject diffs; store artifacts
  - [STORY] S3: Real AI pipeline verification (no mocks)
    - [TASK: S3-P] Audit code for mocked/stubbed AI calls; enumerate modules; map to @google/genai usage
    - [TASK: S3-I] Replace remaining stubs with real calls; gate with env; add unit contract tests
    - [TASK: S3-V] Use sdk‑verification checklist; run targeted integration tests; include logs
  - [STORY] S4: Export system completeness check (CSV/Excel/QB Online/Desktop, Xero, Zoho, Wave, Sage, Tally)
    - [TASK: S4-P] Define minimal dataset; expected rows/headers per format
    - [TASK: S4-I] Run scripts/export-system-validation.py; fix failures iteratively
    - [TASK: S4-V] Attach generated files from export_validation_output and final JSON report
  - [STORY] S5: Performance smoke (customer‑facing <500ms; batch processing target <60s/1k tx)
    - [TASK: S5-P] Select 2–3 critical endpoints for latency check; define thresholds
    - [TASK: S5-I] Add lightweight perf test in tests/performance; instrument timing
    - [TASK: S5-V] Run: bun run test:performance if available or via playwright tags; record metrics

### 6.3 Pitch Prep — Stability after service reorg
- [EPIC] E2: App/API cohesion and test coverage expansion
  - [STORY] S6: Service boot and health checks
    - [TASK: S6-P] List services/ports; confirm envs; define success signals
    - [TASK: S6-I] Start with bun run dev (or dev:pitch-prep); fix any port/env issues
    - [TASK: S6-V] bun run status; attach logs/ping health endpoints
  - [STORY] S7: Template integration tests Phase 1B
    - [TASK: S7-P] Choose next batch of templates to cover; document expected outputs
    - [TASK: S7-I] Extend tests under tests/integration and tests/e2e to cover prompts/templates
    - [TASK: S7-V] Run: bun run test:integration and bun run test:e2e:direct; store artifacts

### 6.4 Tooling and Developer Experience
- [STORY] S8: Scripts and docs alignment with reorg
  - [TASK: S8-P] Audit scripts/ start/stop/test scripts for broken paths
  - [TASK: S8-I] Update scripts to reference new locations; keep commands in package.json stable
  - [TASK: S8-V] Prove with: bun run dev:all; bun run test; bun run lint; attach success logs

Execution notes
- Always run from repo root (see §3 Development Commands)
- Evidence attachments are mandatory: logs, screenshots, reports
- After each code change, immediately add a validation todo and execute it
