# Bun configuration file

[install]
# Performance optimizations
auto = "disable"              # Disable auto-install on import
frozen = false                # Allow lockfile updates in development
exact = false                 # Use ^ for versions (better deduplication)
workspaces = true            # Enable workspace support
production = false           # Install all dependencies by default

# Cache configuration
[install.cache]
disable = false              # Use cache for faster installs
dir = "~/.bun/install/cache" # Cache directory

# Registry configuration
[install.registry]
default = "https://registry.npmjs.org"
timeout = 60000              # 60 second timeout

# Lockfile configuration
[install.lockfile]
save = true                  # Save lockfile after install

[run]
# Performance optimizations for scripts
silent = false               # Show output for debugging
bun = true                   # Use bun for scripts

[test]
# Root directory for tests
root = "."

# Preload files before running tests
# preload = ["./test-setup.ts"]

# Use less memory for tests
smol = true

# Test timeout in milliseconds
timeout = 10000

# Coverage configuration
coverage = true
coverageThreshold = { line = 80, function = 80, branch = 80, statement = 80 }
coverageSkipTestFiles = true

# Reporter format
bail = 1

# Telemetry
telemetry = false            # Disable telemetry for privacy

# Development
[debug]
htmx = false                # Disable HTMX debugging
quiet = false               # Show normal output

# Bundle configuration (when using Bun.build)
[bundle]
minify = true               # Minify in production
sourcemap = "external"      # External sourcemaps
target = "browser"          # Target environment

# Macro configuration
[macro]
enabled = true              # Enable compile-time macros
max-depth = 10             # Maximum macro recursion depth