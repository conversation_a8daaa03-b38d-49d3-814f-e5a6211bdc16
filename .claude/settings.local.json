{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Write(/Users/<USER>/giki-ai-workspace/*)", "Edit(/Users/<USER>/giki-ai-workspace/*)", "MultiEdit(/Users/<USER>/giki-ai-workspace/*)", "NotebookEdit(*)", "WebFetch(*)", "WebSearch(*)", "TodoWrite(*)", "Bash(npm lint:api:*)", "Bash(npm lint:app:*)", "Bash(npm test:*)", "Bash(npm serve:api:*)", "Bash(git commit:*)", "Bash(uv run pytest:*)", "Bash(rg:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(ln:*)", "Bash(npm nx:*)", "mcp__postgres-dev__query", "<PERSON><PERSON>(curl:*)", "mcp__filesystem__list_directory", "mcp__filesystem__directory_tree", "Bash(grep:*)", "Bash(git add:*)", "mcp__nx-mcp__nx_workspace", "Bash(echo)", "Bash(psql:*)", "Bash(export:*)", "mcp__postgres-dev__list_tables", "mcp__postgres-dev__describe_table", "mcp__postgres-dev__list_schemas", "mcp__filesystem__get_file_info", "mcp__filesystem__search_files", "mcp__postgres-dev__explain_query", "Bash(npm vite:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(uv run:*)", "Bash(gh pr diff:*)", "Bash(gh pr checkout:*)", "Bash(gh pr view:*)", "Bash(git branch:*)", "Bash(git push:*)", "Bash(git checkout:*)", "mcp__filesystem__read_file", "<PERSON><PERSON>(python test:*)", "WebFetch(domain:github.com)", "Bash(git reset:*)", "<PERSON><PERSON>(source:*)", "Bash(npm eslint:*)", "Bash(timeout 30 npm nx:*)", "Bash(git tag:*)", "Bash(npm prettier:*)", "<PERSON><PERSON>(nx show project:*)", "<PERSON><PERSON>(jq:*)", "Bash(npm add:*)", "<PERSON><PERSON>(npx playwright test:*)", "mcp__screenshot__analyze_screenshot_comprehensively", "Bash(npx:*)", "mcp__filesystem__write_file", "<PERSON><PERSON>(chmod:*)", "Bash(npm run build:*)", "mcp__filesystem__read_multiple_files", "mcp__postgres-dev__get_constraints", "Bash(cp * /Users/<USER>/giki-ai-workspace/*)", "<PERSON><PERSON>(echo:*)", "Bash(mkdir /Users/<USER>/giki-ai-workspace/*)", "Bash(mv * /Users/<USER>/giki-ai-workspace/*)", "<PERSON><PERSON>(true)", "Bash(tree:*)", "mcp__filesystem__list_allowed_directories", "Bash(touch /Users/<USER>/giki-ai-workspace/*)", "<PERSON><PERSON>(python:*)", "mcp__filesystem__edit_file", "Bash(brew install:*)", "Bash(brew services start:*)", "Bash(redis-cli:*)", "Bash(gcloud config get-value:*)", "Bash(gcloud builds list:*)", "Bash(awk:*)", "<PERSON><PERSON>(sed:*)", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "<PERSON><PERSON>(npx prettier:*)", "Bash(npm audit:*)", "<PERSON><PERSON>(pip-audit:*)", "Bash(npm update:*)", "<PERSON><PERSON>(playwright --version)", "Bash(git rm:*)", "Bash(npm:*)", "Bash(node:*)", "Bash(pytest:*)", "Bash(npm test:*)", "Bash(npm run build:*)", "Bash(bash:*)", "<PERSON><PERSON>(test:*)", "<PERSON><PERSON>(pkill:*)", "WebFetch(domain:docs.anthropic.com)", "mcp__postgres-dev__list_indexes", "WebFetch(domain:giki.ai)", "<PERSON><PERSON>(scripts/test-schema-interpretation.sh:*)", "Bash(kill:*)", "mcp__screenshot__analyze_screenshots", "Bash(/dev/null)", "Bash(timeout 60 npm run lint:check)", "Bash(ENVIRONMENT=development python:*)", "WebFetch(domain:cloud.google.com)", "WebFetch(domain:pypi.org)", "<PERSON><PERSON>(npx playwright test:*)", "<PERSON><PERSON>(timeout:*)", "Bash(./scripts/generate_code_index.sh:*)", "Bash(for:*)", "Bash(do)", "Bash(done)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(diff:*)", "Bash(do echo \"=== $domain ===\")", "<PERSON><PERSON>(tail:*)", "Bash(schemas.py)", "Bash(bun:*)", "Bash(ruff check:*)", "<PERSON><PERSON>(gcloud auth list:*)", "Bash(git restore:*)", "Bash(brew services info:*)", "Bash(open http://localhost:4200/)", "Bash(tsc --noEmit)", "<PERSON><PERSON>(axe:*)", "Bash(./demos/run-demo.sh:*)", "WebFetch(domain:bun.sh)", "Bash(smart_log:*)", "WebFetch(domain:google.github.io)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(cp:*)", "Bash(./scripts/validate-deployment.sh:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_click", "mcp__playwright__browser_type", "<PERSON><PERSON>(uv:*)", "Bash(turbo run build:*)", "<PERSON><PERSON>(touch:*)", "Bash(gh auth:*)", "Bash(./.github/scripts/migrate-secrets-to-github.sh:*)", "Bash(./.github/scripts/auto-migrate-secrets.sh:*)", "Bash(./.github/scripts/migrate-all-secrets.sh:*)", "Bash(brew services:*)", "<PERSON><PERSON>(createdb:*)", "Bash(DATABASE_URL=postgresql://postgres:postgres@localhost:5432/pitch_prep_dev uv run uvicorn src.main:app --host 0.0.0.0 --port 8001 --reload)", "Bash(DATABASE_URL=postgresql://postgres:postgres@localhost:5432/pitch_prep_dev SECRET_KEY=dev-secret-key-change-in-production uv run uvicorn src.main:app --host 0.0.0.0 --port 8001 --reload)", "Bash(/Users/<USER>/giki-ai-workspace/scripts/cleanup-playwright.sh:*)", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_evaluate", "mcp__playwright__browser_network_requests", "mcp__playwright__browser_resize", "mcp__playwright__browser_press_key", "Bash(/usr/bin/python3:*)", "Bash(pip3 install:*)", "mcp__playwright__browser_close", "Bash(turbo run:*)", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_snapshot", "mcp__Context7__resolve-library-id", "mcp__Context7__get-library-docs", "Bash(./scripts/audit-design-tokens.sh:*)", "Bash(/Users/<USER>/giki-ai-workspace/scripts/fix-port-references.sh:*)", "Bash(pg_isready:*)", "Bash(./scripts/giki-startup-with-health.sh:*)", "Bash(VITE_API_BASE_URL=http://localhost:8000/api/v1 VITE_ENVIRONMENT=development bun run dev)", "Bash(./scripts/cleanup-processes.sh:*)", "Bash(/Users/<USER>/giki-ai-workspace/scripts/test-auth-flow.sh:*)", "Bash(./scripts/test-user-journey.sh:*)", "Bash(./scripts/workspace-commands/serve-all-3file.sh:*)", "Bash(gh project create:*)", "Bash(gh project list:*)", "Bash(gh project field-list:*)", "Bash(gh project field-create:*)", "Bash(gh issue create:*)", "Bash(gh label create:*)", "Bash(gh project item-add:*)", "Bash(gh project view:*)", "<PERSON><PERSON>(gh project edit:*)", "Bash(gh label:*)", "Bash(git remote get-url:*)", "Bash(do gh project item-add 6 --owner nikhil<PERSON><PERSON><PERSON> --url https://github.com/nikhilgikiai/giki-ai-workspace/issues/$issue)", "Bash(gh issue list:*)", "Bash(./deploy-production.sh)", "Bash(./deploy-quick.sh)", "<PERSON><PERSON>(sips:*)", "mcp__playwright__browser_handle_dialog", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev SECRET_KEY=dev-secret-key GOOGLE_API_KEY=$GOOGLE_API_KEY uv run uvicorn src.api.main:app --host 0.0.0.0 --port 8001)", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev uv run uvicorn src.api.main:app --host 0.0.0.0 --port 8001 --reload)", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev SECRET_KEY=dev-secret-key GOOGLE_API_KEY=$GOOGLE_API_KEY uv run uvicorn src.api.main:app --host 0.0.0.0 --port 8001 --reload)", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/critical-analysis-protocols-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/development-framework-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/quality-standards-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/file-organization-patterns-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/git-commit-protocols-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/todo-core-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/todo-workflows-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/shared/stack-decisions.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/shared/monorepo-architecture.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/shared/development-standards.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/shared/ci-cd-pipeline.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/shared/project-context.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/development-automation-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/core-tools-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/mcp-integration-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/parallel-execution-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/session-context-framework-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/document-structure-enforcement-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/browser-testing-automation-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/migration-strategies-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/global/service-configuration-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/giki-ai/product-vision-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/giki-ai/testing-playwright-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/giki-ai/debugging-playwright-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/pitch-prep/product-vision-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/pitch-prep/data-pipeline-architecture-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/pitch-prep/project-overview-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/pitch-prep/genai-sdk-best-practices-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/pitch-prep/chained-ai-analysis-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/pitch-prep/no-mock-ai-memory.md )", "Bash(/Users/<USER>/giki-ai-workspace/.claude/memory/pitch-prep/transcript-fetcher-integration-memory.md)", "mcp__playwright__browser_navigate_back", "Bash(/Applications/WezTerm.app/Contents/MacOS/wezterm --version)", "Bash(/Applications/WezTerm.app/Contents/MacOS/wezterm ls-fonts --list-system)", "Bash(rsync:*)", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev SECRET_KEY=dev-secret-key GOOGLE_API_KEY=$GOOGLE_API_KEY uv run uvicorn src.pitch_prep.api.main:app --host 0.0.0.0 --port 8001 --reload)", "Bash(vercel ls:*)", "<PERSON><PERSON>(vercel:*)", "mcp__playwright__browser_select_option", "Bash(pg_config:*)", "Bash(brew uninstall:*)", "Bash(brew info:*)", "Bash(brew search:*)", "<PERSON><PERSON>(git clone:*)", "<PERSON><PERSON>(make:*)", "Bash(DB_USER=nikhilsingh DB_PASSWORD=\"\" bun test-vector.ts)", "<PERSON><PERSON>(playwright install)", "Bash(VITE_API_BASE_URL=http://localhost:8001/api/v1 bun run dev --host 0.0.0.0 --port 3001)", "Bash(GEMINI_API_KEY=test NODE_ENV=development PORT=8001 bun run src/index.ts)", "Bash(./test-dr-paws-api.sh:*)", "Bash(GEMINI_API_KEY=AIzaSyBhyNf92o6Vyd0HYSwES6symAFP_ZDvZxY bun run test-ai.ts)", "Bash(NODE_ENV=development bun run serve:pitch-prep)", "Bash(./test-sse-curl.sh:*)", "Bash(open test-frontend-sse.html)", "<PERSON><PERSON>(open:*)", "Bash(# Move test files from old tests/ to new src/__tests__/\ncd /Users/<USER>/giki-ai-workspace/apps/pitch-prep/server\n\n# Move unit tests\nif [ -f tests/unit/services/aiAnalysisService.test.ts ]; then\n  mv tests/unit/services/aiAnalysisService.test.ts src/__tests__/unit/\nfi\n\nif [ -f tests/unit/services/mockGenAI.ts ]; then\n  mv tests/unit/services/mockGenAI.ts src/__tests__/unit/\nfi\n\n# Move integration tests\nif [ -f tests/integration/api/analysis.test.ts ]; then\n  mv tests/integration/api/analysis.test.ts src/__tests__/integration/\nfi\n\n# Move e2e tests\nif [ -f tests/e2e/complete-analysis.test.ts ]; then\n  mv tests/e2e/complete-analysis.test.ts src/__tests__/e2e/\nfi\n\n# Move test utilities and fixtures to a helpers directory\nmkdir -p src/__tests__/helpers\nif [ -f tests/utils/testHelpers.ts ]; then\n  mv tests/utils/testHelpers.ts src/__tests__/helpers/\nfi\n\nif [ -f tests/fixtures/testData.ts ]; then\n  mv tests/fixtures/testData.ts src/__tests__/helpers/\nfi\n\necho \"\"Files moved successfully\"\")", "Bash(# Remove old tests directory if it''s empty\ncd /Users/<USER>/giki-ai-workspace/apps/pitch-prep/server\nif [ -d tests ]; then\n  # Check if there are any files left\n  remaining=$(find tests -type f | wc -l)\n  if [ \"\"$remaining\"\" -eq 1 ] && [ -f tests/README.md ]; then\n    rm -rf tests\n    echo \"\"Removed old tests directory\"\"\n  else\n    echo \"\"Old tests directory still has $remaining files\"\"\n  fi\nfi)", "Bash(GEMINI_API_KEY=$GOOGLE_API_KEY bun run /Users/<USER>/giki-ai-workspace/test-gemini-simple.js)", "Bash(GEMINI_API_KEY=$GOOGLE_API_KEY bun run ../../../test-gemini-simple.js)", "Bash(./scripts/workspace-commands/serve-pitch-prep.ts)", "Bash([ -n \"$GEMINI_API_KEY\" ])", "Bash(./scripts/workspace-commands/cleanup.ts:*)", "Bash(git log:*)", "Bash(NODE_ENV=development GEMINI_API_KEY=test npx tsc --noEmit src/services/multiGeminiReviewService.ts)", "Bash(NODE_ENV=development GEMINI_API_KEY=$GEMINI_API_KEY bun test-complete-modernization.ts)", "<PERSON><PERSON>(od:*)", "<PERSON><PERSON>(git worktree:*)", "Bash(git stash:*)", "Bash(git merge:*)", "Bash(GEMINI_API_KEY=test timeout 20 bun run src/index.ts)", "Bash(GEMINI_API_KEY=AIzaSyBhyNf92o6Vyd0HYSwES6symAFP_ZDvZxY bun run test-genai-integration.ts)", "Bash(# Delete clearly redundant files that have been fully consolidated\n\n# Global files fully merged into consolidated files\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/global/critical-analysis-protocols-memory.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/global/git-commit-protocols-memory.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/global/mcp-integration-memory.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/global/development-automation-memory.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/global/parallel-execution-memory.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/global/session-context-framework-memory.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/global/development-framework-memory.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/global/document-structure-enforcement-memory.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/global/todo-core-memory.md\n\n# Shared files fully merged into project-architecture.md  \nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/shared/monorepo-architecture.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/shared/stack-decisions.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/shared/project-context.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/shared/development-standards.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/shared/ci-cd-pipeline.md\n\n# Migration files fully merged into migration-strategies.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/shared/bhvr-migration-strategy.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/shared/bhvr-migration-best-practices.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/shared/bun-test-migration-strategy.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/shared/bun-shell-scripts-strategy.md\n\n# Pitch Prep files merged into pitch-prep-complete.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/pitch-prep/data-pipeline-architecture-memory.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/pitch-prep/port-configuration-memory.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/pitch-prep/product-vision-memory.md\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/pitch-prep/project-overview-memory.md\n\necho \"\"Deleted 22 redundant memory files\"\")", "Bash(# Delete additional redundant files\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/global/background-development-workflow.md  # Merged into development-standards\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/global/browser-automation-memory.md  # Merged into workflows\nrm -f /Users/<USER>/giki-ai-workspace/.claude/memory/giki-ai/port-configuration-memory.md  # Ports are in project-architecture\n\necho \"\"Deleted 3 more redundant files\"\"\n\n# Check what''s left\necho -e \"\"\\n=== Remaining memory files ===\"\"\nfind /Users/<USER>/giki-ai-workspace/.claude/memory -type f -name \"\"*.md\"\" | wc -l\necho \"\"files remaining\"\")", "Bash(gcloud services enable:*)", "Bash(gcloud iam service-accounts describe:*)", "Bash(gcloud organizations:*)", "Bash(gcloud domains:*)", "Bash(gh repo list:*)", "mcp__playwright__browser_tab_new", "mcp__playwright__browser_tab_select", "mcp__playwright__browser_tab_close", "mcp__playwright__browser_tab_list", "Bash(vite build)", "Bash(GEMINI_API_KEY=$GOOGLE_API_KEY bun run test-genai-sdk-integration.ts)", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev psql -c \"\\dt\")", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev psql -c \"SELECT table_name FROM information_schema.tables WHERE table_schema = ''public'' AND table_name IN (''users'', ''payments'', ''user_companies'', ''analysis_sessions'', ''shark_tank_companies'');\")", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev psql:*)", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev bun run test-payment-integration.ts)", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev bun -e \"\nimport { sql } from ''./src/database/connection.ts'';\ntry {\n  const result = await sql\\`SELECT current_database(), current_user\\`;\n  console.log(''Connected to:'', result[0]);\n  await sql.end();\n} catch (error) {\n  console.error(''Connection error:'', error.message);\n}\n\")", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev bun -e \"\nprocess.env.DATABASE_URL = ''postgresql://nikhilsingh@localhost:5432/pitch_prep_dev'';\nconst { sql } = await import(''./src/database/connection.ts'');\nconst result = await sql\\`SELECT table_name FROM information_schema.tables WHERE table_name IN (''users'', ''payments'') ORDER BY table_name\\`;\nconsole.log(''Tables found:'', result);\nawait sql.end();\n\")", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev bun -e \"\nprocess.env.DATABASE_URL = ''postgresql://nikhilsingh@localhost:5432/pitch_prep_dev'';\nconsole.log(''Process env DB:'', process.env.DATABASE_URL);\nconst { sql } = await import(''./src/database/connection.ts'');\nconst result = await sql\\`SELECT current_database(), current_user\\`;\nconsole.log(''Actually connected to:'', result[0]);\nconst tables = await sql\\`SELECT table_name FROM information_schema.tables WHERE table_schema = ''public'' ORDER BY table_name\\`;\nconsole.log(''Available tables:'', tables.slice(0, 10));\nawait sql.end();\n\")", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev bun -e \"\nprocess.env.DATABASE_URL = ''postgresql://nikhilsingh@localhost:5432/pitch_prep_dev'';\nconst { sql } = await import(''./src/database/connection.ts'');\nconst allTables = await sql\\`SELECT table_name FROM information_schema.tables WHERE table_schema = ''public'' ORDER BY table_name\\`;\nconsole.log(''All tables:'');\nallTables.forEach(t => console.log(t.table_name));\n\nconst specificTables = await sql\\`SELECT table_name FROM information_schema.tables WHERE table_schema = ''public'' AND table_name IN (''users'', ''payments'', ''user_companies'') ORDER BY table_name\\`;\nconsole.log(''\\nSpecific tables:'');\nconsole.log(specificTables);\nawait sql.end();\n\")", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev bun -e \"\nprocess.env.DATABASE_URL = ''postgresql://nikhilsingh@localhost:5432/pitch_prep_dev'';\nconst { sql } = await import(''./src/database/connection.ts'');\nconst result = await sql\\`SELECT current_database()\\`;\nconsole.log(''Database:'', result[0].current_database);\n\n// Check if our tables exist\nconst checkUsers = await sql\\`SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = ''public'' AND table_name = ''users'') as exists\\`;\nconst checkPayments = await sql\\`SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = ''public'' AND table_name = ''payments'') as exists\\`;\n\nconsole.log(''Users table exists:'', checkUsers[0].exists);\nconsole.log(''Payments table exists:'', checkPayments[0].exists);\nawait sql.end();\n\")", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev bun -e \"\nprocess.env.DATABASE_URL = ''postgresql://nikhilsingh@localhost:5432/pitch_prep_dev'';\nconst { sql } = await import(''./src/database/connection.ts'');\n\nconst currentDb = await sql\\`SELECT current_database(), current_user\\`;\nconsole.log(''Connected to:'', currentDb[0]);\n\nconst checkUsers = await sql\\`SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = ''public'' AND table_name = ''users'') as exists\\`;\nconst checkPayments = await sql\\`SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = ''public'' AND table_name = ''payments'') as exists\\`;\n\nconsole.log(''Users table exists:'', checkUsers[0].exists);\nconsole.log(''Payments table exists:'', checkPayments[0].exists);\n\nawait sql.end();\n\")", "Bash(git ls-tree:*)", "mcp__playwright__browser_file_upload", "Bash(./.claude/scripts/validate-memory-loading.sh:*)", "Bash(PORT=8000 bun run dev)", "Bash(VITE_API_BASE_URL=https://pitch-prep-api-production.up.railway.app/api/v1 VITE_ENVIRONMENT=production bun run build)", "<PERSON><PERSON>(railway login:*)", "<PERSON><PERSON>(gcloud sql:*)", "Bash(gcloud auth activate-service-account:*)", "Bash(gcloud config set:*)", "Bash(gcloud builds submit:*)", "Bash(gcloud projects add-iam-policy-binding:*)", "Bash(--member=\"serviceAccount:<EMAIL>\" )", "<PERSON>sh(--role=\"roles/cloudbuild.builds.builder\")", "Bash(--member=\"serviceAccount:<EMAIL>\" )", "Bash(--condition=None)", "Bash(docker build:*)", "Bash(--member=\"serviceAccount:<EMAIL>\" )", "Bash(--role=\"roles/storage.objectAdmin\" )", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev bun run src/scripts/simpleImport.ts)", "Bash(/Users/<USER>/giki-ai-workspace/scripts/validate-todo-decomposition.sh:*)", "Bash(./fetch_all_seasons.sh:*)", "Bash(DATABASE_URL=postgresql://nikhilsingh@localhost:5432/pitch_prep_dev bun run src/scripts/prefillTranscriptDatabase.ts)", "Bash(GOOGLE_APPLICATION_CREDENTIALS=dev-service-account.json uv run python -c \"\nimport sys\nsys.path.insert(0, ''src'')\nfrom pathlib import Path\nprint(''Current working directory:'', Path.cwd())\nprint(''__file__ would be:'', Path(''src/transcript_fetcher/youtube_fetcher.py'').resolve())\ntest_path = Path(''src/transcript_fetcher/youtube_fetcher.py'').parent.parent.parent / ''data'' / ''master_video_list.json''\nprint(''Calculated path:'', test_path.resolve())\nprint(''Path exists:'', test_path.exists())\nprint(''Actual data dir:'', Path(''data'').resolve())\nprint(''Data dir exists:'', Path(''data'').exists())\n\")", "Bash(GOOGLE_APPLICATION_CREDENTIALS=dev-service-account.json uv run python -c \"\nimport sys\nsys.path.insert(0, ''src'')\nfrom transcript_fetcher.youtube_fetcher import YouTubeFetcher\nfrom pathlib import Path\n\n# Create a fetcher and test the path\nfetcher = YouTubeFetcher()\n\n# Check the path calculation\nimport transcript_fetcher.youtube_fetcher\nfile_path = Path(transcript_fetcher.youtube_fetcher.__file__)\nprint(''YouTube fetcher file path:'', file_path)\nprint(''Parent:'', file_path.parent)\nprint(''Parent.parent:'', file_path.parent.parent)\nprint(''Parent.parent.parent:'', file_path.parent.parent.parent)\n\n# Check the actual path construction\nmaster_video_path = file_path.parent.parent.parent / ''data'' / ''master_video_list.json''\nfiltered_path = file_path.parent.parent.parent / ''data'' / ''filtered_video_list.json''\n\nprint(''Master video path:'', master_video_path)\nprint(''Master exists:'', master_video_path.exists())\nprint(''Filtered path:'', filtered_path)\nprint(''Filtered exists:'', filtered_path.exists())\n\n# Try to load the master file\nif master_video_path.exists():\n    import json\n    with open(master_video_path) as f:\n        data = json.load(f)\n    print(''Master file loaded successfully, seasons:'', list(data.get(''seasons'', {}).keys()))\n\")", "Bash(DEBUG_EXTRACTION=true bun run single-transcript.integration.test.ts)", "Bash(GEMINI_API_KEY=$GOOGLE_API_KEY bun run --hot src/index.ts)", "Bash(GEMINI_API_KEY=$GOOGLE_API_KEY bun run src/services/transcriptSegmentationService.ts)", "Bash(GEMINI_API_KEY=$GOOGLE_API_KEY timeout 60 bun run src/services/transcriptSegmentationService.ts)", "Bash(VITE_API_BASE_URL=http://localhost:8001/api/v1 bun run dev --port 3001)", "Bash(GEMINI_API_KEY=$GOOGLE_API_KEY bun test apps/giki/tests/integration/ai-sdk-services.test.ts)", "Bash(GEMINI_API_KEY=$GOOGLE_API_KEY bun run extraction-pipeline.test.ts)", "Bash(GOOGLE_GENERATIVE_AI_API_KEY=$GOOGLE_API_KEY GEMINI_API_KEY=$GOOGLE_API_KEY bun test tests/integration/ai-sdk-services.test.ts --grep \"Extract vendor from Amazon\")", "Bash(GEMINI_API_KEY=$GOOGLE_API_KEY bun run improved-extraction.test.ts)", "Bash(GEMINI_API_KEY=$GOOGLE_API_KEY timeout 15 bun test apps/giki/tests/integration/ai-sdk-services.test.ts --grep \"Categorize Starbucks\")", "Bash(GEMINI_API_KEY=$GOOGLE_API_KEY bun run template-website-analysis.integration.test.ts)", "Bash(GEMINI_API_KEY=$GOOGLE_API_KEY bun run template-question-generation.integration.test.ts)"], "deny": ["Read(node_modules)", "Read(.nx)", "Read(.venv)"], "additionalDirectories": ["/Users/<USER>/.claude", "/Users/<USER>/mcp-servers/"]}, "enabledMcpjsonServers": ["screenshot-validator", "context8"]}