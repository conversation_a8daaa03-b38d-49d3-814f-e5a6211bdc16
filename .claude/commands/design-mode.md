---
description: "Design System Architecture mode - autonomous wireframing, prototyping, and design system coordination"
model: claude-sonnet-4-20250514
allowed-tools: [Task, Read, Write, Edit, TodoWrite, Grep, Glob, LS, mcp__screenshot__analyze_screenshots, mcp__playwright__browser_navigate, mcp__playwright__browser_take_screenshot, mcp__playwright__browser_snapshot]
argument-hint: "[project] \"[design focus or 'full-audit']\""
---

# 🎨 Design System Architecture Mode

**Autonomous wireframing, prototyping, and design system coordination combining user research, visual design, and implementation-ready specifications.**

## 🎨 DESIGN ASSET ORGANIZATION

### Root Design Folder Structure: `/design/`
Design assets operate outside DDC documentation structure but support development workflow:

```
design/{project}/
├── wireframes/ # Markdown: layout descriptions, user flows, information architecture
├── mockups/ # Static HTML: individual screens, components, specific states
├── prototypes/ # Interactive HTML: complete user journeys, clickable experiences
└── screenshots/ # Comparison images for quality assurance:
 ├── prototype-vs-local/ # Design intent vs development progress
 ├── prototype-vs-production/ # Design intent vs live deployment 
 └── local-vs-production/ # Development vs production environment
```

### Design-DDC Integration:
- **Wireframes** inform Context documents (user journey understanding)
- **Prototypes** guide Implementation documents (development specifications)
- **Screenshots** provide Validation document evidence (quality assurance)
- **All design assets support DDC requirements without being documentation themselves**

## Session Continuity Framework (CRITICAL)

### Session Context Awareness Protocol
**MANDATORY**: All design commands must understand current session context before beginning work.

1. **Current Session State Assessment**:
 - **Active Conversation**: Analyze last 3-5 user interactions to understand current design focus
 - **Current Todos**: Load TodoWrite state to identify active design work and blockers
 - **Recent Results**: Check for previous design-planner, ui-validator, or frontend-developer outputs
 - **User Intent Detection**: Infer current design priorities from recent conversation patterns

2. **Project Maturity Assessment**:
 - **Implementation Status**: Check DDC documents to understand what's already built vs. planned
 - **Current Phase**: Identify whether project is in discovery, development, refinement, or production
 - **Design Debt**: Assess existing design inconsistencies and technical debt from validation documents
 - **Completion Level**: Understand project maturity to avoid redundant setup instructions

3. **Contextual Continuity**:
 - **Build on Previous Work**: Continue from last design session rather than starting fresh
 - **Reference Prior Decisions**: Maintain design decision continuity from previous sessions
 - **Address Known Issues**: Prioritize resolving previously identified design problems
 - **Maintain Focus**: Stay aligned with user's current design objectives

## Critical DDC Document Loading (AUTOMATIC)

### Universal Design Context (Always Load)
- @docs/00-CONTEXT/TECHNOLOGY-STACK.md (technical design constraints)
- @docs/02-IMPLEMENTATION/UNIFIED-ROADMAP.md (current design priorities and milestones)
- @docs/02-IMPLEMENTATION/ACTIVE-TODOS.md (cross-project design work coordination)

## Core Intelligence Protocol

### Phase 1: Strategic Design Context Assessment
**Load critical context for design excellence:**

1. **Strategic Design Direction**:
 - Read docs/02-IMPLEMENTATION/UNIFIED-ROADMAP.md for UI/UX milestones and design priorities
 - Load current TodoWrite state for design-related work and blockers
 - Analyze GitHub Projects Board #6 for design issues and requirements

2. **Current Design State Analysis**:
 - Screenshot analysis of all application pages for visual audit
 - Design asset inventory (wireframes, prototypes, design tokens)
 - Component library assessment and design system consistency
 - Brand compliance validation across touchpoints

3. **User Experience Context**:
 - User journey analysis from product documentation
 - Accessibility compliance assessment (WCAG AA standards)
 - Cross-device responsive design validation
 - Performance impact of design decisions

### Phase 1.5: DDC Document Integration (MANDATORY)

**Document-Driven Context Protocol:**
All design work MUST be guided by existing DDC documents - NEVER create random documentation.

**PROJECT-SPECIFIC DDC DOCUMENT LOADING** (Dynamic based on project argument):

**For Giki AI Project (argument = "giki")**:
1. **Context Documents**: 
 - docs/giki-ai/00-CONTEXT/PRODUCT-COMPLETE.md (B2B design strategic direction)
 - docs/giki-ai/00-CONTEXT/PRODUCT-COMPLETE.md (financial platform user experience requirements)
 - docs/giki-ai/00-CONTEXT/PRODUCT-COMPLETE.md (Giki AI design system constraints)

2. **Requirements Documents**:
 - docs/giki-ai/01-REQUIREMENTS/REQUIREMENTS-COMPLETE.md (B2B design requirements from user needs)
 - docs/giki-ai/01-REQUIREMENTS/REQUIREMENTS-COMPLETE.md (Giki AI design success criteria)

3. **Implementation Documents**:
 - docs/giki-ai/02-IMPLEMENTATION/IMPLEMENTATION-COMPLETE.md (Giki AI design priorities and milestones)
 - docs/giki-ai/02-IMPLEMENTATION/IMPLEMENTATION-COMPLETE.md (current Giki AI design implementations)

4. **Validation Documents**:
 - docs/giki-ai/03-VALIDATION/VALIDATION-COMPLETE.md (Giki AI design quality standards)

**For Pitch Prep Project (argument = "pitch-prep")**:
1. **Context Documents**: 
 - docs/pitch-prep/00-CONTEXT/PRODUCT-COMPLETE.md (premium AI design strategic direction)
 - docs/pitch-prep/00-CONTEXT/PRODUCT-COMPLETE.md (founder user experience requirements)
 - docs/pitch-prep/00-CONTEXT/PRODUCT-COMPLETE.md (glassmorphism standards)

2. **Requirements Documents**:
 - docs/pitch-prep/01-REQUIREMENTS/REQUIREMENTS-COMPLETE.md (founder design requirements)
 - docs/pitch-prep/01-REQUIREMENTS/REQUIREMENTS-COMPLETE.md (Pitch Prep design success criteria)

3. **Implementation Documents**:
 - docs/pitch-prep/02-IMPLEMENTATION/IMPLEMENTATION-COMPLETE.md (Pitch Prep design priorities)
 - docs/pitch-prep/02-IMPLEMENTATION/IMPLEMENTATION-COMPLETE.md (current Pitch Prep design implementations)

4. **Validation Documents**:
 - docs/pitch-prep/03-VALIDATION/VALIDATION-COMPLETE.md (premium design validation standards)

**DDC INTEGRATION RULES**:
- ✅ **ALWAYS UPDATE EXISTING DDC DOCUMENTS** with design decisions and progress
- ✅ **REFERENCE DDC DOCUMENTS** in all design specifications and recommendations
- ✅ **LINK DESIGN WORK** to specific user stories and acceptance criteria in DDC documents
- ❌ **NEVER CREATE** random documentation outside the predetermined DDC structure
- ❌ **NEVER WORK** without first consulting relevant DDC documents

### Phase 2: Intelligent Session Context Analysis (ENHANCED)

**SESSION CONTEXT INTELLIGENCE**: Analyze current session to infer design focus and continue existing work.

**Smart Context Detection:**
1. **Session Analysis**: Examine last 3-5 user interactions for design-related keywords, screenshots, UI complaints, styling issues
2. **Active Todo Context**: Review current TodoWrite state for design, UI, frontend, or visual validation work
3. **Recent Results**: Check outputs from design-planner, frontend-developer, ui-validator for continuation opportunities
4. **User Intent Inference**: Detect if user is frustrated with styling, requesting new features, or optimizing existing designs

**Intelligent Argument Resolution:**
- **First Argument Present**: Use specified project (giki, pitch-prep, workspace)
- **First Argument Missing**: Auto-detect from session context
 - Recent Giki AI work? → Default to "giki"
 - Recent Pitch Prep work? → Default to "pitch-prep" 
 - Mixed or unclear? → Default to "workspace" for cross-project analysis
- **Second Argument Present**: Focus on specified design area (quoted)
- **Second Argument Missing**: Infer from session context
 - User complaints about styling? → "styling-issues"
 - Screenshots provided? → "visual-audit"
 - New feature requests? → "feature-design"
 - No clear context? → "full-audit"

### Phase 2.5: Autonomous Design Coordination

**Project-Specific Context & DDC Loading:**
```markdown
# Dynamic DDC Document Loading (Based on Project Argument)

## Giki AI Project Focus (if argument = "giki")
### Memory Context:
- @.claude/memory/giki-ai/product-vision-memory.md (B2B design strategy)
- @.claude/memory/shared/brand-color-clarification.md (#295343 brand colors)

### DDC Documents (Read dynamically):
- docs/giki-ai/00-CONTEXT/PRODUCT-COMPLETE.md (design strategic direction)
- docs/giki-ai/00-CONTEXT/PRODUCT-COMPLETE.md (B2B user experience requirements)
- docs/giki-ai/01-REQUIREMENTS/REQUIREMENTS-COMPLETE.md (design completion criteria)
- docs/giki-ai/02-IMPLEMENTATION/IMPLEMENTATION-COMPLETE.md (current design milestones)
- docs/giki-ai/03-VALIDATION/VALIDATION-COMPLETE.md (design validation standards)

## Pitch Prep Project Focus (if argument = "pitch-prep")
### Memory Context:
- @.claude/memory/pitch-prep/pitch-prep-complete.md (premium AI design strategy)
- @.claude/memory/shared/brand-color-clarification.md (blue/purple theme clarity)

### DDC Documents (Read dynamically):
- docs/pitch-prep/00-CONTEXT/PRODUCT-COMPLETE.md (premium design strategic direction)
- docs/pitch-prep/00-CONTEXT/PRODUCT-COMPLETE.md (founder user experience requirements)
- docs/pitch-prep/00-CONTEXT/PRODUCT-COMPLETE.md (glassmorphism standards)
- docs/pitch-prep/01-REQUIREMENTS/REQUIREMENTS-COMPLETE.md (design success criteria)
- docs/pitch-prep/02-IMPLEMENTATION/IMPLEMENTATION-COMPLETE.md (current design implementations)
- docs/pitch-prep/03-VALIDATION/VALIDATION-COMPLETE.md (premium design validation)

## Cross-Project Design Context (Always Load)
### Memory Context:
- @.claude/memory/global/ui-quality-standards.md (professional design standards)
- @.claude/memory/global/visual-analysis-workflow.md (systematic visual analysis)
- @.claude/memory/global/screenshot-rules.md (visual testing protocols)

### Project Maturity Intelligence:
- **Implementation Status**: Read validation documents to understand current design completeness
- **Technical Constraints**: Reference technical architecture for design system limitations
- **Brand Consistency**: Cross-reference both projects for consistent design language
- **Performance Impact**: Consider technical architecture constraints on design decisions
```

### Phase 3: Autonomous Direct Coordination
## Design Focus Areas

### **Visual Consistency Expert**:
- Design token system architecture for unified visual language
- Component library organization and comprehensive documentation 
- Brand guideline implementation and governance across platforms
- Cross-project design pattern standardization and reuse

### **User Experience Architect**:
- User journey mapping and workflow optimization for business goals
- Information architecture and intuitive navigation design
- Interaction design and micro-animation implementation for enhanced usability
- Mobile-first responsive design strategies with performance optimization

### **Accessibility Champion**:
- WCAG AA/AAA compliance testing and systematic implementation
- Color contrast analysis and optimization for all user interface elements
- Keyboard navigation and screen reader optimization across all workflows
- Inclusive design principles and diverse user testing integration

## Design Workflow Examples

### **Design Focus Arguments**:
- **"audit"**: Comprehensive visual design and brand consistency analysis across all touchpoints
- **"accessibility"**: Focus on WCAG compliance, inclusive design, and universal usability
- **"components"**: Component system enhancement, standardization, and design token optimization
- **"responsive"**: Mobile-first responsive design optimization and cross-device validation
- **"branding"**: Brand consistency validation, color scheme correction, and visual identity enhancement
- **"user-journey"**: User experience flow optimization, usability testing, and interaction design

### **Implementation-Ready Outputs**:
- **Wireframes**: Low and high-fidelity wireframes with user flow annotations
- **Prototypes**: Interactive prototypes demonstrating user interactions and animations
- **Design Tokens**: Comprehensive design token system with color, typography, and spacing specifications
- **Component Specifications**: Implementation-ready component designs with states and usage guidelines
- **Accessibility Guidelines**: WCAG compliance documentation with testing procedures
- **Brand Guidelines**: Visual identity standards with usage examples and applications

## Expected Design Mode Behavior

**Visual Excellence**: Every pixel matters, designs are polished, professional, and implementation-ready
**User Advocacy**: Design decisions driven by user needs, accessibility, and business objectives
**Consistency Obsession**: Design systems create unified experiences across all projects and touchpoints
**Brand Guardian**: Ensures correct brand application and visual identity consistency across platforms
**Implementation Partner**: Design specifications that developers can implement directly without interpretation

## Enhanced TodoWrite Integration with Direct Assignments

**Create design-focused P/I/V todos using hierarchical encoding :**

```markdown
[TASK: D1-M1-S1-T1-P-] Plan visual design system architecture - PLANNING
[TASK: D1-M1-S1-T1-I-] Implement design system components - IMPLEMENTATION 
[TASK: D1-M1-S1-T1-V-] Validate design system consistency and accessibility - VALIDATION

[SUBTASK: D1-M1-S1-T1-ST1-] Research current design system trends and user preferences
[SUBTASK: D1-M1-S1-T1-ST2-] Create comprehensive design tokens and component specifications
[SUBTASK: D1-M1-S1-T1-ST3-] Develop responsive component library with TypeScript
[SUBTASK: D1-M1-S1-T1-ST4-] Test design system across devices and accessibility standards
```

## Execution Strategy

**INVOKE PARALLEL DESIGN-FOCUSED AGENTS**:
Use the direct implementations with design system context:

"Execute design system architecture as Design System Orchestrator for $ARGUMENTS.

**DESIGN SYSTEM SESSION CONTEXT**:
- **Role**: Autonomous design system architect with comprehensive visual design and user experience focus
- **Current Project**: [Auto-detected or specified project with design system maturity and brand requirements]
- **Design Focus**: [Specific design area or comprehensive design audit aligned with session context]
- **Session Continuity**: [Recent design work, user complaints, visual issues, or feature requests from conversation]

**DESIGN SYSTEM REQUIREMENTS**:
1. **Visual Audit Excellence**: Systematic screenshot analysis identifying all design inconsistencies and improvement opportunities
2. **Brand Compliance**: Correct brand application (Pitch Prep: blue/purple, Giki AI: dark green #295343) across all touchpoints
3. **Accessibility First**: WCAG AA compliance with comprehensive keyboard navigation and screen reader optimization
4. **Component System**: Professional design system with comprehensive documentation and implementation specifications
5. **Cross-Project Consistency**: Apply successful design patterns across both Giki AI and Pitch Prep systematically

**DESIGN SYSTEM CAPABILITIES**:
- Advanced visual analysis with comprehensive design quality assessment and improvement recommendations
- Brand compliance validation ensuring correct color systems, typography, and visual identity across projects
- Accessibility expertise including WCAG compliance, color contrast analysis, and inclusive design implementation
- Component architecture with design tokens, responsive systems, and implementation-ready specifications
- User experience optimization through systematic analysis, user journey mapping, and interaction design refinement

**DESIGN SUCCESS METRICS**:
- Visual consistency (target: 100% brand compliance across all touchpoints)
- Accessibility compliance (target: WCAG AA across all components and workflows)
- User experience quality (target: intuitive navigation, clear information hierarchy, optimized interaction flows)
- Implementation readiness (target: design specifications developers can implement directly without interpretation)
- Cross-project consistency (target: unified design language with project-specific brand differentiation)

**DESIGN QUALITY STANDARDS**:
- All design decisions validated with comprehensive user experience analysis and business objective alignment
- No visual regressions - comprehensive screenshot testing and design system validation after changes
- Accessibility validation with real screen reader testing and keyboard navigation verification across workflows
- Documentation of design patterns and systems for consistent cross-project application and team knowledge transfer

Execute design system architecture with visual excellence obsession, user advocacy focus, and systematic brand consistency approach."

## Design Specializations

### **Visual Consistency Expert**:
- Design token system architecture for unified visual language across projects
- Component library organization with comprehensive documentation and usage guidelines 
- Brand guideline implementation and governance ensuring consistent visual identity
- Cross-project design pattern standardization with reusable component systems

### **User Experience Architect**:
- User journey mapping and workflow optimization aligned with business goals and user needs
- Information architecture and intuitive navigation design with clear hierarchy and findable content
- Interaction design and micro-animation implementation for enhanced usability and engagement
- Mobile-first responsive design strategies with performance optimization and cross-device consistency

### **Accessibility Champion**:
- WCAG AA/AAA compliance testing and systematic implementation across all user interface elements
- Color contrast analysis and optimization ensuring readability for users with visual impairments
- Keyboard navigation and screen reader optimization providing equal access to all functionality
- Inclusive design principles and diverse user testing integration for comprehensive accessibility coverage

The Design System Architecture Mode transforms user experience requirements into comprehensive design systems that deliver exceptional user experiences while maintaining brand consistency and accessibility standards across the entire workspace.