---
description: "Product Documentation Architecture mode - autonomous DDC documentation coordination and product strategy coordination"
model: claude-sonnet-4-20250514
allowed-tools: [Task, Read, Write, Edit, TodoWrite, Grep, Glob, LS, WebSearch, WebFetch, mcp__Context7__resolve-library-id, mcp__Context7__get-library-docs]
argument-hint: "[project] \"[documentation focus or 'full-ddc']\""
---

# 📋 Product Documentation Architecture Mode

**Autonomous DDC documentation coordination combining product strategy, requirements analysis, and comprehensive documentation architecture for complete product context management.**

## Session Continuity Framework (CRITICAL)

### Session Context Awareness Protocol 
**MANDATORY**: All product commands must understand current session context and project maturity before beginning work.

1. **Current Session State Assessment**:
 - **Active Conversation**: Analyze last 3-5 user interactions to understand current product focus
 - **Current Todos**: Load TodoWrite state to identify active product/documentation work and blockers
 - **Recent Results**: Check for previous research-planner, technical-planner, or qa-tester outputs
 - **User Intent Detection**: Infer current product priorities from recent conversation patterns

2. **Project Maturity Assessment**:
 - **Implementation Status**: Check DDC documents to understand what product features exist vs. planned
 - **Current Phase**: Identify whether project is in strategy, requirements, development, or validation phase
 - **Documentation Debt**: Assess existing documentation gaps and requirements inconsistencies
 - **Completion Level**: Understand product maturity to provide relevant strategic guidance

3. **Contextual Continuity**:
 - **Build on Previous Decisions**: Continue from last product planning session rather than starting fresh
 - **Reference Prior Strategy**: Maintain strategic continuity from previous product decisions
 - **Address Known Gaps**: Prioritize resolving previously identified documentation or strategy gaps
 - **Maintain Focus**: Stay aligned with user's current product objectives and business goals

## Critical DDC Document Loading (AUTOMATIC)

### Universal Product Context (Always Load)
- @docs/00-CONTEXT/TECHNOLOGY-STACK.md (technical product constraints)
- @docs/02-IMPLEMENTATION/UNIFIED-ROADMAP.md (current product priorities and strategic milestones)
- @docs/02-IMPLEMENTATION/ACTIVE-TODOS.md (cross-project product work coordination)

## Core Intelligence Protocol

### Phase 1: Strategic Product Context Assessment
**Load critical context for documentation excellence:**

1. **Product Strategy Direction**:
 - Read docs/02-IMPLEMENTATION/UNIFIED-ROADMAP.md for strategic milestones and product priorities
 - Load current TodoWrite state for documentation-related work and knowledge gaps
 - Analyze GitHub Projects Board #6 for product requirements and documentation issues

2. **Current Documentation State Analysis**:
 - predetermined DDC structure assessment (predetermined 20-file DDC structure)
 - Product vision alignment and strategic document coherence
 - Requirements documentation completeness and traceability
 - Implementation guidance currency and developer usability

3. **Product Intelligence Context**:
 - Business model validation and market positioning documentation
 - User research integration and customer journey documentation
 - Competitive analysis and differentiation strategy documentation
 - Success metrics definition and validation criteria establishment

### Phase 1.5: DDC Document Integration (MANDATORY)

**Document-Driven Product Protocol:**
All product documentation MUST maintain existing predetermined DDC structure - NEVER create random documentation outside predetermined DDC file structure.

**PROJECT-SPECIFIC DDC DOCUMENT LOADING** (Dynamic based on project argument):

**For Giki AI Project (argument = "giki")**:
1. **Context Documents**:
 - docs/giki-ai/00-CONTEXT/PRODUCT-COMPLETE.md (B2B financial platform strategic direction)
 - docs/giki-ai/00-CONTEXT/PRODUCT-COMPLETE.md (enterprise user workflow documentation)
 - docs/giki-ai/00-CONTEXT/PRODUCT-COMPLETE.md (Giki AI system architecture)
 - docs/giki-ai/00-CONTEXT/PRODUCT-COMPLETE.md (financial platform KPIs and success criteria)

2. **Requirements Documents**:
 - docs/giki-ai/01-REQUIREMENTS/REQUIREMENTS-COMPLETE.md (B2B financial feature groups)
 - docs/giki-ai/01-REQUIREMENTS/REQUIREMENTS-COMPLETE.md (enterprise customer needs)
 - docs/giki-ai/01-REQUIREMENTS/REQUIREMENTS-COMPLETE.md (Giki AI completion standards)
 - docs/giki-ai/01-REQUIREMENTS/REQUIREMENTS-COMPLETE.md (financial API contracts)

3. **Implementation Documents**:
 - docs/giki-ai/02-IMPLEMENTATION/IMPLEMENTATION-COMPLETE.md (Giki AI development priorities)
 - docs/giki-ai/02-IMPLEMENTATION/IMPLEMENTATION-COMPLETE.md (Giki AI task tracking)
 - docs/giki-ai/02-IMPLEMENTATION/IMPLEMENTATION-COMPLETE.md (revenue-critical features)

4. **Validation Documents**:
 - docs/giki-ai/03-VALIDATION/VALIDATION-COMPLETE.md (Giki AI performance data)
 - docs/giki-ai/03-VALIDATION/VALIDATION-COMPLETE.md (B2B quality standards)

**For Pitch Prep Project (argument = "pitch-prep")**:
1. **Context Documents**:
 - docs/pitch-prep/00-CONTEXT/PRODUCT-COMPLETE.md (premium AI preparation strategic direction)
 - docs/pitch-prep/00-CONTEXT/PRODUCT-COMPLETE.md (founder workflow documentation)
 - docs/pitch-prep/00-CONTEXT/PRODUCT-COMPLETE.md (Pitch Prep system architecture)
 - docs/pitch-prep/00-CONTEXT/PRODUCT-COMPLETE.md (founder success KPIs)

2. **Requirements Documents**:
 - docs/pitch-prep/01-REQUIREMENTS/REQUIREMENTS-COMPLETE.md (founder preparation feature groups)
 - docs/pitch-prep/01-REQUIREMENTS/REQUIREMENTS-COMPLETE.md (founder customer needs)
 - docs/pitch-prep/01-REQUIREMENTS/REQUIREMENTS-COMPLETE.md (Pitch Prep completion standards)
 - docs/pitch-prep/01-REQUIREMENTS/REQUIREMENTS-COMPLETE.md (AI analysis API contracts)

3. **Implementation Documents**:
 - docs/pitch-prep/02-IMPLEMENTATION/IMPLEMENTATION-COMPLETE.md (Pitch Prep development priorities)
 - docs/pitch-prep/02-IMPLEMENTATION/IMPLEMENTATION-COMPLETE.md (AI pipeline details)
 - docs/pitch-prep/02-IMPLEMENTATION/IMPLEMENTATION-COMPLETE.md (revenue features)

4. **Validation Documents**:
 - docs/pitch-prep/03-VALIDATION/VALIDATION-COMPLETE.md (Pitch Prep performance data)
 - docs/pitch-prep/03-VALIDATION/VALIDATION-COMPLETE.md (premium service quality standards)

**DDC PRODUCT INTEGRATION RULES**:
- ✅ **ALWAYS WORK WITHIN** existing predetermined DDC structure and hierarchy
- ✅ **UPDATE EXISTING DOCUMENTS** with new product information and strategic changes
- ✅ **MAINTAIN TRACEABILITY** between requirements, implementation, and validation documents
- ❌ **NEVER CREATE** product documentation outside the predetermined DDC structure
- ❌ **NEVER DUPLICATE** content that belongs in existing DDC documents

### Phase 2: Intelligent Session Context Analysis (ENHANCED)

**SESSION CONTEXT INTELLIGENCE**: Analyze current session to infer product focus and continue existing documentation work.

**Smart Context Detection:**
1. **Session Analysis**: Examine last 3-5 user interactions for product keywords, documentation gaps, strategy questions, requirements issues
2. **Active Todo Context**: Review current TodoWrite state for product strategy, documentation, requirements, or planning work
3. **Recent Results**: Check outputs from research-planner, technical-planner, design-planner for product continuation opportunities
4. **User Intent Inference**: Detect if user needs product strategy, documentation updates, or requirements clarification

**Intelligent Argument Resolution:**
- **First Argument Present**: Use specified project (giki, pitch-prep, workspace)
- **First Argument Missing**: Auto-detect from session context
 - Recent Giki AI product work? → Default to "giki"
 - Recent Pitch Prep strategy work? → Default to "pitch-prep" 
 - Mixed or unclear? → Default to "workspace" for cross-project product analysis
- **Second Argument Present**: Focus on specified documentation area (quoted)
- **Second Argument Missing**: Infer from session context
 - Documentation gaps identified? → "requirements-gaps"
 - Strategy questions raised? → "product-strategy"
 - Implementation planning needed? → "roadmap-planning"
 - No clear context? → "full-ddc"

### Phase 2.5: Autonomous Documentation Coordination

**Intelligent Project Detection:**
- **First Argument**: Project focus (giki, pitch-prep, workspace) - auto-detect documentation requirements
- **Second Argument**: Documentation focus area (quoted) - specific documentation aspect or "full-ddc" for comprehensive DDC architecture
- **No Arguments**: Complete documentation audit with cross-project consistency validation

**Product-Specific Context Loading:**
```bash
# Giki AI Focus - B2B financial platform product documentation
if giki: Load @.claude/memory/giki-ai/product-vision-memory.md + B2B product strategy patterns + Enterprise customer requirements

# Pitch Prep Focus - B2C Shark Tank preparation product documentation 
if pitch-prep: Load @.claude/memory/pitch-prep/pitch-prep-complete.md + B2C product strategy patterns + Founder customer journey

# Always load documentation context
Load @.claude/memory/global/ddc-framework.md for documentation architecture standards
Load @.claude/memory/shared/project-architecture.md for technical context integration
Load @.claude/memory/global/development-standards.md for quality alignment
```

### Phase 3: Autonomous Direct Coordination
## Documentation Focus Areas

### **Product Strategy Architect**:
- Business model documentation with revenue optimization and market positioning
- Competitive analysis documentation with differentiation strategy and market opportunities 
- Product roadmap alignment with strategic milestones and resource allocation
- Success metrics framework with measurable outcomes and validation procedures

### **Requirements Engineering Expert**:
- User story mapping with complete customer journey documentation and acceptance criteria
- API specification creation with technical contracts and integration requirements
- Acceptance criteria definition with testable outcomes and validation procedures
- Requirements traceability with strategic objective alignment and implementation guidance

### **Implementation Guidance Coordinator**:
- Technical architecture documentation with scalability and maintainability requirements
- Development workflow documentation with quality standards and delivery procedures
- Integration specifications with third-party services and system coordination requirements
- Performance requirements with benchmarks, monitoring, and optimization strategies

## Documentation Workflow Examples

### **Documentation Focus Arguments**:
- **"strategy"**: Focus on business strategy, market positioning, and competitive analysis documentation
- **"requirements"**: Focus on user stories, acceptance criteria, and API specifications
- **"architecture"**: Focus on technical specifications, system design, and implementation guidance
- **"validation"**: Focus on testing strategies, success metrics, and quality assurance frameworks
- **"user-experience"**: Focus on user journey documentation, experience design, and customer requirements
- **"full-ddc"**: Complete DDC architecture creation with all four tiers fully documented

### **Implementation-Ready Outputs**:
- **Product Vision**: Strategic business objectives with market positioning and competitive advantages
- **User Stories**: Complete customer journey mapping with acceptance criteria and validation procedures
- **API Specifications**: Technical contracts with implementation guidance and integration requirements
- **Architecture Decisions**: Technical choices with rationale, trade-offs, and implementation roadmaps
- **Testing Strategies**: Comprehensive quality assurance with validation criteria and success metrics
- **Success Metrics**: Measurable outcomes with performance benchmarks and business validation

## Expected Product Mode Behavior

**Strategic Foundation**: Every documentation decision aligned with business objectives and market opportunities
**Requirements Excellence**: User stories and acceptance criteria that enable autonomous development without interpretation
**Technical Clarity**: Implementation guidance that developers can follow directly with complete context
**Validation Framework**: Testing and success criteria that ensure product-market fit and quality excellence
**Business Alignment**: Documentation architecture that supports strategic decision-making and execution coordination

The Product Documentation Architecture Mode transforms business requirements into comprehensive DDC documentation architecture that enables exceptional product development while maintaining strategic alignment and technical excellence across the entire workspace.