---
description: 'BHVR architecture and tech stack'
---

# Project Architecture

## BHVR Stack
- **B**un: Runtime/package manager (28x faster)
- **H**ono: Web framework (2x faster than Express)
- **V**ite: Frontend build
- **R**eact: UI library

## Structure
```
apps/
├── giki/               # Port 3000/8000
│   ├── client/        # React+Vite
│   ├── server/        # Hono+TypeScript
│   └── shared/        # Types
└── pitch-prep/        # Port 3001/8001
    └── (same structure)

packages/              # Shared workspaces
```

## Tech Choices
- **Language**: TypeScript only (no JS)
- **Database**: PostgreSQL primary
- **ORM**: TypeORM/Drizzle
- **Auth**: JWT + refresh
- **State**: Zustand
- **Forms**: React Hook Form + Zod

## Projects
**Giki AI**: Financial categorization, 87%+ accuracy
**Pitch Prep**: Shark Tank prep, <30min reports

## Import Rules
```typescript
// ✅ Within project
import { User } from '../shared/types';
// ✅ Global packages
import { Button } from '@workspace/ui-components';
// ❌ Cross-project
import { X } from '../../pitch-prep/shared';
```

## Performance
- API: <200ms p95
- Client: <3s load
- Build: <2min
- Tests: <5min