---
description: 'Todo decomposition quick reference'
---

# Todo Decomposition Rules

## Instant Decision Tree
```
[EPIC] → Create 2-8 [STORY] + [CAPACITY] + [LEARNING]
[STORY] → Create [TASK-P/I/V] phases
[TASK >30min] → Create 2-10 [SUBTASK] <2hrs each
```

## Size Triggers
- >3 files → Subtask per file
- >1 service → Task per service
- >30 minutes → P/I/V phases
- >2 hours → Subtask breakdown
- >1 epic scope → Elevate to EPIC

## Blocking Rules
❌ Uncomposed EPICs/STORIEs
❌ >30min TASKs without subtasks
❌ Work without todos
❌ Permission requests (automatic)

## Success
✅ 100% hierarchical
✅ ZERO undecomposed
✅ IMMEDIATE breakdown
✅ IMMEDIATE execution