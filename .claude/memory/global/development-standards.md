---
description: 'Development standards and workspace management'
---

# Development Standards

## Documentation
- **Strategic**: `docs/` (DDC structure)
- **Design**: `design/{project}/` (wireframes, mockups, prototypes)
- **No scattered READMEs**

## Environment Loading (CRITICAL)
```typescript
// FIRST import in any server file
import '../../../lib/env-loader.js';
```
Location: `/Users/<USER>/giki-ai-workspace/.env.development` (73+ vars)

## Bun Commands (Run from Workspace Root)
```bash
cd /Users/<USER>/giki-ai-workspace  # MANDATORY

# Development
bun run dev                # Pitch Prep (3001/8001)
bun run dev:giki          # Giki AI (3000/8000)
bun run dev:all           # Both projects

# Testing (E2E→Integration→Unit)
bun run test              # All with services
bun run test:e2e          # Customer journeys
bun run test:integration  # API validation

# Quality
bun run lint              # All projects
bun run build             # Production builds
bun run type-check        # TypeScript validation

# Service Management
bun run clean:ports       # Kill 3000/3001/8000/8001
bun run stop:all          # Graceful shutdown
```

## Standards
- **Package Manager**: bun only (28x faster)
- **TypeScript**: Strict mode, no any
- **React**: Functional components only
- **Testing**: 100% journey coverage
- **Performance**: API <200ms, Client <3s

## Git Commits
**Validate→Commit→Push immediately**

### Validation
```bash
bun run test && bun run build && bun run lint && bun run type-check
```

### Format
```
<type>: <description>

[Evidence/metrics]

🤖 Generated with Claude Code
Co-Authored-By: Claude <<EMAIL>>
```

## MCP Tools
- **Libraries**: Context7 first (never trust training)
- **Browser**: Playwright MCP
- **Screenshots**: MCP server (not Read)
- **Database**: PostgreSQL/MongoDB MCPs

## File Reading Standards
- **Documentation Files**: ALWAYS read complete docs/ files, never use offset/limit
- **When file exceeds token limit**: Use Grep to search specific sections first, then read targeted content
- **Large files**: Break into logical sections using Grep, then read each section completely
- **NEVER** read partial docs/ files when intending to read the full content

## Success Metrics
- 100% background execution compliance
- 80%+ test coverage
- <5s service startup
- 100% evidence-based claims