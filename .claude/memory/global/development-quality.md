---
description: 'Development quality standards and fragment prevention'
---

# Development Quality Standards

## Core Mandate
**MODIFY EXISTING FILES** - Creation prohibited unless necessary. Evidence > agreeability.

## Problem-Focus Validation
**FIND PROBLEMS FIRST** - What's broken/missing/risky before claiming success.

Questions: What fails? Requirements unmet? Technical debt? Missing tests? Security/performance issues?

## Documentation Integration
- **Structure**: `docs/` (strategic), `design/` (UI/UX)
- **Workflow**: Consult→Update→Validate with appropriate docs
- **No random READMEs** - Use established structure

## Fragment Prevention

### Detection (Run Before File Creation)
```bash
find . -name "*-new.*" -o -name "*-v2.*" -o -name "*-temp.*" -not -path "*/node_modules/*"
find . -name "test-*" -not -path "*/tests/*" -not -path "*/node_modules/*"
```
**If results**: STOP - consolidate first.

### Location Rules
| Type | Location |
|------|----------|
| Components | `apps/{project}/src/components/` |
| API | `services/{service}/src/api/endpoints/` |
| Utils | `packages/utils/src/` |
| Tests | `tests/{unit,integration,e2e}/` |

### Decision Tree
Need functionality? → Search → Found? → Modify existing ✅
                             ↘ New domain? → Create (rare) ✅

## Visual Confirmation Protocol
**UI work requires user confirmation**:
1. Three-tab setup (prototype/local/prod)
2. Take screenshots from each
3. Show results with labels
4. Ask "Please confirm this looks correct"
5. Wait for explicit approval

## Code Modification Protocol (MANDATORY)
**EVERY code change triggers validation cycle**:
1. **Modify/Create** → Immediate validation todo
2. **Never mark fix complete** without test pass
3. **Chain**: Fix → Test → Verify → Next

### Validation Requirements
- **Critical Fix**: Create test todo IMMEDIATELY
- **Service Update**: Integration test todo
- **API Change**: E2E test todo
- **Mock Removal**: Verify with real API call

## Testing Hierarchy
1. **Agent Tests** (100%): Markdown for Claude
2. **E2E** (100%): Customer journeys
3. **Integration** (90%): Workflows
4. **Unit** (80%): Functions

Structure: `tests/{unit,integration,e2e}/`

## Quality Gates
**Pre-creation**: Fragment scan, exhaustive search, integration plan
**Post-creation**: No duplicates, proper location, pattern compliance

## Metrics
- <1% create new files
- ZERO suffixed files
- 100% fragment protocol compliance
- 95%+ pattern compliance