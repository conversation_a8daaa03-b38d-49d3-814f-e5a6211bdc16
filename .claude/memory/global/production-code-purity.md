---
description: 'Production code purity standards'
---

# Production Code Purity

## Zero Tolerance
**NO**: Primitive logic, mocks outside tests, fake fallbacks

## Enforcement
Strict enforcement across all code - no mocks in services, no hardcoded values, real SDKs only.

## Prohibited Patterns

### 1. Primitive Logic
```typescript
// ❌ WRONG
if (description.includes("restaurant")) category = "dining";
// ✅ RIGHT
category = await classifier.classify(transaction);
```

### 2. Mocks Outside Tests
```bash
# Detection
find . -name "*mock*" -not -path "*/test*" -not -path "*/node_modules/*"
```
If found → Move to tests/ or delete

### 3. Fake Fallbacks
```typescript
// ❌ WRONG
catch { return { success: true, data: "fake" }; }
// ✅ RIGHT
catch (error) { throw new ServiceUnavailableError(error.message); }
```

## Current Violations
- geminiService.ts:6-8: Environment conditional
- mockRazorpay.ts: Mock in production path

## Refactoring Pattern
Use ConfigService and ErrorHandlingService patterns (not primitive conditionals).

## Metrics
- ZERO primitive conditionals in production
- ZERO mock services outside tests
- ZERO hardcoded fallbacks
- 100% predictable failures