---
description: 'SDK verification protocol - NEVER trust training'
---

# SDK Verification

## Golden Rule
**Not looking at Context7/tests = GUESSING**

## Mandatory Workflow
1. **Context7**: resolve-library-id → get-library-docs
2. **Integration Test**: Real calls, log responses
3. **Implementation**: Only after test passes
4. **Documentation**: Version, test file, migration guide

## Red Flags
- Package suffixes (-api, -sdk, -client)
- Methods: getGenerativeModel(), createXXX()
- Response: .response.text() vs .text
- Callbacks instead of async/await

## Common Mistakes
```typescript
// ❌ WRONG (memory/training)
genai.getGenerativeModel({model: 'gemini-pro'});
// ✅ RIGHT (Context7 verified)
ai.models.generateContent({model: 'gemini-2.0-flash-001'});
```

## Quick Reference
```
Context7 → Integration Test → Implementation
Package ID → Real API Call → Verified Code
Current Docs → Response Log → Version Doc
```

## Rules
- No Context7 = No code
- No test = No implementation
- No evidence = Not complete
- 100% SDK implementations have tests
- 0% mocks in production