---
description: 'Document-Driven Context structure'
---

# DDC Framework

## Structure
```
docs/                    # Strategic documentation
├── 00-CONTEXT/         # Architecture, tech stack
├── 01-REQUIREMENTS/    # Epics, standards
├── 02-IMPLEMENTATION/  # Roadmap, todos
└── 03-VALIDATION/      # Metrics, evidence

design/{project}/       # UI/UX assets
├── wireframes/        # Markdown layouts
├── mockups/          # Static HTML
├── prototypes/       # Interactive HTML
├── styles/          # CSS systems
└── screenshots/     # Comparisons
```

## Files (20 Total)
- **Global** (8): Tech stack, standards, roadmap, metrics
- **Per Project** (6×2): Product, requirements, implementation, validation

## Usage
- **Before work**: Read context/requirements
- **During**: Update implementation
- **After**: Document validation/evidence

## Anti-Patterns
❌ Duplicate files (-new, -v2)
❌ Version directories (v1/, v2/)
❌ Scattered READMEs
❌ Fragment systems

## Decision Tree
```
Need functionality? → Search existing → Found? → Modify ✅
                                     ↘ New domain? → Create (rare) ✅
```