---
description: 'Development workflows and migration strategies'
---

# Development Workflows

## Documentation Integration
- **Planning**: Read context/requirements
- **Implementation**: Update decisions/design
- **Validation**: Document evidence/results
- Use `docs/` for strategic, `design/` for UI/UX

## Hooks & Automation
- PreToolUse, PostToolUse, PrePrompt, PostResponse
- Fragment detection, BHVR validation, type safety

## BHVR Migration (In-Place Only)
```javascript
// Express → Hono
app.use(cors()) → app.use('*', cors())
(req, res) → (c)
req.body → await c.req.json()
res.json() → return c.json()

// Jest → Bun Test
@jest/globals → bun:test
jest.mock() → import { mock } from 'bun:test'
```

## Roadmap Integration
1. Read ROADMAP.md first
2. Check active sprint/capacity
3. Link todos to objectives
4. Update with evidence

## PCIV Decomposition
- **P**lanning: Research, design, requirements
- **C**oordination: Integration, API, config
- **I**mplementation: Core dev, UI/UX
- **V**alidation: Testing, performance

## Frontend Consistency
1. HTML Prototype (file:///)
2. Local React (localhost)
3. Production deployment
→ Three-tab comparison

## Session Context
- Current todos (in_progress/priority)
- Recent activity
- Known blockers
- Project phase

## Success Metrics
- 100% roadmap alignment
- <24h todo completion
- >90% cross-session continuity