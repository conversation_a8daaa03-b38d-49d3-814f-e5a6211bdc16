---
description: 'Hierarchical project management system with Google Sheets integration'
---

# Enhanced Todo System

## Project Management Hierarchy
```
PROJECT → MILESTONE → EPIC → STORY → TASK → SUBTASK → TODO
  Giki    →  M1-Giki  → E1   → S1   → T1  →  ST1   → P/I/V
```

## Google Sheets Integration
**Strategic Level (Sheets)**: PROJECT → MILESTONE → EPIC → STORY → TASK → SUBTASK
**Tactical Level (Claude)**: TASK/SUBTASK → TODO (P/I/V decomposition)

## Hierarchy Definitions
- **PROJECT**: App-level (Giki, Pitch-Prep)
- **MILESTONE**: Demo-able version (M1-Giki: Aug 15 MIS Demo)
- **EPIC**: Feature set delivering milestone (E1-M1-Giki: Upload-to-MIS Flow)
- **STORY**: User-facing feature (S1-E1-M1-Giki: File Upload & Schema Detection)
- **TASK**: Specific work item (T1-S1-E1-M1-Giki: Fix schema interpretation tests)
- **SUBTASK**: Granular breakdown (ST1-T1-S1-E1-M1-Giki: Update test fixtures)
- **TODO**: Claude's P/I/V phases

## P/I/V Phases (Claude's Tactical Decomposition)
Each TASK/SUBTASK from sheets decomposes into THREE todos:
- `[TASK: T1-S1-E1-M1-Giki-P] Plan fix schema interpretation tests`
- `[TASK: T1-S1-E1-M1-Giki-I] Implement schema test fixes`  
- `[TASK: T1-S1-E1-M1-Giki-V] Validate schema interpretation works`

**No agent assignments** - Claude handles work directly

## Special Categories
- `[CAPACITY]` 60/40 split monitoring
- `[LEARNING: D1→C2]` Cross-project transfer
- `[COORDINATION]` Sync activities

## Auto-Decomposition Rules
```typescript
// Strategic Level (Sheets)
if (scope > feature) → Create EPIC
if (epic_scope > story) → Break into STORIEs  
if (story_scope > task) → Break into TASKs
if (task_complexity > 4hrs) → Break into SUBTASKs

// Tactical Level (Claude)
if (task/subtask assigned) → Create P/I/V TODOs
if (P phase > 30min) → Create planning subtasks
if (I phase > 2hrs) → Create implementation subtasks
if (V phase > 1hr) → Create validation subtasks
```

## Completion Rules
- Evidence required (tests, screenshots, metrics)
- No TODO comments = not complete
- Warnings/errors = blocking todos
- User confirmation for UI
- **CODE MODIFICATION RULE**: Every code change MUST create immediate validation todo
- **VALIDATION CHAIN**: Fix → Test Todo → Run Test → Verify → Mark Complete
- **BLOCKING**: Cannot mark "completed" without passing test evidence

## Progress Tracking (Hierarchical Rollup)
- **PROJECT** = avg(milestones)
- **MILESTONE** = avg(epics) 
- **EPIC** = avg(stories)
- **STORY** = avg(tasks)
- **TASK** = avg(subtasks) or P/I/V phases (0/33/66/100%)
- **SUBTASK** = P/I/V phases (0/33/66/100%)

## Workflow Integration
1. **Strategic Planning**: Review PROJECT/MILESTONE progress in sheets
2. **Daily Planning**: Select next TASK/SUBTASK based on priority + blockers
3. **Tactical Execution**: Claude creates P/I/V todos and executes
4. **Progress Updates**: Update worklog and progress in sheets
5. **Evidence Collection**: Link completed work to evidence column