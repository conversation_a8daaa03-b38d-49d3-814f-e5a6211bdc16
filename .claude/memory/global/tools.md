---
description: 'Tool usage and visual analysis'
---

# Tools & Visual Analysis

## Tool Hierarchy
```
Bun commands → Workspace root
Unknown → WebSearch
URL → WebFetch
Library → Context7 FIRST
Code → Grep/Glob
UI → Playwright MCP
Visual → Screenshot MCP
Database → PostgreSQL/MongoDB MCP
```

## Screenshot Rules (8000px limit)
```javascript
// ✅ RIGHT
browser_take_screenshot({ fullPage: false });
mcp__screenshot__analyze_screenshots({...});
// ❌ WRONG
browser_take_screenshot({ fullPage: true });
Read({ file_path: "screenshot.png" });
```

## Visual Analysis Workflow
**User provides screenshot** → Load memory + use MCP server

```typescript
mcp__screenshot__analyze_screenshots({
  image_paths: ["screenshots..."],
  markdown_paths: ["ui-standards.md"],
  query: "Identify styling issues...",
  output_markdown_path: "/tmp/analysis.md"
});
```

## Test Organization
```
apps/{project}/tests/
├── unit/
├── integration/
├── e2e/
└── performance/
```

**Naming**: `*.test.ts`, `*.spec.ts`
**Environment**: Import env-loader first

## UI Quality Standards
- **Typography**: H1 48-64px, Body 16-18px
- **Spacing**: 8px grid (8,16,24,32,48,64)
- **Colors**: Brand compliance, WCAG AA
- **Components**: 44px touch targets, consistent radius
- **Polish**: Shadows, transitions, alignment

## Tab Management
- Tab 0: HTML Prototype
- Tab 1: Local Dev
- Tab 2: Production
→ Three-tab comparison

## Anti-Patterns
- Don't skip Context7 for SDKs
- Don't use fullPage screenshots
- Don't Read image files
- Don't create test fragments