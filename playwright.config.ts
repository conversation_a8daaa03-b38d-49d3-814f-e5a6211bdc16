import { defineConfig, devices } from '@playwright/test';

/**
 * Workspace-level Playwright E2E Testing Configuration
 * Runs E2E tests for all projects with proper service management
 */
export default defineConfig({
  // Test directory - workspace level E2E tests
  testDir: './tests/e2e',
  
  // Run tests in files in parallel
  fullyParallel: false,
  
  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,
  
  // Retry on CI only
  retries: process.env.CI ? 2 : 0,
  
  // Workers - limit for service stability
  workers: process.env.CI ? 1 : 1,
  
  // Reporter to use
  reporter: [
    ['line'],
    ['html', { outputFolder: 'test-results/playwright-report' }]
  ],
  
  // Shared settings for all projects
  use: {
    // Collect trace when retrying the failed test
    trace: 'on-first-retry',
    
    // Take screenshot on failure
    screenshot: 'only-on-failure',
    
    // Record video on failure
    video: 'retain-on-failure',
    
    // Global timeout for each test
    actionTimeout: 10000,
    
    // Timeout for each assertion
    expectTimeout: 5000,
  },

  // Configure projects - test against different apps
  projects: [
    {
      name: 'giki-ai',
      use: { 
        ...devices['Desktop Chrome'],
        baseURL: 'http://localhost:3000',
        headless: true,
      },
      testMatch: '**/giki-*.spec.ts',
    },
    {
      name: 'pitch-prep',
      use: { 
        ...devices['Desktop Chrome'],
        baseURL: 'http://localhost:3001',
        headless: true,
      },
      testMatch: '**/pitch-prep-*.spec.ts',
    },
    {
      name: 'customer-journey',
      use: { 
        ...devices['Desktop Chrome'],
        baseURL: 'http://localhost:3000', // Default to Giki AI
        headless: false, // Show customer journey tests
      },
      testMatch: '**/customer-*.spec.ts',
    },
  ],

  // Test timeout
  timeout: 30000,

  // Output directory for test artifacts
  outputDir: 'test-results/playwright-artifacts',

  // Test metadata
  metadata: {
    platform: process.platform,
    project: 'giki-ai-workspace',
    version: '1.0.0',
  },
});