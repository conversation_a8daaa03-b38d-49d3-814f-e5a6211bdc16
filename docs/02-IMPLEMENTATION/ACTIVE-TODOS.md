# Active Todos - Cross-Project P/I/V Coordination

## Current Epic Status

### [EPIC: DDC-PREDETERMINED-STRUCTURE] Revolutionary DDC Overhaul
**Status**: IN PROGRESS
**Priority**: P1 - Ultimate Documentation Control
**Progress**: 70% Complete (Templates Created, Consolidation & Enforcement Remaining)

**Active Tasks**:
- ✅ **[COMPLETED]** Fix validation script (test violations vs warnings)
- ✅ **[COMPLETED]** Add DDC compliance section to frontend-developer.md
- 🔄 **[IN PROGRESS]** Create 20-file predetermined structure templates
- ⏳ **[PENDING]** Consolidate 192 existing files into 60 predetermined files (90% reduction)
- ⏳ **[PENDING]** Update validation and agents for predetermined structure enforcement


## Tactical Task Queue (Next 2–3 sessions) — as of 2025-08-13

Follow the Enhanced Todo System and QUICK-TODO-DECOMPOSITION. Each TASK/SUBTASK must be decomposed into P/I/V todos with validation evidence (tests, logs, screenshots, reports).

### Cross‑Project Coordination
- [COORDINATION] Service reorg completion and verification
  - [TASK: REORG-P] Plan final folder/service map and script impacts (identify start/stop/test scripts to update)
  - [TASK: REORG-I] Apply minimal changes; ensure dev:all, dev:giki, dev:pitch-prep still work
  - [TASK: REORG-V] Validate with: bun run status; start both projects; smoke endpoints; attach logs
- [CAPACITY] Maintain 60/40 split (Giki/Pitch‑Prep) during this block; log effort in worklog

### Giki AI — Near‑term priorities (align with Product Vision)
- [EPIC] E1: Upload → Categorize → Export happy path hardening
  - [STORY] S1: Auth + App startup stability (post “auth infinite loop” fix)
    - [TASK: S1-P] Define regression criteria: no redirect loops; stored session resume; protected routes
    - [TASK: S1-I] Add Playwright e2e covering login, logout, refresh, deep-link
    - [TASK: S1-V] Run: bun run test:e2e:direct; attach key screenshots and logs
  - [STORY] S2: Dashboard redesign validation lock‑in
    - [TASK: S2-P] Snapshot target states and acceptance checklist from current UI
    - [TASK: S2-I] Add visual regression baselines for key routes (dashboard, reports, upload)
    - [TASK: S2-V] Run: bun run test:e2e:direct; approve/reject diffs; store artifacts
  - [STORY] S3: Real AI pipeline verification (no mocks)
    - [TASK: S3-P] Audit code for mocked/stubbed AI calls; enumerate modules; map to @google/genai usage
    - [TASK: S3-I] Replace remaining stubs with real calls; gate with env; add unit contract tests
    - [TASK: S3-V] Use sdk‑verification checklist; run targeted integration tests; include logs
  - [STORY] S4: Export system completeness check (CSV/Excel/QB Online/Desktop, Xero, Zoho, Wave, Sage, Tally)
    - [TASK: S4-P] Define minimal dataset; expected rows/headers per format
    - [TASK: S4-I] Run scripts/export-system-validation.py; fix failures iteratively
    - [TASK: S4-V] Attach generated files from export_validation_output and final JSON report
  - [STORY] S5: Performance smoke (customer‑facing <500ms; batch processing target <60s/1k tx)
    - [TASK: S5-P] Select 2–3 critical endpoints for latency check; define thresholds
    - [TASK: S5-I] Add lightweight perf test in tests/performance; instrument timing
    - [TASK: S5-V] Run: bun run test:performance if available or via playwright tags; record metrics

### Pitch Prep — Stability after service reorg
- [EPIC] E2: App/API cohesion and test coverage expansion
  - [STORY] S6: Service boot and health checks
    - [TASK: S6-P] List services/ports; confirm envs; define success signals
    - [TASK: S6-I] Start with bun run dev (or dev:pitch-prep); fix any port/env issues
    - [TASK: S6-V] bun run status; attach logs/ping health endpoints
  - [STORY] S7: Template integration tests Phase 1B
    - [TASK: S7-P] Choose next batch of templates to cover; document expected outputs
    - [TASK: S7-I] Extend tests under tests/integration and tests/e2e to cover prompts/templates
    - [TASK: S7-V] Run: bun run test:integration and bun run test:e2e:direct; store artifacts

### Tooling and Developer Experience
- [STORY] S8: Scripts and docs alignment with reorg
  - [TASK: S8-P] Audit scripts/ start/stop/test scripts for broken paths
  - [TASK: S8-I] Update scripts to reference new locations; keep commands in package.json stable
  - [TASK: S8-V] Prove with: bun run dev:all; bun run test; bun run lint; attach success logs

## Cross-Project Task Coordination

### Strategic Epic Breakdown

#### [TASK: DDC-STRUCTURE-P] DDC Structure Planning Phase
**Status**: COMPLETED
**Direct**: research-planner
**Output**: Predetermined 20-file architecture designed

#### [TASK: DDC-STRUCTURE-I] DDC Structure Implementation Phase
**Status**: IN PROGRESS
**Direct**: technical-planner + multiple specialists
**Current Work**: Creating predetermined file templates

**Sub-Tasks**:
- ✅ Create architectural design document
- 🔄 Create global DDC file templates (8 files)
- ⏳ Create project-specific DDC file templates (12 files)
- ⏳ Create consolidation mapping strategy

#### [TASK: DDC-STRUCTURE-V] DDC Structure Validation Phase
**Status**: PENDING
**Direct**: qa-tester + validation specialists
**Requirements**: Validate 20-file structure compliance, zero fragmentation

### Cross-Project Learning Tasks

#### [LEARNING: D1→C2] BHVR Migration Patterns
**Status**: ACTIVE - Pattern Collection
**Source**: Pitch Prep BHVR migration experience
**Target**: Giki AI migration preparation
**Timeline**: Patterns shared within 48 hours of D1 discoveries

#### [LEARNING: QUALITY→BOTH] Production Standards
**Status**: ACTIVE - Standards Application
**Source**: Quality gate development for D1
**Target**: Both projects must meet same standards
**Timeline**: Quality standards applied consistently across projects

## P/I/V Hierarchy Management

### Planning Phase Todos (P)
```markdown
[TASK: GLOBAL-ARCH-P] Plan cross-project architecture decisions - PLANNING
[TASK: QUALITY-STANDARDS-P] Plan quality gate enforcement strategy - PLANNING
[TASK: DEPLOYMENT-STRATEGY-P] Plan production deployment coordination - PLANNING
```

### Implementation Phase Todos (I)
```markdown
[TASK: DDC-CONSOLIDATION-I] Implement 192→20 file consolidation - IMPLEMENTATION
[TASK: QUALITY-ENFORCEMENT-I] Implement quality gate automation - IMPLEMENTATION
[TASK: CROSS-PROJECT-COORD-I] Implement cross-project learning systems - IMPLEMENTATION
```

### Validation Phase Todos (V)
```markdown
[TASK: DDC-COMPLIANCE-V] Validate predetermined structure enforcement - VALIDATION
[TASK: QUALITY-METRICS-V] Validate quality standards across projects - VALIDATION
[TASK: INTEGRATION-TEST-V] Validate cross-project coordination - VALIDATION
```

## Direct Assignment Coordination

### Current Direct Workload
- **technical-planner**: DDC architecture design, cross-project coordination
- **qa-tester**: Quality gate validation, testing strategy coordination
- **frontend-developer**: UI consistency across projects
- **backend-developer**: API standards and integration patterns
- **research-planner**: Requirements analysis and strategic planning

### Direct Handoff Protocols
```markdown
**Context Package Template**:
- **Epic Context**: [Current epic and business objectives]
- **Cross-Project Impact**: [How work affects both Giki AI and Pitch Prep]
- **Learning Requirements**: [Patterns to capture for cross-project sharing]
- **Quality Standards**: [Production readiness criteria that must be met]
- **Timeline Coordination**: [Dependencies and capacity allocation considerations]
```

## Capacity Allocation Tracking

### Current Allocation: 60/40 Split
- **60% Pitch Prep (D1)**: Production readiness, BHVR migration completion
- **40% Giki AI (C2)**: Performance optimization, migration preparation

### Capacity Coordination Todos
```markdown
[CAPACITY: MONITOR-ALLOCATION] Track actual vs planned capacity allocation - ONGOING
[CAPACITY: BALANCE-WORKLOAD] Adjust allocation based on milestone progress - WEEKLY
[CAPACITY: ESCALATE-BLOCKERS] Reallocate capacity for critical path blockers - AS-NEEDED
```

## Cross-Project Synchronization

### Daily Sync Requirements
- **Pattern Sharing**: New patterns from D1 → C2 within 24 hours
- **Blocker Escalation**: Critical blockers shared across projects immediately
- **Quality Standards**: Consistent application of standards across both projects

### Weekly Sync Requirements
- **Milestone Progress**: Progress assessment against unified roadmap
- **Capacity Reallocation**: Adjustment based on project needs and bottlenecks
- **Learning Integration**: Systematic application of cross-project learnings

## Success Metrics Tracking

### DDC System Metrics
- **File Count**: Target exactly 20 predetermined files
- **Consolidation**: 192→20 files (90% reduction) completion tracking
- **Compliance**: 100% agent compliance with predetermined structure

### Cross-Project Coordination Metrics
- **Learning Velocity**: Time from pattern discovery to cross-project application
- **Quality Consistency**: Same standards met across both projects
- **Capacity Utilization**: Efficient use of 60/40 allocation

### Production Readiness Metrics
- **D1 Progress**: Pitch Prep production milestone completion
- **C2 Optimization**: Giki AI performance and quality improvements
- **System Integration**: Cross-project feature and infrastructure coordination

*[This file consolidates cross-project todo coordination using P/I/V hierarchy with direct orchestration]*