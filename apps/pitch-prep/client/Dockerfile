# Production Dockerfile for Pitch Prep Client
FROM node:20-alpine AS builder

WORKDIR /app

# Copy workspace files
COPY package.json ./
COPY apps/pitch-prep/client/package.json ./apps/pitch-prep/client/
COPY apps/pitch-prep/shared/package.json ./apps/pitch-prep/shared/

# Install dependencies
RUN npm ci --only=production

# Copy source files
COPY apps/pitch-prep/client ./apps/pitch-prep/client
COPY apps/pitch-prep/shared ./apps/pitch-prep/shared

# Build the application
WORKDIR /app/apps/pitch-prep/client
RUN npm run build

# Production stage - nginx
FROM nginx:alpine

# Copy nginx configuration
COPY apps/pitch-prep/client/nginx.conf /etc/nginx/conf.d/default.conf

# Copy built application
COPY --from=builder /app/apps/pitch-prep/client/dist /usr/share/nginx/html

# Add health check endpoint
RUN echo "OK" > /usr/share/nginx/html/health

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]