// Core UI Components
export { 
  <PERSON><PERSON>, 
  SharkButton, 
  GlassButton 
} from './Button';

// Import PremiumButton from its dedicated file
export { PremiumButton } from './PremiumButton';

export { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardContent, 
  CardFooter,
  HeroCard,
  ValueCard,
  CounterCard,
  SharkCard,
  QuestionCard
} from './Card';

export { 
  Input, 
  TextArea, 
  FormField, 
  FormGroup,
  SearchInput,
  EmailInput,
  PasswordInput
} from './Input';

export {
  Background,
  Section,
  Container,
  Grid,
  HeroSection,
  ContentSection,
  FeatureSection,
  CTASection,
  Flex,
  Stack
} from './Layout';

export {
  Progress,
  StepProgress,
  CircularProgress,
  LoadingProgress,
  FormProgress
} from './Progress';

export {
  Modal,
  ModalHeader,
  ModalContent,
  ModalFooter,
  ConfirmationModal,
  LoadingModal
} from './Modal';

export {
  Badge,
  NotificationBadge,
  StatusBadge,
  SharkBadge,
  CategoryBadge,
  ProgressBadge
} from './Badge';

export {
  ScrollAnimation,
  Parallax,
  StaggeredAnimation,
  FloatingElement,
  MagneticElement,
  MorphingBackground,
  TextReveal,
  CountUp
} from './ScrollAnimations';

export {
  ShowOn,
  HideOn,
  MobileOnly,
  TabletOnly,
  DesktopOnly,
  MobileUp,
  TabletUp,
  DesktopUp,
  ResponsiveGrid,
  ResponsiveStack,
  ResponsiveText,
  ResponsivePadding,
  ResponsiveMargin,
  ResponsiveContainer,
  AspectRatio
} from './Responsive';

// Premium Glassmorphism Design System Components
export { GlassContainer, GlassCard } from './GlassContainer'
export { AnimatedBackground, FloatingElement as GlassFloatingElement } from './AnimatedBackground'
export { CounterCard as GlassCounterCard, StatsGrid } from './CounterCard'
export { ValueCard as GlassValueCard, ValueGrid } from './ValueCard'
export { SharkTile, SharkGrid } from './SharkTile'
// ProcessingScreen moved to components/analysis/ProcessingScreen
export { PremiumForm } from './PremiumForm'
export { RealTimeProcessing } from './RealTimeProcessing'
export { ProcessingWorkflow } from './ProcessingWorkflow'
// FlipCard moved to components/analysis/FlipCard
export { FlipCard } from '../analysis/FlipCard'

// New Form Components
export { ProgressIndicator } from './ProgressIndicator'

// Connection Status Component (from OfflineMode)
export { ConnectionStatus } from '../OfflineMode'

// Report Preview Component
export { ReportPreview } from './ReportPreview'

// Premium Icons
export * from './Icons'

// Re-export utility functions
export { cn } from '@/lib/utils';