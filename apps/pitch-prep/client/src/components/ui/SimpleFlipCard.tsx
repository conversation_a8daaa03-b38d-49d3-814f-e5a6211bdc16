import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface SimpleFlipCardProps {
  id: string
  frontContent: React.ReactElement
  backContent: React.ReactElement
  className?: string
  autoFlip?: boolean
  flipInterval?: number
}

export const SimpleFlipCard: React.FC<SimpleFlipCardProps> = ({
  id,
  frontContent,
  backContent,
  className,
  autoFlip = false,
  flipInterval = 3000
}) => {
  const [isFlipped, setIsFlipped] = useState(false)

  useEffect(() => {
    if (autoFlip) {
      const interval = setInterval(() => {
        setIsFlipped(prev => !prev)
      }, flipInterval)
      return () => clearInterval(interval)
    }
  }, [autoFlip, flipInterval])

  return (
    <div
      className={cn(
        "relative w-full h-full preserve-3d transition-transform duration-700",
        isFlipped && "rotate-y-180",
        className
      )}
      onClick={() => setIsFlipped(!isFlipped)}
      style={{ transformStyle: 'preserve-3d' }}
    >
      <div className="absolute inset-0 backface-hidden">
        {frontContent}
      </div>
      <div className="absolute inset-0 backface-hidden rotate-y-180">
        {backContent}
      </div>
    </div>
  )
}