import React, { useState, useEffect } from 'react'
import { ArrowR<PERSON>, Check, Star, Shield, Clock, Zap, TrendingUp, Upload, FileText, BarChart3, Users, Target, DollarSign, Loader2, Eye, EyeOff, Lock, Unlock, Download, Mail, Phone, Globe, Activity, X, AlertTriangle, Info, Linkedin, Smartphone, CreditCard, Building, QrCode, Copy } from 'lucide-react'
import { cn } from '@/lib/utils'

// ============================================================================
// PREMIUM GLASSMORPHISM COMPONENT LIBRARY
// Based on shark_ai_premium_design.html - Unified for both projects
// ============================================================================

interface PremiumComponentProps {
  className?: string
  children?: React.ReactNode
}

// === PREMIUM ANIMATED BACKGROUND ===
export const PremiumAnimatedBackground: React.FC<PremiumComponentProps> = ({ className, children }) => {
  return (
    <div className={cn("relative min-h-screen", className)}>
      <div className="premium-animated-bg" />
      <div className="floating-element" />
      <div className="floating-element" />
      <div className="floating-element" />
      {children}
    </div>
  )
}

// === PREMIUM GLASS CONTAINER ===
interface PremiumGlassContainerProps extends PremiumComponentProps {
  hover?: boolean
  onClick?: () => void
}

export const PremiumGlassContainer: React.FC<PremiumGlassContainerProps> = ({ 
  className, 
  children, 
  hover = true,
  onClick 
}) => {
  return (
    <div 
      className={cn(
        "glass-container",
        hover && "cursor-pointer",
        className
      )}
      onClick={onClick}
    >
      {children}
    </div>
  )
}

// === PREMIUM GLASS CARD ===
interface PremiumGlassCardProps extends PremiumComponentProps {
  hover?: boolean
  withTopBorder?: boolean
  onClick?: () => void
}

export const PremiumGlassCard: React.FC<PremiumGlassCardProps> = ({
  className,
  children,
  hover = true,
  withTopBorder = true,
  onClick
}) => {
  return (
    <div 
      className={cn(
        "glass-card",
        !withTopBorder && "[&::before]:hidden",
        className
      )}
      onClick={onClick}
    >
      {children}
    </div>
  )
}

// === PREMIUM BUTTONS ===
interface PremiumButtonProps extends PremiumComponentProps {
  variant?: 'primary' | 'secondary' | 'success' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  type?: 'button' | 'submit' | 'reset'
  icon?: React.ReactNode
  onClick?: () => void | Promise<void>
  disabled?: boolean
  loading?: boolean
}

export const PremiumButton: React.FC<PremiumButtonProps> = ({
  className,
  children,
  variant = 'primary',
  size = 'md',
  type = 'button',
  icon,
  onClick,
  disabled = false,
  loading = false
}) => {
  const baseClasses = "font-semibold transition-all duration-300 border-0 cursor-pointer inline-flex items-center justify-center gap-2 relative overflow-hidden"
  
  const variants = {
    primary: "btn-premium",
    secondary: "btn-secondary", 
    success: "bg-gradient-to-r from-emerald-500 to-emerald-600 text-white shadow-lg hover:shadow-xl hover:shadow-emerald-500/25",
    ghost: "bg-transparent text-white/90 hover:bg-white/10 border border-white/20"
  }

  const sizes = {
    sm: "px-6 py-2 text-sm rounded-full min-w-[120px]",
    md: "px-8 py-3 text-base rounded-full min-w-[160px]", 
    lg: "px-10 py-4 text-lg rounded-full min-w-[200px]"
  }

  return (
    <button
      type={type}
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      onClick={onClick}
      disabled={disabled || loading}
    >
      {loading ? (
        <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
      ) : icon}
      {children}
    </button>
  )
}

// === PREMIUM COUNTER ANIMATION ===
interface PremiumCounterProps {
  target: number
  suffix?: string
  duration?: number
  className?: string
}

export const PremiumCounter: React.FC<PremiumCounterProps> = ({ 
  target, 
  suffix = '', 
  duration = 2000,
  className 
}) => {
  const [count, setCount] = useState(0)

  useEffect(() => {
    let startTime: number | null = null
    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime
      const progress = (currentTime - startTime) / duration
      
      if (progress < 1) {
        setCount(Math.floor(target * progress))
        requestAnimationFrame(animate)
      } else {
        setCount(target)
      }
    }
    
    requestAnimationFrame(animate)
  }, [target, duration])

  return (
    <span className={cn("counter-number", className)}>
      {count}{suffix}
    </span>
  )
}

// === PREMIUM VALUE CARD ===
interface PremiumValueCardProps extends PremiumComponentProps {
  icon: React.ReactNode
  title: string
  items: string[]
  onClick?: () => void
}

export const PremiumValueCard: React.FC<PremiumValueCardProps> = ({
  className,
  icon,
  title,
  items,
  onClick
}) => {
  return (
    <div className={cn("value-card", className)} onClick={onClick}>
      <div className="value-icon">
        {icon}
      </div>
      <h3 className="text-xl font-semibold mb-4 text-white relative z-2">
        {title}
      </h3>
      <ul className="space-y-3 relative z-2">
        {items.map((item, index) => (
          <li key={index} className="flex items-start gap-3 text-white/90 transition-all duration-300">
            <div className="text-purple-400 text-lg mt-0.5">✨</div>
            <span>{item}</span>
          </li>
        ))}
      </ul>
    </div>
  )
}

// === PREMIUM PROCESSING ANIMATION ===
interface PremiumProcessingProps {
  progress?: number
  message?: string
  className?: string
}

export const PremiumProcessing: React.FC<PremiumProcessingProps> = ({
  progress = 0,
  message,
  className
}) => {
  return (
    <div className={cn("text-center", className)}>
      <div className="processing-animation">
        <div className="processing-circle" />
      </div>
      {progress > 0 && (
        <div className="mb-4">
          <div className="w-full bg-white/10 rounded-full h-2">
            <div 
              className="h-2 rounded-full transition-all duration-500"
              style={{ 
                width: `${progress}%`,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
              }}
            />
          </div>
          <p className="text-sm text-white/70 mt-2">{progress}% Complete</p>
        </div>
      )}
      {message && (
        <h3 className="text-xl font-semibold text-white mb-2">
          {message}
        </h3>
      )}
    </div>
  )
}

// === PREMIUM FORM INPUT ===
interface PremiumInputProps {
  id?: string
  type?: string
  placeholder?: string
  value?: string | number
  onChange?: (value: string) => void
  onBlur?: () => void
  className?: string
  required?: boolean
  disabled?: boolean
  error?: boolean | string
  icon?: React.ReactNode
  label?: string
  maxLength?: number
}

export const PremiumInput: React.FC<PremiumInputProps> = ({
  id,
  type = 'text',
  placeholder,
  value,
  onChange,
  onBlur,
  className,
  required = false,
  disabled = false,
  error,
  icon,
  label,
  maxLength
}) => {
  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-white/90">
          {label}
        </label>
      )}
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50">
            {icon}
          </div>
        )}
        <input
          id={id}
          type={type}
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          onBlur={onBlur}
          maxLength={maxLength}
          required={required}
          disabled={disabled}
          className={cn(
            "form-input",
            icon && "pl-12",
            error && "border-red-500 focus:border-red-500 focus:shadow-red-500/25"
          )}
      />
      </div>
      {error && typeof error === 'string' && (
        <p className="text-red-400 text-sm">{error}</p>
      )}
    </div>
  )
}

// === PREMIUM STATS CARD ===
interface PremiumStatsCardProps extends PremiumComponentProps {
  icon: React.ReactNode
  number: string | number
  label: string
  gradient?: 'blue' | 'purple' | 'green' | 'gold'
  animated?: boolean
}

export const PremiumStatsCard: React.FC<PremiumStatsCardProps> = ({
  className,
  icon,
  number,
  label,
  gradient = 'blue',
  animated = true
}) => {
  const gradients = {
    blue: 'text-gradient-primary',
    purple: 'text-gradient-secondary', 
    green: 'text-gradient-success',
    gold: 'bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent'
  }

  return (
    <PremiumGlassCard className={cn("text-center", className)}>
      <div className="text-blue-400 mb-3">
        {icon}
      </div>
      <div className={cn(
        "text-4xl font-bold mb-2",
        gradients[gradient]
      )}>
        {typeof number === 'number' && animated ? (
          <PremiumCounter target={number} />
        ) : (
          number
        )}
      </div>
      <p className="text-white/80 text-sm font-medium">
        {label}
      </p>
    </PremiumGlassCard>
  )
}

// === PREMIUM TESTIMONIAL CARD ===
interface PremiumTestimonialProps extends PremiumComponentProps {
  quote: string
  author: string
  role: string
  rating?: number
}

export const PremiumTestimonial: React.FC<PremiumTestimonialProps> = ({
  className,
  quote,
  author,
  role,
  rating = 5
}) => {
  return (
    <PremiumGlassCard className={cn("text-left", className)}>
      {rating && (
        <div className="flex gap-1 mb-4">
          {Array.from({ length: rating }).map((_, i) => (
            <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
          ))}
        </div>
      )}
      <blockquote className="text-white/90 text-lg leading-relaxed mb-6">
        "{quote}"
      </blockquote>
      <footer>
        <div className="font-semibold text-white">{author}</div>
        <div className="text-white/70 text-sm">{role}</div>
      </footer>
    </PremiumGlassCard>
  )
}

// === PREMIUM FEATURE GRID ===
interface PremiumFeatureProps {
  icon: React.ReactNode
  title: string
  description: string
}

interface PremiumFeatureGridProps extends PremiumComponentProps {
  features: PremiumFeatureProps[]
  columns?: number
}

export const PremiumFeatureGrid: React.FC<PremiumFeatureGridProps> = ({
  className,
  features,
  columns = 3
}) => {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2', 
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  }

  return (
    <div className={cn(
      "grid gap-6",
      gridCols[columns as keyof typeof gridCols],
      className
    )}>
      {features.map((feature, index) => (
        <PremiumGlassCard key={index} className="text-center">
          <div className="text-blue-400 mb-4 flex justify-center">
            {feature.icon}
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">
            {feature.title}
          </h3>
          <p className="text-white/80 text-sm">
            {feature.description}
          </p>
        </PremiumGlassCard>
      ))}
    </div>
  )
}

// === PREMIUM PROGRESS STEPS ===
interface PremiumProgressStep {
  label: string
  completed: boolean
  active: boolean
}

interface PremiumProgressStepsProps extends PremiumComponentProps {
  steps: PremiumProgressStep[]
}

export const PremiumProgressSteps: React.FC<PremiumProgressStepsProps> = ({
  className,
  steps
}) => {
  return (
    <div className={cn("flex items-center justify-between", className)}>
      {steps.map((step, index) => (
        <div key={index} className="flex items-center">
          <div className={cn(
            "w-10 h-10 rounded-full flex items-center justify-center border-2 transition-all duration-300",
            step.completed 
              ? "bg-gradient-to-r from-emerald-500 to-emerald-600 border-emerald-500 text-white" 
              : step.active
              ? "border-blue-500 text-blue-500 bg-blue-500/10"
              : "border-white/30 text-white/50"
          )}>
            {step.completed ? (
              <Check className="w-5 h-5" />
            ) : (
              <span className="text-sm font-semibold">{index + 1}</span>
            )}
          </div>
          <span className={cn(
            "ml-3 text-sm font-medium transition-colors duration-300",
            step.completed 
              ? "text-emerald-400"
              : step.active 
              ? "text-blue-400"
              : "text-white/50"
          )}>
            {step.label}
          </span>
          {index < steps.length - 1 && (
            <div className={cn(
              "w-16 h-0.5 mx-4 transition-colors duration-300",
              steps[index + 1].completed 
                ? "bg-emerald-500"
                : "bg-white/20"
            )} />
          )}
        </div>
      ))}
    </div>
  )
}

// === PREMIUM TRUST BADGES ===
interface PremiumTrustBadgeProps extends PremiumComponentProps {
  icon: React.ReactNode
  text: string
}

export const PremiumTrustBadge: React.FC<PremiumTrustBadgeProps> = ({
  className,
  icon,
  text
}) => {
  return (
    <div className={cn(
      "flex items-center gap-2 text-white/80 text-sm",
      className
    )}>
      <div className="text-emerald-400">
        {icon}
      </div>
      <span>{text}</span>
    </div>
  )
}

// === PREMIUM CTA SECTION ===
interface PremiumCTASectionProps extends PremiumComponentProps {
  title: string
  subtitle?: string
  primaryButton: {
    text: string
    onClick: () => void
  }
  secondaryButton?: {
    text: string
    onClick: () => void
  }
  trustBadges?: Array<{
    icon: React.ReactNode
    text: string
  }>
}

export const PremiumCTASection: React.FC<PremiumCTASectionProps> = ({
  className,
  title,
  subtitle,
  primaryButton,
  secondaryButton,
  trustBadges
}) => {
  return (
    <div className={cn("text-center max-w-2xl mx-auto", className)}>
      <h2 className="premium-title text-4xl md:text-5xl mb-4">
        {title}
      </h2>
      {subtitle && (
        <p className="premium-subtitle text-xl mb-8">
          {subtitle}
        </p>
      )}
      <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
        <PremiumButton 
          variant="primary"
          size="lg"
          icon={<ArrowRight className="w-5 h-5" />}
          onClick={primaryButton.onClick}
        >
          {primaryButton.text}
        </PremiumButton>
        {secondaryButton && (
          <PremiumButton
            variant="secondary"
            size="lg"
            onClick={secondaryButton.onClick}
          >
            {secondaryButton.text}
          </PremiumButton>
        )}
      </div>
      {trustBadges && (
        <div className="flex flex-wrap justify-center gap-6">
          {trustBadges.map((badge, index) => (
            <PremiumTrustBadge
              key={index}
              icon={badge.icon}
              text={badge.text}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// === PREMIUM HERO SECTION ===
interface PremiumHeroProps extends PremiumComponentProps {
  title: string
  subtitle: string
  ctaText: string
  onCTAClick: () => void
  stats?: Array<{
    icon: React.ReactNode
    number: string | number
    label: string
  }>
  trustBadges?: Array<{
    icon: React.ReactNode
    text: string
  }>
}

export const PremiumHero: React.FC<PremiumHeroProps> = ({
  className,
  title,
  subtitle,
  ctaText,
  onCTAClick,
  stats,
  trustBadges
}) => {
  return (
    <section className={cn("min-h-screen flex items-center justify-center px-6 py-20 relative", className)}>
      <div className="max-w-6xl mx-auto text-center relative z-10">
        <h1 className="premium-title mb-6">
          {title}
        </h1>
        <p className="premium-subtitle mb-12">
          {subtitle}
        </p>
        
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto mb-12">
            {stats.map((stat, index) => (
              <PremiumStatsCard
                key={index}
                icon={stat.icon}
                number={stat.number}
                label={stat.label}
                animated={typeof stat.number === 'number'}
              />
            ))}
          </div>
        )}

        <div className="mb-8">
          <PremiumButton
            variant="primary"
            size="lg"
            icon={<ArrowRight className="w-5 h-5" />}
            onClick={onCTAClick}
          >
            {ctaText}
          </PremiumButton>
        </div>

        {trustBadges && (
          <div className="flex flex-wrap justify-center gap-6">
            {trustBadges.map((badge, index) => (
              <PremiumTrustBadge
                key={index}
                icon={badge.icon}
                text={badge.text}
              />
            ))}
          </div>
        )}
      </div>
    </section>
  )
}

// === PREMIUM FILE UPLOAD ===
interface PremiumFileUploadProps extends PremiumComponentProps {
  onFileSelect: (files: FileList) => void
  accept?: string
  multiple?: boolean
  maxSize?: number // in MB
  description?: string
}

export const PremiumFileUpload: React.FC<PremiumFileUploadProps> = ({
  className,
  onFileSelect,
  accept = ".xlsx,.xls,.csv",
  multiple = false,
  maxSize = 10,
  description = "Upload your financial data"
}) => {
  const [dragActive, setDragActive] = useState(false)
  const [files, setFiles] = useState<File[]>([])

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      onFileSelect(e.dataTransfer.files)
      setFiles(Array.from(e.dataTransfer.files))
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault()
    if (e.target.files && e.target.files[0]) {
      onFileSelect(e.target.files)
      setFiles(Array.from(e.target.files))
    }
  }

  return (
    <PremiumGlassCard className={cn("text-center p-12", dragActive && "border-blue-400 bg-blue-500/10", className)}>
      <div 
        className={cn(
          "border-2 border-dashed border-white/30 rounded-xl p-8 transition-all duration-300",
          dragActive && "border-blue-400 bg-blue-500/5"
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={handleChange}
          className="hidden"
          id="file-upload"
        />
        <label htmlFor="file-upload" className="cursor-pointer">
          <div className="mb-4">
            <Upload className="w-16 h-16 mx-auto text-blue-400 mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">
              {dragActive ? "Drop files here" : "Upload Your Files"}
            </h3>
            <p className="text-white/70 mb-4">{description}</p>
            <PremiumButton variant="secondary" size="md">
              Choose Files
            </PremiumButton>
          </div>
        </label>
        
        {files.length > 0 && (
          <div className="mt-6 pt-6 border-t border-white/20">
            <h4 className="text-sm font-medium text-white mb-3">Selected Files:</h4>
            {files.map((file, index) => (
              <div key={index} className="flex items-center justify-between bg-white/10 rounded-lg p-3 mb-2">
                <div className="flex items-center gap-3">
                  <FileText className="w-4 h-4 text-blue-400" />
                  <span className="text-white text-sm">{file.name}</span>
                </div>
                <span className="text-white/70 text-xs">
                  {(file.size / 1024 / 1024).toFixed(2)} MB
                </span>
              </div>
            ))}
          </div>
        )}
        
        <p className="text-xs text-white/50 mt-4">
          Supported formats: {accept} • Max size: {maxSize}MB
        </p>
      </div>
    </PremiumGlassCard>
  )
}

// === PREMIUM BLUR REVEAL ===
interface PremiumBlurRevealProps extends PremiumComponentProps {
  title: string
  previewText: string
  isRevealed?: boolean
  onReveal?: () => void
  revealButtonText?: string
  sections?: Array<{
    title: string
    items: string[]
  }>
}

export const PremiumBlurReveal: React.FC<PremiumBlurRevealProps> = ({
  className,
  title,
  previewText,
  isRevealed = false,
  onReveal,
  revealButtonText = "Unlock Full Report - ₹1999",
  sections = []
}) => {
  return (
    <PremiumGlassCard className={cn("relative overflow-hidden", className)}>
      <div className={cn(
        "transition-all duration-500",
        !isRevealed && "blur-sm"
      )}>
        <h2 className="text-2xl font-bold text-white mb-4">{title}</h2>
        <p className="text-white/80 mb-6">{previewText}</p>
        
        {sections.map((section, index) => (
          <div key={index} className="mb-6">
            <h3 className="text-lg font-semibold text-white mb-3">{section.title}</h3>
            <ul className="space-y-2">
              {section.items.map((item, itemIndex) => (
                <li key={itemIndex} className="flex items-start gap-2 text-white/70">
                  <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0" />
                  <span>{item}</span>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
      
      {!isRevealed && (
        <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-t from-black/80 via-transparent to-transparent">
          <div className="text-center">
            <Lock className="w-12 h-12 text-blue-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-white mb-2">Premium Content</h3>
            <p className="text-white/80 mb-6">Unlock detailed analysis and insights</p>
            <PremiumButton 
              variant="primary"
              size="lg"
              onClick={onReveal}
              icon={<Unlock className="w-5 h-5" />}
            >
              {revealButtonText}
            </PremiumButton>
          </div>
        </div>
      )}
    </PremiumGlassCard>
  )
}

// === PREMIUM METRICS DASHBOARD ===
interface PremiumMetricsDashboardProps extends PremiumComponentProps {
  metrics: Array<{
    icon: React.ReactNode
    label: string
    value: string | number
    change?: string
    trend?: 'up' | 'down' | 'neutral'
    color?: 'blue' | 'green' | 'purple' | 'gold'
  }>
  title?: string
}

export const PremiumMetricsDashboard: React.FC<PremiumMetricsDashboardProps> = ({
  className,
  metrics,
  title = "Key Metrics"
}) => {
  return (
    <PremiumGlassContainer className={cn("space-y-6", className)}>
      <h2 className="text-2xl font-bold text-white mb-6">{title}</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <div key={index} className="bg-white/5 rounded-xl p-6 border border-white/10">
            <div className="flex items-center gap-3 mb-3">
              <div className="text-blue-400">
                {metric.icon}
              </div>
              <span className="text-white/70 text-sm font-medium">{metric.label}</span>
            </div>
            <div className="mb-2">
              <span className={cn(
                "text-2xl font-bold",
                metric.color === 'green' && "text-emerald-400",
                metric.color === 'purple' && "text-purple-400", 
                metric.color === 'gold' && "text-yellow-400",
                !metric.color && "text-white"
              )}>
                {metric.value}
              </span>
            </div>
            {metric.change && (
              <div className={cn(
                "text-xs flex items-center gap-1",
                metric.trend === 'up' && "text-emerald-400",
                metric.trend === 'down' && "text-red-400",
                metric.trend === 'neutral' && "text-white/50"
              )}>
                {metric.trend === 'up' && '↗'}
                {metric.trend === 'down' && '↘'}
                {metric.trend === 'neutral' && '→'}
                {metric.change}
              </div>
            )}
          </div>
        ))}
      </div>
    </PremiumGlassContainer>
  )
}

// === PREMIUM PRICING CARD ===
interface PremiumPricingCardProps extends PremiumComponentProps {
  plan: {
    name: string
    price: string
    originalPrice?: string
    features: string[]
    highlighted?: boolean
    buttonText?: string
    popular?: boolean
  }
  onSelect: () => void
}

export const PremiumPricingCard: React.FC<PremiumPricingCardProps> = ({
  className,
  plan,
  onSelect
}) => {
  return (
    <PremiumGlassCard 
      className={cn(
        "relative",
        plan.highlighted && "border-blue-400 bg-blue-500/10 scale-105 shadow-2xl",
        className
      )}
      onClick={onSelect}
    >
      {plan.popular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <span className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-2 rounded-full text-sm font-semibold">
            Most Popular
          </span>
        </div>
      )}
      
      <div className="text-center mb-6">
        <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
        <div className="mb-4">
          <span className="text-3xl font-bold text-gradient-primary">{plan.price}</span>
          {plan.originalPrice && (
            <span className="text-white/50 line-through ml-2 text-lg">{plan.originalPrice}</span>
          )}
        </div>
      </div>
      
      <ul className="space-y-3 mb-8">
        {plan.features.map((feature, index) => (
          <li key={index} className="flex items-start gap-3 text-white/90">
            <Check className="w-5 h-5 text-emerald-400 mt-0.5 flex-shrink-0" />
            <span>{feature}</span>
          </li>
        ))}
      </ul>
      
      <PremiumButton
        variant={plan.highlighted ? "primary" : "secondary"}
        size="lg"
        onClick={onSelect}
        className="w-full"
      >
        {plan.buttonText || "Get Started"}
      </PremiumButton>
    </PremiumGlassCard>
  )
}

// === PREMIUM NOTIFICATION TOAST ===
interface PremiumNotificationProps {
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  onClose: () => void
  duration?: number
}

export const PremiumNotification: React.FC<PremiumNotificationProps> = ({
  type,
  title,
  message,
  onClose,
  duration = 5000
}) => {
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(onClose, duration)
      return () => clearTimeout(timer)
    }
  }, [duration, onClose])

  const icons = {
    success: <Check className="w-5 h-5" />,
    error: <X className="w-5 h-5" />,
    warning: <AlertTriangle className="w-5 h-5" />,
    info: <Info className="w-5 h-5" />
  }

  const colors = {
    success: "border-emerald-500 bg-emerald-500/10",
    error: "border-red-500 bg-red-500/10",
    warning: "border-yellow-500 bg-yellow-500/10", 
    info: "border-blue-500 bg-blue-500/10"
  }

  return (
    <div className={cn(
      "fixed top-4 right-4 z-50 max-w-md w-full",
      "glass-container border-2",
      colors[type],
      "animate-slide-in-right"
    )}>
      <div className="flex items-start gap-3">
        <div className={cn(
          "mt-0.5",
          type === 'success' && "text-emerald-400",
          type === 'error' && "text-red-400", 
          type === 'warning' && "text-yellow-400",
          type === 'info' && "text-blue-400"
        )}>
          {icons[type]}
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="font-semibold text-white mb-1">{title}</h4>
          <p className="text-white/80 text-sm">{message}</p>
        </div>
        <button 
          onClick={onClose}
          className="text-white/50 hover:text-white transition-colors"
        >
          ×
        </button>
      </div>
    </div>
  )
}

// === PREMIUM ANALYSIS FORM ===
interface PremiumAnalysisFormProps {
  onSubmit: (data: { email: string; website: string; linkedin?: string }) => Promise<void>
  onAutoSave: (data: Partial<{ email: string; website: string; linkedin?: string }>) => void
  initialData: Partial<{ email: string; website: string; linkedin?: string }>
  loading?: boolean
  error?: string | null
}

export const PremiumAnalysisForm: React.FC<PremiumAnalysisFormProps> = ({
  onSubmit,
  onAutoSave,
  initialData,
  loading = false,
  error
}) => {
  const [formData, setFormData] = useState({
    email: initialData.email || '',
    website: initialData.website || '',
    linkedin: initialData.linkedin || ''
  })
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})

  // Auto-save functionality
  useEffect(() => {
    if (Object.keys(touched).length > 0) {
      const timer = setTimeout(() => {
        onAutoSave(formData)
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [formData, touched, onAutoSave])

  // Validation
  const validateField = (name: string, value: string) => {
    const errors: Record<string, string> = {}
    
    if (name === 'email') {
      if (!value) {
        errors.email = 'Email is required'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        errors.email = 'Please enter a valid email address'
      }
    }
    
    if (name === 'website') {
      if (!value) {
        errors.website = 'Website URL is required'
      } else {
        // Clean and validate URL
        let url = value.trim()
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
          url = 'https://' + url
        }
        try {
          new URL(url)
        } catch {
          errors.website = 'Please enter a valid website URL'
        }
      }
    }
    
    if (name === 'linkedin' && value) {
      if (!value.includes('linkedin.com')) {
        errors.linkedin = 'Please enter a valid LinkedIn profile URL'
      }
    }
    
    return errors
  }

  const handleInputChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
    setTouched(prev => ({ ...prev, [name]: true }))
    
    // Clear validation error when user starts typing
    if (validationErrors[name]) {
      setValidationErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const handleBlur = (name: string) => {
    const errors = validateField(name, formData[name as keyof typeof formData])
    setValidationErrors(prev => ({ ...prev, ...errors }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate all fields
    const emailErrors = validateField('email', formData.email)
    const websiteErrors = validateField('website', formData.website)
    const linkedinErrors = validateField('linkedin', formData.linkedin)
    
    const allErrors = { ...emailErrors, ...websiteErrors, ...linkedinErrors }
    setValidationErrors(allErrors)
    
    if (Object.keys(allErrors).length > 0) {
      return
    }
    
    // Clean website URL before submission
    let cleanedWebsite = formData.website.trim()
    if (!cleanedWebsite.startsWith('http://') && !cleanedWebsite.startsWith('https://')) {
      cleanedWebsite = 'https://' + cleanedWebsite
    }
    
    try {
      await onSubmit({
        email: formData.email.trim(),
        website: cleanedWebsite,
        linkedin: formData.linkedin.trim() || undefined
      })
    } catch (err) {
      console.error('Form submission failed:', err)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Email Input */}
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-white/90 mb-2">
          Email Address *
        </label>
        <PremiumInput
          id="email"
          type="email"
          value={formData.email}
          onChange={(value) => handleInputChange('email', value)}
          onBlur={() => handleBlur('email')}
          placeholder="<EMAIL>"
          error={!!validationErrors.email}
          disabled={loading}
          icon={<Mail className="w-5 h-5" />}
          className={cn(
            validationErrors.email && "border-red-500/50 focus:border-red-500",
            "transition-all duration-200"
          )}
        />
        {validationErrors.email && (
          <p className="mt-1 text-sm text-red-400 flex items-center gap-1">
            <AlertTriangle className="w-4 h-4" />
            {validationErrors.email}
          </p>
        )}
      </div>

      {/* Website Input */}
      <div>
        <label htmlFor="website" className="block text-sm font-medium text-white/90 mb-2">
          Company Website *
        </label>
        <PremiumInput
          id="website"
          type="url"
          value={formData.website}
          onChange={(value) => handleInputChange('website', value)}
          onBlur={() => handleBlur('website')}
          placeholder="www.yourcompany.com"
          error={!!validationErrors.website}
          disabled={loading}
          icon={<Globe className="w-5 h-5" />}
          className={cn(
            validationErrors.website && "border-red-500/50 focus:border-red-500",
            "transition-all duration-200"
          )}
        />
        {validationErrors.website && (
          <p className="mt-1 text-sm text-red-400 flex items-center gap-1">
            <AlertTriangle className="w-4 h-4" />
            {validationErrors.website}
          </p>
        )}
        <p className="mt-1 text-xs text-white/50">
          We'll analyze only public information from your website
        </p>
      </div>

      {/* LinkedIn Input (Optional) */}
      <div>
        <label htmlFor="linkedin" className="block text-sm font-medium text-white/90 mb-2">
          LinkedIn Profile <span className="text-white/50">(Optional)</span>
        </label>
        <PremiumInput
          id="linkedin"
          type="url"
          value={formData.linkedin}
          onChange={(value) => handleInputChange('linkedin', value)}
          onBlur={() => handleBlur('linkedin')}
          placeholder="https://www.linkedin.com/in/yourname"
          error={!!validationErrors.linkedin}
          disabled={loading}
          icon={<Linkedin className="w-5 h-5" />}
          className={cn(
            validationErrors.linkedin && "border-red-500/50 focus:border-red-500",
            "transition-all duration-200"
          )}
        />
        {validationErrors.linkedin && (
          <p className="mt-1 text-sm text-red-400 flex items-center gap-1">
            <AlertTriangle className="w-4 h-4" />
            {validationErrors.linkedin}
          </p>
        )}
        <p className="mt-1 text-xs text-white/50">
          Helps us understand your professional background better
        </p>
      </div>

      {/* Submit Button */}
      <div className="pt-4">
        <PremiumButton
          type="submit"
          variant="primary"
          size="lg"
          loading={loading}
          disabled={loading || Object.keys(validationErrors).some(key => !!validationErrors[key])}
          icon={loading ? undefined : <ArrowRight className="w-5 h-5" />}
          className="w-full"
        >
          {loading ? (
            <span className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Starting Analysis...
            </span>
          ) : (
            'Start AI Analysis'
          )}
        </PremiumButton>
        
        <div className="text-center mt-4">
          <p className="text-xs text-white/60">
            Takes 30 seconds • Public data only • 100% secure
          </p>
        </div>
      </div>
    </form>
  )
}

// === PREMIUM PROCESSING WORKFLOW ===
interface PremiumProcessingWorkflowProps {
  companyData: {
    companyName: string
    website: string
    industry: string
    stage: string
    email: string
    fundingNeeds?: string
    description?: string
  }
  onComplete: (results: any) => void
  onError: (error: Error) => void
}

type ProcessingStage = {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  estimatedDuration: number
  status: 'pending' | 'active' | 'completed' | 'error'
  progress: number
  substeps: string[]
  currentSubstep: number
}

export const PremiumProcessingWorkflow: React.FC<PremiumProcessingWorkflowProps> = ({
  companyData,
  onComplete,
  onError
}) => {
  const [currentView, setCurrentView] = useState<'starting' | 'processing' | 'completed' | 'error'>('starting')
  const [analysisId, setAnalysisId] = useState<string>('')
  const [error, setError] = useState<Error | null>(null)
  const [overallProgress, setOverallProgress] = useState(0)
  const [currentStatusMessage, setCurrentStatusMessage] = useState('Initializing analysis...')
  const [timeRemaining, setTimeRemaining] = useState(70)

  // Define the 5 engaging processing stages with premium icons
  const [processingStages, setProcessingStages] = useState<ProcessingStage[]>([
    {
      id: 'website_analysis',
      title: 'Deep Website Analysis',
      description: 'Our AI is reading every page, understanding your business model',
      icon: <Globe className="w-8 h-8" />,
      estimatedDuration: 15,
      status: 'pending',
      progress: 0,
      substeps: [
        'Scanning website content & pages',
        'Extracting business information', 
        'Understanding your value proposition',
        'Identifying your target market'
      ],
      currentSubstep: 0
    },
    {
      id: 'similarity_matching',
      title: 'Finding Your Tribe',
      description: 'Comparing against 500+ Shark Tank companies',
      icon: <Users className="w-8 h-8" />,
      estimatedDuration: 20,
      status: 'pending',
      progress: 0,
      substeps: [
        'Matching your business model',
        'Comparing market sectors & stages',
        'Analyzing success patterns',
        'Calculating similarity scores'
      ],
      currentSubstep: 0
    },
    {
      id: 'shark_compatibility',
      title: 'Shark Matchmaking',
      description: 'Analyzing which sharks invested in companies like yours',
      icon: <Target className="w-8 h-8" />,
      estimatedDuration: 15,
      status: 'pending',
      progress: 0,
      substeps: [
        'Studying shark investment patterns',
        'Matching shark preferences to your business',
        'Calculating compatibility scores',
        'Identifying your ideal shark partners'
      ],
      currentSubstep: 0
    },
    {
      id: 'pitch_strategy',
      title: 'Crafting Your Strategy',
      description: 'Building your personalized pitch strategy',
      icon: <BarChart3 className="w-8 h-8" />,
      estimatedDuration: 10,
      status: 'pending',
      progress: 0,
      substeps: [
        'Analyzing successful pitch patterns',
        'Identifying your key talking points',
        'Creating winning strategy recommendations',
        'Preparing shark-specific approaches'
      ],
      currentSubstep: 0
    },
    {
      id: 'insights_generation',
      title: 'Generating Insights',
      description: 'Compiling everything into actionable insights',
      icon: <Zap className="w-8 h-8" />,
      estimatedDuration: 10,
      status: 'pending',
      progress: 0,
      substeps: [
        'Synthesizing all analysis data',
        'Generating personalized insights',
        'Creating recommendation summary',
        'Finalizing your strategy blueprint'
      ],
      currentSubstep: 0
    }
  ])

  // Start analysis when component mounts
  useEffect(() => {
    const startAnalysis = async () => {
      try {
        setCurrentView('starting')
        
        // Brief starting delay for UX
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        setCurrentView('processing')
        
        // Simulate realistic progress over 70 seconds
        const totalDuration = 70000
        const startTime = Date.now()
        
        const progressInterval = setInterval(() => {
          const elapsed = Date.now() - startTime
          const progress = Math.min((elapsed / totalDuration) * 100, 100)
          
          setOverallProgress(progress)
          setTimeRemaining(Math.max(0, Math.ceil((totalDuration - elapsed) / 1000)))
          
          // Update current stage based on progress
          updateCurrentStage(progress)
          
          if (progress >= 100) {
            clearInterval(progressInterval)
            setAnalysisId(`analysis-${Date.now()}`)
            setCurrentView('completed')
            
            onComplete({
              analysisId: `analysis-${Date.now()}`,
              analysisData: {
                companyName: companyData.companyName,
                website: companyData.website,
                industry: companyData.industry,
                completedAt: new Date().toISOString()
              },
              companyData: companyData,
              completedAt: new Date().toISOString(),
              reportUrl: `/results/analysis-${Date.now()}`
            })
          }
        }, 500)
        
      } catch (err) {
        setError(err as Error)
        setCurrentView('error')
        onError(err as Error)
      }
    }

    startAnalysis()
  }, [companyData, onComplete, onError])

  // Update current stage based on progress
  const updateCurrentStage = (progress: number) => {
    setProcessingStages(prev => prev.map((stage, index) => {
      const stageStartProgress = (index / prev.length) * 100
      const stageEndProgress = ((index + 1) / prev.length) * 100
      
      if (progress >= stageEndProgress) {
        return { ...stage, status: 'completed', progress: 100, currentSubstep: stage.substeps.length - 1 }
      } else if (progress >= stageStartProgress) {
        const stageProgress = ((progress - stageStartProgress) / (stageEndProgress - stageStartProgress)) * 100
        return { 
          ...stage, 
          status: 'active', 
          progress: stageProgress,
          currentSubstep: Math.floor((stageProgress / 100) * stage.substeps.length)
        }
      } else {
        return { ...stage, status: 'pending', progress: 0, currentSubstep: 0 }
      }
    }))

    // Update status message based on progress
    const statusMessages = [
      { progress: 0, text: `🔍 Reading every page of ${companyData.website}...` },
      { progress: 15, text: `🧠 Understanding ${companyData.companyName} business model...` },
      { progress: 35, text: '📊 Comparing against 500+ Shark Tank companies...' },
      { progress: 55, text: '🎯 Found similar companies - analyzing success patterns...' },
      { progress: 70, text: '🦈 Matching you with compatible sharks...' },
      { progress: 85, text: '💡 Crafting your personalized pitch strategy...' },
      { progress: 95, text: '⚡ Generating insights and recommendations...' },
      { progress: 100, text: '✅ Analysis complete! Your strategy is ready.' }
    ]

    const currentMessage = statusMessages.reverse().find(msg => progress >= msg.progress)
    if (currentMessage) {
      setCurrentStatusMessage(currentMessage.text)
    }
  }

  const handleRetry = () => {
    setCurrentView('starting')
    setError(null)
    setOverallProgress(0)
    setProcessingStages(prev => prev.map(stage => ({
      ...stage,
      status: 'pending' as const,
      progress: 0,
      currentSubstep: 0
    })))
  }

  const handleCancel = () => {
    window.location.href = '/'
  }

  // Starting View
  if (currentView === 'starting') {
    return (
      <div className="min-h-screen flex items-center justify-center p-6">
        <PremiumGlassContainer className="text-center max-w-lg">
          <div className="animate-bounce-subtle mb-6">
            <div className="w-20 h-20 mx-auto bg-gradient-to-br from-blue-400 to-purple-600 rounded-full flex items-center justify-center">
              <Zap className="w-10 h-10 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-white mb-4">
            Initializing Analysis
          </h1>
          <p className="text-white/80 text-lg mb-2">
            Setting up your personalized Shark Tank analysis for
          </p>
          <p className="text-gradient-primary text-xl font-semibold mb-6">
            {companyData.companyName}
          </p>
          <div className="flex items-center justify-center gap-2 text-white/60">
            <Clock className="w-4 h-4" />
            <span>Estimated time: ~1 minute</span>
          </div>
        </PremiumGlassContainer>
      </div>
    )
  }

  // Error View
  if (currentView === 'error') {
    return (
      <div className="min-h-screen flex items-center justify-center p-6">
        <PremiumGlassContainer className="text-center max-w-lg">
          <div className="w-20 h-20 bg-red-500/20 border border-red-500/50 rounded-full flex items-center justify-center mx-auto mb-6">
            <X className="w-10 h-10 text-red-400" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-4">
            Analysis Failed
          </h1>
          <p className="text-white/80 mb-6">
            {error?.message || 'Something went wrong during the analysis. Please try again.'}
          </p>
          <div className="flex gap-4 justify-center">
            <PremiumButton variant="secondary" onClick={handleCancel}>
              Go Back
            </PremiumButton>
            <PremiumButton variant="primary" onClick={handleRetry}>
              Retry Analysis
            </PremiumButton>
          </div>
        </PremiumGlassContainer>
      </div>
    )
  }

  // Completed View
  if (currentView === 'completed') {
    return (
      <div className="min-h-screen flex items-center justify-center p-6">
        <PremiumGlassContainer className="text-center max-w-lg">
          <div className="animate-bounce-subtle mb-6">
            <div className="w-20 h-20 mx-auto bg-gradient-to-br from-green-400 to-emerald-600 rounded-full flex items-center justify-center">
              <Check className="w-10 h-10 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-white mb-4">
            Analysis Complete!
          </h1>
          <p className="text-white/80 text-lg mb-6">
            Your comprehensive Shark Tank analysis is ready. We've analyzed your business and found the best strategies for success.
          </p>
          <PremiumButton 
            variant="primary"
            size="lg"
            className="w-full"
            onClick={() => { window.location.href = `/results/${analysisId}` }}
            icon={<ArrowRight className="w-5 h-5" />}
          >
            View Your Report
          </PremiumButton>
        </PremiumGlassContainer>
      </div>
    )
  }

  // Main Processing View
  return (
    <div className="min-h-screen p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gradient-primary mb-4">
            Analyzing {companyData.companyName}
          </h1>
          <p className="text-white/80 text-xl mb-6">
            {currentStatusMessage}
          </p>
          
          {/* Overall Progress Bar */}
          <div className="max-w-2xl mx-auto">
            <div className="flex justify-between text-sm text-white/60 mb-2">
              <span>Progress: {Math.round(overallProgress)}%</span>
              <span>~{timeRemaining}s remaining</span>
            </div>
            <div className="h-3 bg-black/20 rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-500"
                style={{ width: `${overallProgress}%` }}
              />
            </div>
          </div>
        </div>

        {/* Processing Stages */}
        <div className="grid gap-6 md:gap-8">
          {processingStages.map((stage, index) => (
            <PremiumGlassCard
              key={stage.id}
              className={cn(
                "p-6 transition-all duration-500",
                stage.status === 'active' && "ring-2 ring-blue-500/50 animate-glow",
                stage.status === 'completed' && "bg-green-500/10 border-green-500/30"
              )}
            >
              <div className="flex items-start gap-6">
                {/* Stage Icon & Status */}
                <div className="flex-shrink-0">
                  <div className={cn(
                    "w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300",
                    stage.status === 'pending' && "bg-white/10 text-white/40",
                    stage.status === 'active' && "bg-gradient-to-br from-blue-500 to-purple-600 text-white animate-bounce-subtle",
                    stage.status === 'completed' && "bg-gradient-to-br from-green-500 to-emerald-600 text-white",
                    stage.status === 'error' && "bg-gradient-to-br from-red-500 to-pink-600 text-white"
                  )}>
                    {stage.status === 'completed' ? <Check className="w-8 h-8" /> : stage.icon}
                  </div>
                </div>

                {/* Stage Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-xl font-semibold text-white">
                      Stage {index + 1}: {stage.title}
                    </h3>
                    {stage.status === 'active' && (
                      <span className="text-sm text-blue-400 font-medium">
                        {Math.round(stage.progress)}%
                      </span>
                    )}
                  </div>
                  
                  <p className="text-white/70 mb-4">
                    {stage.description}
                  </p>

                  {/* Stage Progress Bar */}
                  {(stage.status === 'active' || stage.status === 'completed') && (
                    <div className="mb-4">
                      <div className="h-2 bg-black/20 rounded-full overflow-hidden">
                        <div 
                          className={cn(
                            "h-full rounded-full transition-all duration-500",
                            stage.status === 'active' && "bg-gradient-to-r from-blue-500 to-purple-600",
                            stage.status === 'completed' && "bg-gradient-to-r from-green-500 to-emerald-600"
                          )}
                          style={{ width: `${stage.progress}%` }}
                        />
                      </div>
                    </div>
                  )}

                  {/* Current Substep */}
                  {stage.status === 'active' && stage.currentSubstep < stage.substeps.length && (
                    <div className="text-sm text-white/60">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                        {stage.substeps[stage.currentSubstep]}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </PremiumGlassCard>
          ))}
        </div>

        {/* Cancel Button */}
        <div className="text-center mt-12">
          <PremiumButton
            variant="secondary"
            onClick={handleCancel}
            className="text-white/60 hover:text-white"
          >
            Cancel Analysis
          </PremiumButton>
        </div>
      </div>
    </div>
  )
}

// Premium Payment Components
export type PaymentMethod = 'upi' | 'card' | 'netbanking'

interface PremiumPaymentMethodSelectorProps {
  selectedMethod: PaymentMethod
  onMethodChange: (method: PaymentMethod) => void
  className?: string
}

export const PremiumPaymentMethodSelector: React.FC<PremiumPaymentMethodSelectorProps> = ({
  selectedMethod,
  onMethodChange,
  className = ''
}) => {
  const methods = [
    { 
      id: 'upi' as const, 
      label: 'UPI', 
      icon: <Smartphone className="w-5 h-5" />, 
      description: 'Quick & secure',
      color: 'from-green-400 to-emerald-400'
    },
    { 
      id: 'card' as const, 
      label: 'Card', 
      icon: <CreditCard className="w-5 h-5" />, 
      description: 'Debit/Credit',
      color: 'from-blue-400 to-cyan-400'
    },
    { 
      id: 'netbanking' as const, 
      label: 'Net Banking', 
      icon: <Building className="w-5 h-5" />, 
      description: 'All banks',
      color: 'from-purple-400 to-pink-400'
    }
  ]

  return (
    <div className={`grid grid-cols-3 gap-4 ${className}`}>
      {methods.map((method) => (
        <button
          key={method.id}
          type="button"
          onClick={() => onMethodChange(method.id)}
          className={`
            p-4 rounded-xl border transition-all duration-300 group hover:scale-105 touch-manipulation
            ${selectedMethod === method.id
              ? 'border-blue-400 bg-blue-400/10 shadow-lg shadow-blue-400/20'
              : 'border-white/10 bg-white/5 hover:border-white/20 hover:bg-white/10'
            }
          `}
        >
          <div className="flex flex-col items-center gap-3">
            <div className={`
              p-3 rounded-lg transition-all duration-300
              ${selectedMethod === method.id 
                ? `bg-gradient-to-r ${method.color} text-white shadow-lg` 
                : 'bg-white/5 text-white/60 group-hover:text-white/80'
              }
            `}>
              {method.icon}
            </div>
            <div className="text-center">
              <div className={`font-semibold transition-colors ${
                selectedMethod === method.id ? 'text-white' : 'text-white/80'
              }`}>
                {method.label}
              </div>
              <div className="text-xs text-white/60 mt-1">
                {method.description}
              </div>
            </div>
          </div>
        </button>
      ))}
    </div>
  )
}

interface PremiumPaymentFormsProps {
  selectedMethod: PaymentMethod
  onSubmit: (data: any) => void
  isProcessing?: boolean
  className?: string
}

export const PremiumPaymentForms: React.FC<PremiumPaymentFormsProps> = ({
  selectedMethod,
  onSubmit,
  isProcessing = false,
  className = ''
}) => {
  const [formData, setFormData] = useState<any>({})
  const [copySuccess, setCopySuccess] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev: any) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit({ method: selectedMethod, ...formData })
  }

  const copyUpiId = async () => {
    try {
      await navigator.clipboard.writeText('pitchprep@paytm')
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (err) {
      console.error('Failed to copy UPI ID:', err)
    }
  }

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '')
    const match = v.match(/\d{4,16}/g)?.[0] || ''
    const parts = []
    for (let i = 0; i < match.length; i += 4) {
      parts.push(match.substring(i, i + 4))
    }
    return parts.join(' ')
  }

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\D/g, '')
    return v.length >= 2 ? `${v.slice(0, 2)}/${v.slice(2, 4)}` : v
  }

  const banks = [
    { value: 'sbi', label: 'State Bank of India' },
    { value: 'hdfc', label: 'HDFC Bank' },
    { value: 'icici', label: 'ICICI Bank' },
    { value: 'axis', label: 'Axis Bank' },
    { value: 'kotak', label: 'Kotak Mahindra Bank' },
    { value: 'pnb', label: 'Punjab National Bank' }
  ]

  return (
    <form onSubmit={handleSubmit} className={className}>
      {/* UPI Form */}
      {selectedMethod === 'upi' && (
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-2">Pay with UPI</h3>
            <p className="text-white/60">Scan QR code or use UPI ID</p>
          </div>

          {/* QR Code Placeholder */}
          <div className="flex justify-center">
            <div className="w-48 h-48 bg-white rounded-xl flex items-center justify-center">
              <div className="text-center">
                <QrCode className="w-16 h-16 mx-auto mb-2 text-gray-600" />
                <p className="text-sm text-gray-600">QR Code</p>
              </div>
            </div>
          </div>

          {/* UPI ID Display */}
          <PremiumGlassCard className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-white/60 mb-1">Pay to UPI ID:</p>
                <p className="font-mono text-white">pitchprep@paytm</p>
              </div>
              <PremiumButton
                variant="secondary"
                size="sm"
                onClick={copyUpiId}
                type="button"
              >
                {copySuccess ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
              </PremiumButton>
            </div>
          </PremiumGlassCard>

          {/* Manual UPI ID Input */}
          <div>
            <PremiumInput
              label="Or enter your UPI ID"
              placeholder="yourname@paytm"
              value={formData.upiId || ''}
              onChange={(value) => handleInputChange('upiId', value)}
            />
          </div>
        </div>
      )}

      {/* Card Form */}
      {selectedMethod === 'card' && (
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-2">Pay with Card</h3>
            <p className="text-white/60">Debit or credit card</p>
          </div>

          <PremiumInput
            label="Card Number"
            placeholder="1234 5678 9012 3456"
            value={formData.cardNumber || ''}
            onChange={(value) => handleInputChange('cardNumber', formatCardNumber(value))}
            maxLength={19}
          />

          <div className="grid grid-cols-2 gap-4">
            <PremiumInput
              label="Expiry Date"
              placeholder="MM/YY"
              value={formData.expiryDate || ''}
              onChange={(value) => handleInputChange('expiryDate', formatExpiryDate(value))}
              maxLength={5}
            />
            <PremiumInput
              label="CVV"
              placeholder="123"
              value={formData.cvv || ''}
              onChange={(value) => handleInputChange('cvv', value.replace(/\D/g, ''))}
              maxLength={3}
            />
          </div>

          <PremiumInput
            label="Name on Card"
            placeholder="John Doe"
            value={formData.cardName || ''}
            onChange={(value) => handleInputChange('cardName', value)}
          />
        </div>
      )}

      {/* Net Banking Form */}
      {selectedMethod === 'netbanking' && (
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-2">Net Banking</h3>
            <p className="text-white/60">Select your bank</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Choose Your Bank
            </label>
            <select
              className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all"
              value={formData.bank || ''}
              onChange={(e) => handleInputChange('bank', e.target.value)}
            >
              <option value="" className="bg-gray-900 text-white">Choose your bank</option>
              {banks.map((bank) => (
                <option key={bank.value} value={bank.value} className="bg-gray-900 text-white">
                  {bank.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}

      {/* Submit Button */}
      <div className="mt-8">
        <PremiumButton
          type="submit"
          size="lg"
          disabled={isProcessing}
          loading={isProcessing}
          className="w-full text-lg font-semibold"
        >
          {isProcessing ? 'Processing...' : `Pay ₹1,999 Securely`}
          <Shield className="w-5 h-5 ml-2" />
        </PremiumButton>
      </div>
    </form>
  )
}

// === PREMIUM REPORT COMPONENTS ===

interface PremiumReportSectionProps extends PremiumComponentProps {
  title: string
  subtitle?: string
  icon?: React.ReactNode
  children: React.ReactNode
}

export const PremiumReportSection: React.FC<PremiumReportSectionProps> = ({
  title,
  subtitle,
  icon,
  children,
  className
}) => {
  return (
    <div className={cn("mb-12", className)}>
      <div className="flex items-center gap-4 mb-6">
        {icon && (
          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center flex-shrink-0">
            {icon}
          </div>
        )}
        <div>
          <h2 className="text-2xl sm:text-3xl font-bold text-white mb-1">{title}</h2>
          {subtitle && <p className="text-white/60">{subtitle}</p>}
        </div>
      </div>
      {children}
    </div>
  )
}

interface PremiumReportCardProps extends PremiumComponentProps {
  title: string
  value?: string
  description?: string
  icon?: React.ReactNode
  color?: 'blue' | 'purple' | 'green' | 'yellow' | 'red'
}

export const PremiumReportCard: React.FC<PremiumReportCardProps> = ({
  title,
  value,
  description,
  icon,
  color = 'blue',
  className,
  children
}) => {
  const colorVariants = {
    blue: 'from-blue-400 to-cyan-400',
    purple: 'from-purple-400 to-pink-400',
    green: 'from-green-400 to-emerald-400',
    yellow: 'from-yellow-400 to-orange-400',
    red: 'from-red-400 to-pink-400'
  }

  return (
    <PremiumGlassCard className={cn("p-6 relative overflow-hidden", className)}>
      <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${colorVariants[color]}`} />
      
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-white mb-2">{title}</h3>
          {value && (
            <div className={`text-2xl font-bold bg-gradient-to-r ${colorVariants[color]} bg-clip-text text-transparent`}>
              {value}
            </div>
          )}
          {description && (
            <p className="text-white/60 text-sm mt-2">{description}</p>
          )}
        </div>
        {icon && (
          <div className={`w-12 h-12 bg-gradient-to-r ${colorVariants[color]} rounded-lg flex items-center justify-center flex-shrink-0 ml-4`}>
            {icon}
          </div>
        )}
      </div>
      
      {children}
    </PremiumGlassCard>
  )
}

interface PremiumCompanyCardProps extends PremiumComponentProps {
  company: {
    name: string
    season: string
    episode: string
    business: string
    founder: string
    dealStatus: 'Deal' | 'No Deal'
    dealDetails?: string
    similarity: number
    keyInsights: string[]
    sharkInvolved?: string
  }
}

export const PremiumCompanyCard: React.FC<PremiumCompanyCardProps> = ({
  company,
  className
}) => {
  const dealColor = company.dealStatus === 'Deal' ? 'green' : 'red'
  
  return (
    <PremiumReportCard 
      title={company.name}
      value={`${Math.round(company.similarity * 100)}% Match`}
      description={`${company.season} • ${company.business}`}
      color={dealColor}
      className={className}
    >
      <div className="space-y-4">
        <div className="flex flex-wrap gap-2">
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
            company.dealStatus === 'Deal' 
              ? 'bg-green-500/10 text-green-400 border border-green-500/20' 
              : 'bg-red-500/10 text-red-400 border border-red-500/20'
          }`}>
            {company.dealStatus}
          </span>
          {company.sharkInvolved && (
            <span className="px-3 py-1 rounded-full text-xs font-medium bg-blue-500/10 text-blue-400 border border-blue-500/20">
              {company.sharkInvolved}
            </span>
          )}
        </div>
        
        {company.dealDetails && (
          <p className="text-white/70 text-sm font-medium">{company.dealDetails}</p>
        )}
        
        <div className="space-y-2">
          <p className="text-white/50 text-xs uppercase tracking-wide">Key Insights</p>
          <ul className="space-y-1">
            {company.keyInsights.slice(0, 3).map((insight, index) => (
              <li key={index} className="flex items-start gap-2 text-sm text-white/80">
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0" />
                <span>{insight}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </PremiumReportCard>
  )
}

interface PremiumSharkProfileProps extends PremiumComponentProps {
  shark: {
    name: string
    title: string
    specialties: string[]
    compatibilityScore: number
    investmentStyle: string
    avgDealSize: string
    successRate: string
    keyReasons: string[]
    recentDeals: string[]
  }
}

export const PremiumSharkProfile: React.FC<PremiumSharkProfileProps> = ({
  shark,
  className
}) => {
  return (
    <PremiumReportCard
      title={shark.name}
      value={`${shark.compatibilityScore}%`}
      description={shark.title}
      icon={<Users className="w-6 h-6 text-white" />}
      color="purple"
      className={className}
    >
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-white/50 uppercase tracking-wide text-xs mb-1">Avg Deal Size</p>
            <p className="text-white font-medium">{shark.avgDealSize}</p>
          </div>
          <div>
            <p className="text-white/50 uppercase tracking-wide text-xs mb-1">Success Rate</p>
            <p className="text-white font-medium">{shark.successRate}</p>
          </div>
        </div>
        
        <div>
          <p className="text-white/50 text-xs uppercase tracking-wide mb-2">Specialties</p>
          <div className="flex flex-wrap gap-1">
            {shark.specialties.map((specialty, index) => (
              <span 
                key={index} 
                className="px-2 py-1 bg-purple-500/10 text-purple-300 text-xs rounded-md border border-purple-500/20"
              >
                {specialty}
              </span>
            ))}
          </div>
        </div>
        
        <div>
          <p className="text-white/50 text-xs uppercase tracking-wide mb-2">Why This Match</p>
          <ul className="space-y-1">
            {shark.keyReasons.map((reason, index) => (
              <li key={index} className="flex items-start gap-2 text-sm text-white/80">
                <Check className="w-3 h-3 text-purple-400 mt-0.5 flex-shrink-0" />
                <span>{reason}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </PremiumReportCard>
  )
}

export default {
  PremiumAnimatedBackground,
  PremiumGlassContainer,
  PremiumGlassCard,
  PremiumButton,
  PremiumCounter,
  PremiumValueCard,
  PremiumProcessing,
  PremiumInput,
  PremiumStatsCard,
  PremiumTestimonial,
  PremiumFeatureGrid,
  PremiumProgressSteps,
  PremiumTrustBadge,
  PremiumCTASection,
  PremiumHero,
  PremiumFileUpload,
  PremiumBlurReveal,
  PremiumMetricsDashboard,
  PremiumPricingCard,
  PremiumNotification,
  PremiumAnalysisForm,
  PremiumProcessingWorkflow,
  PremiumPaymentMethodSelector,
  PremiumPaymentForms,
  PremiumReportSection,
  PremiumReportCard,
  PremiumCompanyCard,
  PremiumSharkProfile
}