import React from 'react';
import { Mic, Clock, Users, Presentation, CheckCircle, AlertCircle, Star } from 'lucide-react';

interface PitchPreparationSectionProps {
  data: any;
}

export const PitchPreparationSection: React.FC<PitchPreparationSectionProps> = ({ data }) => {
  const preparation = data?.preparation || {
    pitch_structure: {
      opening: 'Hook with problem statement and market opportunity',
      business_overview: 'Clear explanation of solution and value proposition',
      market_validation: 'Traction, customer testimonials, and metrics',
      business_model: 'Revenue model and unit economics',
      competition: 'Competitive landscape and differentiation',
      financials: 'Current performance and projections',
      ask: 'Investment amount, equity, and use of funds',
      closing: 'Vision and call to action'
    },
    time_allocation: {
      'Opening Hook': 1,
      'Problem & Solution': 2,
      'Market & Traction': 2,
      'Business Model': 1,
      'Competition': 1,
      'Financials': 1,
      'Ask & Close': 1,
      'Q&A': 3
    },
    key_talking_points: [
      'Market is growing at 35% CAGR with ₹5000 Cr opportunity',
      'Already achieved ₹50L revenue in 6 months',
      '87% customer retention rate with 4.8 star rating',
      'Unit economics positive with 40% gross margins',
      'Clear path to ₹5 Cr revenue in 12 months'
    ],
    demonstration_items: [
      'Live product demo on mobile',
      'Customer testimonial video (30 seconds)',
      'Revenue dashboard screenshot',
      'Growth chart visualization'
    ]
  };

  return (
    <div className="space-y-6">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-white mb-2">Pitch Preparation</h2>
        <p className="text-text-light">Complete guide for your Shark Tank pitch</p>
      </div>

      {/* Pitch Structure Timeline */}
      <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg p-6 border border-purple-400/30">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
            <Clock className="w-5 h-5 text-purple-400" />
          </div>
          <h3 className="text-xl font-bold text-white">12-Minute Pitch Timeline</h3>
        </div>
        
        <div className="space-y-3">
          {Object.entries(preparation.time_allocation).map(([segment, minutes], index) => {
            const minuteValue = typeof minutes === 'number' ? minutes : 0;
            return (
              <div key={segment} className="flex items-center gap-4">
                <div className="w-12 text-right">
                  <span className="text-purple-400 font-semibold">{minuteValue}m</span>
                </div>
              <div className="flex-1">
                <div className="bg-glass rounded-lg p-3 border border-glass-border">
                  <div className="flex items-center justify-between">
                    <span className="text-white font-medium">{segment}</span>
                    <span className="text-purple-300 text-sm">
                      {index === 0 ? '0:00-1:00' : 
                       index === 1 ? '1:00-3:00' :
                       index === 2 ? '3:00-5:00' :
                       index === 3 ? '5:00-6:00' :
                       index === 4 ? '6:00-7:00' :
                       index === 5 ? '7:00-8:00' :
                       index === 6 ? '8:00-9:00' :
                       '9:00-12:00'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            );
          })}
        </div>
      </div>

      {/* Pitch Flow & Structure */}
      <div className="bg-glass rounded-lg p-6 border border-glass-border">
        <div className="flex items-center gap-3 mb-4">
          <Presentation className="w-5 h-5 text-blue-400" />
          <h3 className="text-xl font-bold text-white">Pitch Flow & Structure</h3>
        </div>
        
        <div className="space-y-4">
          {Object.entries(preparation.pitch_structure).map(([phase, content], index) => {
            const contentText = typeof content === 'string' ? content : String(content);
            return (
              <div key={phase} className="flex gap-4">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                    <span className="text-blue-400 text-sm font-bold">{index + 1}</span>
                  </div>
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-white capitalize mb-1">
                    {phase.replace(/_/g, ' ')}
                  </h4>
                  <p className="text-text-light text-sm">{contentText}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Key Talking Points */}
      <div className="bg-gradient-to-br from-green-500/10 to-blue-500/10 rounded-lg p-6 border border-green-400/30">
        <div className="flex items-center gap-3 mb-4">
          <Star className="w-5 h-5 text-green-400" />
          <h3 className="text-xl font-bold text-white">Key Talking Points</h3>
        </div>
        
        <div className="space-y-3">
          {preparation.key_talking_points.map((point: string, index: number) => (
            <div key={index} className="flex items-start gap-3">
              <div className="w-6 h-6 bg-green-500/20 rounded-full flex items-center justify-center mt-0.5">
                <CheckCircle className="w-4 h-4 text-green-400" />
              </div>
              <p className="text-white flex-1">{point}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Demonstration Items */}
      <div className="bg-glass rounded-lg p-6 border border-glass-border">
        <div className="flex items-center gap-3 mb-4">
          <Mic className="w-5 h-5 text-purple-400" />
          <h3 className="text-xl font-bold text-white">Demonstration Items</h3>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {preparation.demonstration_items.map((item: string, index: number) => (
            <div key={index} className="bg-glass-dark rounded-lg p-4 border border-glass-border">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                  <span className="text-purple-400 font-bold">{index + 1}</span>
                </div>
                <p className="text-white">{item}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Do's and Don'ts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-gradient-to-br from-green-500/10 to-green-600/10 rounded-lg p-6 border border-green-400/30">
          <div className="flex items-center gap-3 mb-4">
            <CheckCircle className="w-5 h-5 text-green-400" />
            <h3 className="text-lg font-bold text-green-300">Do's</h3>
          </div>
          <ul className="space-y-2">
            <li className="text-green-100 text-sm flex items-start gap-2">
              <span className="text-green-400 mt-0.5">✓</span>
              <span>Start with a compelling hook or story</span>
            </li>
            <li className="text-green-100 text-sm flex items-start gap-2">
              <span className="text-green-400 mt-0.5">✓</span>
              <span>Show real traction and customer validation</span>
            </li>
            <li className="text-green-100 text-sm flex items-start gap-2">
              <span className="text-green-400 mt-0.5">✓</span>
              <span>Be clear about numbers and unit economics</span>
            </li>
            <li className="text-green-100 text-sm flex items-start gap-2">
              <span className="text-green-400 mt-0.5">✓</span>
              <span>Demonstrate passion and domain expertise</span>
            </li>
            <li className="text-green-100 text-sm flex items-start gap-2">
              <span className="text-green-400 mt-0.5">✓</span>
              <span>Practice smooth product demonstration</span>
            </li>
            <li className="text-green-100 text-sm flex items-start gap-2">
              <span className="text-green-400 mt-0.5">✓</span>
              <span>Be confident but open to feedback</span>
            </li>
          </ul>
        </div>
        
        <div className="bg-gradient-to-br from-red-500/10 to-red-600/10 rounded-lg p-6 border border-red-400/30">
          <div className="flex items-center gap-3 mb-4">
            <AlertCircle className="w-5 h-5 text-red-400" />
            <h3 className="text-lg font-bold text-red-300">Don'ts</h3>
          </div>
          <ul className="space-y-2">
            <li className="text-red-100 text-sm flex items-start gap-2">
              <span className="text-red-400 mt-0.5">✗</span>
              <span>Don't overvalue your company</span>
            </li>
            <li className="text-red-100 text-sm flex items-start gap-2">
              <span className="text-red-400 mt-0.5">✗</span>
              <span>Don't hide problems or challenges</span>
            </li>
            <li className="text-red-100 text-sm flex items-start gap-2">
              <span className="text-red-400 mt-0.5">✗</span>
              <span>Don't argue with shark feedback</span>
            </li>
            <li className="text-red-100 text-sm flex items-start gap-2">
              <span className="text-red-400 mt-0.5">✗</span>
              <span>Don't forget to mention competition</span>
            </li>
            <li className="text-red-100 text-sm flex items-start gap-2">
              <span className="text-red-400 mt-0.5">✗</span>
              <span>Don't ramble or go off-script</span>
            </li>
            <li className="text-red-100 text-sm flex items-start gap-2">
              <span className="text-red-400 mt-0.5">✗</span>
              <span>Don't underestimate operational challenges</span>
            </li>
          </ul>
        </div>
      </div>

      {/* Shark-Specific Tips */}
      <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg p-6 border border-purple-400/30">
        <div className="flex items-center gap-3 mb-4">
          <Users className="w-5 h-5 text-purple-400" />
          <h3 className="text-xl font-bold text-white">Shark-Specific Tips</h3>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div className="bg-glass rounded-lg p-4 border border-glass-border">
            <h4 className="font-semibold text-purple-300 mb-2">Aman Gupta</h4>
            <p className="text-text-light text-sm">Focus on brand building, D2C strategy, and repeat customer metrics</p>
          </div>
          <div className="bg-glass rounded-lg p-4 border border-glass-border">
            <h4 className="font-semibold text-purple-300 mb-2">Vineeta Singh</h4>
            <p className="text-text-light text-sm">Emphasize product quality, customer experience, and organic growth</p>
          </div>
          <div className="bg-glass rounded-lg p-4 border border-glass-border">
            <h4 className="font-semibold text-purple-300 mb-2">Namita Thapar</h4>
            <p className="text-text-light text-sm">Highlight scalability, operational excellence, and social impact</p>
          </div>
          <div className="bg-glass rounded-lg p-4 border border-glass-border">
            <h4 className="font-semibold text-purple-300 mb-2">Anupam Mittal</h4>
            <p className="text-text-light text-sm">Show technology edge, market size, and network effects</p>
          </div>
          <div className="bg-glass rounded-lg p-4 border border-glass-border">
            <h4 className="font-semibold text-purple-300 mb-2">Peyush Bansal</h4>
            <p className="text-text-light text-sm">Demonstrate customer-first approach and long-term vision</p>
          </div>
          <div className="bg-glass rounded-lg p-4 border border-glass-border">
            <h4 className="font-semibold text-purple-300 mb-2">Amit Jain</h4>
            <p className="text-text-light text-sm">Present clear path to profitability and sustainable growth</p>
          </div>
        </div>
      </div>

      {/* Practice Checklist */}
      <div className="bg-glass rounded-lg p-6 border border-glass-border">
        <h3 className="text-xl font-bold text-white mb-4">Pre-Pitch Practice Checklist</h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div>
            <h4 className="font-semibold text-purple-200 mb-3">Content Preparation</h4>
            <ul className="space-y-2">
              <li className="text-purple-100 text-sm flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-purple-400" />
                <span>Pitch script memorized</span>
              </li>
              <li className="text-purple-100 text-sm flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-purple-400" />
                <span>Numbers and metrics ready</span>
              </li>
              <li className="text-purple-100 text-sm flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-purple-400" />
                <span>Demo rehearsed 10+ times</span>
              </li>
              <li className="text-purple-100 text-sm flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-purple-400" />
                <span>Q&A responses prepared</span>
              </li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-purple-200 mb-3">Logistics & Setup</h4>
            <ul className="space-y-2">
              <li className="text-purple-100 text-sm flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-purple-400" />
                <span>Demo devices charged</span>
              </li>
              <li className="text-purple-100 text-sm flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-purple-400" />
                <span>Backup materials ready</span>
              </li>
              <li className="text-purple-100 text-sm flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-purple-400" />
                <span>Professional attire selected</span>
              </li>
              <li className="text-purple-100 text-sm flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-purple-400" />
                <span>Team roles defined</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PitchPreparationSection;