import React from 'react';
import { TrendingUp, DollarSign, Target, PieChart, BarChart3, Calculator } from 'lucide-react';

interface InvestmentStrategySectionProps {
  data: any;
}

export const InvestmentStrategySection: React.FC<InvestmentStrategySectionProps> = ({ data }) => {
  const investment = data?.investment || {
    funding_requirement: 5000000,
    equity_offered: 10,
    valuation: 50000000,
    use_of_funds: {
      'Product Development': 30,
      'Marketing & Sales': 35,
      'Operations & Infrastructure': 20,
      'Working Capital': 10,
      'Team Expansion': 5
    },
    financial_projections: {
      year1: { revenue: 5000000, profit: -2000000 },
      year2: { revenue: 25000000, profit: 3000000 },
      year3: { revenue: 75000000, profit: 15000000 }
    },
    roi_metrics: {
      payback_period: '24 months',
      irr: '45%',
      multiple: '5x in 3 years'
    }
  };

  const formatCurrency = (amount: number) => {
    if (amount >= 10000000) {
      return `₹${(amount / 10000000).toFixed(1)} Cr`;
    } else if (amount >= 100000) {
      return `₹${(amount / 100000).toFixed(0)} L`;
    }
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  return (
    <div className="space-y-6">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-white mb-2">Investment Strategy</h2>
        <p className="text-text-light">Funding requirements, valuation, and financial projections</p>
      </div>

      {/* Investment Ask Overview */}
      <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg p-6 border border-purple-400/30">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
            <DollarSign className="w-5 h-5 text-purple-400" />
          </div>
          <h3 className="text-xl font-bold text-white">Investment Ask</h3>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="text-center">
            <p className="text-purple-200 text-sm mb-2">Funding Required</p>
            <p className="text-3xl font-bold text-purple-400">{formatCurrency(investment.funding_requirement)}</p>
            <p className="text-purple-300 text-xs mt-1">Investment Amount</p>
          </div>
          <div className="text-center">
            <p className="text-purple-200 text-sm mb-2">Equity Offered</p>
            <p className="text-3xl font-bold text-purple-400">{investment.equity_offered}%</p>
            <p className="text-purple-300 text-xs mt-1">Company Stake</p>
          </div>
          <div className="text-center">
            <p className="text-purple-200 text-sm mb-2">Pre-Money Valuation</p>
            <p className="text-3xl font-bold text-purple-400">{formatCurrency(investment.valuation)}</p>
            <p className="text-purple-300 text-xs mt-1">Current Valuation</p>
          </div>
        </div>
      </div>

      {/* Use of Funds */}
      <div className="bg-glass rounded-lg p-6 border border-glass-border">
        <div className="flex items-center gap-3 mb-4">
          <PieChart className="w-5 h-5 text-blue-400" />
          <h3 className="text-xl font-bold text-white">Use of Funds</h3>
        </div>
        
        <div className="space-y-4">
          {Object.entries(investment.use_of_funds).map(([category, percentage]) => {
            const percentValue = typeof percentage === 'number' ? percentage : 0;
            return (
              <div key={category}>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-white font-medium">{category}</span>
                  <span className="text-purple-400 font-semibold">{percentValue}%</span>
                </div>
                <div className="bg-glass-dark rounded-full h-3 overflow-hidden">
                  <div 
                    className="h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full transition-all duration-500"
                    style={{ width: `${percentValue}%` }}
                  />
                </div>
                <p className="text-text-light text-xs mt-1">
                  {formatCurrency((investment.funding_requirement * percentValue) / 100)}
                </p>
              </div>
            );
          })}
        </div>
      </div>

      {/* Financial Projections */}
      <div className="bg-gradient-to-br from-green-500/10 to-blue-500/10 rounded-lg p-6 border border-green-400/30">
        <div className="flex items-center gap-3 mb-4">
          <BarChart3 className="w-5 h-5 text-green-400" />
          <h3 className="text-xl font-bold text-white">3-Year Financial Projections</h3>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <div className="bg-glass rounded-lg p-4 border border-glass-border">
            <h4 className="font-semibold text-green-300 mb-3">Year 1</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-text-light">Revenue</span>
                <span className="text-white font-semibold">{formatCurrency(investment.financial_projections.year1.revenue)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-text-light">Profit/Loss</span>
                <span className={`font-semibold ${investment.financial_projections.year1.profit < 0 ? 'text-red-400' : 'text-green-400'}`}>
                  {formatCurrency(investment.financial_projections.year1.profit)}
                </span>
              </div>
            </div>
          </div>
          
          <div className="bg-glass rounded-lg p-4 border border-glass-border">
            <h4 className="font-semibold text-green-300 mb-3">Year 2</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-text-light">Revenue</span>
                <span className="text-white font-semibold">{formatCurrency(investment.financial_projections.year2.revenue)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-text-light">Profit/Loss</span>
                <span className={`font-semibold ${investment.financial_projections.year2.profit < 0 ? 'text-red-400' : 'text-green-400'}`}>
                  {formatCurrency(investment.financial_projections.year2.profit)}
                </span>
              </div>
            </div>
          </div>
          
          <div className="bg-glass rounded-lg p-4 border border-glass-border">
            <h4 className="font-semibold text-green-300 mb-3">Year 3</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-text-light">Revenue</span>
                <span className="text-white font-semibold">{formatCurrency(investment.financial_projections.year3.revenue)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-text-light">Profit/Loss</span>
                <span className={`font-semibold ${investment.financial_projections.year3.profit < 0 ? 'text-red-400' : 'text-green-400'}`}>
                  {formatCurrency(investment.financial_projections.year3.profit)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* ROI Metrics */}
      <div className="bg-glass rounded-lg p-6 border border-glass-border">
        <div className="flex items-center gap-3 mb-4">
          <TrendingUp className="w-5 h-5 text-purple-400" />
          <h3 className="text-xl font-bold text-white">Return on Investment</h3>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-20 h-20 mx-auto bg-purple-500/20 rounded-full flex items-center justify-center mb-3">
              <Target className="w-10 h-10 text-purple-400" />
            </div>
            <p className="text-purple-200 text-sm mb-1">Payback Period</p>
            <p className="text-2xl font-bold text-purple-400">{investment.roi_metrics.payback_period}</p>
          </div>
          
          <div className="text-center">
            <div className="w-20 h-20 mx-auto bg-blue-500/20 rounded-full flex items-center justify-center mb-3">
              <BarChart3 className="w-10 h-10 text-blue-400" />
            </div>
            <p className="text-blue-200 text-sm mb-1">Internal Rate of Return</p>
            <p className="text-2xl font-bold text-blue-400">{investment.roi_metrics.irr}</p>
          </div>
          
          <div className="text-center">
            <div className="w-20 h-20 mx-auto bg-green-500/20 rounded-full flex items-center justify-center mb-3">
              <Calculator className="w-10 h-10 text-green-400" />
            </div>
            <p className="text-green-200 text-sm mb-1">Expected Multiple</p>
            <p className="text-2xl font-bold text-green-400">{investment.roi_metrics.multiple}</p>
          </div>
        </div>
      </div>

      {/* Deal Structure Options */}
      <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg p-6 border border-purple-400/30">
        <h3 className="text-xl font-bold text-white mb-4">Deal Structure Options</h3>
        
        <div className="space-y-4">
          <div className="bg-glass rounded-lg p-4 border border-glass-border">
            <h4 className="font-semibold text-purple-300 mb-2">Option 1: Standard Equity</h4>
            <p className="text-white mb-2">{investment.equity_offered}% equity for {formatCurrency(investment.funding_requirement)}</p>
            <ul className="space-y-1">
              <li className="text-text-light text-sm">• Clean equity deal with board seat</li>
              <li className="text-text-light text-sm">• Pro-rata rights for future rounds</li>
              <li className="text-text-light text-sm">• Monthly reporting and governance</li>
            </ul>
          </div>
          
          <div className="bg-glass rounded-lg p-4 border border-glass-border">
            <h4 className="font-semibold text-purple-300 mb-2">Option 2: Convertible Note</h4>
            <p className="text-white mb-2">Debt with 20% discount on next round</p>
            <ul className="space-y-1">
              <li className="text-text-light text-sm">• Deferred valuation to next round</li>
              <li className="text-text-light text-sm">• 12-month maturity period</li>
              <li className="text-text-light text-sm">• Interest rate of 8% per annum</li>
            </ul>
          </div>
          
          <div className="bg-glass rounded-lg p-4 border border-glass-border">
            <h4 className="font-semibold text-purple-300 mb-2">Option 3: Revenue Share</h4>
            <p className="text-white mb-2">5% revenue share until 2x return</p>
            <ul className="space-y-1">
              <li className="text-text-light text-sm">• No equity dilution</li>
              <li className="text-text-light text-sm">• Capped at 2x investment amount</li>
              <li className="text-text-light text-sm">• Quarterly payments from revenue</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Exit Strategy */}
      <div className="bg-glass rounded-lg p-6 border border-glass-border">
        <h3 className="text-xl font-bold text-white mb-4">Exit Strategy</h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold text-purple-200 mb-3">Potential Exit Routes</h4>
            <ul className="space-y-2">
              <li className="text-purple-100 text-sm flex items-start gap-2">
                <span className="text-purple-400">1.</span>
                <span><strong>Strategic Acquisition:</strong> Sale to larger industry player (3-5 years)</span>
              </li>
              <li className="text-purple-100 text-sm flex items-start gap-2">
                <span className="text-purple-400">2.</span>
                <span><strong>Private Equity:</strong> Growth capital round with partial exit (2-3 years)</span>
              </li>
              <li className="text-purple-100 text-sm flex items-start gap-2">
                <span className="text-purple-400">3.</span>
                <span><strong>IPO:</strong> Public listing on NSE/BSE (5-7 years)</span>
              </li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-purple-200 mb-3">Value Creation Drivers</h4>
            <ul className="space-y-2">
              <li className="text-purple-100 text-sm">• Revenue growth to ₹100 Cr+</li>
              <li className="text-purple-100 text-sm">• EBITDA margins of 20%+</li>
              <li className="text-purple-100 text-sm">• Market leadership position</li>
              <li className="text-purple-100 text-sm">• Diversified revenue streams</li>
              <li className="text-purple-100 text-sm">• Strong brand and IP portfolio</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvestmentStrategySection;