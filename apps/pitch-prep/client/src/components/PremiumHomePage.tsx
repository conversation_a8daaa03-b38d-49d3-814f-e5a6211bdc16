import React, { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { 
  Globe, 
  TrendingUp, 
  Users, 
  Target, 
  Award, 
  ChevronRight,
  Sparkles,
  BarChart3,
  Brain,
  Zap
} from 'lucide-react'
import { 
  AnimatedBackground,
  GlassContainer,
  GlassCard,
  PremiumButton,
  StatsGrid,
  ValueGrid,
  SharkGrid,
  PremiumForm,
  ValueCard
} from '@/components/ui'
import { SimpleFlipCard } from '@/components/ui/SimpleFlipCard'
import { RealitySection } from '@/components/RealitySection'
import { ValueProposition } from '@/components/ValueProposition'
import { TestimonialsSection } from '@/components/TestimonialsSection'
import { SharkInsightsCarousel } from '@/components/SharkInsightsCarousel'

export const PremiumHomePage: React.FC = () => {
  const navigate = useNavigate()
  const [isFormSubmitting, setIsFormSubmitting] = useState(false)
  const [firstCardValue, setFirstCardValue] = useState('0')
  const [secondCardValue, setSecondCardValue] = useState('0')
  const hasAnimated = useRef(false)

  // Counter animation hook
  const animateCounter = (
    target: number,
    setter: (value: string) => void,
    suffix: string = '',
    finalText?: string,
    delay: number = 0
  ) => {
    setTimeout(() => {
      let current = 0
      const increment = target / 100
      const timer = setInterval(() => {
        current += increment
        if (current >= target) {
          current = target
          clearInterval(timer)
          if (finalText) {
            // Show final text after a brief delay
            setTimeout(() => setter(finalText), 1000)
          } else {
            setter(Math.floor(current).toLocaleString() + suffix)
          }
        } else {
          setter(Math.floor(current).toLocaleString() + suffix)
        }
      }, 30)
    }, delay)
  }

  // Initialize counter animations on mount
  useEffect(() => {
    if (!hasAnimated.current) {
      hasAnimated.current = true
      // First card: 0→280+
      animateCounter(280, setFirstCardValue, '+', undefined, 500)
      // Second card: 0→3920+
      animateCounter(3920, setSecondCardValue, '+', undefined, 700)
    }
  }, [])

  const handleFormSubmit = async (formData: any) => {
    setIsFormSubmitting(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Navigate to analysis page with form data
    navigate('/analysis', { state: formData })
    
    setIsFormSubmitting(false)
  }

  const statsFlipData = [
    { 
      value: firstCardValue, 
      label: 'Shark Tank pitches analyzed', 
      icon: <BarChart3 className="w-6 h-6" />,
      backContent: (
        <div className="text-center">
          <div className="text-sm text-text-light leading-relaxed">
            All Seasons of Shark Tank India
          </div>
        </div>
      )
    },
    { 
      value: secondCardValue, 
      label: 'Indian startups tracked', 
      icon: <Users className="w-6 h-6" />,
      backContent: (
        <div className="text-center">
          <div className="text-sm text-text-light leading-relaxed">
            Contextual investment insights
          </div>
        </div>
      )
    }
  ]

  const valueFlipData = [
    {
      title: 'AI-Powered Analysis',
      description: 'Smart website analysis and pattern matching',
      icon: <Brain className="w-6 h-6 text-white" />,
      backContent: (
        <div className="text-center">
          <h4 className="text-sm font-semibold text-white mb-2">How It Works</h4>
          <div className="text-xs text-text-light leading-relaxed">
            • Website content analysis
            <br />
            • Business model extraction
            <br />
            • Similar company matching
            <br />
            • Success pattern recognition
          </div>
        </div>
      )
    },
    {
      title: 'Shark Compatibility',
      description: 'Personalized shark investment matching',
      icon: <Users className="w-6 h-6 text-white" />,
      backContent: (
        <div className="text-center">
          <h4 className="text-sm font-semibold text-white mb-2">Matching Factors</h4>
          <div className="text-xs text-text-light leading-relaxed">
            • Industry preferences
            <br />
            • Investment size patterns
            <br />
            • Success rate by sector
            <br />
            • Personality compatibility
          </div>
        </div>
      )
    },
    {
      title: 'Pitch Strategy',
      description: 'Data-driven presentation optimization',
      icon: <Target className="w-6 h-6 text-white" />,
      backContent: (
        <div className="text-center">
          <h4 className="text-sm font-semibold text-white mb-2">Strategy Areas</h4>
          <div className="text-xs text-text-light leading-relaxed">
            • Opening hook tactics
            <br />
            • Financial storytelling
            <br />
            • Question anticipation
            <br />
            • Negotiation approach
          </div>
        </div>
      )
    },
    {
      title: 'Success Metrics',
      description: 'Key performance indicators analysis',
      icon: <TrendingUp className="w-6 h-6 text-white" />,
      backContent: (
        <div className="text-center">
          <h4 className="text-sm font-semibold text-white mb-2">Critical Metrics</h4>
          <div className="text-xs text-text-light leading-relaxed">
            • Revenue growth trends
            <br />
            • Customer acquisition cost
            <br />
            • Market penetration
            <br />
            • Scalability factors
          </div>
        </div>
      )
    }
  ]

  const sharkProfiles = [
    {
      name: 'Aman Gupta',
      expertise: ['Consumer Electronics', 'D2C Brands', 'Marketing'],
      color: '#667eea',
      investmentRange: '₹10L - ₹2Cr',
      focusAreas: 'Consumer products, Tech accessories',
      personality: 'Data-driven, marketing-focused investor'
    },
    {
      name: 'Namita Thapar',
      expertise: ['Healthcare', 'Pharma', 'Consumer Goods'],
      color: '#f093fb',
      investmentRange: '₹20L - ₹5Cr',
      focusAreas: 'Healthcare, Education, Women entrepreneurs',
      personality: 'Detail-oriented, social impact focused'
    },
    {
      name: 'Anupam Mittal',
      expertise: ['Digital Platforms', 'Fintech', 'B2B Services'],
      color: '#4facfe',
      investmentRange: '₹25L - ₹10Cr',
      focusAreas: 'Digital platforms, Marketplaces, Tech',
      personality: 'Tech-savvy, platform business expert'
    }
  ]

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background */}
      <AnimatedBackground />
      
      {/* Header */}
      <header className="relative z-10 p-6">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="text-2xl font-bold text-white">
            <span className="gradient-text">Shark AI</span>
          </div>
          <nav className="hidden md:flex space-x-6 text-text-light">
            <a href="#features" className="hover:text-white transition-colors">Features</a>
            <a href="#analysis" className="hover:text-white transition-colors">Analysis</a>
            <a href="#sharks" className="hover:text-white transition-colors">Sharks</a>
            <a href="#pricing" className="hover:text-white transition-colors">Pricing</a>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-container">
          <div className="hero-content">
            <h1 className="hero-title">
              Prep for <span className="gradient-text">Shark Tank India</span>
              <br />in 30 Minutes
            </h1>
            <p className="hero-subtitle">
              AI-powered insights from 500+ real Shark Tank pitches to help you 
              craft the perfect pitch and maximize your chances of getting a deal.
            </p>
            
            {/* Stats Flip Cards */}
            <div className="hero-stats grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 justify-items-center mb-12 px-4 max-w-5xl mx-auto">
              {statsFlipData.map((stat, index) => (
                <div 
                  key={index} 
                  className="animate-fadeInUp w-full flex justify-center group"
                  style={{ animationDelay: `${0.6 + index * 0.1}s` }}
                >
                  <SimpleFlipCard
                    id={`stat-card-${index}`}
                    frontContent={
                      <div className="glass-card p-6 text-center">
                        <div className="flex items-center justify-center mb-3">
                          {stat.icon}
                        </div>
                        <div className="text-3xl font-bold text-white mb-2">
                          {stat.value}
                        </div>
                        <div className="text-sm text-text-light">
                          {stat.label}
                        </div>
                      </div>
                    }
                    backContent={
                      <div className="glass-card p-6">
                        {stat.backContent}
                      </div>
                    }
                    className="w-full max-w-48 sm:max-w-56"
                    autoFlip={index === 1}
                    flipInterval={6000}
                  />
                </div>
              ))}
            </div>
            
            {/* Click hint for mobile users */}
            <div className="text-center mb-8 sm:hidden">
              <p className="text-xs text-text-muted opacity-70">
                💡 Tap the cards above to see more details
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <PremiumButton 
                size="xl" 
                onClick={() => navigate('/start')}
              >
                Start Your Analysis
                <ChevronRight className="w-5 h-5 ml-2" />
              </PremiumButton>
              
              <PremiumButton 
                variant="glass" 
                size="xl"
                onClick={() => document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' })}
              >
                Learn More
              </PremiumButton>
            </div>

            {/* Trust indicators */}
            <div className="trust-badges">
              <div className="trust-badge">
                <Globe className="w-4 h-4" />
                <span>Public data only</span>
              </div>
              <div className="trust-badge">
                <Zap className="w-4 h-4" />
                <span>30 min results</span>
              </div>
              <div className="trust-badge">
                <Award className="w-4 h-4" />
                <span>95% accuracy</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Reality Section */}
      <RealitySection />

      {/* Value Proposition Section */}
      <ValueProposition />

      {/* Testimonials Section */}
      <TestimonialsSection />

      {/* Features Section */}
      <section id="features" className="py-20 relative z-10">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Why Founders Choose <span className="gradient-text">Shark AI</span>
            </h2>
            <p className="text-xl text-text-light max-w-3xl mx-auto">
              Our AI analyzes your business against successful Shark Tank companies 
              to give you data-driven insights and strategic recommendations.
            </p>
          </div>

          {/* Value Flip Cards Grid */}
          <div className="value-cards-grid grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            {valueFlipData.map((value, index) => (
              <SimpleFlipCard
                key={index}
                id={`value-card-${index}`}
                frontContent={
                  <div className="glass-card p-6 text-center h-48 flex flex-col justify-center">
                    <div className="mb-4 flex justify-center">
                      <div className="p-3 bg-gradient-primary rounded-full">
                        {value.icon}
                      </div>
                    </div>
                    <h3 className="text-xl font-semibold text-white mb-3">
                      {value.title}
                    </h3>
                    <p className="text-text-light leading-relaxed">
                      {value.description}
                    </p>
                  </div>
                }
                backContent={
                  <div className="glass-card p-6 h-48 flex items-center justify-center">
                    {value.backContent}
                  </div>
                }
                className=""
                autoFlip={index === 0}
                flipInterval={5000}
              />
            ))}
          </div>
          
          {/* Click hint for interaction */}
          <div className="text-center mt-8">
            <p className="text-xs text-text-muted opacity-70">
              💡 Click on the cards to discover detailed insights
            </p>
          </div>
        </div>
      </section>

      {/* Analysis Form Section */}
      <section id="analysis" className="py-20 relative z-10">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-white mb-4">
              Get Your <span className="gradient-text">AI Analysis</span>
            </h2>
            <p className="text-xl text-text-light">
              Just provide your company details and get personalized insights in minutes
            </p>
          </div>

          <div id="analysis-form">
            <PremiumForm 
              onSubmit={handleFormSubmit}
              loading={isFormSubmitting}
            />
          </div>
        </div>
      </section>

      {/* Shark Insights Carousel */}
      <SharkInsightsCarousel />

      {/* Sharks Section */}
      <section id="sharks" className="py-20 relative z-10">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Meet the <span className="gradient-text">Sharks</span>
            </h2>
            <p className="text-xl text-text-light max-w-3xl mx-auto">
              Our AI has analyzed every shark's investment patterns, preferences, 
              and decision-making criteria to help you target the right investor.
            </p>
          </div>

          <SharkGrid 
            sharks={sharkProfiles}
            className="max-w-6xl mx-auto"
          />
        </div>
      </section>

      {/* Social Proof Section */}
      <section className="py-20 relative z-10">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <GlassCard className="p-12">
            <h3 className="text-2xl font-bold text-white mb-6">
              Join 10,000+ Founders Who Trust Shark AI
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
              <div>
                <div className="text-3xl font-bold gradient-text mb-2">10,000+</div>
                <div className="text-text-light">Analyses completed</div>
              </div>
              <div>
                <div className="text-3xl font-bold gradient-text mb-2">500+</div>
                <div className="text-text-light">Pitches in database</div>
              </div>
              <div>
                <div className="text-3xl font-bold gradient-text mb-2">95%</div>
                <div className="text-text-light">Accuracy rate</div>
              </div>
            </div>

            <blockquote className="text-lg text-text-light italic mb-6">
              "Shark AI helped me understand exactly what Aman Gupta looks for in consumer products. 
              The insights were spot-on and helped me refine my pitch strategy."
            </blockquote>
            <cite className="text-text-muted">— Priya Sharma, D2C Brand Founder</cite>
          </GlassCard>
        </div>
      </section>

      {/* CTA Section */}
      <section id="pricing" className="py-20 relative z-10">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <GlassContainer className="p-12">
            <h2 className="text-4xl font-bold text-white mb-4">
              Ready to Get Your <span className="gradient-text">Shark Tank Edge</span>?
            </h2>
            <p className="text-xl text-text-light mb-8">
              Get comprehensive AI analysis for just ₹1,999. Includes shark compatibility, 
              pitch strategy, and success predictions.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <PremiumButton 
                size="xl" 
                onClick={() => navigate('/start')}
              >
                Start Analysis - ₹1,999
                <ChevronRight className="w-5 h-5 ml-2" />
              </PremiumButton>
            </div>
            
            <p className="text-sm text-text-muted mt-4">
              🔒 Secure payment • 💰 Money-back guarantee • ⚡ 30-minute delivery
            </p>
          </GlassContainer>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 relative z-10 border-t border-glass-border">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-white font-bold text-xl mb-4 md:mb-0">
              <span className="gradient-text">Shark AI</span>
            </div>
            <div className="flex gap-6 text-text-light text-sm">
              <a href="#" className="hover:text-white transition-colors">Privacy Policy</a>
              <a href="#" className="hover:text-white transition-colors">Terms of Service</a>
              <a href="#" className="hover:text-white transition-colors">Contact</a>
            </div>
          </div>
          <div className="text-center text-text-muted text-sm mt-6">
            © 2024 Shark AI. All rights reserved. Not affiliated with Shark Tank India.
          </div>
        </div>
      </footer>
    </div>
  )
}