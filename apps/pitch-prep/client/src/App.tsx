import { Routes, Route, useLocation } from 'react-router-dom'
import { useState, useEffect, Suspense, lazy } from 'react'
import { AuthProvider } from '@/contexts/AuthContext'
import { MobileNavigation } from '@/components/MobileNavigation'
import { ConnectionStatus, useOnlineStatus } from '@/components/OfflineMode'
import { AnimatedBackground } from '@/components/ui'

// Lazy load pages for better performance
const HomePage = lazy(() => import('@/pages/HomePage').then(module => ({ default: module.HomePage })))
const InputFormPage = lazy(() => import('@/pages/InputFormPage').then(module => ({ default: module.InputFormPage })))
const PaymentPage = lazy(() => import('@/pages/PaymentPage').then(module => ({ default: module.default })))
const ConfirmationPage = lazy(() => import('@/pages/ConfirmationPage').then(module => ({ default: module.default })))
const ProcessingPage = lazy(() => import('@/pages/ProcessingPage').then(module => ({ default: module.ProcessingPage })))
const ContextualProcessingPage = lazy(() => import('@/pages/ContextualProcessingPage').then(module => ({ default: module.default })))
const ReportPreviewPage = lazy(() => import('@/pages/ReportPreviewPage').then(module => ({ default: module.default })))
const FullReportPage = lazy(() => import('@/pages/FullReportPage').then(module => ({ default: module.default })))
const ResultsPage = lazy(() => import('@/pages/ResultsPage').then(module => ({ default: module.ResultsPage })))
const PreparationWorkflowPage = lazy(() => import('@/pages/PreparationWorkflowPage').then(module => ({ default: module.PreparationWorkflowPage })))
const ReportPage = lazy(() => import('@/pages/ReportPage').then(module => ({ default: module.ReportPage })))
// DashboardPage not yet implemented
// const DashboardPage = lazy(() => import('@/pages/DashboardPage').then(module => ({ default: module.DashboardPage })))

// Static pages
const PricingPage = lazy(() => import('@/pages/PricingPage').then(module => ({ default: module.PricingPage })))
const AboutPage = lazy(() => import('@/pages/AboutPage').then(module => ({ default: module.AboutPage })))
const ContactPage = lazy(() => import('@/pages/ContactPage').then(module => ({ default: module.ContactPage })))
const TermsPage = lazy(() => import('@/pages/TermsPage').then(module => ({ default: module.TermsPage })))

// Loading component
const PageLoader = () => (
  <div className="min-h-screen relative flex items-center justify-center">
    <AnimatedBackground />
    <div className="relative z-10 text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p className="text-white">Loading...</p>
    </div>
  </div>
)

function App() {
  const location = useLocation()
  const isOnline = useOnlineStatus()
  const [isMobile, setIsMobile] = useState(false)
  const [, setInstallPromptEvent] = useState<any>(null)

  // Detect mobile screen size - be more restrictive for desktop experience
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 640) // Only show mobile nav on truly mobile screens
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Handle PWA install prompt
  useEffect(() => {
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setInstallPromptEvent(e)
    }
    
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    return () => window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
  }, [])

  // Determine if mobile navigation should be shown
  const showMobileNav = isMobile && isOnline && !location.pathname.includes('/practice')

  return (
    <AuthProvider>
      <div className="min-h-screen">
        {/* Connection Status Indicator */}
        <ConnectionStatus />
        
        {/* Main App Content */}
        <main className={`min-h-screen ${showMobileNav ? 'pb-20' : ''}`}>
          <Suspense fallback={<PageLoader />}>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/start" element={<InputFormPage />} />
              <Route path="/processing" element={<ProcessingPage />} />
              <Route path="/confirmation" element={<ConfirmationPage />} />
              <Route path="/contextual-processing" element={<ContextualProcessingPage />} />
              <Route path="/report-preview/:sessionId" element={<ReportPreviewPage />} />
              <Route path="/payment" element={<PaymentPage />} />
              <Route path="/full-report/:sessionId" element={<FullReportPage />} />
              <Route path="/results" element={<ResultsPage />} />
              <Route path="/results/:analysisId" element={<ResultsPage />} />
              <Route path="/report" element={<ReportPage />} />
              <Route path="/report/:analysisId" element={<ReportPage />} />
              <Route path="/preparation/:analysisId" element={<PreparationWorkflowPage />} />
              
              {/* Static pages */}
              <Route path="/pricing" element={<PricingPage />} />
              <Route path="/about" element={<AboutPage />} />
              <Route path="/contact" element={<ContactPage />} />
              <Route path="/terms" element={<TermsPage />} />
              <Route path="/privacy" element={<TermsPage />} />
              <Route path="/terms-privacy" element={<TermsPage />} />
            
            
            {/* Mobile-specific routes */}
            <Route 
              path="/practice" 
              element={
                <div className="min-h-screen relative">
                  <AnimatedBackground />
                  <div className="text-center p-8 relative z-10">
                    <h2 className="text-xl font-semibold text-white mb-4">Practice Mode</h2>
                    <p className="text-text-light">Complete your analysis first to access practice questions.</p>
                  </div>
                </div>
              } 
            />
            <Route 
              path="/profile" 
              element={
                <div className="min-h-screen relative">
                  <AnimatedBackground />
                  <div className="text-center p-8 relative z-10">
                    <h2 className="text-xl font-semibold text-white mb-4">Profile</h2>
                    <p className="text-text-light">User profile features coming soon.</p>
                  </div>
                </div>
              } 
            />
            {/* Dashboard route disabled - page not implemented yet */}
            {/* <Route path="/dashboard" element={<DashboardPage />} /> */}
            
            {/* Offline fallback */}
            <Route 
              path="/offline" 
              element={
                <div className="min-h-screen relative flex items-center justify-center p-4">
                  <AnimatedBackground />
                  <div className="text-center relative z-10">
                    <h2 className="text-xl font-semibold text-white mb-4">You're Offline</h2>
                    <p className="text-text-light mb-4">Some features require an internet connection.</p>
                    <button 
                      onClick={() => window.location.reload()}
                      className="btn-primary"
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              } 
            />
            </Routes>
          </Suspense>
        </main>

        {/* Mobile Navigation - Only show on mobile and when online */}
        {showMobileNav && <MobileNavigation />}
        
        {/* Mobile-specific features */}
        {isMobile && (
          <>
            {/* Pull-to-refresh indicator */}
            <div 
              id="pull-refresh-indicator" 
              className="pull-refresh"
              style={{ display: 'none' }}
            >
              <div className="flex items-center justify-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-sm">Release to refresh</span>
              </div>
            </div>
          </>
        )}
      </div>
    </AuthProvider>
  )
}

export default App