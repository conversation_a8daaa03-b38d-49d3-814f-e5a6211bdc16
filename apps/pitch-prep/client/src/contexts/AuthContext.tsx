import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import axios from 'axios'

export interface User {
  id: string
  name: string
  email: string
  phone?: string
  token?: string
  createdAt: string
  purchases: Purchase[]
}

interface Purchase {
  id: string
  userId: string
  analysisId: string
  amount: number
  createdAt: string
  razorpayOrderId?: string
  razorpayPaymentId?: string
}

interface AuthContextValue {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  isPaidUser: boolean
  hasPurchased: boolean
  signIn: (email: string, password: string, remember?: boolean) => Promise<void>
  signUp: (name: string, email: string, password: string) => Promise<void>
  signOut: () => void
  hasAccess: (analysisId: string) => boolean
  refreshUser: () => Promise<void>
  recordPurchase: (analysisId: string, paymentData: any) => Promise<any>
}

const AuthContext = createContext<AuthContextValue | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

const TOKEN_KEY = 'shark_ai_token'

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Set up axios interceptor to include token in requests
  useEffect(() => {
    const token = localStorage.getItem(TOKEN_KEY)
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
    }

    // Response interceptor to handle token expiration
    const interceptor = axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          signOut()
        }
        return Promise.reject(error)
      }
    )

    return () => {
      axios.interceptors.response.eject(interceptor)
    }
  }, [])

  // Load user from token on app start
  useEffect(() => {
    const token = localStorage.getItem(TOKEN_KEY)
    if (token) {
      loadUserFromToken()
    } else {
      setIsLoading(false)
    }
  }, [])

  const loadUserFromToken = async () => {
    try {
      const response = await axios.get('/api/auth/me')
      if (response.data.success) {
        setUser(response.data.user)
      } else {
        // Invalid token
        localStorage.removeItem(TOKEN_KEY)
        delete axios.defaults.headers.common['Authorization']
      }
    } catch (error) {
      // Invalid token
      localStorage.removeItem(TOKEN_KEY)
      delete axios.defaults.headers.common['Authorization']
    } finally {
      setIsLoading(false)
    }
  }

  const signIn = async (email: string, password: string, remember: boolean = false) => {
    setIsLoading(true)
    try {
      const response = await axios.post('/api/auth/signin', {
        email,
        password,
        remember
      })

      if (response.data.success) {
        const { token, user: userData } = response.data
        
        // Store token
        localStorage.setItem(TOKEN_KEY, token)
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
        
        // Set user
        setUser(userData)
      } else {
        throw new Error(response.data.error || 'Sign in failed')
      }
    } catch (error: any) {
      const message = error.response?.data?.error || error.message || 'Sign in failed'
      throw new Error(message)
    } finally {
      setIsLoading(false)
    }
  }

  const signUp = async (name: string, email: string, password: string) => {
    setIsLoading(true)
    try {
      const response = await axios.post('/api/auth/signup', {
        name,
        email,
        password
      })

      if (response.data.success) {
        const { token, user: userData } = response.data
        
        // Store token
        localStorage.setItem(TOKEN_KEY, token)
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
        
        // Set user
        setUser(userData)
      } else {
        throw new Error(response.data.error || 'Sign up failed')
      }
    } catch (error: any) {
      const message = error.response?.data?.error || error.message || 'Sign up failed'
      throw new Error(message)
    } finally {
      setIsLoading(false)
    }
  }

  const signOut = () => {
    // Clear token
    localStorage.removeItem(TOKEN_KEY)
    delete axios.defaults.headers.common['Authorization']
    
    // Clear user
    setUser(null)
  }

  const hasAccess = (analysisId: string): boolean => {
    if (!user) return false
    return user.purchases.some(purchase => purchase.analysisId === analysisId)
  }

  const refreshUser = async () => {
    const token = localStorage.getItem(TOKEN_KEY)
    if (token) {
      await loadUserFromToken()
    }
  }

  const recordPurchase = async (analysisId: string, paymentData: any) => {
    setIsLoading(true)
    try {
      const response = await axios.post('/api/auth/record-purchase', {
        analysisId,
        ...paymentData
      })

      if (response.data.success) {
        // Refresh user data to get updated purchases
        await refreshUser()
        return response.data.purchase
      } else {
        throw new Error(response.data.error || 'Purchase recording failed')
      }
    } catch (error: any) {
      const message = error.response?.data?.error || error.message || 'Purchase recording failed'
      throw new Error(message)
    } finally {
      setIsLoading(false)
    }
  }

  const value: AuthContextValue = {
    user,
    isLoading,
    isAuthenticated: !!user,
    isPaidUser: !!user && user.purchases && user.purchases.length > 0,
    hasPurchased: !!user && user.purchases && user.purchases.length > 0,
    signIn,
    signUp,
    signOut,
    hasAccess,
    refreshUser,
    recordPurchase
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Higher-order component for protected routes
export function withAuth<T extends object>(Component: React.ComponentType<T>) {
  return function AuthenticatedComponent(props: T) {
    const { isAuthenticated, isLoading } = useAuth()

    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      )
    }

    if (!isAuthenticated) {
      // Redirect to home with auth modal
      window.location.href = '/?auth=signin'
      return null
    }

    return <Component {...props} />
  }
}

// Hook for purchase-protected content
export function usePurchaseProtection(analysisId: string) {
  const { user, hasAccess, isAuthenticated } = useAuth()
  
  return {
    hasAccess: hasAccess(analysisId),
    requiresAuth: !isAuthenticated,
    requiresPurchase: isAuthenticated && !hasAccess(analysisId),
    user
  }
}