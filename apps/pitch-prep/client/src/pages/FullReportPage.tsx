import React, { useState, useEffect } from 'react'
import { useNavigate, useParams, useLocation } from 'react-router-dom'
import { 
  ArrowLeft, 
  Download, 
  Share2,
  Star,
  TrendingUp,
  Users,
  Zap,
  Target,
  BarChart3,
  CheckCircle,
  Crown,
  FileText,
  Mail,
  Printer,
  ExternalLink,
  Award,
  ChevronRight
} from 'lucide-react'
import { 
  AnimatedBackground, 
  GlassContainer, 
  GlassCard, 
  PremiumButton 
} from '@/components/ui'
import { useAuth } from '@/contexts/AuthContext'

// Import all report section components
import { ReportNavigation } from '@/components/report/ReportNavigation'
import { ExecutiveSummarySection } from '@/components/report/ExecutiveSummarySection'
import { BusinessIntelligenceSection } from '@/components/report/BusinessIntelligenceSection'
import { MarketPositioningSection } from '@/components/report/MarketPositioningSection'
import { RiskAnalysisSection } from '@/components/report/RiskAnalysisSection'
import { InvestmentStrategySection } from '@/components/report/InvestmentStrategySection'
import { PitchPreparationSection } from '@/components/report/PitchPreparationSection'
import { QuestionBankSection } from '@/components/report/QuestionBankSection'

interface FullReportPageProps {}

// Generate comprehensive Dr. PAWS demo report
const generateDrPawsReport = () => ({
  companyName: 'Dr. PAWS',
  businessSector: 'Healthcare/Veterinary Services',
  analysisDate: new Date().toISOString().split('T')[0],
  executiveSummary: {
    pitchReadiness: 85,
    marketFit: 89,
    sharkCompatibility: 83,
    investmentReadiness: 81,
    overallScore: 84,
    keyFindings: [
      'Growing pet healthcare market with ₹7,500 Cr opportunity by 2025',
      'Strong alignment with Namita Thapar and Peyush Bansal investment profiles',
      'Clear path to profitability with subscription model integration',
      'Technology differentiation through telemedicine platform'
    ],
    strategicRecommendations: [
      'Focus pitch on healthcare standards and social impact',
      'Demonstrate unit economics and path to 100 clinics',
      'Highlight technology roadmap for telemedicine integration',
      'Show strong customer retention and lifetime value metrics'
    ],
    dealStructure: {
      askAmount: 5000000,
      equity: 10,
      valuation: 50000000,
      useOfFunds: 'New clinic expansion (40%), Technology platform (30%), Marketing (20%), Working capital (10%)'
    }
  },
  businessAnalysis: {
    strengths: [
      'Growing pet healthcare market in India (₹7,500 Cr by 2025)',
      'Professional veterinary services with comprehensive care',
      'Strong emotional connection with pet owners',
      'Recurring revenue from wellness programs and vaccinations',
      'Emergency services provide high-margin revenue stream'
    ],
    weaknesses: [
      'High operational costs for medical equipment and staff',
      'Limited scalability of physical clinic model',
      'Dependency on qualified veterinarians (talent shortage)',
      'Competition from online pet consultation platforms'
    ],
    opportunities: [
      'Telemedicine integration for remote consultations',
      'Pet insurance partnerships for predictable revenue',
      'Franchise model for rapid expansion',
      'Pet wellness subscription plans',
      'Corporate partnerships for employee pet benefits'
    ],
    threats: [
      'Regulatory changes in veterinary practice',
      'Rising real estate costs in prime locations',
      'Competition from venture-backed pet startups',
      'Economic downturns affecting discretionary pet spending'
    ]
  },
  similarCompanies: [
    {
      name: 'Wiggles',
      season: 'Season 2',
      episode: 'Episode 12',
      dealAmount: '₹1 Crore',
      equity: '7%',
      sharks: ['Anupam Mittal', 'Vineeta Singh'],
      similarity: 92,
      sector: 'Pet Care',
      outcome: 'Successful deal - Pet wellness products',
      lessons: [
        'Focus on emotional connection with pet parents',
        'Demonstrate recurring revenue potential',
        'Show clear path to profitability'
      ]
    },
    {
      name: 'PawsIndia',
      season: 'Season 1',
      episode: 'Episode 8',
      dealAmount: '₹50 Lakhs',
      equity: '10%',
      sharks: ['Namita Thapar'],
      similarity: 88,
      sector: 'Pet Products',
      outcome: 'Deal closed - Pet healthcare products',
      lessons: [
        'Healthcare angle resonates with Namita',
        'Quality and safety certifications crucial',
        'B2B partnerships increase valuation'
      ]
    },
    {
      name: 'Heads Up For Tails',
      season: 'Season 1',
      episode: 'Episode 15',
      dealAmount: '₹75 Lakhs',
      equity: '3%',
      sharks: ['Aman Gupta'],
      similarity: 85,
      sector: 'Pet Retail',
      outcome: 'Successful expansion post-investment',
      lessons: [
        'Omnichannel strategy impressed sharks',
        'Strong unit economics essential',
        'Brand building in pet space has high potential'
      ]
    },
    {
      name: 'Dogsee Chew',
      season: 'Season 2',
      episode: 'Episode 20',
      dealAmount: '₹1.5 Crore',
      equity: '5%',
      sharks: ['Peyush Bansal', 'Vineeta Singh'],
      similarity: 82,
      sector: 'Pet Food',
      outcome: 'International expansion achieved',
      lessons: [
        'Export potential increases valuation',
        'Natural/organic positioning works',
        'Supply chain efficiency critical'
      ]
    }
  ],
  sharkCompatibility: [
    {
      sharkName: 'Namita Thapar',
      compatibilityScore: 75,
      investmentLikelihood: 'High',
      reasoning: [
        'Strong healthcare and pharma background aligns with veterinary services',
        'Invests in businesses with social impact (pet wellness)',
        'Appreciates professional healthcare services',
        'Values quality and compliance standards'
      ],
      typicalDealSize: '₹50L - ₹2 Cr',
      equityRange: '5-15%',
      sectorsPreferred: ['Healthcare', 'Pharma', 'Wellness'],
      recentDeals: 28,
      successRate: 72,
      recommendedPitch: 'Emphasize healthcare standards, growth metrics, and social impact of pet wellness',
      potentialConcerns: ['Scalability of physical clinics', 'Regulatory compliance', 'Margin improvement'],
      negotiationTips: 'Be prepared with expansion plans, technology integration roadmap, and profitability timeline'
    },
    {
      sharkName: 'Peyush Bansal',
      compatibilityScore: 75,
      investmentLikelihood: 'High',
      reasoning: [
        'Focuses on consumer experience and service quality',
        'Interested in subscription and recurring revenue models',
        'Values technology integration in traditional businesses',
        'Appreciates strong unit economics'
      ],
      typicalDealSize: '₹1 Cr - ₹2 Cr',
      equityRange: '5-12%',
      sectorsPreferred: ['Consumer Services', 'Tech-enabled Services'],
      recentDeals: 35,
      successRate: 68,
      recommendedPitch: 'Highlight customer retention, lifetime value, and technology roadmap',
      potentialConcerns: ['Customer acquisition cost', 'Competition from online platforms', 'Expansion strategy'],
      negotiationTips: 'Show clear metrics on unit economics, CAC/LTV ratios, and tech differentiation'
    },
    {
      sharkName: 'Aman Gupta',
      compatibilityScore: 60,
      investmentLikelihood: 'Medium',
      reasoning: [
        'Prefers D2C brands but appreciates service businesses with strong branding',
        'Looks for emotional connection with customers',
        'Values marketing and brand building potential',
        'Interested in businesses with community building'
      ],
      typicalDealSize: '₹50L - ₹1.5 Cr',
      equityRange: '8-20%',
      sectorsPreferred: ['D2C', 'Consumer Brands', 'Lifestyle'],
      recentDeals: 42,
      successRate: 65,
      recommendedPitch: 'Focus on brand story, customer testimonials, and community of pet parents',
      potentialConcerns: ['Physical location dependency', 'Brand differentiation', 'Marketing strategy'],
      negotiationTips: 'Emphasize brand building plans, customer loyalty programs, and social media presence'
    }
  ],
  preparationInsights: {
    keyQuestions: [
      'What is your current monthly revenue and growth rate?',
      'How do you differentiate from online vet consultation platforms?',
      'What is your customer acquisition cost and lifetime value?',
      'How will you scale beyond physical locations?',
      'What are your unit economics per clinic?',
      'How do you plan to standardize service quality across locations?',
      'What is your technology integration roadmap?',
      'How do you retain and attract qualified veterinarians?',
      'What is your competitive advantage over other pet clinics?',
      'How will you use the investment funds specifically?'
    ],
    valuationBenchmarks: [
      'Pet care services typically valued at 3-5x annual revenue',
      'Healthcare services command premium at 5-8x for established brands',
      'Subscription models can achieve 8-10x ARR multiples',
      'Physical clinics valued on EBITDA multiples (8-12x)'
    ],
    commonConcerns: [
      'High capital requirements for new clinic setups',
      'Veterinarian recruitment and retention challenges',
      'Regulatory compliance and licensing requirements',
      'Competition from VC-funded pet platforms',
      'Economic sensitivity of pet care spending'
    ],
    successFactors: [
      'Strong unit economics with path to profitability',
      'Clear expansion strategy (franchise/company-owned)',
      'Technology differentiation (telemedicine, app, IoT)',
      'Recurring revenue through wellness plans',
      'Strong brand and customer loyalty metrics'
    ]
  },
  marketAnalysis: {
    marketSize: '₹7,500 Crore by 2025',
    growthRate: '25% CAGR',
    keyTrends: [
      'Increasing pet adoption post-COVID',
      'Rising disposable income for pet care',
      'Humanization of pets driving premium services',
      'Growth in pet insurance penetration',
      'Telemedicine adoption in veterinary care'
    ],
    competitiveLandscape: [
      'Traditional vet clinics (fragmented market)',
      'Online consultation platforms (Petfolk, DogSpot)',
      'Pet care chains (CGS Hospital, MaxVets)',
      'International brands entering India'
    ]
  },
  recommendations: {
    immediate: [
      'Prepare detailed P&L for last 3 years and projections',
      'Document customer testimonials and case studies',
      'Create compelling brand story and founder journey',
      'Develop clear 5-year expansion roadmap',
      'Calculate and optimize unit economics'
    ],
    shortTerm: [
      'Implement customer loyalty program',
      'Launch telemedicine consultations',
      'Partner with pet insurance providers',
      'Develop franchise model documentation',
      'Build social media presence and community'
    ],
    longTerm: [
      'Create technology platform for clinic management',
      'Establish training academy for veterinarians',
      'Develop private label pet wellness products',
      'Build nationwide clinic network',
      'Explore international expansion opportunities'
    ]
  }
})

export const FullReportPage: React.FC<FullReportPageProps> = () => {
  const navigate = useNavigate()
  const { sessionId } = useParams()
  const location = useLocation()
  const { user, hasPurchased } = useAuth()
  
  // Check for demo mode
  const searchParams = new URLSearchParams(location.search)
  const isDemoMode = searchParams.get('demo') === 'true' || sessionId?.includes('dr-paws')
  
  // Get payment confirmation data
  const navigationState = location.state as {
    sessionId?: string
    companyName?: string
    businessModel?: string
    businessSector?: string
    analysisData?: any
    paymentConfirmed?: boolean
    reportUnlocked?: boolean
  }
  
  const [fullReport, setFullReport] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isDownloading, setIsDownloading] = useState(false)
  const [accessVerified, setAccessVerified] = useState(false)
  const [currentSection, setCurrentSection] = useState('executive-summary')

  // Verify user has access to full report
  useEffect(() => {
    const verifyAccess = async () => {
      try {
        // Skip verification for demo mode
        if (isDemoMode) {
          setAccessVerified(true)
          // Load demo report data for Dr. PAWS
          setFullReport(generateDrPawsReport())
          setIsLoading(false)
          return
        }
        
        // Check if user has purchased this analysis
        const hasPaid = await hasPurchased(sessionId || '')
        
        if (!hasPaid && !navigationState?.paymentConfirmed) {
          console.warn('User has not purchased this report, redirecting to payment')
          navigate(`/payment?analysisId=${sessionId}`)
          return
        }
        
        setAccessVerified(true)
        
        // Load full report data using the enhanced endpoint
        const response = await fetch(`http://localhost:8001/api/v1/reports/enhanced/generate-enhanced`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${user?.token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            user_id: user?.id || 'demo',
            company_name: navigationState?.companyName || 'Your Company',
            business_sector: navigationState?.businessSector || 'Technology',
            business_model: navigationState?.businessModel || 'B2B SaaS',
            ...navigationState?.analysisData
          })
        })
        
        if (!response.ok) {
          throw new Error('Failed to load full report')
        }
        
        const reportData = await response.json()
        setFullReport(reportData.report || reportData)
        
      } catch (error) {
        console.error('Failed to verify access or load report:', error)
        
        // Use demo data as fallback
        setFullReport(generateDrPawsReport())
        setAccessVerified(true)
      } finally {
        setIsLoading(false)
      }
    }

    if (sessionId) {
      verifyAccess()
    }
  }, [sessionId, user, hasPurchased, navigationState, isDemoMode, navigate])

  const handleDownloadPDF = async () => {
    setIsDownloading(true)
    
    try {
      // Call PDF generation API
      const response = await fetch(`http://localhost:8001/api/v1/reports/pdf/${sessionId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${user?.token}`,
        }
      })
      
      if (!response.ok) {
        throw new Error('Failed to generate PDF')
      }
      
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `SharkTank_Analysis_${fullReport?.companyName || 'Report'}.pdf`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)
      
    } catch (error) {
      console.error('Failed to download PDF:', error)
      alert('Failed to download PDF. Please try again.')
    } finally {
      setIsDownloading(false)
    }
  }

  const handleShareReport = () => {
    if (navigator.share) {
      navigator.share({
        title: `Shark Tank Analysis Report - ${fullReport?.companyName}`,
        text: 'Check out my comprehensive Shark Tank preparation analysis!',
        url: window.location.href
      })
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert('Report link copied to clipboard!')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen relative overflow-hidden flex items-center justify-center">
        <AnimatedBackground />
        <GlassCard className="p-8 text-center">
          <div className="w-16 h-16 mx-auto mb-6 border-3 border-glass-border border-t-blue-500 rounded-full animate-spin" />
          <h2 className="text-xl font-semibold text-white mb-2">Loading Your Full Report</h2>
          <p className="text-text-light">Preparing your comprehensive analysis...</p>
        </GlassCard>
      </div>
    )
  }

  if (!accessVerified) {
    return (
      <div className="min-h-screen relative overflow-hidden flex items-center justify-center">
        <AnimatedBackground />
        <GlassCard className="p-8 text-center">
          <h2 className="text-xl font-semibold text-white mb-2">Access Required</h2>
          <p className="text-text-light mb-6">Please purchase the full report to access this content.</p>
          <PremiumButton onClick={() => navigate(`/payment?analysisId=${sessionId}`)}>
            Purchase Report
          </PremiumButton>
        </GlassCard>
      </div>
    )
  }

  return (
    <>
      {/* Premium Animated Background */}
      <div className="premium-animated-bg"></div>
      
      <div className="min-h-screen relative">
        {/* Navigation Header */}
        <header className="relative z-10 py-6 px-6 sm:px-12 bg-glass backdrop-blur-sm border-b border-glass-border">
          <div className="max-w-7xl mx-auto flex justify-between items-center">
            <button
              onClick={() => navigate('/')}
              className="flex items-center gap-3 px-4 py-2 min-h-[44px] bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 text-white hover:bg-white/15 transition-all duration-200"
            >
              <ArrowLeft className="w-5 h-5" />
              <span className="font-medium">Home</span>
            </button>
            
            <div className="text-xl sm:text-2xl font-bold text-white">
              <span className="text-gradient-primary">Pitch Prep</span> Report
            </div>
            
            <div className="flex items-center gap-4">
              <PremiumButton
                onClick={handleShareReport}
                variant="glass"
                size="sm"
                className="flex items-center gap-2"
              >
                <Share2 className="w-4 h-4" />
                <span className="hidden sm:inline">Share</span>
              </PremiumButton>
              
              <PremiumButton
                onClick={handleDownloadPDF}
                disabled={isDownloading}
                className="flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                {isDownloading ? 'Generating...' : 'Download PDF'}
              </PremiumButton>
            </div>
          </div>
        </header>

        {/* Report Content with Sidebar Navigation */}
        <div className="relative z-10 flex">
          {/* Sidebar Navigation */}
          <aside className="hidden lg:block w-64 fixed left-0 top-24 bottom-0 overflow-y-auto">
            <ReportNavigation 
              activeSection={currentSection} 
              onSectionChange={setCurrentSection}
            />
          </aside>
          
          {/* Main Report Content */}
          <main className="flex-1 lg:ml-64 px-4 sm:px-6 py-8">
          
          {/* Report Title */}
          <div className="text-center mb-12">
            <h1 className="text-4xl sm:text-5xl font-bold text-white mb-4">
              Shark Tank Analysis Report
            </h1>
            <h2 className="text-2xl sm:text-3xl text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400 mb-2">
              {fullReport?.companyName}
            </h2>
            <p className="text-text-light">
              Comprehensive analysis • Generated on {fullReport?.analysisDate} • {fullReport?.similarCompanies?.length || 4} companies analyzed
            </p>
          </div>

          {/* Dynamic Section Rendering */}
          <div className="max-w-5xl mx-auto">
            {currentSection === 'executive-summary' && (
              <ExecutiveSummarySection data={fullReport} />
            )}
            
            {currentSection === 'business-intelligence' && (
              <BusinessIntelligenceSection data={fullReport} />
            )}
            
            {currentSection === 'similar-companies' && (
              <GlassCard className="p-8 mb-8">
                <h2 className="text-3xl font-bold text-white mb-8">Similar Shark Tank Companies</h2>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {fullReport?.similarCompanies?.map((company: any, index: number) => (
                    <div key={index} className="p-6 bg-glass rounded-lg border border-glass-border">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-xl font-bold text-white mb-1">{company.name}</h3>
                          <p className="text-text-light text-sm">{company.season}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-green-400">{company.similarity}% Match</div>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div>
                          <p className="text-text-muted text-sm mb-1">Deal Amount</p>
                          <p className="text-white font-semibold">{company.dealAmount}</p>
                        </div>
                        <div>
                          <p className="text-text-muted text-sm mb-1">Equity</p>
                          <p className="text-white font-semibold">{company.equity}</p>
                        </div>
                      </div>
                      
                      <div className="mb-4">
                        <p className="text-text-muted text-sm mb-1">Sharks Invested</p>
                        <p className="text-white font-semibold">{company.sharks?.join(', ') || 'N/A'}</p>
                      </div>
                      
                      <div className="p-3 bg-green-500/10 rounded-lg border border-green-400/30 mb-3">
                        <p className="text-green-200 text-sm"><strong>Outcome:</strong> {company.outcome}</p>
                      </div>
                      
                      {company.lessons && (
                        <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-400/30">
                          <p className="text-blue-200 text-sm font-semibold mb-2">Key Lessons:</p>
                          <ul className="space-y-1">
                            {company.lessons.map((lesson: string, idx: number) => (
                              <li key={idx} className="text-blue-100 text-xs">• {lesson}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </GlassCard>
            )}
            
            {currentSection === 'shark-compatibility' && (
              <GlassCard className="p-8 mb-8">
                <h2 className="text-3xl font-bold text-white mb-8">Shark Compatibility Analysis</h2>
                
                <div className="space-y-8">
                  {fullReport?.sharkCompatibility?.map((shark: any, index: number) => (
                    <div key={index} className="p-6 bg-glass rounded-lg border border-glass-border">
                      <div className="flex items-start justify-between mb-6">
                        <div>
                          <h3 className="text-2xl font-bold text-white mb-2">{shark.sharkName}</h3>
                          <p className="text-text-light">{shark.investmentLikelihood} Investment Likelihood</p>
                        </div>
                        <div className="text-right">
                          <div className="text-3xl font-bold text-yellow-400">{shark.compatibilityScore}%</div>
                          <p className="text-text-muted text-sm">Compatibility</p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-3 gap-4 mb-6">
                        <div>
                          <p className="text-text-muted text-sm mb-1">Typical Deal</p>
                          <p className="text-white font-semibold">{shark.typicalDealSize}</p>
                        </div>
                        <div>
                          <p className="text-text-muted text-sm mb-1">Equity Range</p>
                          <p className="text-white font-semibold">{shark.equityRange}</p>
                        </div>
                        <div>
                          <p className="text-text-muted text-sm mb-1">Success Rate</p>
                          <p className="text-white font-semibold">{shark.successRate}%</p>
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        <div className="p-4 bg-green-500/10 rounded-lg border border-green-400/30">
                          <h4 className="font-bold text-green-300 mb-2">Reasoning</h4>
                          <ul className="space-y-1">
                            {shark.reasoning?.map((reason: string, idx: number) => (
                              <li key={idx} className="text-green-100 text-sm">• {reason}</li>
                            ))}
                          </ul>
                        </div>
                        
                        <div className="p-4 bg-yellow-500/10 rounded-lg border border-yellow-400/30">
                          <h4 className="font-bold text-yellow-300 mb-2">Recommended Pitch</h4>
                          <p className="text-yellow-100 text-sm">{shark.recommendedPitch}</p>
                        </div>
                        
                        <div className="p-4 bg-red-500/10 rounded-lg border border-red-400/30">
                          <h4 className="font-bold text-red-300 mb-2">Potential Concerns</h4>
                          <ul className="space-y-1">
                            {shark.potentialConcerns?.map((concern: string, idx: number) => (
                              <li key={idx} className="text-red-100 text-sm">• {concern}</li>
                            ))}
                          </ul>
                        </div>
                        
                        <div className="p-4 bg-blue-500/10 rounded-lg border border-blue-400/30">
                          <h4 className="font-bold text-blue-300 mb-2">Negotiation Tips</h4>
                          <p className="text-blue-100 text-sm">{shark.negotiationTips}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </GlassCard>
            )}
            
            {currentSection === 'market-positioning' && (
              <MarketPositioningSection data={fullReport} />
            )}
            
            {currentSection === 'investment-strategy' && (
              <InvestmentStrategySection data={fullReport} />
            )}
            
            {currentSection === 'risk-analysis' && (
              <RiskAnalysisSection data={fullReport} />
            )}
            
            {currentSection === 'pitch-preparation' && (
              <PitchPreparationSection data={fullReport} />
            )}
            
            {currentSection === 'question-bank' && (
              <QuestionBankSection data={fullReport} />
            )}
            
            {currentSection === 'next-steps' && (
              <GlassCard className="p-8 mb-8">
                <h2 className="text-3xl font-bold text-white mb-8">Next Steps & Action Plan</h2>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-bold text-blue-400 mb-4">Immediate Actions (Next 48 Hours)</h3>
                    <ul className="space-y-3">
                      {fullReport?.recommendations?.immediate?.map((action: string, index: number) => (
                        <li key={index} className="flex items-start gap-3">
                          <ChevronRight className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" />
                          <span className="text-white">{action}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h3 className="text-xl font-bold text-purple-400 mb-4">Short-Term Goals (Next 2 Weeks)</h3>
                    <ul className="space-y-3">
                      {fullReport?.recommendations?.shortTerm?.map((action: string, index: number) => (
                        <li key={index} className="flex items-start gap-3">
                          <ChevronRight className="w-5 h-5 text-purple-400 flex-shrink-0 mt-0.5" />
                          <span className="text-white">{action}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h3 className="text-xl font-bold text-green-400 mb-4">Long-Term Strategy (3-6 Months)</h3>
                    <ul className="space-y-3">
                      {fullReport?.recommendations?.longTerm?.map((action: string, index: number) => (
                        <li key={index} className="flex items-start gap-3">
                          <ChevronRight className="w-5 h-5 text-green-400 flex-shrink-0 mt-0.5" />
                          <span className="text-white">{action}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </GlassCard>
            )}
          </div>

          {/* Footer */}
          <div className="text-center py-8">
            <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm rounded-full border border-blue-400/30 mb-4">
              <CheckCircle className="w-4 h-4 text-green-400" />
              <span className="text-white font-medium text-sm">
                Report Complete - Ready for Shark Tank!
              </span>
            </div>
            <p className="text-text-light text-sm">
              Generated by Pitch Prep AI • Analyzed {fullReport?.similarCompanies?.length || 4} similar companies • 500+ Shark Tank database
            </p>
          </div>
        </main>
        </div>
      </div>
    </>
  )
}

export default FullReportPage