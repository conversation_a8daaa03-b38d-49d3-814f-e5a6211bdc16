import React, { useState, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { ProcessingScreen } from '@/components/analysis/ProcessingScreen'

interface ProcessingPageProps {}

export const ProcessingPage: React.FC<ProcessingPageProps> = () => {
  const location = useLocation()
  const navigate = useNavigate()
  
  // Get form data from InputFormPage
  const navigationState = location.state as {
    sessionId?: string
    email?: string
    website?: string
    linkedin?: string
  }
  
  const [progress, setProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState(0)
  const [statusMessage, setStatusMessage] = useState('Starting website analysis...')
  const [isProcessing, setIsProcessing] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Website analysis specific steps (Step 3)
  const websiteAnalysisSteps = [
    { message: 'Connecting to your website for content analysis...', progress: 10 },
    { message: 'Extracting business model and value proposition...', progress: 25 },
    { message: 'Analyzing product/service offerings and target market...', progress: 40 },
    { message: 'Identifying business sector and competitive landscape...', progress: 60 },
    { message: 'Generating business intelligence summary...', progress: 80 },
    { message: 'Preparing business details for your confirmation...', progress: 95 },
    { message: 'Website analysis complete! Please confirm your business details.', progress: 100 }
  ]

  // Real-time SSE connection for website analysis updates
  useEffect(() => {
    if (!navigationState?.sessionId) {
      // No session data, redirect back to start
      console.warn('No session data found, redirecting to input form')
      navigate('/start')
      return
    }

    if (!navigationState?.website || !navigationState?.email) {
      console.warn('Missing website or email data, redirecting to input form')
      navigate('/start')
      return
    }

    const sessionId = navigationState.sessionId;
    console.log(`[ProcessingPage] Starting website analysis for session: ${sessionId}`);
    console.log('Website to analyze:', navigationState.website);

    let cleanupSSE: (() => void) | null = null;

    // Import website analysis service
    import('../services/analysisService').then(({ AnalysisService }) => {
      // Start website analysis in background
      const websiteAnalysisRequest = {
        sessionId: sessionId,
        email: navigationState.email || '',
        website: navigationState.website || '',
        linkedin: navigationState.linkedin,
        analysisPhase: 'website_analysis'
      };

      // Start background website analysis
      AnalysisService.runWebsiteAnalysis(websiteAnalysisRequest)
        .then(result => {
          console.log('[ProcessingPage] Website analysis completed:', result);
          // Store website analysis results for confirmation page
          sessionStorage.setItem(`website_analysis_${sessionId}`, JSON.stringify(result.analysis));
        })
        .catch(error => {
          console.error('[ProcessingPage] Website analysis failed:', error);
          setError('Website analysis failed: ' + error.message);
        });

      // Create SSE stream for website analysis progress updates
      cleanupSSE = AnalysisService.createProgressStream(
        sessionId,
        // Progress handler for website analysis
        (progressEvent) => {
          console.log('[ProcessingPage] Website analysis progress:', progressEvent);
          if (progressEvent.progress !== undefined) {
            setProgress(progressEvent.progress);
          }
          if (progressEvent.message) {
            setStatusMessage(progressEvent.message);
          }
          if (progressEvent.stepNumber && progressEvent.totalSteps) {
            setCurrentStep(progressEvent.stepNumber - 1);
          }
        },
        // Completion handler - navigate to confirmation page (Step 4)
        (completionEvent) => {
          console.log('[ProcessingPage] Website analysis completed:', completionEvent);
          setProgress(100);
          setIsProcessing(false);
          setStatusMessage('Website analysis complete! Please confirm your business details.');
          
          // Navigate to confirmation page (Step 4) with website analysis results
          setTimeout(() => {
            navigate('/confirmation', {
              state: {
                sessionId: sessionId,
                email: navigationState.email,
                website: navigationState.website,
                linkedin: navigationState.linkedin,
                websiteAnalysisComplete: true,
                analysisData: sessionStorage.getItem(`website_analysis_${sessionId}`)
              }
            });
          }, 2000);
        },
        // Error handler
        (error) => {
          console.error('[ProcessingPage] SSE stream error:', error);
          setError('Website analysis stream failed. Falling back to simulated progress...');
          
          // Fallback to simulated website analysis progress
          startSimulatedWebsiteAnalysisProgress();
        }
      );

    }).catch(error => {
      console.error('[ProcessingPage] Failed to load analysis service:', error);
      setError('Failed to initialize website analysis: ' + error.message);
      
      // Fallback to simulated website analysis progress
      startSimulatedWebsiteAnalysisProgress();
    });

    // Cleanup function
    return () => {
      if (cleanupSSE) {
        cleanupSSE();
      }
    };
  }, []);

  // Fallback simulated website analysis progress when SSE is not available
  const startSimulatedWebsiteAnalysisProgress = () => {
    console.log('Using simulated website analysis progress fallback');
    
    let stepIndex = 0;
    const sessionId = navigationState?.sessionId;

    const runStep = () => {
      if (stepIndex >= websiteAnalysisSteps.length) {
        setIsProcessing(false);
        
        // Navigate to confirmation page (Step 4)
        setTimeout(() => {
          navigate('/confirmation', {
            state: {
              sessionId: sessionId,
              email: navigationState.email,
              website: navigationState.website,
              linkedin: navigationState.linkedin,
              websiteAnalysisComplete: true,
              simulatedWebsiteAnalysis: true
            }
          });
        }, 2000);
        
        return;
      }

      const step = websiteAnalysisSteps[stepIndex];
      setProgress(step.progress);
      setStatusMessage(step.message);
      setCurrentStep(stepIndex);

      stepIndex++;
      
      // Realistic timing between website analysis steps (2-4 seconds)
      const nextDelay = 2000 + Math.random() * 2000;
      setTimeout(runStep, nextDelay);
    };

    // Start website analysis simulation after a brief delay
    setTimeout(runStep, 1000);
  };

  // Component rendering
  return (
    <div className="min-h-screen relative">
      <ProcessingScreen
        progress={progress}
        statusMessage={statusMessage}
        websiteUrl={navigationState?.website}
        onCancel={() => {
          setError(null);
          setProgress(0);
          setCurrentStep(0);
          setIsProcessing(false);
          window.location.href = '/';
        }}
      />
    </div>
  )
}