import { use<PERSON>ara<PERSON>, useLocation, useNavigate } from 'react-router-dom'
import { useState, useEffect } from 'react'
import { 
  CreditCard, 
  Lock, 
  CheckCircle, 
  ArrowRight, 
  AlertCircle,
  ChevronLeft,
  Download,
  FileText,
  TrendingUp,
  Users,
  Target,
  Award,
  Share2,
  Eye
} from 'lucide-react'
import { ReportGenerator } from '@/components/ui/ReportGenerator'
import { ReportPreview } from '@/components/ui/ReportPreview'
import { ReportViewer } from '@/components/ui/ReportViewer'
import { 
  AnimatedBackground, 
  PremiumButton, 
  GlassCard, 
  GlassContainer 
} from '@/components/ui'
import { AuthModal } from '@/components/auth'
import { paymentService } from '@/services/paymentService'
import { reportService } from '@/services/reportService'
import type { SessionData, CompanyInfo, SimilarCompany, SharkInsights } from '@/services/simpleAnalysisService'

interface ResultsState {
  companyName: string
  website: string
  industry: string
  stage: string
  email: string
  analysisData?: any
}

interface ResultsData {
  companyInfo: CompanyInfo
  similarCompanies: SimilarCompany[]
  sharkInsights: SharkInsights
  overallScore: number
  bestSharkMatch: {
    name: string
    score: number
    alignment: number
    reasons: string[]
  }
  processingTimeMs: number
}

export function ResultsPage() {
  const { analysisId } = useParams()
  const location = useLocation()
  const navigate = useNavigate()
  const state = location.state as ResultsState & { analysisId?: string }
  
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin')
  const [showPaymentGate, setShowPaymentGate] = useState(true)
  const [paymentLoading, setPaymentLoading] = useState(false)
  const [paymentError, setPaymentError] = useState<string | null>(null)
  const [paymentSuccess, setPaymentSuccess] = useState(false)
  const [generatedReport, setGeneratedReport] = useState<any>(null)
  const [reportLoading, setReportLoading] = useState(false)
  const [resultsData, setResultsData] = useState<ResultsData | null>(null)
  const [isAnalysisComplete, setIsAnalysisComplete] = useState(false)
  const [animationStage, setAnimationStage] = useState(0)
  const [loadingMessage, setLoadingMessage] = useState('Analyzing your business...')

  // Load analysis results from API
  useEffect(() => {
    const fetchResults = async () => {
      // Get analysis ID from state or params
      const currentAnalysisId = state?.analysisId || analysisId
      
      if (!currentAnalysisId) {
        console.error('No analysis ID provided')
        setLoadingMessage('No analysis ID found. Please start over.')
        return
      }
      
      console.log('Fetching results for analysis:', currentAnalysisId)
      setLoadingMessage('Loading your analysis results...')
      
      try {
        // Poll for results
        let attempts = 0
        const maxAttempts = 30 // 30 seconds max
        
        while (attempts < maxAttempts) {
          const response = await fetch(`http://localhost:8001/api/v1/analysis/result/${currentAnalysisId}`)
          
          if (response.ok) {
            const data = await response.json()
            
            if (data.success) {
              // Transform real API data to ResultsData format
              const results: ResultsData = {
                companyInfo: {
                  name: data.user_company?.company_name || state?.companyName || 'Your Company',
                  website: data.user_company?.website_url || state?.website || '',
                  businessSector: data.user_company?.business_sector || state?.industry || 'Technology',
                  businessModel: data.user_company?.business_model || 'B2B',
                  keyStrengths: data.competitive_analysis?.key_advantages || ['Innovation', 'Team', 'Market Timing'],
                  challenges: data.readiness_assessment?.improvement_areas || ['Funding', 'Competition', 'Scale']
                },
                similarCompanies: data.similar_companies || [],
                sharkInsights: data.shark_compatibility || {
                  overallReadiness: 0.7,
                  bestMatches: [],
                  keyPitchPoints: [],
                  preparationNeeded: []
                },
                overallScore: data.readiness_assessment?.overall_score || 0.75,
                bestSharkMatch: data.shark_compatibility?.best_matches?.[0] || {
                  name: 'Anupam Mittal',
                  score: 0.8,
                  alignment: 0.85,
                  reasons: ['Tech focus', 'B2B expertise']
                },
                processingTimeMs: data.processing_time || 5000
              }
              
              setResultsData(results)
              setIsAnalysisComplete(true)
              setLoadingMessage('')
              break
            }
          } else if (response.status === 202) {
            // Still processing
            setLoadingMessage(`Processing analysis... (${attempts + 1}/${maxAttempts})`)
          } else {
            throw new Error('Failed to fetch results')
          }
          
          // Wait 1 second before retry
          await new Promise(resolve => setTimeout(resolve, 1000))
          attempts++
        }
        
        if (!isAnalysisComplete && attempts >= maxAttempts) {
          setLoadingMessage('Analysis is taking longer than expected. Please refresh.')
        }
      } catch (error) {
        console.error('Failed to fetch analysis results:', error)
        setLoadingMessage('Failed to load results. Using sample data.')
        
        // Fallback to mock data
        const mockResultsData: ResultsData = {
        companyInfo: {
          name: state.companyName,
          website: state.website,
          businessSector: state.industry || 'Technology',
          businessModel: 'B2B SaaS',
          keyStrengths: ['Strong tech team', 'Early traction', 'Clear market need'],
          challenges: ['Market competition', 'Scaling challenges', 'Funding requirements']
        },
        similarCompanies: [
          {
            companyName: 'Zoho',
            season: 1,
            businessSector: 'Technology',
            businessModel: 'B2B SaaS',
            similarityScore: 0.85,
            matchingReasons: ['B2B focus', 'SaaS model', 'Enterprise clients'],
            keyDifferences: ['Scale', 'Product maturity'],
            dealStatus: 'Deal',
            sharksThatInvested: ['Namita Thapar', 'Peyush Bansal']
          },
          {
            companyName: 'Lenskart',
            season: 2,
            businessSector: 'E-commerce',
            businessModel: 'D2C',
            similarityScore: 0.65,
            matchingReasons: ['Tech-enabled', 'Direct sales'],
            keyDifferences: ['Industry', 'Business model'],
            dealStatus: 'Deal',
            sharksThatInvested: ['Peyush Bansal']
          }
        ],
        sharkInsights: {
          mostLikelyInvestors: ['Aman Gupta', 'Namita Thapar'],
          preparationTips: [
            'Focus on unit economics',
            'Show clear path to profitability',
            'Emphasize technology differentiation'
          ],
          potentialConcerns: [
            'Customer acquisition cost',
            'Market competition',
            'Burn rate'
          ],
          recommendedAsk: '₹1 Crore for 2% equity'
        },
        overallScore: 85,
        bestSharkMatch: {
          name: 'Aman Gupta',
          score: 92,
          alignment: 88,
          reasons: [
            'Sector alignment',
            'Proven track record in your space',
            'Similar investment preferences'
          ]
        },
        processingTimeMs: 30000
      }
      
        setResultsData(mockResultsData)
        setIsAnalysisComplete(true)
      }
      
      // Start animations after data loads
      if (isAnalysisComplete) {
        setTimeout(() => setAnimationStage(1), 300)
        setTimeout(() => setAnimationStage(2), 800)
        setTimeout(() => setAnimationStage(3), 1300)
      }
    }
    
    fetchResults()
  }, [state, analysisId])

  // Check payment status - show payment gate after results are shown
  useEffect(() => {
    // In production, check if user has already paid for this analysis
    const hasPaid = localStorage.getItem(`paid_${analysisId}`) === 'true'
    setShowPaymentGate(!hasPaid && isAnalysisComplete)
  }, [analysisId, isAnalysisComplete])

  const handlePayment = async () => {
    if (!analysisId || !state?.email) {
      setPaymentError('Missing required information for payment')
      return
    }

    setPaymentLoading(true)
    setPaymentError(null)
    
    try {
      // Generate a user ID based on email for this session
      const userId = `user_${state.email.replace('@', '_').replace('.', '_')}`
      
      const result = await paymentService.processPayment(
        userId,
        analysisId,
        state.email,
        '+919999999999' // Placeholder phone number for demo
      )
      
      if (result.success) {
        // Mark as paid and show success
        localStorage.setItem(`paid_${analysisId}`, 'true')
        setPaymentSuccess(true)
        
        // Generate comprehensive report
        setReportLoading(true)
        const reportResult = await reportService.generateReport(analysisId, state)
        
        if (reportResult.success && reportResult.data) {
          setGeneratedReport(reportResult.data)  // Use the full report data directly
        }
        
        setReportLoading(false)
        
        // Show full report after a brief success message
        setTimeout(() => {
          setShowPaymentGate(false)
        }, 2000)
      } else {
        setPaymentError(result.error || 'Payment failed. Please try again.')
      }
    } catch (error) {
      console.error('Payment error:', error)
      setPaymentError('Payment processing failed. Please try again.')
    } finally {
      setPaymentLoading(false)
    }
  }

  // Show loading state while fetching results
  if (!isAnalysisComplete && loadingMessage) {
    return (
      <>
        {/* Premium Animated Background */}
        <div className="premium-animated-bg"></div>
        
        {/* Floating Elements */}
        <div className="floating-elements">
          <div className="floating-element"></div>
          <div className="floating-element"></div>
          <div className="floating-element"></div>
        </div>
        
        <div className="min-h-screen relative">
          <header className="relative z-10 py-6 px-6 sm:px-12">
            <div className="max-w-none mx-auto flex justify-between items-center">
              <div className="text-xl sm:text-2xl font-bold text-white">
                <span className="text-gradient-primary">Pitch Prep</span>
              </div>
            </div>
          </header>
          
          <div className="flex items-center justify-center min-h-[calc(100vh-120px)] px-6 sm:px-12">
            <div className="glass-card p-8 lg:p-10 text-center max-w-md mx-auto">
              <div className="mb-6">
                <div className="w-20 h-20 mx-auto mb-4 bg-blue-500/10 rounded-full flex items-center justify-center animate-pulse">
                  <div className="w-12 h-12 border-4 border-blue-400 border-t-transparent rounded-full animate-spin" />
                </div>
              </div>
              <h2 className="text-2xl font-bold text-white mb-4" style={{fontWeight: 800}}>Analyzing Your Business</h2>
              <p className="text-base text-white/80 leading-relaxed">
                {loadingMessage}
              </p>
            </div>
          </div>
        </div>
      </>
    )
  }

  if (!state) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        <AnimatedBackground />
        
        {/* Navigation Header */}
        <header className="relative z-10 p-4 sm:p-6">
          <div className="max-w-7xl mx-auto flex justify-between items-center">
            <PremiumButton 
              variant="glass" 
              size="sm"
              onClick={() => navigate('/')}
              className="flex items-center gap-2 touch-manipulation"
            >
              <ChevronLeft className="w-4 h-4" />
              <span className="hidden sm:inline">Back to Home</span>
              <span className="sm:hidden">Home</span>
            </PremiumButton>
            
            <div className="text-lg sm:text-xl font-bold text-white">
              <span className="gradient-text">Shark AI</span>
            </div>
            
            <div className="flex items-center gap-2 text-xs sm:text-sm text-text-muted">
              <AlertCircle className="w-3 h-3 sm:w-4 sm:h-4 text-red-400" />
              <span className="hidden sm:inline">Not Found</span>
            </div>
          </div>
        </header>
        
        <div className="flex items-center justify-center min-h-[calc(100vh-120px)] px-4 sm:px-6">
          <GlassContainer className="p-6 sm:p-8 lg:p-12 text-center max-w-md mx-auto">
            <div className="mb-4 sm:mb-6">
              <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 bg-red-500/10 rounded-full flex items-center justify-center">
                <AlertCircle className="w-8 h-8 sm:w-10 sm:h-10 text-red-400" />
              </div>
            </div>
            <h2 className="text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4">Analysis Not Found</h2>
            <p className="text-sm sm:text-base text-text-light mb-6 sm:mb-8 leading-relaxed">
              Unable to load analysis results. The session may have expired or the link is invalid.
            </p>
            <div className="space-y-3">
              <PremiumButton 
                size="lg"
                onClick={() => navigate('/')}
                className="w-full touch-manipulation"
              >
                Start New Analysis
                <ArrowRight className="w-4 h-4 ml-2" />
              </PremiumButton>
              <PremiumButton 
                variant="glass" 
                size="sm"
                onClick={() => navigate('/start')}
                className="w-full touch-manipulation"
              >
                Go to Analysis Form
              </PremiumButton>
            </div>
          </GlassContainer>
        </div>
      </div>
    )
  }

  // Show analysis results first if completed but not paid
  if (isAnalysisComplete && resultsData && !localStorage.getItem(`paid_${analysisId}`)) {
    return (
      <>
        {/* Premium Animated Background */}
        <div className="premium-animated-bg"></div>
        
        {/* Floating Elements */}
        <div className="floating-elements">
          <div className="floating-element"></div>
          <div className="floating-element"></div>
          <div className="floating-element"></div>
        </div>
        
        <div className="min-h-screen relative">
          {/* Navigation Header */}
          <header className="relative z-10 py-6 px-6 sm:px-12">
            <div className="max-w-none mx-auto flex justify-between items-center">
              <button 
                onClick={() => navigate('/')}
                className="flex items-center gap-3 px-4 py-2 min-h-[44px] bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 text-white hover:bg-white/15 transition-all duration-200"
              >
                <ChevronLeft className="w-5 h-5" />
                <span className="hidden sm:inline font-medium">New Analysis</span>
                <span className="sm:hidden font-medium">Home</span>
              </button>
              
              <div className="text-xl sm:text-2xl font-bold text-white">
                <span className="text-gradient-primary">Pitch Prep</span>
              </div>
              
              <div className="flex items-center gap-2 text-xs sm:text-sm text-white/70">
                <CheckCircle className="w-4 h-4 text-green-400" />
                <span className="hidden sm:inline font-medium">Complete</span>
              </div>
            </div>
          </header>

          {/* Success Header */}
          <section className={`relative z-10 text-center mb-12 px-6 sm:px-12 transition-all duration-800 ${
            animationStage >= 1 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
          }`}>
            <div className="max-w-none mx-auto">
              <div className="w-24 h-24 sm:w-32 sm:h-32 mx-auto mb-6 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full flex items-center justify-center shadow-2xl shadow-blue-500/40">
                <CheckCircle className="w-12 h-12 sm:w-16 sm:h-16 text-white" />
              </div>
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-[1.1]" style={{fontWeight: 800}}>
                Your Analysis is Complete!
              </h1>
              <p className="text-lg sm:text-xl font-normal text-white/85 max-w-4xl mx-auto leading-relaxed">
                We've analyzed your company against 500+ Shark Tank pitches and generated your personalized preparation report.
              </p>
              
              <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm rounded-full border border-blue-400/30 mt-8">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-white font-medium text-sm">
                  Step 4 of 4: Analysis Complete
                </span>
              </div>
            </div>
          </section>

        {/* Summary Cards */}
        <section className={`relative z-10 px-4 sm:px-6 mb-12 transition-all duration-800 delay-300 ${
          animationStage >= 2 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>
          <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Overall Readiness Card */}
            <GlassCard className="p-6 relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-cyan-400" />
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-lg font-bold text-white">Overall Readiness</h3>
                <div className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                  {resultsData.overallScore}%
                </div>
              </div>
              <p className="text-white/80 text-sm mb-4">
                Your business shows strong potential for Shark Tank with solid fundamentals and clear value proposition.
              </p>
              <ul className="space-y-2">
                {resultsData.companyInfo.keyStrengths.slice(0, 3).map((strength, index) => (
                  <li key={index} className="flex items-center gap-2 text-sm text-white/80">
                    <div className="w-4 h-4 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-2.5 h-2.5 text-white" />
                    </div>
                    <span>{strength}</span>
                  </li>
                ))}
              </ul>
            </GlassCard>

            {/* Best Shark Match Card */}
            <GlassCard className="p-6 relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-400 to-pink-400" />
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-lg font-bold text-white">Best Shark Match</h3>
                <div className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  {resultsData.bestSharkMatch.score}%
                </div>
              </div>
              <p className="text-white/80 text-sm mb-4">
                <strong>{resultsData.bestSharkMatch.name}</strong> shows highest compatibility with your business model and sector focus.
              </p>
              <ul className="space-y-2">
                {resultsData.bestSharkMatch.reasons.map((reason, index) => (
                  <li key={index} className="flex items-center gap-2 text-sm text-white/80">
                    <div className="w-4 h-4 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-2.5 h-2.5 text-white" />
                    </div>
                    <span>{reason}</span>
                  </li>
                ))}
              </ul>
            </GlassCard>

            {/* Similar Companies Card */}
            <GlassCard className="p-6 relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-400 to-blue-400" />
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-lg font-bold text-white">Similar Companies</h3>
                <div className="text-2xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
                  {resultsData.similarCompanies.length}
                </div>
              </div>
              <p className="text-white/80 text-sm mb-4">
                Found {resultsData.similarCompanies.length} companies with similar business models, {resultsData.similarCompanies.filter(c => c.dealStatus === 'Deal').length} received deals on the show.
              </p>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-sm text-white/80">
                  <div className="w-4 h-4 bg-gradient-to-r from-green-400 to-blue-400 rounded-full flex items-center justify-center flex-shrink-0">
                    <CheckCircle className="w-2.5 h-2.5 text-white" />
                  </div>
                  <span>{Math.round((resultsData.similarCompanies.filter(c => c.dealStatus === 'Deal').length / resultsData.similarCompanies.length) * 100)}% success rate in your category</span>
                </li>
                <li className="flex items-center gap-2 text-sm text-white/80">
                  <div className="w-4 h-4 bg-gradient-to-r from-green-400 to-blue-400 rounded-full flex items-center justify-center flex-shrink-0">
                    <CheckCircle className="w-2.5 h-2.5 text-white" />
                  </div>
                  <span>Average deal: ₹45L for 18% equity</span>
                </li>
                <li className="flex items-center gap-2 text-sm text-white/80">
                  <div className="w-4 h-4 bg-gradient-to-r from-green-400 to-blue-400 rounded-full flex items-center justify-center flex-shrink-0">
                    <CheckCircle className="w-2.5 h-2.5 text-white" />
                  </div>
                  <span>Key success patterns identified</span>
                </li>
              </ul>
            </GlassCard>
          </div>
        </section>

        {/* Call to Action */}
        <section className={`relative z-10 px-4 sm:px-6 mb-12 transition-all duration-800 delay-600 ${
          animationStage >= 3 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>
          <div className="max-w-4xl mx-auto text-center">
            <GlassCard className="p-8 relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-purple-400" />
              
              <h2 className="text-2xl sm:text-3xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Your Complete 12-Page Analysis Report
              </h2>
              <p className="text-white/80 mb-8">
                Everything you need to prepare for Shark Tank India
              </p>

              {/* Report Sections Preview */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
                {[
                  { icon: '📊', title: 'Executive Summary' },
                  { icon: '⭐', title: 'Business Analysis' },
                  { icon: '📈', title: 'Market Opportunity' },
                  { icon: '✅', title: 'Similar Companies' },
                  { icon: '🦈', title: 'Shark Compatibility' },
                  { icon: '❓', title: 'Question Bank' }
                ].map((section, index) => (
                  <div key={index} className="flex flex-col items-center gap-2 p-3 bg-white/5 rounded-xl hover:bg-white/10 transition-all duration-300">
                    <div className="text-2xl">{section.icon}</div>
                    <span className="text-xs font-medium text-white/80">{section.title}</span>
                  </div>
                ))}
              </div>

              <div className="space-y-4">
                <PremiumButton 
                  size="lg"
                  onClick={() => setShowPaymentGate(true)}
                  className="w-full sm:w-auto min-w-[280px] touch-manipulation"
                >
                  Download Full Report (₹1999)
                  <Download className="w-4 h-4 ml-2" />
                </PremiumButton>
                
                <div className="flex justify-center items-center gap-6 text-xs text-white/60">
                  <div className="flex items-center gap-1">
                    <Award className="w-3 h-3" />
                    <span>12-page report</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <CheckCircle className="w-3 h-3" />
                    <span>Instant download</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Target className="w-3 h-3" />
                    <span>Personalized insights</span>
                  </div>
                </div>
              </div>
            </GlassCard>
          </div>
        </section>
        </div>
      </>
    )
  }

  if (showPaymentGate) {
    return (
      <>
        {/* Premium Animated Background */}
        <div className="premium-animated-bg"></div>
        
        {/* Floating Elements */}
        <div className="floating-elements">
          <div className="floating-element"></div>
          <div className="floating-element"></div>
          <div className="floating-element"></div>
        </div>
        
        <div className="min-h-screen relative">
        
        {/* Navigation Header */}
        <header className="relative z-10 p-4 sm:p-6">
          <div className="max-w-7xl mx-auto flex justify-between items-center">
            <PremiumButton 
              variant="glass" 
              size="sm"
              onClick={() => navigate('/analysis')}
              className="flex items-center gap-2 touch-manipulation"
            >
              <ChevronLeft className="w-4 h-4" />
              <span className="hidden sm:inline">Back to Analysis</span>
              <span className="sm:hidden">Back</span>
            </PremiumButton>
            
            <div className="text-lg sm:text-xl font-bold text-white">
              <span className="gradient-text">Shark AI</span>
            </div>
            
            <div className="flex items-center gap-2 text-xs sm:text-sm text-text-muted">
              <span className="hidden sm:inline">Report Preview</span>
              <span className="sm:hidden">Preview</span>
              <Eye className="w-3 h-3 sm:w-4 sm:h-4 text-blue-400" />
            </div>
          </div>
        </header>
        
        {/* Hero Section */}
        <section className="relative z-10 text-center mb-6 sm:mb-8 px-4 sm:px-6">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-3 sm:mb-4 leading-tight">
              Your <span className="gradient-text">Analysis Report</span> is Ready
            </h1>
            <p className="text-sm sm:text-base text-text-light mb-4 sm:mb-6 leading-relaxed max-w-2xl mx-auto">
              Get your comprehensive 12-page Shark Tank analysis report with personalized insights and strategy recommendations.
            </p>
            
            {/* Quick Stats */}
            <div className="flex flex-wrap justify-center gap-2 sm:gap-4 mb-6 sm:mb-8">
              <div className="trust-badge text-xs sm:text-sm">
                <FileText className="w-3 h-3 sm:w-4 sm:h-4" />
                <span>12-page report</span>
              </div>
              <div className="trust-badge text-xs sm:text-sm">
                <TrendingUp className="w-3 h-3 sm:w-4 sm:h-4" />
                <span>Personalized insights</span>
              </div>
              <div className="trust-badge text-xs sm:text-sm">
                <Download className="w-3 h-3 sm:w-4 sm:h-4" />
                <span>Instant download</span>
              </div>
            </div>
          </div>
        </section>
        
        <ReportPreview
          companyData={state}
          analysisData={state.analysisData}
          onPurchase={handlePayment}
          paymentLoading={paymentLoading}
          paymentError={paymentError}
          paymentSuccess={paymentSuccess}
          reportLoading={reportLoading}
        />

        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          defaultMode={authMode}
        />
        </div>
      </>
    )
  }

  // Show the full comprehensive report
  return (
    <>
      {/* Premium Animated Background */}
      <div className="premium-animated-bg"></div>
      
      {/* Floating Elements */}
      <div className="floating-elements">
        <div className="floating-element"></div>
        <div className="floating-element"></div>
        <div className="floating-element"></div>
      </div>
      
      <div className="min-h-screen relative">
      
      {/* Navigation Header */}
      <header className="relative z-10 p-4 sm:p-6">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <PremiumButton 
            variant="glass" 
            size="sm"
            onClick={() => navigate('/payment')}
            className="flex items-center gap-2 touch-manipulation"
          >
            <ChevronLeft className="w-4 h-4" />
            <span className="hidden sm:inline">Back to Payment</span>
            <span className="sm:hidden">Back</span>
          </PremiumButton>
          
          <div className="text-lg sm:text-xl font-bold text-white">
            <span className="gradient-text">Shark AI</span>
          </div>
          
          <div className="flex items-center gap-2">
            {generatedReport && (
              <PremiumButton 
                variant="glass" 
                size="sm"
                onClick={() => reportService.downloadReportPDF(generatedReport.report_id)}
                className="flex items-center gap-1 sm:gap-2 touch-manipulation"
              >
                <Download className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden sm:inline">Download</span>
                <span className="sm:hidden text-xs">PDF</span>
              </PremiumButton>
            )}
          </div>
        </div>
      </header>
      
      {/* Hero Section */}
      {generatedReport && (
        <section className="relative z-10 text-center mb-6 sm:mb-8 px-4 sm:px-6">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-3 sm:mb-4 leading-tight">
              Your Complete <span className="gradient-text">Shark Tank Analysis</span>
            </h1>
            <p className="text-sm sm:text-base text-text-light mb-4 sm:mb-6 leading-relaxed max-w-2xl mx-auto">
              Your comprehensive analysis report is ready. Scroll down to explore all insights or download the PDF version.
            </p>
            
            {/* Report Stats */}
            <div className="flex flex-wrap justify-center gap-2 sm:gap-4 mb-6 sm:mb-8">
              <div className="trust-badge text-xs sm:text-sm">
                <Award className="w-3 h-3 sm:w-4 sm:h-4" />
                <span>Analysis Complete</span>
              </div>
              <div className="trust-badge text-xs sm:text-sm">
                <Users className="w-3 h-3 sm:w-4 sm:h-4" />
                <span>Shark compatibility</span>
              </div>
              <div className="trust-badge text-xs sm:text-sm">
                <Target className="w-3 h-3 sm:w-4 sm:h-4" />
                <span>Strategy insights</span>
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center">
              <PremiumButton 
                size="lg"
                onClick={() => reportService.downloadReportPDF(generatedReport.report_id)}
                className="w-full sm:w-auto min-w-[200px] touch-manipulation"
              >
                Download PDF Report
                <Download className="w-4 h-4 ml-2" />
              </PremiumButton>
              
              <PremiumButton 
                variant="glass" 
                size="lg"
                onClick={() => navigator.share && navigator.share({
                  title: 'My Shark Tank Analysis Report',
                  text: 'Check out my comprehensive Shark Tank analysis report!',
                  url: window.location.href
                })}
                className="w-full sm:w-auto min-w-[200px] touch-manipulation"
              >
                Share Report
                <Share2 className="w-4 h-4 ml-2" />
              </PremiumButton>
            </div>
          </div>
        </section>
      )}
      
      {/* Main Content */}
      <main className="relative z-10">
        {generatedReport ? (
          <div className="px-4 sm:px-6 pb-12 sm:pb-16">
            <ReportViewer 
              reportData={generatedReport as any}
            />
          </div>
        ) : (
          <div className="px-4 sm:px-6 pb-12 sm:pb-16">
            {/* Report Generation Header */}
            <section className="text-center mb-6 sm:mb-8">
              <div className="max-w-4xl mx-auto">
                <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-3 sm:mb-4 leading-tight">
                  Generating Your <span className="gradient-text">Analysis Report</span>
                </h1>
                <p className="text-sm sm:text-base text-text-light mb-4 sm:mb-6 leading-relaxed max-w-2xl mx-auto">
                  Our AI is creating your personalized 12-page analysis report. This process typically takes 2-3 minutes.
                </p>
                
                {/* Generation Progress */}
                <div className="flex flex-wrap justify-center gap-2 sm:gap-4 mb-6 sm:mb-8">
                  <div className="trust-badge text-xs sm:text-sm">
                    <div className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
                    <span>Analyzing data</span>
                  </div>
                  <div className="trust-badge text-xs sm:text-sm">
                    <FileText className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span>Creating report</span>
                  </div>
                  <div className="trust-badge text-xs sm:text-sm">
                    <Target className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span>Strategy insights</span>
                  </div>
                </div>
              </div>
            </section>
            
            <ReportGenerator 
              companyData={state}
              analysisResults={state.analysisData}
            />
          </div>
        )}
      </main>
      
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        defaultMode={authMode}
      />
      </div>
    </>
  )
}