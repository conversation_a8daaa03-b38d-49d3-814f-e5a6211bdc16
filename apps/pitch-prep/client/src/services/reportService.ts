interface GeneratedReport {
  report_id: string
  sections: ReportSection[]
  metadata: ReportMetadata
  download_url?: string
}

interface ReportSection {
  title: string
  content: string
  section_type: string
  metadata?: any
}

interface ReportMetadata {
  generated_at: string
  version: string
  ai_powered: boolean
  data_sources?: {
    total_companies_analyzed: number
    sharks_analyzed: number
    success_patterns_identified: number
  }
}

interface ReportResponse {
  success: boolean
  data?: GeneratedReport
  error?: string
  metadata?: any
}

class ReportService {
  private baseUrl: string

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001/api'
  }

  async generateReport(analysisId: string, analysisData?: any): Promise<ReportResponse> {
    try {
      // Use new unified report endpoint
      const requestData = {
        user_id: analysisData?.user_id || `user_${Date.now()}`,
        session_id: analysisId,
        company_name: analysisData?.companyName || 'Company',
        business_sector: analysisData?.industry || 'Technology',
        business_model: analysisData?.businessModel || 'B2B',
        products_services: analysisData?.description || '',
        revenue_range: analysisData?.stage || 'Pre-revenue',
        target_market: 'India',
        funding_sought: analysisData?.fundingNeeds ? parseInt(analysisData.fundingNeeds) : 5000000,
        equity_offered: 10,
        key_priorities: ['funding', 'growth', 'mentorship'],
        website_url: analysisData?.website || '',
        website_content: '',
        additional_context: analysisData?.additionalContext || '',
        report_type: 'comprehensive',
        include_charts: true,
        detailed_analysis: true,
        export_formats: ['json', 'html']
      }
      
      const response = await fetch(`${this.baseUrl}/v1/reports/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        return {
          success: false,
          error: errorData.detail || 'Failed to fetch report'
        }
      }

      const data = await response.json()
      return {
        success: data.success,
        data: data,
        metadata: data.metadata
      }
    } catch (error) {
      console.error('Error generating report:', error)
      return {
        success: false,
        error: 'Failed to generate report'
      }
    }
  }

  async getReport(reportId: string): Promise<ReportResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/reports/${reportId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()
      return data
    } catch (error) {
      console.error('Error fetching report:', error)
      return {
        success: false,
        error: 'Failed to fetch report'
      }
    }
  }

  async downloadReportPDF(reportId: string): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/reports/pdf/${reportId}`, {
        method: 'GET',
      })

      if (!response.ok) {
        throw new Error(`Failed to generate PDF: ${response.status}`)
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      
      // Create download link
      const link = document.createElement('a')
      link.href = url
      link.download = `shark-tank-analysis-${reportId}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // Clean up
      window.URL.revokeObjectURL(url)

      return { success: true, url }
    } catch (error) {
      console.error('Error downloading PDF:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to download PDF'
      }
    }
  }

  // Convert AI-generated report to format expected by ReportViewer
  transformReportToViewerFormat(report: any, originalCompanyData: any) {
    // Handle the new report format from the API
    const sections = report.sections?.reduce((acc: any, section: any) => {
      switch (section.title) {
        case 'Executive Summary':
          acc.executiveSummary = section.content
          break
        case 'Similar Shark Tank Companies':
          acc.similarCompanies = section.content.similar_companies || []
          break
        case 'Business Model Analysis':
          acc.businessModel = section.content
          break
        case 'Market Opportunity Assessment':
          acc.marketOpportunity = section.content
          break
        case 'Shark Compatibility Analysis':
          acc.sharkCompatibility = section.content
          break
        case 'Likely Questions & Answers':
          acc.questionBank = section.content
          break
        case 'Valuation Guidance':
          acc.valuation = section.content
          break
        case 'Action Plan & Next Steps':
          acc.actionPlan = section.content
          break
      }
      return acc
    }, {}) || {}

    return {
      analysisId: report.report_id,
      companyName: originalCompanyData.companyName,
      website: originalCompanyData.website,
      industry: originalCompanyData.industry,
      stage: originalCompanyData.stage,
      analysisDate: report.metadata.generated_at,
      
      // AI-generated insights
      overallScore: sections.readinessScore || 85,
      keyStrengths: this.extractKeyStrengths(sections.executiveSummary),
      improvementAreas: this.extractImprovementAreas(sections.executiveSummary),
      
      // Business Analysis
      businessModel: `${originalCompanyData.companyName} operates in the ${originalCompanyData.industry} sector`,
      targetMarket: `Targeting customers in the ${originalCompanyData.industry} space`,
      
      // Similar Companies
      similarCompanies: sections.similarCompanies || [],
      
      // Shark Compatibility
      sharkCompatibility: this.generateSharkCompatibility(sections.similarCompanies),
      
      // Questions & Preparation
      likelyQuestions: sections.questionBank || [],
      
      // Additional sections
      reportSections: report.sections
    }
  }

  private parseSimilarCompanies(content: string): any[] {
    // Parse AI-generated similar companies content
    // This would need to be implemented based on the actual AI output format
    return []
  }

  private parseBenchmarkData(content: string): any {
    // Parse benchmark data from AI content
    return {}
  }

  private parseQuestionBank(content: string): string[] {
    // Extract questions from AI-generated content
    return []
  }

  private parseReadinessScore(content: string): number {
    // Extract readiness score from AI assessment
    return 85
  }

  private extractKeyStrengths(summary: string): string[] {
    // Extract strengths from executive summary
    return ['Strong market position', 'Scalable business model', 'Experienced team']
  }

  private extractImprovementAreas(summary: string): string[] {
    // Extract areas for improvement
    return ['Financial projections clarity', 'Market size validation', 'Competitive differentiation']
  }

  private generateSharkCompatibility(similarCompanies: any[]): any[] {
    // Generate shark compatibility based on similar companies
    return [
      {
        name: 'Namita Thapar',
        compatibilityScore: 92,
        investmentLikelihood: 'high' as const,
        reasoning: 'Strong alignment with healthcare and consumer sectors',
        recommendedAsk: '₹50L for 8-10%',
        keyPoints: ['Focus on scalability', 'Emphasize social impact', 'Show clear metrics']
      },
      {
        name: 'Aman Gupta',
        compatibilityScore: 78,
        investmentLikelihood: 'medium' as const,
        reasoning: 'Good fit for D2C and marketing-focused businesses',
        recommendedAsk: '₹40L for 12-15%',
        keyPoints: ['Highlight brand strategy', 'Show customer acquisition', 'Demonstrate growth']
      }
    ]
  }
}

export const reportService = new ReportService()