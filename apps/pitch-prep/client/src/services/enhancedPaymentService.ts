import axios from 'axios';

// Razorpay global interface
declare global {
  interface Window {
    Razorpay: any;
  }
}

interface CreateOrderRequest {
  userId: string;
  analysisId: string;
  userEmail: string;
  amount?: number;
}

interface CreateOrderResponse {
  success: boolean;
  order?: {
    id: string;
    razorpayOrderId: string;
    amount: number;
    currency: string;
    key: string;
  };
  error?: string;
}

interface PaymentVerificationRequest {
  razorpay_order_id: string;
  razorpay_payment_id: string;
  razorpay_signature: string;
  analysis_id: string;
  user_email?: string;
}

interface PaymentVerificationResponse {
  success: boolean;
  data?: {
    paymentVerified: boolean;
    orderId: string;
    analysisId: string;
    status: string;
    paidAt: string;
    reportAccess: {
      granted: boolean;
      downloadUrl: string;
      accessToken: string;
      dashboardUrl: string;
    };
  };
  error?: string;
}

interface RazorpayOptions {
  key: string;
  amount: number;
  currency: string;
  name: string;
  description: string;
  order_id: string;
  handler: (response: any) => void;
  prefill: {
    name?: string;
    email?: string;
    contact?: string;
  };
  theme: {
    color: string;
  };
  modal: {
    ondismiss: () => void;
  };
  notes?: Record<string, string>;
}

export class EnhancedPaymentService {
  private baseURL: string;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001/api';
  }

  private async loadRazorpayScript(): Promise<boolean> {
    return new Promise((resolve) => {
      if (typeof window.Razorpay !== 'undefined') {
        resolve(true);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => resolve(true);
      script.onerror = () => resolve(false);
      document.body.appendChild(script);
    });
  }

  async createOrder(request: CreateOrderRequest): Promise<CreateOrderResponse> {
    try {
      console.log('[Enhanced Payment Service] Creating order:', request);

      const response = await axios.post(`${this.baseURL}/payment/create-order`, {
        user_id: request.userId,
        analysis_id: request.analysisId,
        user_email: request.userEmail,
        amount: request.amount || 199900 // ₹1999 in paise
      });

      if (response.data.success) {
        console.log('[Enhanced Payment Service] Order created successfully:', response.data);
        return {
          success: true,
          order: {
            id: response.data.order_id,
            razorpayOrderId: response.data.order_id,
            amount: response.data.amount,
            currency: response.data.currency,
            key: response.data.key_id
          }
        };
      } else {
        throw new Error(response.data.error || 'Failed to create order');
      }
    } catch (error) {
      console.error('[Enhanced Payment Service] Order creation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create payment order'
      };
    }
  }

  async verifyPayment(request: PaymentVerificationRequest): Promise<PaymentVerificationResponse> {
    try {
      console.log('[Enhanced Payment Service] Verifying payment:', request);

      const response = await axios.post(`${this.baseURL}/payment/verify`, {
        razorpay_order_id: request.razorpay_order_id,
        razorpay_payment_id: request.razorpay_payment_id,
        razorpay_signature: request.razorpay_signature,
        analysis_id: request.analysis_id,
        user_email: request.user_email || `customer_${Date.now()}@pitch-prep.com`
      });

      if (response.data.success) {
        console.log('[Enhanced Payment Service] Payment verified successfully:', response.data);
        return {
          success: true,
          data: {
            paymentVerified: true,
            orderId: response.data.order?.id || request.razorpay_order_id,
            analysisId: response.data.analysisId || request.analysis_id,
            status: 'paid',
            paidAt: new Date().toISOString(),
            reportAccess: {
              granted: true,
              downloadUrl: `/api/reports/download/${request.analysis_id}`,
              accessToken: 'granted',
              dashboardUrl: '/results'
            }
          }
        };
      } else {
        throw new Error(response.data.error || 'Payment verification failed');
      }
    } catch (error) {
      console.error('[Enhanced Payment Service] Payment verification failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to verify payment'
      };
    }
  }

  async processPayment(
    orderData: CreateOrderRequest,
    userDetails: {
      name?: string;
      email: string;
      contact?: string;
    },
    onSuccess: (data: any) => void,
    onFailure: (error: string) => void,
    onDismiss?: () => void
  ): Promise<void> {
    try {
      // Load Razorpay script
      const isScriptLoaded = await this.loadRazorpayScript();
      if (!isScriptLoaded) {
        throw new Error('Failed to load Razorpay payment gateway');
      }

      // Create order
      const orderResponse = await this.createOrder(orderData);
      if (!orderResponse.success || !orderResponse.order) {
        throw new Error(orderResponse.error || 'Failed to create payment order');
      }

      const order = orderResponse.order;

      // Configure Razorpay options
      const razorpayOptions: RazorpayOptions = {
        key: order.key,
        amount: order.amount,
        currency: order.currency,
        name: 'Pitch Prep AI',
        description: 'Premium Shark Tank Analysis Report',
        order_id: order.razorpayOrderId,
        handler: async (response: any) => {
          try {
            console.log('[Enhanced Payment Service] Payment handler called:', response);

            // Verify payment
            const verificationResponse = await this.verifyPayment({
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
              analysis_id: orderData.analysisId
            });

            if (verificationResponse.success && verificationResponse.data) {
              console.log('[Enhanced Payment Service] Payment completed successfully');
              onSuccess({
                ...verificationResponse.data,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_order_id: response.razorpay_order_id
              });
            } else {
              throw new Error(verificationResponse.error || 'Payment verification failed');
            }
          } catch (error) {
            console.error('[Enhanced Payment Service] Payment verification error:', error);
            onFailure(error instanceof Error ? error.message : 'Payment verification failed');
          }
        },
        prefill: {
          name: userDetails.name,
          email: userDetails.email,
          contact: userDetails.contact
        },
        theme: {
          color: '#3B82F6' // Blue theme matching the UI
        },
        modal: {
          ondismiss: () => {
            console.log('[Enhanced Payment Service] Payment dismissed by user');
            if (onDismiss) {
              onDismiss();
            }
          }
        },
        notes: {
          analysis_id: orderData.analysisId,
          user_email: userDetails.email,
          product: 'Shark AI Premium Report'
        }
      };

      // Open Razorpay checkout
      const razorpayInstance = new window.Razorpay(razorpayOptions);
      razorpayInstance.open();

    } catch (error) {
      console.error('[Enhanced Payment Service] Payment processing error:', error);
      onFailure(error instanceof Error ? error.message : 'Payment processing failed');
    }
  }

  async getOrderStatus(orderId: string): Promise<{
    success: boolean;
    order?: {
      id: string;
      amount: number;
      currency: string;
      analysisId: string;
      status: string;
      createdAt: string;
      paidAt?: string;
    };
    error?: string;
  }> {
    try {
      const response = await axios.get(`${this.baseURL}/payment?action=get-order&orderId=${orderId}`);

      if (response.data.success) {
        return {
          success: true,
          order: response.data.order
        };
      } else {
        throw new Error(response.data.error || 'Failed to get order status');
      }
    } catch (error) {
      console.error('[Enhanced Payment Service] Get order status failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get order status'
      };
    }
  }

  // Helper method to format amount
  formatAmount(amountInPaise: number): string {
    return `₹${(amountInPaise / 100).toLocaleString('en-IN')}`;
  }

  // Helper method to generate user ID from email
  generateUserId(email: string): string {
    return `user_${email.split('@')[0]}_${Date.now()}`;
  }

  // Helper method to generate analysis ID
  generateAnalysisId(): string {
    return `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Singleton instance
let enhancedPaymentService: EnhancedPaymentService;

export function getEnhancedPaymentService(): EnhancedPaymentService {
  if (!enhancedPaymentService) {
    enhancedPaymentService = new EnhancedPaymentService();
  }
  return enhancedPaymentService;
}

// Export for direct use
export default EnhancedPaymentService;