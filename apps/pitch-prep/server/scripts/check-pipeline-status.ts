#!/usr/bin/env bun

/**
 * Pipeline Status Checker
 * Monitors background pipeline execution and provides status updates
 */

import { readFileSync, existsSync } from 'fs';

class PipelineStatusChecker {
  private progressFile = '/Users/<USER>/giki-ai-workspace/apps/pitch-prep/server/logs/pipeline-progress.json';
  private pidFile = '/Users/<USER>/giki-ai-workspace/apps/pitch-prep/server/logs/pipeline.pid';

  checkStatus(): void {
    console.log('🔍 PIPELINE STATUS CHECK');
    console.log('=' .repeat(50));

    // Check if pipeline is running
    const isRunning = this.checkIfRunning();
    console.log(`Status: ${isRunning ? '🟢 RUNNING' : '🔴 NOT RUNNING'}`);

    if (!isRunning) {
      console.log('\n💡 To start the pipeline:');
      console.log('cd /Users/<USER>/giki-ai-workspace/apps/pitch-prep/server');
      console.log('nohup bun run scripts/run-pipeline-background.ts > logs/pipeline-output.log 2>&1 &');
      return;
    }

    // Show progress if running
    this.showProgress();
  }

  private checkIfRunning(): boolean {
    if (!existsSync(this.pidFile)) {
      return false;
    }

    try {
      const pidContent = readFileSync(this.pidFile, 'utf-8').trim();
      if (!pidContent) return false;

      const pid = parseInt(pidContent);
      
      // Check if process is actually running
      try {
        process.kill(pid, 0); // Signal 0 checks if process exists
        return true;
      } catch {
        return false;
      }
    } catch {
      return false;
    }
  }

  private showProgress(): void {
    if (!existsSync(this.progressFile)) {
      console.log('⚠️ Progress file not found');
      return;
    }

    try {
      const progress = JSON.parse(readFileSync(this.progressFile, 'utf-8'));
      
      const elapsedHours = ((Date.now() - progress.startTime) / (1000 * 60 * 60)).toFixed(1);
      const completionPercent = ((progress.processedCount / progress.totalFiles) * 100).toFixed(1);
      const successRate = progress.processedCount > 0 ? 
        ((progress.successCount / progress.processedCount) * 100).toFixed(1) : '0.0';

      let etaString = 'Calculating...';
      if (progress.estimatedCompletionTime) {
        const eta = new Date(progress.estimatedCompletionTime);
        const now = new Date();
        if (eta > now) {
          const hoursRemaining = ((eta.getTime() - now.getTime()) / (1000 * 60 * 60)).toFixed(1);
          etaString = `${eta.toLocaleTimeString()} (${hoursRemaining}h remaining)`;
        } else {
          etaString = 'Should be complete soon';
        }
      }

      console.log(`
📊 PROGRESS DETAILS
⏰ Runtime: ${elapsedHours} hours
📈 Progress: ${progress.processedCount}/${progress.totalFiles} files (${completionPercent}%)
✅ Success Rate: ${successRate}% 
📊 Successful: ${progress.successCount}
❌ Failed: ${progress.failureCount}
⚡ Avg Speed: ${progress.avgProcessingTimeSeconds?.toFixed(1) || 'N/A'}s per file
🎯 ETA: ${etaString}
📄 Current: ${progress.currentFile || 'None'}

📈 Improvement vs Previous: ${successRate}% vs 30.9% = +${(parseFloat(successRate) - 30.9).toFixed(1)} points!
`);

    } catch (error) {
      console.log(`⚠️ Error reading progress: ${error.message}`);
    }
  }

  watchProgress(intervalSeconds = 10): void {
    console.log(`👀 Watching pipeline progress (updates every ${intervalSeconds}s)`);
    console.log('Press Ctrl+C to stop watching\n');

    const interval = setInterval(() => {
      console.clear();
      this.checkStatus();
    }, intervalSeconds * 1000);

    process.on('SIGINT', () => {
      clearInterval(interval);
      console.log('\n👋 Stopped watching pipeline');
      process.exit(0);
    });
  }
}

// Command line interface
const args = process.argv.slice(2);
const checker = new PipelineStatusChecker();

if (args[0] === 'watch') {
  const interval = args[1] ? parseInt(args[1]) : 10;
  checker.watchProgress(interval);
} else {
  checker.checkStatus();
}