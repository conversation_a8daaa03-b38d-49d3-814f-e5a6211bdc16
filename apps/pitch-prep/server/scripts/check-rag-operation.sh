#\!/bin/bash

# Check status of RAG corpus creation operation
OPERATION_NAME="projects/273348121056/locations/us-central1/operations/6848756791526817792"
ACCESS_TOKEN=$(gcloud auth print-access-token)

echo "🔍 Checking RAG corpus operation status..."
echo ""

RESPONSE=$(curl -s \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  "https://us-central1-aiplatform.googleapis.com/v1beta1/$OPERATION_NAME")

echo "$RESPONSE" | jq '.'

# Check if done
if echo "$RESPONSE" | jq -e '.done == true' > /dev/null 2>&1; then
  echo ""
  echo "✅ Operation completed\!"
  
  # Extract the corpus name if available
  if echo "$RESPONSE" | jq -e '.response.name' > /dev/null 2>&1; then
    CORPUS_NAME=$(echo "$RESPONSE" | jq -r '.response.name')
    echo "📚 RAG Corpus Created: $CORPUS_NAME"
  fi
else
  echo ""
  echo "⏳ Operation still in progress..."
fi
