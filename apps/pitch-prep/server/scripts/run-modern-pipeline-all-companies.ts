#!/usr/bin/env bun

/**
 * Run the Modern 7-Phase Pipeline on All Companies
 * This implements the user's complete approved plan:
 * 
 * 1. Multi-phase extraction with structured output using @google/genai SDK
 * 2. Grounding with Shark Tank database  
 * 3. Context caching for repeated analysis
 * 4. Streaming for progress updates
 * 5. Exponential backoff for rate limiting
 * 6. Flexible number extraction with context
 * 7. Narrative & evidence preservation
 */

import '../../../../lib/env-loader.js';
import { transcriptProcessingPipeline } from '../src/services/transcriptProcessingPipeline.js';

async function runModernPipelineOnAllCompanies() {
  console.log('🚀 STARTING MODERN 7-PHASE EXTRACTION PIPELINE');
  console.log('=' .repeat(80));
  console.log('');
  console.log('📋 APPROVED FEATURES:');
  console.log('✅ Multi-phase extraction with structured output (@google/genai SDK)');
  console.log('✅ Grounding with Shark Tank database');
  console.log('✅ Context caching for repeated analysis');
  console.log('✅ Streaming for progress updates');
  console.log('✅ Exponential backoff for rate limiting (60s, 120s, 240s, 300s)');
  console.log('✅ Flexible number extraction with context');
  console.log('✅ Narrative & evidence preservation');
  console.log('✅ English normalization (Hindi → English conversion)');
  console.log('');
  console.log('🎯 PROCESSING PHASES:');
  console.log('  Phase 1: Basic Company Information (English-only structured output)');
  console.log('  Phase 2: Financial Data (with Phase 1 context)');
  console.log('  Phase 3: Business Model Analysis (with accumulated context)');
  console.log('  Phase 4: Deal Dynamics & Shark Responses');
  console.log('  Phase 5: Individual Shark Analysis');
  console.log('  Phase 6: Strategic Insights & Lessons');
  console.log('  Phase 7: Rich Narrative Generation (2000+ words)');
  console.log('');
  console.log('🔥 IMPROVEMENTS OVER PREVIOUS PIPELINE:');
  console.log('  • Hindi/Devanagari → English preprocessing (fixes "क्वालीफाई" → "Qualify")');
  console.log('  • Context accumulation across phases (each phase uses previous results)');
  console.log('  • Structured JSON schemas with Type constraints');
  console.log('  • Modern retry logic with exponential backoff');
  console.log('  • Comprehensive narrative generation');
  console.log('  • Individual confidence scoring per phase');
  console.log('');
  console.log('📊 EXPECTED IMPROVEMENT:');
  console.log('  Previous Success Rate: 30.9% (210/679 companies)');
  console.log('  Target Success Rate: >70% (based on Qualify test success)');
  console.log('');
  console.log('⏰ ESTIMATED TIME: ~2-3 hours for all 679 companies');
  console.log('  (Rate limiting delays included)');
  console.log('');

  const startTime = Date.now();

  try {
    // Process all companies (limit removed for full processing)
    await transcriptProcessingPipeline.processAllCompanies();
    
    const endTime = Date.now();
    const totalTimeHours = (endTime - startTime) / (1000 * 60 * 60);
    
    console.log('\n' + '=' .repeat(80));
    console.log('🎉 MODERN 7-PHASE PIPELINE COMPLETE!');
    console.log('=' .repeat(80));
    console.log(`⏰ Total Processing Time: ${totalTimeHours.toFixed(1)} hours`);
    console.log('');
    console.log('📋 NEXT STEPS:');
    console.log('1. 📊 Analyze success rate improvement');
    console.log('2. 🔍 Review companies with low confidence scores');
    console.log('3. 🗂️  Populate RAG vector store with rich narratives');
    console.log('4. 🚀 Deploy enhanced similarity matching service');
    console.log('');
    
  } catch (error) {
    console.error('❌ Modern pipeline failed:', error);
    process.exit(1);
  }
}

// Process with limited batch first to validate, then full run
console.log('🧪 Do you want to run a test batch first? (Recommended)');
console.log('   - Test batch: 10 companies (15-20 minutes)');  
console.log('   - Full run: All 679 companies (2-3 hours)');
console.log('');
console.log('💡 Running TEST BATCH first to validate pipeline...');
console.log('   (You can modify this script to run full batch after validation)');
console.log('');

// Run test batch first (can be changed to full run)
const TEST_BATCH_SIZE = 10;

async function runTestBatch() {
  console.log(`🧪 Running TEST BATCH (${TEST_BATCH_SIZE} companies)...`);
  await transcriptProcessingPipeline.processAllCompanies(TEST_BATCH_SIZE);
}

async function runFullPipeline() {
  console.log('🚀 Running FULL PIPELINE (all companies)...');
  await transcriptProcessingPipeline.processAllCompanies();
}

// Default: Run test batch
// Change this to runFullPipeline() after validating test results
await runTestBatch();