#!/usr/bin/env bun

/**
 * Performance Check Script
 * Validates that API response times meet production requirements
 */

import '../../../../lib/env-loader.js';

interface PerformanceResult {
  endpoint: string;
  method: string;
  avgTime: number;
  p95Time: number;
  p99Time: number;
  samples: number;
  passed: boolean;
}

class PerformanceChecker {
  private baseUrl = 'http://localhost:8001/api/v1';
  private results: PerformanceResult[] = [];

  async checkEndpoint(
    endpoint: string,
    method: string = 'GET',
    body?: any,
    samples: number = 10
  ): Promise<PerformanceResult> {
    const times: number[] = [];
    
    console.log(`\n📊 Testing ${method} ${endpoint}...`);
    
    for (let i = 0; i < samples; i++) {
      const startTime = performance.now();
      
      try {
        const response = await fetch(`${this.baseUrl}${endpoint}`, {
          method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: body ? JSON.stringify(body) : undefined,
        });
        
        if (!response.ok && response.status !== 404) {
          console.log(`  ⚠️ Response status: ${response.status}`);
        }
        
        const endTime = performance.now();
        const responseTime = endTime - startTime;
        times.push(responseTime);
        
        process.stdout.write(`  Sample ${i + 1}/${samples}: ${responseTime.toFixed(0)}ms\r`);
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.log(`  ❌ Request failed: ${error.message}`);
      }
    }
    
    // Calculate statistics
    times.sort((a, b) => a - b);
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const p95Index = Math.floor(times.length * 0.95);
    const p99Index = Math.floor(times.length * 0.99);
    const p95Time = times[p95Index] || times[times.length - 1];
    const p99Time = times[p99Index] || times[times.length - 1];
    
    const result: PerformanceResult = {
      endpoint,
      method,
      avgTime,
      p95Time,
      p99Time,
      samples,
      passed: p95Time < 200, // Target: <200ms p95
    };
    
    console.log(`\n  ✅ Average: ${avgTime.toFixed(0)}ms`);
    console.log(`  📈 P95: ${p95Time.toFixed(0)}ms`);
    console.log(`  📊 P99: ${p99Time.toFixed(0)}ms`);
    console.log(`  ${result.passed ? '✅ PASS' : '❌ FAIL'} (Target: <200ms p95)`);
    
    this.results.push(result);
    return result;
  }

  async runAllChecks(): Promise<void> {
    console.log('🚀 PITCH PREP PERFORMANCE CHECK');
    console.log('=' .repeat(50));
    console.log('Target: <200ms p95 response time for all endpoints\n');

    // Check health endpoint
    await this.checkEndpoint('/health', 'GET', null, 20);
    
    // Check analysis endpoints
    await this.checkEndpoint('/analysis/start', 'POST', {
      companyName: 'Test Company',
      website: 'https://example.com',
      description: 'Test description',
      askAmount: 5000000,
      equity: 10,
    }, 5);
    
    // Check similar companies (may be slower due to database query)
    await this.checkEndpoint('/companies/similar', 'POST', {
      companyName: 'Test Company',
      description: 'Veterinary services',
    }, 5);
    
    // Check session status
    await this.checkEndpoint('/analysis/session/test-session-id', 'GET', null, 10);
    
    // Print summary
    this.printSummary();
  }

  private printSummary(): void {
    console.log('\n' + '=' .repeat(50));
    console.log('📊 PERFORMANCE SUMMARY');
    console.log('=' .repeat(50));
    
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    
    console.table(
      this.results.map(r => ({
        Endpoint: r.endpoint,
        Method: r.method,
        'Avg (ms)': r.avgTime.toFixed(0),
        'P95 (ms)': r.p95Time.toFixed(0),
        'P99 (ms)': r.p99Time.toFixed(0),
        Status: r.passed ? '✅ PASS' : '❌ FAIL',
      }))
    );
    
    console.log(`\n🎯 Overall: ${passed}/${total} endpoints meet target`);
    console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(0)}%`);
    
    if (passed === total) {
      console.log('✅ ALL PERFORMANCE TARGETS MET!');
    } else {
      console.log('⚠️ Some endpoints need optimization');
      
      // Suggest optimizations
      const slowEndpoints = this.results.filter(r => !r.passed);
      console.log('\n💡 Optimization Suggestions:');
      slowEndpoints.forEach(endpoint => {
        console.log(`  • ${endpoint.endpoint}: Consider caching, query optimization, or async processing`);
      });
    }
  }
}

// Run performance checks
const checker = new PerformanceChecker();
await checker.runAllChecks();