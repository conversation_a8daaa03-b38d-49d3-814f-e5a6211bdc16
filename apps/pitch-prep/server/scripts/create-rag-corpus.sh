#!/bin/bash

# Create Vertex AI RAG Corpus using REST API
# This script creates a RAG corpus for Shark Tank companies

set -e

# Configuration
PROJECT_ID="rezolve-poc"
LOCATION="us-central1"
CORPUS_NAME="shark-tank-companies"
CORPUS_DISPLAY_NAME="Shark Tank India Companies"
CORPUS_DESCRIPTION="500+ Shark Tank India companies database for similarity matching"

echo "🚀 Creating Vertex AI RAG Corpus"
echo "================================"
echo "Project: $PROJECT_ID"
echo "Location: $LOCATION"
echo "Corpus: $CORPUS_NAME"
echo ""

# Get access token
echo "🔐 Getting access token..."
ACCESS_TOKEN=$(gcloud auth print-access-token)

# Create request JSON
cat > /tmp/rag-corpus-request.json <<EOF
{
  "display_name": "$CORPUS_DISPLAY_NAME",
  "description": "$CORPUS_DESCRIPTION"
}
EOF

# Create RAG corpus
echo "📚 Creating RAG corpus via REST API..."
RESPONSE=$(curl -X POST \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json; charset=utf-8" \
  -d @/tmp/rag-corpus-request.json \
  "https://${LOCATION}-aiplatform.googleapis.com/v1beta1/projects/${PROJECT_ID}/locations/${LOCATION}/ragCorpora" \
  2>/dev/null)

# Check if creation was successful
if echo "$RESPONSE" | grep -q "error"; then
  echo "❌ Error creating RAG corpus:"
  echo "$RESPONSE" | jq '.'
  exit 1
else
  echo "✅ RAG corpus creation initiated:"
  echo "$RESPONSE" | jq '.'
  
  # Extract corpus name from response
  CORPUS_RESOURCE_NAME=$(echo "$RESPONSE" | jq -r '.name' | sed 's/.*ragCorpora\///')
  
  echo ""
  echo "📝 RAG Corpus Resource Name: $CORPUS_RESOURCE_NAME"
  echo ""
  echo "Next steps:"
  echo "1. Wait for the operation to complete"
  echo "2. Upload company data to the corpus"
  echo "3. Test the RAG integration"
fi

# Clean up
rm -f /tmp/rag-corpus-request.json