/**
 * TypeScript Prompt Management System
 * 
 * Centralized template-driven prompt management for consistent AI interactions
 * across all services with version control, validation, and context injection.
 */

import fs from 'fs/promises';
import path from 'path';
import { GoogleGenAI } from '@google/genai';

export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  version: string;
  category: 'transcript_processing' | 'similarity_analysis' | 'shark_compatibility' | 'report_generation' | 'user_interaction';
  model: string;
  template: string;
  variables: {
    [key: string]: {
      type: 'string' | 'number' | 'boolean' | 'object' | 'array';
      required: boolean;
      description: string;
      example?: any;
      validation?: {
        minLength?: number;
        maxLength?: number;
        pattern?: string;
        enum?: any[];
      };
    };
  };
  output_format: 'json' | 'markdown' | 'text' | 'structured';
  output_schema?: any;
  usage_notes?: string[];
  examples?: Array<{
    input: Record<string, any>;
    expected_output: string;
  }>;
  created_at: Date;
  updated_at: Date;
}

export interface PromptExecution {
  template_id: string;
  execution_id: string;
  input_variables: Record<string, any>;
  rendered_prompt: string;
  model_response: string;
  execution_time_ms: number;
  token_usage?: {
    input_tokens: number;
    output_tokens: number;
    total_tokens: number;
  };
  success: boolean;
  error_message?: string;
  executed_at: Date;
}

export interface ContextInjectionConfig {
  include_user_context?: boolean;
  include_similar_companies?: boolean;
  include_shark_profiles?: boolean;
  include_historical_patterns?: boolean;
  context_limit?: number;
}

export class PromptManagementService {
  private geminiClient: GoogleGenAI;
  private templatesPath: string;
  private templateCache: Map<string, PromptTemplate> = new Map();
  private executionHistory: PromptExecution[] = [];

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY or GOOGLE_API_KEY environment variable is required for PromptManagementService');
    }
    this.geminiClient = new GoogleGenAI({ apiKey });
    this.templatesPath = path.join(process.cwd(), 'src', 'prompts', 'templates');
    
    // Initialize template directory
    this.initializeTemplateDirectory();
  }

  /**
   * Load a prompt template by ID
   */
  async loadTemplate(templateId: string): Promise<PromptTemplate> {
    // Check cache first
    if (this.templateCache.has(templateId)) {
      return this.templateCache.get(templateId)!;
    }

    // Load from file system
    const templatePath = path.join(this.templatesPath, `${templateId}.json`);
    
    try {
      const templateContent = await fs.readFile(templatePath, 'utf-8');
      const template: PromptTemplate = JSON.parse(templateContent);
      
      // Validate template structure
      this.validateTemplate(template);
      
      // Cache the template
      this.templateCache.set(templateId, template);
      
      return template;
    } catch (error) {
      throw new Error(`Failed to load template '${templateId}': ${error}`);
    }
  }

  /**
   * Save a prompt template
   */
  async saveTemplate(template: PromptTemplate): Promise<void> {
    // Validate template
    this.validateTemplate(template);
    
    // Update timestamps
    template.updated_at = new Date();
    if (!template.created_at) {
      template.created_at = new Date();
    }

    // Save to file system
    const templatePath = path.join(this.templatesPath, `${template.id}.json`);
    await fs.writeFile(templatePath, JSON.stringify(template, null, 2));
    
    // Update cache  
    this.templateCache.set(template.id, template);
    
    console.log(`[PromptManagementService] ✅ Saved template: ${template.id} v${template.version}`);
  }

  /**
   * Execute a prompt template with variables and context injection
   */
  async executeTemplate(
    templateId: string,
    variables: Record<string, any>,
    contextConfig?: ContextInjectionConfig
  ): Promise<{
    result: string;
    execution: PromptExecution;
  }> {
    const startTime = Date.now();
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // Load template
      const template = await this.loadTemplate(templateId);
      
      // Validate input variables
      this.validateVariables(template, variables);
      
      // Inject context if configured
      const enhancedVariables = await this.injectContext(variables, contextConfig);
      
      // Check if we have a website URL to analyze
      const hasWebsiteUrl = variables.website_url && !variables.website_content;
      
      // Prepare the prompt
      let promptContent: string;
      let tools: any[] = [];
      
      if (hasWebsiteUrl && templateId === 'website-analysis') {
        // For website analysis with URL, include the URL in the prompt
        promptContent = this.renderTemplate(template.template, {
          ...enhancedVariables,
          website_content: `Please fetch and analyze the website at ${variables.website_url} to extract business information. Use the website's actual content to determine the business type, products/services, and target market.`
        });
        
        // Use googleSearchRetrieval tool to fetch and ground the response with website content
        tools = [{
          googleSearchRetrieval: {
            dynamicRetrievalConfig: {
              mode: 'MODE_DYNAMIC',
              dynamicThreshold: 0.3
            }
          }
        }];
      } else {
        // Regular prompt rendering
        promptContent = this.renderTemplate(template.template, enhancedVariables);
      }
      
      // Execute with appropriate model - using correct API structure
      const generateParams: any = {
        model: template.model,
        contents: [{ role: 'user', parts: [{ text: promptContent }] }],
        config: {
          temperature: 0.1,  // Lower temperature for consistent results
          maxOutputTokens: 8192,  // Increased for complex responses like question generation
        }
      };

      if (template.output_format === 'json') {
        generateParams.config.responseMimeType = 'application/json';
        
        // Use controlled generation with JSON schema if available
        if (template.output_schema) {
          generateParams.config.responseSchema = template.output_schema;
          console.log(`[PromptManagementService] Using controlled generation schema for ${templateId}:`, JSON.stringify(template.output_schema, null, 2));
        }
      }

      if (tools.length > 0) {
        generateParams.config.tools = tools;
      }

      const result = await this.geminiClient.models.generateContent(generateParams);

      let response = result.text;
      
      // Clean up AI response - remove markdown formatting if present
      if (template.output_format === 'json') {
        response = this.cleanJsonResponse(response);
      }
      
      const executionTime = Date.now() - startTime;

      // Create execution record
      const execution: PromptExecution = {
        template_id: templateId,
        execution_id: executionId,
        input_variables: variables,
        rendered_prompt: promptContent,
        model_response: response,
        execution_time_ms: executionTime,
        token_usage: {
          input_tokens: result.usageMetadata?.promptTokenCount || 0,
          output_tokens: result.usageMetadata?.responseTokenCount || 0,
          total_tokens: result.usageMetadata?.totalTokenCount || 0
        },
        success: true,
        executed_at: new Date()
      };

      // Store execution history
      this.executionHistory.push(execution);
      
      // Validate output format if schema provided
      if (template.output_schema) {
        try {
          this.validateOutput(response, template.output_schema, template.output_format);
        } catch (error) {
          console.warn(`[PromptManagementService] Output validation failed for ${templateId}:`, error);
          // Continue without failing - log the warning but don't block execution
        }
      }

      console.log(`[PromptManagementService] ✅ Executed ${templateId} in ${executionTime}ms`);

      return { result: response, execution };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      // Create failed execution record
      const execution: PromptExecution = {
        template_id: templateId,
        execution_id: executionId,
        input_variables: variables,
        rendered_prompt: '',
        model_response: '',
        execution_time_ms: executionTime,
        success: false,
        error_message: error instanceof Error ? error.message : 'Unknown error',
        executed_at: new Date()
      };

      this.executionHistory.push(execution);
      
      console.error(`[PromptManagementService] ❌ Failed to execute ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * List all available templates
   */
  async listTemplates(): Promise<PromptTemplate[]> {
    try {
      const files = await fs.readdir(this.templatesPath);
      const templateFiles = files.filter(f => f.endsWith('.json'));
      
      const templates: PromptTemplate[] = [];
      
      for (const file of templateFiles) {
        const templateId = path.basename(file, '.json');
        try {
          const template = await this.loadTemplate(templateId);
          templates.push(template);
        } catch (error) {
          console.warn(`[PromptManagementService] Failed to load template ${templateId}:`, error);
        }
      }
      
      return templates.sort((a, b) => a.category.localeCompare(b.category) || a.name.localeCompare(b.name));
    } catch (error) {
      console.error('[PromptManagementService] Failed to list templates:', error);
      return [];
    }
  }

  /**
   * Get templates by category
   */
  async getTemplatesByCategory(category: PromptTemplate['category']): Promise<PromptTemplate[]> {
    const allTemplates = await this.listTemplates();
    return allTemplates.filter(t => t.category === category);
  }

  /**
   * Get execution statistics
   */
  getExecutionStats(): {
    total_executions: number;
    successful_executions: number;
    failed_executions: number;
    average_execution_time: number;
    total_tokens_used: number;
    most_used_templates: Array<{ template_id: string; usage_count: number }>;
  } {
    const totalExecutions = this.executionHistory.length;
    const successfulExecutions = this.executionHistory.filter(e => e.success).length;
    const failedExecutions = totalExecutions - successfulExecutions;
    
    const totalExecutionTime = this.executionHistory.reduce((sum, e) => sum + e.execution_time_ms, 0);
    const averageExecutionTime = totalExecutions > 0 ? totalExecutionTime / totalExecutions : 0;
    
    const totalTokens = this.executionHistory.reduce((sum, e) => sum + (e.token_usage?.total_tokens || 0), 0);
    
    // Calculate most used templates
    const templateUsage = new Map<string, number>();
    this.executionHistory.forEach(e => {
      templateUsage.set(e.template_id, (templateUsage.get(e.template_id) || 0) + 1);
    });
    
    const mostUsedTemplates = Array.from(templateUsage.entries())
      .map(([template_id, usage_count]) => ({ template_id, usage_count }))
      .sort((a, b) => b.usage_count - a.usage_count)
      .slice(0, 10);

    return {
      total_executions: totalExecutions,
      successful_executions: successfulExecutions,
      failed_executions: failedExecutions,
      average_execution_time: Math.round(averageExecutionTime),
      total_tokens_used: totalTokens,
      most_used_templates: mostUsedTemplates
    };
  }

  /**
   * Private helper methods
   */
  private async initializeTemplateDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.templatesPath, { recursive: true });
    } catch (error) {
      console.error('[PromptManagementService] Failed to create templates directory:', error);
    }
  }

  private validateTemplate(template: PromptTemplate): void {
    const required = ['id', 'name', 'template', 'variables', 'category', 'model'];
    
    for (const field of required) {
      if (!template[field as keyof PromptTemplate]) {
        throw new Error(`Template missing required field: ${field}`);
      }
    }

    // Skip model validation - allow any model string per user directive

    // Validate category
    const validCategories = ['transcript_processing', 'similarity_analysis', 'shark_compatibility', 'report_generation', 'user_interaction'];
    if (!validCategories.includes(template.category)) {
      throw new Error(`Invalid category: ${template.category}. Must be one of: ${validCategories.join(', ')}`);
    }
  }

  private validateVariables(template: PromptTemplate, variables: Record<string, any>): void {
    // Check required variables
    for (const [varName, varConfig] of Object.entries(template.variables)) {
      if (varConfig.required && !(varName in variables)) {
        throw new Error(`Missing required variable: ${varName}`);
      }
      
      if (varName in variables) {
        const value = variables[varName];
        
        // Type validation
        if (varConfig.type === 'string' && typeof value !== 'string') {
          throw new Error(`Variable '${varName}' must be a string`);
        }
        if (varConfig.type === 'number' && typeof value !== 'number') {
          throw new Error(`Variable '${varName}' must be a number`);
        }
        if (varConfig.type === 'boolean' && typeof value !== 'boolean') {
          throw new Error(`Variable '${varName}' must be a boolean`);
        }
        
        // Validation rules
        if (varConfig.validation) {
          const validation = varConfig.validation;
          
          if (typeof value === 'string') {
            if (validation.minLength && value.length < validation.minLength) {
              throw new Error(`Variable '${varName}' must be at least ${validation.minLength} characters`);
            }
            if (validation.maxLength && value.length > validation.maxLength) {
              throw new Error(`Variable '${varName}' must be at most ${validation.maxLength} characters`);
            }
            if (validation.pattern && !new RegExp(validation.pattern).test(value)) {
              throw new Error(`Variable '${varName}' does not match required pattern`);
            }
          }
          
          if (validation.enum && !validation.enum.includes(value)) {
            throw new Error(`Variable '${varName}' must be one of: ${validation.enum.join(', ')}`);
          }
        }
      }
    }
  }

  private renderTemplate(template: string, variables: Record<string, any>): string {
    let rendered = template;
    
    // Replace template variables with actual values
    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{{${key}}}`;
      const stringValue = typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value);
      rendered = rendered.replace(new RegExp(placeholder, 'g'), stringValue);
    }
    
    // Check for unreplaced variables
    const unreplacedMatches = rendered.match(/\{\{([^}]+)\}\}/g);
    if (unreplacedMatches) {
      console.warn(`[PromptManagementService] Unreplaced variables found: ${unreplacedMatches.join(', ')}`);
    }
    
    return rendered;
  }

  private async injectContext(
    variables: Record<string, any>,
    contextConfig?: ContextInjectionConfig
  ): Promise<Record<string, any>> {
    if (!contextConfig) return variables;

    const enhancedVariables = { ...variables };

    // Add context sections based on configuration
    if (contextConfig.include_user_context) {
      enhancedVariables._user_context = await this.getUserContext();
    }

    if (contextConfig.include_similar_companies) {
      enhancedVariables._similar_companies = await this.getSimilarCompaniesContext(contextConfig.context_limit);
    }

    if (contextConfig.include_shark_profiles) {
      enhancedVariables._shark_profiles = await this.getSharkProfilesContext();
    }

    if (contextConfig.include_historical_patterns) {
      enhancedVariables._historical_patterns = await this.getHistoricalPatternsContext();
    }

    return enhancedVariables;
  }

  private cleanJsonResponse(response: string): string {
    // Remove markdown code blocks
    let cleaned = response.replace(/```json\s*/g, '').replace(/```\s*$/g, '');
    
    // Remove any leading/trailing whitespace
    cleaned = cleaned.trim();
    
    // Try to extract JSON if there's surrounding text
    const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      cleaned = jsonMatch[0];
    }
    
    // Fix common JSON issues
    try {
      // Test if it's valid JSON first
      JSON.parse(cleaned);
      return cleaned;
    } catch (error) {
      // Try to fix unclosed arrays/objects by balancing brackets
      let balanced = cleaned;
      
      // Count brackets to see what's missing
      const openBraces = (balanced.match(/\{/g) || []).length;
      const closeBraces = (balanced.match(/\}/g) || []).length;
      const openBrackets = (balanced.match(/\[/g) || []).length;
      const closeBrackets = (balanced.match(/\]/g) || []).length;
      
      // Add missing closing brackets
      for (let i = 0; i < openBrackets - closeBrackets; i++) {
        balanced += ']';
      }
      
      // Add missing closing braces
      for (let i = 0; i < openBraces - closeBraces; i++) {
        balanced += '}';
      }
      
      // Test if the balanced version is valid
      try {
        JSON.parse(balanced);
        return balanced;
      } catch (balanceError) {
        // If balancing doesn't work, return original and let the error be thrown
        return cleaned;
      }
    }
  }

  private validateOutput(output: string, schema: any, format: string): void {
    if (format === 'json') {
      try {
        JSON.parse(output);
      } catch (error) {
        throw new Error('Output is not valid JSON');
      }
    }
    
    // Additional schema validation would go here
    // Could integrate with libraries like Joi or Zod for comprehensive validation
  }

  // Context injection helpers - fetch real data from database
  private async getUserContext(): Promise<any> {
    // Fetch real user session context from database
    const { sql } = await import('../database/connection');
    
    try {
      // This would integrate with session management
      // For now, throw error if context is requested but not available
      throw new Error('User context not yet implemented - session management required');
    } catch (error) {
      console.error('[PromptManagementService] Failed to fetch user context:', error);
      throw error;
    }
  }

  private async getSimilarCompaniesContext(limit = 10): Promise<any> {
    // Fetch real similar companies from database
    const { sql } = await import('../database/connection');
    
    try {
      const companies = await sql`
        SELECT company_name, industry, founded_year, business_model
        FROM "sharkTankCompanies"
        WHERE company_name IS NOT NULL
        LIMIT ${limit}
      `;
      
      if (companies.length === 0) {
        throw new Error('No similar companies data available in database');
      }
      
      return { 
        companies: companies,
        total_count: companies.length 
      };
    } catch (error) {
      console.error('[PromptManagementService] Failed to fetch similar companies:', error);
      throw error;
    }
  }

  private async getSharkProfilesContext(): Promise<any> {
    // Fetch real shark profiles from database
    const { sql } = await import('../database/connection');
    
    try {
      const sharkProfiles = await sql`
        SELECT DISTINCT 
          jsonb_array_elements_text(sharks_who_invested) as shark_name
        FROM "sharkTankCompanies"
        WHERE sharks_who_invested IS NOT NULL 
          AND jsonb_array_length(sharks_who_invested) > 0
      `;
      
      if (sharkProfiles.length === 0) {
        throw new Error('No shark profiles data available in database');
      }
      
      // Get unique shark names and their investment counts
      const sharkCounts = {};
      sharkProfiles.forEach(row => {
        const name = row.shark_name;
        sharkCounts[name] = (sharkCounts[name] || 0) + 1;
      });
      
      const profiles = Object.entries(sharkCounts).map(([name, count]) => ({
        name,
        investment_count: count
      }));
      
      return { 
        profiles,
        last_updated: new Date() 
      };
    } catch (error) {
      console.error('[PromptManagementService] Failed to fetch shark profiles:', error);
      throw error;
    }
  }

  private async getHistoricalPatternsContext(): Promise<any> {
    // Fetch real historical patterns from database
    const { sql } = await import('../database/connection');
    
    try {
      const patterns = await sql`
        SELECT 
          industry,
          COUNT(*) as company_count,
          AVG((deal_metrics->>'valuation')::numeric) as avg_valuation,
          AVG((deal_metrics->>'equity_offered')::numeric) as avg_equity
        FROM "sharkTankCompanies"
        WHERE industry IS NOT NULL
        GROUP BY industry
        HAVING COUNT(*) > 1
        ORDER BY company_count DESC
        LIMIT 10
      `;
      
      if (patterns.length === 0) {
        throw new Error('No historical patterns data available in database');
      }
      
      return { 
        patterns,
        confidence_score: patterns.length >= 5 ? 0.85 : 0.65 // Dynamic confidence based on data
      };
    } catch (error) {
      console.error('[PromptManagementService] Failed to fetch historical patterns:', error);
      throw error;
    }
  }
}

/**
 * Singleton instance for global access
 */
export const promptManager = new PromptManagementService();

/**
 * CLI runner for prompt management operations
 */
if (require.main === module) {
  const command = process.argv[2];
  
  switch (command) {
    case 'list':
      promptManager.listTemplates()
        .then(templates => {
          console.log('Available Prompt Templates:');
          console.log('=' .repeat(50));
          templates.forEach(t => {
            console.log(`${t.id} (v${t.version})`);
            console.log(`  Category: ${t.category}`);
            console.log(`  Model: ${t.model}`);
            console.log(`  Description: ${t.description}`);
            console.log(`  Variables: ${Object.keys(t.variables).length}`);
            console.log('');
          });
          process.exit(0);
        })
        .catch(error => {
          console.error('Failed to list templates:', error);
          process.exit(1);
        });
      break;

    case 'stats':
      const stats = promptManager.getExecutionStats();
      console.log('Prompt Execution Statistics:');
      console.log('=' .repeat(40));
      console.log(`Total Executions: ${stats.total_executions}`);
      console.log(`Success Rate: ${stats.total_executions > 0 ? Math.round((stats.successful_executions / stats.total_executions) * 100) : 0}%`);
      console.log(`Average Execution Time: ${stats.average_execution_time}ms`);
      console.log(`Total Tokens Used: ${stats.total_tokens_used.toLocaleString()}`);
      console.log('\nMost Used Templates:');
      stats.most_used_templates.forEach((template, i) => {
        console.log(`  ${i+1}. ${template.template_id}: ${template.usage_count} uses`);
      });
      process.exit(0);
      break;

    case 'test':
      const templateId = process.argv[3];
      if (!templateId) {
        console.error('Usage: bun src/services/promptManagementService.ts test <template_id>');
        process.exit(1);
      }
      
      promptManager.loadTemplate(templateId)
        .then(template => {
          console.log(`Template: ${template.name} (v${template.version})`);
          console.log(`Category: ${template.category}`);
          console.log(`Model: ${template.model}`);
          console.log(`Variables: ${Object.keys(template.variables).join(', ')}`);
          console.log(`\nTemplate Preview:`);
          console.log(template.template.substring(0, 200) + '...');
          process.exit(0);
        })
        .catch(error => {
          console.error(`Failed to load template '${templateId}':`, error);
          process.exit(1);
        });
      break;

    default:
      console.log('Prompt Management Service CLI');
      console.log('Usage: bun src/services/promptManagementService.ts <command>');
      console.log('');
      console.log('Commands:');
      console.log('  list              List all available prompt templates');
      console.log('  stats             Show execution statistics');
      console.log('  test <template>   Test load a specific template');
      console.log('');
      console.log('Examples:');
      console.log('  bun src/services/promptManagementService.ts list');
      console.log('  bun src/services/promptManagementService.ts test company-analysis');
      process.exit(1);
  }
}