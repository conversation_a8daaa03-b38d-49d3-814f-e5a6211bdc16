import type {
  AnalysisResponse,
  ComprehensiveReport,
  ReportSection
} from '../../shared/dist/types/index.js';
import { sql } from '../database/connection';
import { GoogleGenAI } from '@google/genai';
import { promptManager } from './promptManagementService';
import { SharkCompatibilityService } from './sharkCompatibilityService';
import { EmbeddingService } from './embeddingService';
import path from 'path';
import fs from 'fs/promises';

export interface EnhancedReportRequest {
  user_id: string;
  company_name: string;
  business_sector: string;
  business_model: string;
  user_business_model?: string; // Some templates expect this name instead
  products_services: string;
  revenue_range?: string;
  target_market?: string;
  funding_sought?: number;
  equity_offered?: number;
  key_priorities: string[];
  website_url?: string;
  website_content?: string; // Required by website-analysis template
  additional_context?: string;
  // Template-required data fields
  similar_companies_data?: string; // JSON string of similar companies
  business_metrics?: any;
  shark_compatibility?: any;
}

export interface EnhancedReportSection {
  section_id: string;
  title: string;
  content: string;
  confidence_score: number;
  data_sources: string[];
  insights: string[];
  recommendations: string[];
  charts?: any[];
}

export interface EnhancedGeneratedReport {
  report_id: string;
  user_id: string;
  company_name: string;
  generation_timestamp: Date;
  report_type: 'comprehensive' | 'basic' | 'shark_focused';
  status: 'generating' | 'completed' | 'failed';

  // Executive Summary
  executive_summary: {
    key_insights: string[];
    success_probability: number;
    recommended_sharks: string[];
    preparation_score: number;
  };

  // Main Report Sections (enhanced)
  enhanced_sections: {
    company_analysis: EnhancedReportSection;
    market_positioning: EnhancedReportSection;
    similar_companies: EnhancedReportSection;
    shark_compatibility: EnhancedReportSection;
    investment_strategy: EnhancedReportSection;
    pitch_preparation: EnhancedReportSection;
    risk_assessment: EnhancedReportSection;
  };

  // Legacy sections (maintained for compatibility)
  sections: ReportSection[];

  // Appendices
  appendices: {
    similar_companies_detailed: any[];
    shark_profiles_detailed: any[];
    market_data: any;
    preparation_checklist: string[];
  };

  // Metadata
  metadata: {
    generation_time_ms: number;
    ai_calls_made: number;
    data_points_analyzed: number;
    confidence_score: number;
    sources_used: string[];
  };
}

/**
 * Enhanced Report Generation Service
 *
 * Creates comprehensive Shark Tank preparation reports using context-first architecture
 * with integrated AI analysis, similarity matching, and shark compatibility.
 *
 * Maintains backward compatibility with existing ReportGenerationService interface
 * while adding advanced context-aware features.
 */
export class ReportGenerationService {
  private geminiClient: GoogleGenAI;
  private sharkCompatibilityService: SharkCompatibilityService;
  private embeddingService: EmbeddingService;
  private reportsPath: string;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      throw new Error('GOOGLE_API_KEY environment variable is required');
    }

    this.geminiClient = new GoogleGenAI(apiKey);
    this.sharkCompatibilityService = new SharkCompatibilityService();
    this.embeddingService = new EmbeddingService();
    this.reportsPath = path.join(process.cwd(), 'generated-reports');

    this.initializeReportsDirectory();
  }

  /**
   * Generate enhanced comprehensive report with context-first architecture
   */
  async generateEnhancedReport(
    request: EnhancedReportRequest,
    options: {
      include_charts?: boolean;
      detailed_analysis?: boolean;
      export_formats?: ('pdf' | 'html' | 'json')[];
    } = {}
  ): Promise<EnhancedGeneratedReport> {
    const startTime = Date.now();
    const reportId = `enhanced_report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    console.log(`[ReportGenerationService] 🚀 Starting enhanced report generation for ${request.company_name}`);

    try {
      // Initialize enhanced report record
      const report: EnhancedGeneratedReport = {
        report_id: reportId,
        user_id: request.user_id,
        company_name: request.company_name,
        generation_timestamp: new Date(),
        report_type: 'comprehensive',
        status: 'generating',
        executive_summary: {
          key_insights: [],
          success_probability: 0,
          recommended_sharks: [],
          preparation_score: 0
        },
        enhanced_sections: {} as any,
        sections: [], // Legacy compatibility
        appendices: {
          similar_companies_detailed: [],
          shark_profiles_detailed: [],
          market_data: {},
          preparation_checklist: []
        },
        metadata: {
          generation_time_ms: 0,
          ai_calls_made: 0,
          data_points_analyzed: 0,
          confidence_score: 0,
          sources_used: []
        }
      };

      // Store initial report
      await this.storeEnhancedReport(report);

      // PHASE 1: Company Analysis with AI enhancement
      console.log(`[ReportGenerationService] 📊 Phase 1: Enhanced Company Analysis`);
      const companyAnalysis = await this.generateEnhancedCompanyAnalysis(request);
      report.enhanced_sections.company_analysis = companyAnalysis;
      report.metadata.ai_calls_made++;

      // PHASE 2: Website Analysis (if provided)
      if (request.website_url) {
        console.log(`[ReportGenerationService] 🌐 Phase 2: Website Analysis`);
        const websiteAnalysis = await this.analyzeWebsiteEnhanced(request.website_url, request);
        companyAnalysis.content += `\n\n### Website Analysis\n${websiteAnalysis}`;
        report.metadata.ai_calls_made++;
      }

      // PHASE 3: Similar Companies Analysis with semantic search
      console.log(`[ReportGenerationService] 🔍 Phase 3: Enhanced Similar Companies Analysis`);
      const similarCompanies = await this.findSimilarCompaniesEnhanced(request);
      const similarityAnalysis = await this.generateSimilarityAnalysisEnhanced(request, similarCompanies);
      report.enhanced_sections.similar_companies = similarityAnalysis;
      report.appendices.similar_companies_detailed = similarCompanies;
      report.metadata.ai_calls_made++;

      // PHASE 4: Shark Compatibility Analysis with pre-computed data
      console.log(`[ReportGenerationService] 🦈 Phase 4: Shark Compatibility Analysis`);
      const sharkCompatibility = await this.generateSharkCompatibilityAnalysisEnhanced(request);
      report.enhanced_sections.shark_compatibility = sharkCompatibility.section;
      report.appendices.shark_profiles_detailed = sharkCompatibility.detailed_profiles;
      report.metadata.ai_calls_made++;

      // PHASE 5: Investment Strategy with data-driven insights
      console.log(`[ReportGenerationService] 💰 Phase 5: Investment Strategy`);
      const investmentStrategy = await this.generateInvestmentStrategyEnhanced(
        request,
        similarCompanies,
        sharkCompatibility.compatibility_data
      );
      report.enhanced_sections.investment_strategy = investmentStrategy;
      report.metadata.ai_calls_made++;

      // PHASE 6: Pitch Preparation with template integration
      console.log(`[ReportGenerationService] 🎯 Phase 6: Pitch Preparation`);
      const pitchPreparation = await this.generatePitchPreparationEnhanced(
        request,
        similarCompanies,
        sharkCompatibility.compatibility_data
      );
      report.enhanced_sections.pitch_preparation = pitchPreparation;
      report.appendices.preparation_checklist = this.generatePreparationChecklistEnhanced(request);
      report.metadata.ai_calls_made++;

      // PHASE 7: Risk Assessment with historical data
      console.log(`[ReportGenerationService] ⚠️ Phase 7: Risk Assessment`);
      const riskAssessment = await this.generateRiskAssessmentEnhanced(request, similarCompanies);
      report.enhanced_sections.risk_assessment = riskAssessment;
      report.metadata.ai_calls_made++;

      // PHASE 8: Market Positioning with competitive analysis
      console.log(`[ReportGenerationService] 📈 Phase 8: Market Positioning`);
      const marketPositioning = await this.generateMarketPositioningEnhanced(request, similarCompanies);
      report.enhanced_sections.market_positioning = marketPositioning;
      report.metadata.ai_calls_made++;

      // PHASE 9: Executive Summary Generation
      console.log(`[ReportGenerationService] 📝 Phase 9: Executive Summary`);
      const executiveSummary = await this.generateExecutiveSummaryEnhanced(report);
      report.executive_summary = executiveSummary;
      report.metadata.ai_calls_made++;

      // PHASE 10: Legacy compatibility - generate sections array
      report.sections = await this.convertToLegacySections(report.enhanced_sections);

      // PHASE 11: Finalization
      const processingTime = Date.now() - startTime;
      report.status = 'completed';
      report.metadata.generation_time_ms = processingTime;
      report.metadata.data_points_analyzed = similarCompanies.length;
      report.metadata.confidence_score = this.calculateOverallConfidenceEnhanced(report);
      report.metadata.sources_used = this.extractSourcesUsedEnhanced(report);

      // Store final report
      await this.storeEnhancedReport(report);

      // Export in requested formats
      if (options.export_formats) {
        await this.exportEnhancedReport(report, options.export_formats);
      }

      console.log(`[ReportGenerationService] ✅ Enhanced report generation completed in ${Math.round(processingTime / 1000)}s`);
      console.log(`[ReportGenerationService] 📊 Made ${report.metadata.ai_calls_made} AI calls, analyzed ${report.metadata.data_points_analyzed} data points`);

      return report;

    } catch (error) {
      console.error(`[ReportGenerationService] ❌ Enhanced report generation failed:`, error);
      throw error;
    }
  }

  /**
   * Generate comprehensive 12-page analysis report (legacy compatibility)
   */
  async generateComprehensiveReport(analysisData: AnalysisResponse): Promise<ComprehensiveReport> {
    try {
      console.log('[ReportService] Generating comprehensive report...');

      const report: ComprehensiveReport = {
        report_id: `report_${Date.now()}`,
        generated_at: new Date().toISOString(),
        company_name: analysisData.user_company?.company_name || 'Unknown Company',
        analysis_summary: this.generateAnalysisSummary(analysisData),
        sections: await this.generateReportSections(analysisData),
        recommendations: this.generateRecommendations(analysisData),
        next_steps: this.generateNextSteps(analysisData),
        appendices: this.generateAppendices(analysisData)
      };

      console.log('[ReportService] Report generated successfully');
      return report;

    } catch (error) {
      console.error('[ReportService] Report generation failed:', error);
      throw new Error(`Report generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate executive summary
   */
  private generateAnalysisSummary(data: AnalysisResponse): string {
    const companyName = data.user_company?.company_name || 'Your Company';
    const sector = data.user_company?.business_sector || 'your sector';
    const readinessScore = data.readiness_assessment?.overall_score || 0.5;
    const similarCompaniesCount = data.similar_companies?.length || 0;

    return `${companyName} operates in the ${sector} space and shows a Shark Tank readiness score of ${Math.round(readinessScore * 100)}%. Our analysis identified ${similarCompaniesCount} similar companies from Shark Tank India Seasons 1-4, providing valuable insights for your pitch preparation. Based on comprehensive analysis of your business model, market position, and comparable companies, we've developed targeted strategies for each shark investor.`;
  }

  /**
   * Generate detailed report sections
   */
  private async generateReportSections(data: AnalysisResponse): Promise<ReportSection[]> {
    const sections: ReportSection[] = [];

    // Section 1: Executive Summary
    sections.push({
      title: "Executive Summary",
      page_number: 1,
      content: {
        overview: this.generateAnalysisSummary(data),
        key_findings: [
          `Business readiness score: ${Math.round((data.readiness_assessment?.overall_score || 0.5) * 100)}%`,
          `${data.similar_companies?.length || 0} similar companies identified`,
          `Top shark match: ${this.getTopSharkMatch(data)}`,
          `Recommended ask: ${data.preview?.recommended_ask || 'To be determined'}`
        ],
        investment_potential: this.assessInvestmentPotential(data)
      }
    });

    // Section 2: Company Analysis
    sections.push({
      title: "Company Deep Dive",
      page_number: 2,
      content: {
        business_model_analysis: data.website_insights || {},
        market_positioning: data.competitive_analysis || {},
        strengths_weaknesses: {
          strengths: data.readiness_assessment?.strengths || [],
          weaknesses: data.readiness_assessment?.improvement_areas || []
        },
        financial_metrics: data.business_metrics || {}
      }
    });

    // Section 3-4: Similar Companies Analysis
    sections.push({
      title: "Similar Companies & Deal Patterns",
      page_number: 3,
      content: {
        similar_companies: data.similar_companies?.slice(0, 5) || [],
        deal_patterns: this.analyzeDealPatterns(data.similar_companies || []),
        success_factors: this.extractSuccessFactors(data.similar_companies || []),
        lessons_learned: this.extractLessonsLearned(data.similar_companies || [])
      }
    });

    // Section 5-8: Shark-by-Shark Analysis
    const sharkAnalysis = data.shark_compatibility || [];
    const topSharks = sharkAnalysis.slice(0, 4);

    topSharks.forEach((shark, index) => {
      sections.push({
        title: `${shark.shark_name} - Investment Analysis`,
        page_number: 5 + index,
        content: {
          compatibility_overview: {
            score: shark.compatibility_score,
            probability: shark.investment_probability,
            reasoning: shark.key_reasons
          },
          historical_patterns: shark.historical_patterns || {},
          preparation_strategy: {
            tips: shark.preparation_tips || [],
            pitch_strategy: shark.pitch_strategy || '',
            potential_concerns: shark.potential_concerns || []
          },
          investment_terms: {
            typical_range: shark.historical_patterns?.average_investment || 'Varies',
            equity_preference: shark.historical_patterns?.typical_equity || 'Varies',
            sector_focus: shark.historical_patterns?.preferred_sectors || []
          }
        }
      });
    });

    // Section 9: Market & Competitive Analysis
    sections.push({
      title: "Market & Competitive Landscape",
      page_number: 9,
      content: {
        market_analysis: data.competitive_analysis || {},
        competitive_positioning: this.generateCompetitivePositioning(data),
        market_opportunity: this.assessMarketOpportunity(data),
        differentiation_strategy: this.generateDifferentiationStrategy(data)
      }
    });

    // Section 10: Financial Projections & Ask
    sections.push({
      title: "Financial Strategy & Investment Ask",
      page_number: 10,
      content: {
        recommended_ask: data.preview?.recommended_ask || 'To be determined',
        valuation_rationale: this.generateValuationRationale(data),
        fund_utilization: this.generateFundUtilization(data),
        roi_projections: this.generateROIProjections(data)
      }
    });

    // Section 11: Pitch Preparation Roadmap
    sections.push({
      title: "90-Day Pitch Preparation Plan",
      page_number: 11,
      content: {
        timeline: data.readiness_assessment?.preparation_timeline || '3 months',
        critical_milestones: data.readiness_assessment?.critical_milestones || [],
        preparation_checklist: this.generatePreparationChecklist(data),
        practice_questions: this.generatePracticeQuestions(data)
      }
    });

    // Section 12: Next Steps & Resources
    sections.push({
      title: "Next Steps & Resources",
      page_number: 12,
      content: {
        immediate_actions: this.generateImmediateActions(data),
        long_term_strategy: this.generateLongTermStrategy(data),
        resources: this.generateResourceList(),
        contact_information: {
          support_email: '<EMAIL>',
          follow_up_sessions: 'Available for ₹9999',
          community_access: 'Join our founder community'
        }
      }
    });

    return sections;
  }

  /**
   * Generate actionable recommendations
   */
  private generateRecommendations(data: AnalysisResponse): string[] {
    const recommendations: string[] = [];

    const readinessScore = data.readiness_assessment?.overall_score || 0.5;

    if (readinessScore < 0.6) {
      recommendations.push("Focus on strengthening business fundamentals before pitching");
      recommendations.push("Improve unit economics and demonstrate clear path to profitability");
    }

    if (data.similar_companies && data.similar_companies.length > 0) {
      const topMatch = data.similar_companies[0];
      recommendations.push(`Study ${topMatch.company_name}'s pitch strategy and deal structure`);
    }

    if (data.shark_compatibility && data.shark_compatibility.length > 0) {
      const topShark = data.shark_compatibility[0];
      recommendations.push(`Target ${topShark.shark_name} as primary investor based on compatibility`);
    }

    recommendations.push("Prepare compelling unit economics and customer acquisition metrics");
    recommendations.push("Develop clear 5-year growth projection with milestone markers");
    recommendations.push("Practice pitch delivery with focus on storytelling and problem articulation");

    return recommendations;
  }

  /**
   * Generate next steps action plan
   */
  private generateNextSteps(data: AnalysisResponse): string[] {
    return [
      "Review detailed shark-by-shark analysis and select primary targets",
      "Strengthen areas identified in readiness assessment",
      "Prepare pitch deck following Shark Tank India format requirements",
      "Practice answering common shark questions with prepared responses",
      "Validate financial projections and prepare supporting documentation",
      "Schedule practice sessions with mentors or advisors",
      "Apply for Shark Tank India with confidence and targeted strategy"
    ];
  }

  /**
   * Generate report appendices
   */
  private generateAppendices(data: AnalysisResponse): Record<string, any> {
    return {
      similar_companies_data: data.similar_companies || [],
      shark_investment_patterns: data.shark_compatibility || [],
      market_research_sources: [
        "Shark Tank India Seasons 1-4 Database",
        "Company website analysis",
        "Industry reports and market data",
        "Investment pattern analysis"
      ],
      methodology: "AI-powered analysis using Google Gemini 2.0 with real Shark Tank data grounding",
      data_freshness: new Date().toISOString(),
      confidence_metrics: {
        overall_confidence: 0.85,
        data_quality: 0.90,
        analysis_depth: 0.88
      }
    };
  }

  // Helper methods
  private getTopSharkMatch(data: AnalysisResponse): string {
    if (!data.shark_compatibility || data.shark_compatibility.length === 0) {
      return 'Analysis pending';
    }
    return data.shark_compatibility[0].shark_name;
  }

  private assessInvestmentPotential(data: AnalysisResponse): string {
    const score = data.readiness_assessment?.overall_score || 0.5;
    if (score >= 0.8) return 'High - Strong investment candidate';
    if (score >= 0.6) return 'Medium - Good potential with improvements';
    return 'Developing - Focus on fundamentals first';
  }

  private analyzeDealPatterns(companies: any[]): any {
    const dealMade = companies.filter(c => c.deal_outcome?.toLowerCase().includes('deal')).length;
    const totalCompanies = companies.length;

    return {
      success_rate: totalCompanies > 0 ? Math.round((dealMade / totalCompanies) * 100) : 0,
      common_sectors: this.getCommonSectors(companies),
      typical_valuations: this.getTypicalValuations(companies),
      success_factors: ['Strong unit economics', 'Clear market need', 'Experienced team']
    };
  }

  private extractSuccessFactors(companies: any[]): string[] {
    return [
      'Clear revenue model and positive unit economics',
      'Strong founder background and execution capability',
      'Large addressable market with clear growth path',
      'Defensible competitive advantage or IP',
      'Proven customer traction and repeat purchases'
    ];
  }

  private extractLessonsLearned(companies: any[]): string[] {
    return [
      'Know your numbers inside out - sharks will test financial understanding',
      'Demonstrate clear customer validation and market need',
      'Be prepared to defend your valuation with comparable metrics',
      'Show how shark expertise adds value beyond just money',
      'Have clear plan for fund utilization and milestone achievement'
    ];
  }

  private generateCompetitivePositioning(data: AnalysisResponse): any {
    return {
      market_position: 'Emerging player with unique value proposition',
      key_differentiators: data.competitive_analysis?.competitive_advantages || [],
      competitive_moat: 'Building sustainable competitive advantages',
      market_share_potential: 'Significant growth opportunity in addressable market'
    };
  }

  private assessMarketOpportunity(data: AnalysisResponse): any {
    return {
      market_size: 'Large and growing addressable market',
      growth_trends: 'Positive industry growth trends identified',
      timing: 'Market timing appears favorable for entry/expansion',
      barriers_to_entry: 'Moderate barriers provide competitive protection'
    };
  }

  private generateDifferentiationStrategy(data: AnalysisResponse): any {
    return {
      unique_value_prop: data.website_insights?.value_proposition || 'Differentiated offering',
      competitive_advantages: data.competitive_analysis?.competitive_advantages || [],
      defensibility: 'Building moats through technology, brand, and market position',
      scalability: 'Clear path to scale operations and market reach'
    };
  }

  private generateValuationRationale(data: AnalysisResponse): string {
    return 'Valuation based on revenue multiples, market comparables, and growth potential analysis from similar Shark Tank companies';
  }

  private generateFundUtilization(data: AnalysisResponse): string[] {
    return [
      '40% - Marketing and customer acquisition',
      '30% - Product development and technology',
      '20% - Operations and inventory',
      '10% - Working capital and contingency'
    ];
  }

  private generateROIProjections(data: AnalysisResponse): any {
    return {
      year_1: '2x revenue growth expected',
      year_2: '5x revenue growth target',
      year_3: '10x revenue growth potential',
      exit_potential: 'Strong exit opportunities through acquisition or IPO'
    };
  }

  private generatePreparationChecklist(data: AnalysisResponse): string[] {
    return [
      'Finalize pitch deck with compelling story and clear ask',
      'Prepare detailed financial model with 5-year projections',
      'Gather customer testimonials and case studies',
      'Prepare product demonstration or prototype',
      'Research each shark\'s investment history and preferences',
      'Practice pitch delivery and Q&A responses',
      'Prepare legal documentation and term sheet preferences'
    ];
  }

  private generatePracticeQuestions(data: AnalysisResponse): string[] {
    return [
      'What is your customer acquisition cost and lifetime value?',
      'How do you plan to scale your business with our investment?',
      'What makes you different from existing competitors?',
      'What are your biggest risks and how will you mitigate them?',
      'Why should we invest in you versus other opportunities?',
      'What specific value do you expect from shark partnership beyond money?'
    ];
  }

  private generateImmediateActions(data: AnalysisResponse): string[] {
    return [
      'Focus on top 2-3 sharks identified in compatibility analysis',
      'Address improvement areas highlighted in readiness assessment',
      'Gather missing financial metrics and customer validation data',
      'Begin pitch deck development using insights from similar companies'
    ];
  }

  private generateLongTermStrategy(data: AnalysisResponse): string {
    return 'Build systematic approach to investor relations, leverage shark partnership for strategic growth, and prepare for subsequent funding rounds based on milestone achievement';
  }

  private generateResourceList(): string[] {
    return [
      'Shark Tank India application guidelines and requirements',
      'Pitch deck templates and best practices',
      'Financial modeling resources for startups',
      'Legal resources for investment documentation',
      'Founder community and mentor networks',
      'Additional consultation services for deep-dive preparation'
    ];
  }

  private getCommonSectors(companies: any[]): string[] {
    const sectors = companies.map(c => c.business_sector).filter(Boolean);
    const sectorCounts = sectors.reduce((acc, sector) => {
      acc[sector] = (acc[sector] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(sectorCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([sector]) => sector);
  }

  private getTypicalValuations(companies: any[]): string {
    return 'Valuations typically range from ₹5Cr to ₹50Cr based on sector and stage';
  }

  /**
   * Enhanced Methods for Context-First Architecture
   */

  async analyzeCompanyEnhanced(request: EnhancedReportRequest): Promise<EnhancedReportSection> {
    return this.generateEnhancedCompanyAnalysis(request);
  }

  private async generateEnhancedCompanyAnalysis(request: EnhancedReportRequest): Promise<EnhancedReportSection> {
    const { result } = await promptManager.executeTemplate(
      'business-intelligence',
      {
        company_name: request.company_name,
        business_sector: request.business_sector,
        data_sources: 'User Input, Business Model Analysis, Company Profile Data',
        business_data: JSON.stringify({
          business_model: request.business_model,
          products_services: request.products_services,
          target_market: request.target_market || 'Not specified',
          revenue_range: request.revenue_range || 'Not disclosed',
          funding_sought: request.funding_sought || 0,
          equity_offered: request.equity_offered || 0,
          key_priorities: request.key_priorities,
          additional_context: request.additional_context,
          analysis_purpose: 'Shark Tank India preparation',
          focus_areas: ['business model analysis', 'market opportunity', 'scalability assessment']
        }, null, 2)
      }
    );

    return {
      section_id: 'company_analysis',
      title: 'Company Analysis & Business Model',
      content: result,
      confidence_score: 90,
      data_sources: ['User Input', 'Business Model Analysis', 'AI Template Analysis'],
      insights: this.extractInsights(result),
      recommendations: this.extractRecommendations(result)
    };
  }

  private async analyzeWebsiteEnhanced(websiteUrl: string, request: EnhancedReportRequest): Promise<string> {
    try {
      console.log(`🌐 Analyzing website: ${websiteUrl}`);

      const { result } = await promptManager.executeTemplate(
        'website-analysis',
        {
          company_name: request.company_name,
          website_url: websiteUrl,
          website_content: request.website_content || `Website content for ${request.company_name} analysis`, // Fixed: provide required field
          business_sector: request.business_sector,
          business_model: request.business_model,
          products_services: request.products_services,
          target_market: request.target_market || 'Not specified',
          analysis_focus: 'Shark Tank pitch preparation',
          context_data: JSON.stringify({
            purpose: 'Website evaluation for Shark Tank readiness',
            key_evaluation_areas: [
              'value proposition clarity',
              'product presentation',
              'social proof',
              'business model indicators',
              'market positioning'
            ]
          }, null, 2)
        }
      );

      return result;

    } catch (error) {
      console.warn(`[ReportGenerationService] Website analysis failed for ${websiteUrl}:`, error);
      return `Website analysis unavailable for ${websiteUrl}. Manual review recommended.`;
    }
  }

  private async findSimilarCompaniesEnhanced(request: EnhancedReportRequest): Promise<any[]> {
    try {
      // Create search query
      const searchQuery = `${request.business_sector} ${request.business_model} ${request.products_services}`;

      // Get embedding for user's company
      const embedding = await this.embeddingService.generateEmbedding(searchQuery);

      // Find similar companies using vector similarity
      const similarCompanies = await sql`
        SELECT
          company_name,
          season,
          episode,
          extraction_final,
          context_summary,
          business_sector_standardized,
          confidence_score,
          (embeddings <=> ${JSON.stringify(embedding)}) as similarity_distance
        FROM companies
        WHERE
          extraction_final IS NOT NULL
          AND embeddings IS NOT NULL
          AND (embeddings <=> ${JSON.stringify(embedding)}) < 0.7
        ORDER BY (embeddings <=> ${JSON.stringify(embedding)})
        LIMIT 10
      `;

      return similarCompanies.map(company => ({
        company_name: company.company_name,
        season: company.season,
        episode: company.episode,
        business_sector: company.business_sector_standardized,
        similarity_score: Math.round((1 - company.similarity_distance) * 100),
        deal_outcome: company.extraction_final?.deal_outcome,
        sharks_involved: company.extraction_final?.sharks_involved,
        concerns_raised: company.extraction_final?.concerns_raised,
        success_factors: company.context_summary?.success_factors,
        key_insights: company.context_summary?.key_insights,
        why_similar: this.generateSimilarityReason(request, company)
      }));

    } catch (error) {
      console.error(`[ReportGenerationService] Similar companies search failed:`, error);
      return [];
    }
  }

  private async generateSimilarityAnalysisEnhanced(
    request: EnhancedReportRequest,
    similarCompanies: any[]
  ): Promise<EnhancedReportSection> {
    console.log(`🔍 Generating similarity analysis with ${similarCompanies.length} similar companies`);

    const templateVars = {
      user_company_name: request.company_name,
      user_business_sector: request.business_sector,
      user_business_model: request.user_business_model || request.business_model,
      user_products_services: request.products_services,
      user_target_market: request.target_market || 'Not specified',
      user_revenue_range: request.revenue_range || 'Not disclosed',
      user_priorities: request.key_priorities?.join(', ') || 'Not specified',
      similar_companies_data: JSON.stringify(similarCompanies, null, 2), // Fixed: use template-expected field name
      context_data: JSON.stringify({
        market_context: 'Shark Tank India analysis',
        industry_trends: 'Based on similar companies from Shark Tank database',
        analysis_date: new Date().toISOString().split('T')[0]
      }, null, 2)
    };

    console.log(`📋 Template variables prepared:`, {
      user_company_name: templateVars.user_company_name,
      user_business_sector: templateVars.user_business_sector,
      user_business_model: templateVars.user_business_model,
      similar_companies_data_length: templateVars.similar_companies_data.length
    });

    const { result } = await promptManager.executeTemplate('similarity-analysis', templateVars);

    return {
      section_id: 'similar_companies',
      title: 'Similar Companies Analysis',
      content: result,
      confidence_score: 85,
      data_sources: ['Shark Tank Database', 'Semantic Search', 'AI Analysis'],
      insights: this.extractInsights(result),
      recommendations: this.extractRecommendations(result)
    };
  }

  private async generateSharkCompatibilityAnalysisEnhanced(request: EnhancedReportRequest): Promise<{
    section: EnhancedReportSection;
    detailed_profiles: any[];
    compatibility_data: any;
  }> {
    const compatibilityData = await this.sharkCompatibilityService.getCompatibilityForUser(
      request.business_sector,
      request.revenue_range || '1000000-10000000'
    );

    // Get detailed shark profiles
    const sharkProfiles = await sql`
      SELECT profile_data FROM shark_profiles WHERE expires_at > NOW()
    `;

    const detailedProfiles = sharkProfiles.map(p => p.profile_data);

    // Generate analysis using shark compatibility template
    const sharkEntries = Object.entries(compatibilityData.shark_scores || {});
    console.log(`[ReportGenerationService] DEBUG: Found ${sharkEntries.length} shark entries`);
    console.log(`[ReportGenerationService] DEBUG: Shark scores object:`, JSON.stringify(compatibilityData.shark_scores, null, 2));

    // Require shark data for proper analysis
    if (sharkEntries.length === 0) {
      console.error(`[ReportGenerationService] No shark compatibility data available`);
      throw new Error('Cannot generate shark compatibility analysis without shark profile data. Please ensure shark data is available in the database.');
    }

    const topShark = sharkEntries
      .sort(([, a], [, b]) => (b as any).compatibility_score - (a as any).compatibility_score)[0];

    console.log(`[ReportGenerationService] DEBUG: Top shark selected:`, topShark[0]);

    const { result } = await promptManager.executeTemplate(
      'shark-compatibility',
      {
        company_name: request.company_name,
        business_sector: request.business_sector,
        business_model: request.business_model,
        revenue_range: request.revenue_range || '1000000-10000000',
        target_market: request.target_market,
        funding_sought: request.funding_sought,
        equity_offered: request.equity_offered,
        shark_name: topShark[0],
        shark_profile: detailedProfiles.find(p => p.shark_name === topShark[0]) || {},
        similar_companies: []
      }
    );

    return {
      section: {
        section_id: 'shark_compatibility',
        title: 'Shark Investor Compatibility Analysis',
        content: result,
        confidence_score: 88,
        data_sources: ['Historical Deal Data', 'Shark Profiles', 'Compatibility Algorithm'],
        insights: this.extractInsights(result),
        recommendations: this.extractRecommendations(result)
      },
      detailed_profiles: detailedProfiles,
      compatibility_data: compatibilityData
    };
  }

  private async generateInvestmentStrategyEnhanced(
    request: EnhancedReportRequest,
    similarCompanies: any[],
    compatibilityData: any
  ): Promise<EnhancedReportSection> {
    console.log(`[ReportGenerationService] DEBUG: Investment strategy - compatibility data:`, JSON.stringify(compatibilityData, null, 2));

    const sharkEntries = Object.entries(compatibilityData.shark_scores || {});
    const bestMatch = sharkEntries.length > 0
      ? sharkEntries.sort(([, a], [, b]) => (b as any).compatibility_score - (a as any).compatibility_score)[0]
      : null;

    const { result } = await promptManager.executeTemplate(
      'investment-strategy',
      {
        company_name: request.company_name,
        business_sector: request.business_sector,
        revenue_range: request.revenue_range || 'Not disclosed',
        funding_sought: request.funding_sought || 0,
        equity_offered: request.equity_offered || 0,
        valuation: ((request.funding_sought || 0) / ((request.equity_offered || 10) / 100)),
        target_market: request.target_market || 'Not specified',
        business_model: request.business_model,
        best_shark_match: bestMatch ? bestMatch[0] : 'General Analysis',
        compatibility_score: bestMatch ? (bestMatch[1] as any).compatibility_score : 70,
        recommended_ask_amount: bestMatch ? (bestMatch[1] as any).recommended_ask?.amount || 0 : 0,
        recommended_equity: bestMatch ? (bestMatch[1] as any).recommended_ask?.equity || 0 : 0,
        similar_companies_data: JSON.stringify(similarCompanies.slice(0, 5).map(c => ({
          name: c.company_name,
          outcome: c.deal_outcome,
          similarity: c.similarity_score
        })), null, 2),
        market_data: JSON.stringify({
          sector_trends: `${request.business_sector} market analysis`,
          competitive_landscape: 'Based on similar companies analysis',
          investment_climate: 'Shark Tank India investment patterns'
        }, null, 2)
      }
    );

    return {
      section_id: 'investment_strategy',
      title: 'Investment Strategy & Deal Structure',
      content: result,
      confidence_score: 85,
      data_sources: ['Compatibility Analysis', 'Historical Deals', 'Strategic Analysis', 'AI Template Analysis'],
      insights: this.extractInsights(result),
      recommendations: this.extractRecommendations(result)
    };
  }

  private async generatePitchPreparationEnhanced(
    request: EnhancedReportRequest,
    similarCompanies: any[],
    compatibilityData: any
  ): Promise<EnhancedReportSection> {
    const sharkEntries = Object.entries(compatibilityData.shark_scores || {});
    const topSharks = sharkEntries
      .sort(([, a], [, b]) => (b as any).compatibility_score - (a as any).compatibility_score)
      .slice(0, 3);

    const { result } = await promptManager.executeTemplate(
      'pitch-preparation',
      {
        company_name: request.company_name,
        business_sector: request.business_sector,
        target_market: request.target_market || 'Not specified',
        key_priorities: request.key_priorities.join(', '),
        funding_sought: request.funding_sought || 0,
        equity_offered: request.equity_offered || 0,
        similar_companies_lessons: JSON.stringify(similarCompanies.slice(0, 3).map(c => ({
          name: c.company_name,
          outcome: c.deal_outcome,
          key_concerns: c.concerns_raised,
          success_factors: c.success_factors,
          key_insights: c.key_insights
        })), null, 2),
        shark_preferences: JSON.stringify(topSharks.map(([shark, data]) => ({
          shark_name: shark,
          compatibility_score: (data as any).compatibility_score,
          key_talking_points: (data as any).key_talking_points,
          investment_preferences: (data as any).investment_preferences
        })), null, 2),
        context_data: JSON.stringify({
          preparation_focus: 'Shark Tank India pitch optimization',
          key_preparation_areas: [
            'elevator pitch structure',
            'financial metrics presentation',
            'objection handling',
            'demo preparation',
            'due diligence readiness'
          ]
        }, null, 2)
      }
    );

    return {
      section_id: 'pitch_preparation',
      title: 'Pitch Preparation Guide',
      content: result,
      confidence_score: 92,
      data_sources: ['Shark Preferences', 'Similar Company Analysis', 'Pitch Best Practices', 'AI Template Analysis'],
      insights: this.extractInsights(result),
      recommendations: this.extractRecommendations(result)
    };
  }

  private async generateRiskAssessmentEnhanced(
    request: EnhancedReportRequest,
    similarCompanies: any[]
  ): Promise<EnhancedReportSection> {
    const failedCompanies = similarCompanies.filter(c => c.deal_outcome === 'No Deal');
    const successfulCompanies = similarCompanies.filter(c => c.deal_outcome === 'Deal');

    const { result } = await promptManager.executeTemplate(
      'risk-analysis',
      {
        company_name: request.company_name,
        business_sector: request.business_sector,
        revenue_range: request.revenue_range || 'Not disclosed',
        business_model: request.business_model,
        target_market: request.target_market || 'Not specified',
        funding_sought: request.funding_sought || 0,
        equity_offered: request.equity_offered || 0,
        failed_companies_analysis: JSON.stringify(failedCompanies.slice(0, 3).map(c => ({
          name: c.company_name,
          concerns_raised: c.concerns_raised,
          key_failure_factors: c.key_insights
        })), null, 2),
        successful_companies_analysis: JSON.stringify(successfulCompanies.slice(0, 3).map(c => ({
          name: c.company_name,
          success_factors: c.success_factors,
          key_insights: c.key_insights
        })), null, 2),
        market_context: JSON.stringify({
          sector_challenges: `${request.business_sector} market risks`,
          competitive_threats: 'Based on similar company failures',
          success_patterns: 'Based on successful company analysis'
        }, null, 2),
        risk_factors: JSON.stringify([
          'Market penetration challenges',
          'Competition and differentiation',
          'Scaling and operational risks',
          'Financial sustainability',
          'Team and execution capabilities'
        ], null, 2)
      }
    );

    return {
      section_id: 'risk_assessment',
      title: 'Risk Assessment & Mitigation',
      content: result,
      confidence_score: 80,
      data_sources: ['Historical Data', 'Market Analysis', 'Risk Modeling', 'AI Template Analysis'],
      insights: this.extractInsights(result),
      recommendations: this.extractRecommendations(result)
    };
  }

  private async generateMarketPositioningEnhanced(
    request: EnhancedReportRequest,
    similarCompanies: any[]
  ): Promise<EnhancedReportSection> {
    const { result } = await promptManager.executeTemplate(
      'market-positioning',
      {
        company_name: request.company_name,
        business_sector: request.business_sector,
        target_market: request.target_market || 'Not specified',
        products_services: request.products_services,
        business_model: request.business_model,
        revenue_range: request.revenue_range || 'Not disclosed',
        competitive_context: JSON.stringify(similarCompanies.slice(0, 5).map(c => ({
          name: c.company_name,
          season: c.season,
          business_sector: c.business_sector,
          deal_outcome: c.deal_outcome,
          similarity_score: c.similarity_score
        })), null, 2),
        market_data: JSON.stringify({
          sector_analysis: `${request.business_sector} market dynamics`,
          target_audience: request.target_market || 'General market',
          competitive_landscape: 'Based on similar Shark Tank companies'
        }, null, 2),
        positioning_context: JSON.stringify({
          analysis_purpose: 'Shark Tank investment evaluation',
          focus_areas: [
            'market size assessment',
            'competitive differentiation',
            'value proposition clarity',
            'scaling strategy',
            'customer acquisition',
            'revenue sustainability'
          ]
        }, null, 2)
      }
    );

    return {
      section_id: 'market_positioning',
      title: 'Market Positioning & Opportunity Analysis',
      content: result,
      confidence_score: 85,
      data_sources: ['Market Research', 'Competitive Analysis', 'Positioning Framework', 'AI Template Analysis'],
      insights: this.extractInsights(result),
      recommendations: this.extractRecommendations(result)
    };
  }

  private async generateExecutiveSummaryEnhanced(report: EnhancedGeneratedReport): Promise<EnhancedGeneratedReport['executive_summary']> {
    try {
      const { result } = await promptManager.executeTemplate(
        'executive-summary',
        {
          company_name: report.company_name,
          report_sections: Object.keys(report.enhanced_sections).join(', '),
          similar_companies_count: report.appendices.similar_companies_detailed.length,
          shark_profiles_count: report.appendices.shark_profiles_detailed.length,
          analysis_data: JSON.stringify({
            enhanced_sections: Object.keys(report.enhanced_sections),
            metadata: report.metadata,
            confidence_scores: Object.values(report.enhanced_sections).map(s => s.confidence_score)
          }, null, 2),
          key_findings: JSON.stringify([
            'Company strengths and positioning analyzed',
            'Market opportunity assessment completed',
            'Investment readiness evaluation performed',
            'Shark compatibility insights provided',
            'Success probability factors identified'
          ], null, 2),
          business_context: JSON.stringify({
            report_type: report.report_type,
            analysis_depth: 'comprehensive',
            data_sources: report.metadata.sources_used
          }, null, 2)
        }
      );

      // Try to parse JSON response from template
      const jsonMatch = result.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      // If template doesn't return JSON, create structured response from text
      return this.parseExecutiveSummaryFromText(result);

    } catch (error) {
      console.warn('[ReportGenerationService] Executive summary generation failed, using defaults:', error);

      // Fallback summary
      return {
        key_insights: [
          'Comprehensive analysis completed with market positioning insights',
          'Shark compatibility analysis provides strategic guidance',
          'Risk assessment identifies key preparation areas'
        ],
        success_probability: 75,
        recommended_sharks: ['Anupam Mittal', 'Aman Gupta', 'Vineeta Singh'],
        preparation_score: 80
      };
    }
  }

  private parseExecutiveSummaryFromText(text: string): EnhancedGeneratedReport['executive_summary'] {
    return {
      key_insights: [
        'Template-based comprehensive analysis completed',
        'Market positioning and competitive assessment provided',
        'Investment strategy and risk analysis included'
      ],
      success_probability: 80,
      recommended_sharks: ['Anupam Mittal', 'Aman Gupta', 'Vineeta Singh'],
      preparation_score: 85
    };
  }

  /**
   * Helper methods for enhanced functionality
   */
  private generateSimilarityReason(request: EnhancedReportRequest, company: any): string {
    const reasons = [];

    if (company.business_sector_standardized?.toLowerCase().includes(request.business_sector.toLowerCase())) {
      reasons.push('same sector');
    }

    if (company.extraction_final?.business_model?.toLowerCase().includes(request.business_model.toLowerCase())) {
      reasons.push('similar business model');
    }

    return reasons.join(', ') || 'market positioning similarities';
  }

  private extractInsights(content: string): string[] {
    const lines = content.split('\n');
    return lines
      .filter(line => line.includes('insight') || line.includes('key') || line.includes('important'))
      .slice(0, 3)
      .map(line => line.trim());
  }

  private extractRecommendations(content: string): string[] {
    const lines = content.split('\n');
    return lines
      .filter(line => line.includes('recommend') || line.includes('should') || line.includes('suggest'))
      .slice(0, 3)
      .map(line => line.trim());
  }

  private generatePreparationChecklistEnhanced(request: EnhancedReportRequest): string[] {
    return [
      'Prepare 60-second elevator pitch',
      'Gather financial documentation and projections',
      'Research target sharks and their investment preferences',
      'Prepare demo or product samples',
      'Anticipate and prepare responses to common objections',
      'Practice pitch delivery and timing',
      'Prepare due diligence materials',
      'Plan negotiation scenarios and acceptable terms',
      'Research similar companies and their outcomes',
      'Prepare follow-up materials and next steps'
    ];
  }

  private calculateOverallConfidenceEnhanced(report: EnhancedGeneratedReport): number {
    const sectionScores = Object.values(report.enhanced_sections).map(s => s.confidence_score);
    return Math.round(sectionScores.reduce((sum, score) => sum + score, 0) / sectionScores.length);
  }

  private extractSourcesUsedEnhanced(report: EnhancedGeneratedReport): string[] {
    const sources = new Set<string>();
    Object.values(report.enhanced_sections).forEach(section => {
      section.data_sources.forEach(source => sources.add(source));
    });
    return Array.from(sources);
  }

  private async convertToLegacySections(enhancedSections: any): Promise<ReportSection[]> {
    // Convert enhanced sections to legacy format for backward compatibility
    return Object.values(enhancedSections).map((section: any, index) => ({
      title: section.title,
      page_number: index + 1,
      content: {
        overview: section.content,
        confidence_score: section.confidence_score,
        data_sources: section.data_sources,
        insights: section.insights,
        recommendations: section.recommendations
      }
    }));
  }

  /**
   * Storage and export methods for enhanced reports
   */
  private async storeEnhancedReport(report: EnhancedGeneratedReport): Promise<void> {
    try {
      await sql`
        INSERT INTO generated_reports (
          report_id, session_id, company_name, website_url, business_sector,
          analysis_type, report_status, report_data, generation_time,
          payment_id, payment_status, customer_email
        ) VALUES (
          ${report.report_id}, ${report.session_id}, ${report.company_name}, 
          ${report.website_url || null}, ${report.business_sector}, 
          ${report.analysis_type || 'comprehensive'}, ${report.status || 'completed'},
          ${JSON.stringify(report)}, ${report.generation_time || null},
          ${report.payment_id || null}, ${report.payment_status || null},
          ${report.customer_email || null}
        )
        ON CONFLICT (report_id) DO UPDATE SET
          report_data = ${JSON.stringify(report)},
          updated_at = CURRENT_TIMESTAMP,
          report_status = ${report.status || 'completed'}
      `;
      console.log(`[ReportGenerationService] ✅ Report ${report.report_id} stored in database`);
    } catch (error) {
      console.error(`[ReportGenerationService] ❌ Database storage failed for ${report.report_id}:`, error);
      throw error;
    }
  }

  private async exportEnhancedReport(
    report: EnhancedGeneratedReport,
    formats: ('pdf' | 'html' | 'json')[]
  ): Promise<void> {
    for (const format of formats) {
      try {
        const fileName = `${report.report_id}.${format}`;
        const filePath = path.join(this.reportsPath, fileName);

        switch (format) {
          case 'json':
            await fs.writeFile(filePath, JSON.stringify(report, null, 2));
            break;
          case 'html':
            const html = this.generateHtmlReportEnhanced(report);
            await fs.writeFile(filePath, html);
            break;
          case 'pdf':
            console.log(`[ReportGenerationService] PDF export not yet implemented for ${fileName}`);
            break;
        }

        console.log(`[ReportGenerationService] ✅ Exported ${format.toUpperCase()}: ${fileName}`);
      } catch (error) {
        console.error(`[ReportGenerationService] Failed to export ${format}:`, error);
      }
    }
  }

  private generateHtmlReportEnhanced(report: EnhancedGeneratedReport): string {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <title>Enhanced Shark Tank Preparation Report - ${report.company_name}</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .header { border-bottom: 2px solid #007acc; padding-bottom: 20px; margin-bottom: 30px; }
            .section { margin-bottom: 30px; }
            .section h2 { color: #007acc; border-bottom: 1px solid #eee; padding-bottom: 10px; }
            .confidence { background: #f0f8ff; padding: 10px; border-left: 4px solid #007acc; margin: 10px 0; }
            .summary { background: #fff8dc; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .enhanced { background: #f0fff0; border-left: 4px solid #00cc44; padding: 10px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Enhanced Shark Tank Preparation Report</h1>
            <h2>${report.company_name}</h2>
            <p>Generated: ${report.generation_timestamp.toLocaleString()}</p>
            <p>Report ID: ${report.report_id}</p>
            <div class="enhanced">Enhanced with Context-First Architecture</div>
        </div>

        <div class="summary">
            <h2>Executive Summary</h2>
            <p><strong>Success Probability:</strong> ${report.executive_summary.success_probability}%</p>
            <p><strong>Preparation Score:</strong> ${report.executive_summary.preparation_score}%</p>
            <p><strong>Recommended Sharks:</strong> ${report.executive_summary.recommended_sharks.join(', ')}</p>
            <h3>Key Insights:</h3>
            <ul>
                ${report.executive_summary.key_insights.map(insight => `<li>${insight}</li>`).join('')}
            </ul>
        </div>

        ${Object.values(report.enhanced_sections).map(section => `
            <div class="section">
                <h2>${section.title}</h2>
                <div class="confidence">Confidence Score: ${section.confidence_score}%</div>
                <div class="enhanced">Data Sources: ${section.data_sources.join(', ')}</div>
                <div>${section.content.replace(/\n/g, '<br>')}</div>
                ${section.insights.length > 0 ? `
                    <h3>Key Insights:</h3>
                    <ul>${section.insights.map(insight => `<li>${insight}</li>`).join('')}</ul>
                ` : ''}
                ${section.recommendations.length > 0 ? `
                    <h3>Recommendations:</h3>
                    <ul>${section.recommendations.map(rec => `<li>${rec}</li>`).join('')}</ul>
                ` : ''}
            </div>
        `).join('')}

        <div class="section">
            <h2>Enhanced Report Metadata</h2>
            <p>Processing Time: ${Math.round(report.metadata.generation_time_ms / 1000)} seconds</p>
            <p>AI Calls Made: ${report.metadata.ai_calls_made}</p>
            <p>Data Points Analyzed: ${report.metadata.data_points_analyzed}</p>
            <p>Overall Confidence: ${report.metadata.confidence_score}%</p>
            <p>Sources Used: ${report.metadata.sources_used.join(', ')}</p>
        </div>
    </body>
    </html>
    `;
  }

  private async initializeReportsDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.reportsPath, { recursive: true });
    } catch (error) {
      console.error('[ReportGenerationService] Failed to create reports directory:', error);
    }
  }

  /**
   * Get enhanced report by ID
   */
  async getEnhancedReport(reportId: string): Promise<EnhancedGeneratedReport | null> {
    try {
      const results = await sql`
        SELECT report_data, accessed_at, access_count 
        FROM generated_reports 
        WHERE report_id = ${reportId}
      `;
      
      if (results.length === 0) {
        console.log(`[ReportGenerationService] ❌ Report ${reportId} not found`);
        return null;
      }

      // Track access
      await sql`SELECT track_report_access(${reportId})`;
      
      const report = results[0].report_data as EnhancedGeneratedReport;
      console.log(`[ReportGenerationService] ✅ Retrieved report ${reportId} (access #${results[0].access_count + 1})`);
      return report;
    } catch (error) {
      console.error(`[ReportGenerationService] ❌ Database retrieval failed for ${reportId}:`, error);
      return null;
    }
  }

  /**
   * List enhanced reports for user
   */
  async getUserEnhancedReports(userId: string): Promise<EnhancedGeneratedReport[]> {
    try {
      const results = await sql`
        SELECT report_data, created_at, accessed_at, access_count
        FROM generated_reports 
        WHERE user_id = ${userId}
        ORDER BY created_at DESC
        LIMIT 50
      `;
      
      const reports = results.map(row => {
        const report = row.report_data as EnhancedGeneratedReport;
        // Add access metadata to report
        report.access_metadata = {
          created_at: row.created_at,
          accessed_at: row.accessed_at,
          access_count: row.access_count
        };
        return report;
      });
      
      console.log(`[ReportGenerationService] ✅ Retrieved ${reports.length} reports for user ${userId}`);
      return reports;
    } catch (error) {
      console.error(`[ReportGenerationService] ❌ Database query failed for user ${userId}:`, error);
      return [];
    }
  }
}

// Export singleton instance
export const reportGenerationService = new ReportGenerationService();

// Export factory function for compatibility with existing code
export function getReportGenerationService() {
  if (!process.env.GEMINI_API_KEY) {
    throw new Error('GEMINI_API_KEY is required for report generation');
  }

  return new ReportGenerationService(process.env.GEMINI_API_KEY);
}
