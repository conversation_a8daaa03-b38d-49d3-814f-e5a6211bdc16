#!/usr/bin/env bun

/**
 * Transcript Segmentation Service
 * 
 * URGENT: Created Aug 13 2025 for same-day release
 * 
 * Splits multi-company transcripts into individual company segments
 * Handles 1-11 companies per transcript file
 * Uses markers: "नमस्ते", timestamps, "my name is", "founder of"
 */

import '../../../../../lib/env-loader.js';
import { GoogleGenAI } from '@google/genai';
import { readFileSync } from 'fs';
import postgres from 'postgres';

// CRITICAL: Override DATABASE_URL to use pitch_prep_dev database
// The .env.development has gikiaidb which is wrong for Pitch Prep
const PITCH_PREP_DB = 'postgresql://nikhilsingh@localhost:5432/pitch_prep_dev';
const sql = postgres(PITCH_PREP_DB);

// Initialize Gemini with API key
const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || '';
if (!apiKey) {
  console.error('❌ No API key found. Set GEMINI_API_KEY or GOOGLE_API_KEY');
}

const ai = new GoogleGenAI({ apiKey });

export interface CompanySegment {
  segmentNumber: number;
  companyName: string;
  companyNameTransliterated?: string;
  founders: string[];
  startTimestamp?: string;
  endTimestamp?: string;
  segmentText: string;
  pitchSections: {
    narratorIntro?: string;
    founderPresentation: string;
    qaDiscussion?: string;
    negotiation?: string;
    outcome: string;
  };
  confidence: number;
  sharksInvolved: string[];
  dealMade: boolean;
  askAmount?: number;
  askEquity?: number;
  finalAmount?: number;
  finalEquity?: number;
}

export interface SegmentationResult {
  transcriptId: string;
  videoId: string;
  season: string;
  episode: string;
  totalCompaniesFound: number;
  segments: CompanySegment[];
  segmentationConfidence: number;
  errors: string[];
}

class TranscriptSegmentationService {
  constructor() {
    // No need to store model - will use ai directly
  }

  /**
   * Main segmentation method - splits transcript into company segments
   */
  async segmentTranscript(
    transcriptText: string,
    transcriptId: string,
    season: string,
    episode: string
  ): Promise<SegmentationResult> {
    console.log(`🔪 Segmenting transcript ${transcriptId} (S${season}E${episode})`);
    
    try {
      // Load segmentation prompt template
      const promptTemplate = JSON.parse(
        readFileSync('/Users/<USER>/giki-ai-workspace/apps/pitch-prep/server/src/prompts/templates/transcript-segmentation.json', 'utf-8')
      );
      
      // Build prompt with transcript
      const prompt = promptTemplate.template
        .replace('{{cleaned_transcript}}', transcriptText)
        .replace('{{season_number}}', season)
        .replace('{{episode_number}}', episode);
      
      // Call Gemini without strict schema - let the prompt guide the structure
      const result = await ai.models.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: prompt,
        config: {
          responseMimeType: 'application/json',
          maxOutputTokens: 30000, // Increased for multi-company transcripts
          temperature: 0.1 // Low temperature for consistent segmentation
        }
      });
      
      if (!result || !result.text) {
        throw new Error('No response from Gemini API');
      }
      
      const responseText = result.text;
      
      // Try to parse JSON, handle incomplete responses
      let response;
      try {
        response = JSON.parse(responseText);
      } catch (parseError) {
        console.warn('⚠️ JSON parse error, attempting to fix incomplete response');
        // Try to fix common issues like unterminated strings
        const fixedText = responseText
          .replace(/,\s*$/, '') // Remove trailing comma
          .replace(/([^\\])"([^"]*$)/, '$1"$2"') // Try to close unterminated string
          + (responseText.includes('{') && !responseText.includes('}') ? '}' : ''); // Close unclosed object
        
        try {
          response = JSON.parse(fixedText);
        } catch (secondError) {
          console.error('❌ Failed to parse Gemini response even after fixes');
          throw new Error(`Invalid JSON response: ${secondError.message}`);
        }
      }
      
      // Transform response to our format
      const segments: CompanySegment[] = response.episode_segments.company_pitches.map((pitch: any, index: number) => {
        // Extract financial numbers from key moments
        let askAmount = 0;
        let askEquity = 0;
        let finalAmount = 0;
        let finalEquity = 0;
        let dealMade = false;
        
        if (pitch.key_moments) {
          pitch.key_moments.forEach((moment: any) => {
            // NEW: Try to get financial data from structured format first
            if (moment.financial_data && moment.moment_type === 'ask') {
              const financialData = moment.financial_data;
              if (financialData.amount_lakhs && financialData.equity_percentage) {
                askAmount = parseFloat(financialData.amount_lakhs);
                askEquity = parseFloat(financialData.equity_percentage);
              }
            } else if (moment.financial_data && moment.moment_type === 'deal') {
              dealMade = true;
              const financialData = moment.financial_data;
              if (financialData.amount_lakhs && financialData.equity_percentage) {
                finalAmount = parseFloat(financialData.amount_lakhs);
                finalEquity = parseFloat(financialData.equity_percentage);
              }
            }
            
            // FALLBACK: Use regex pattern matching if structured data not available
            if ((askAmount === 0 || askEquity === 0) && moment.moment_type === 'ask') {
              const content = moment.content || '';
              // Enhanced regex to handle Hindi numbers and various formats
              const askMatch = content.match(/(\d+(?:\.\d+)?)\s*(?:लाख|lakh|lakhs).*?(\d+(?:\.\d+)?)\s*(?:%|परसेंट|percent)/i);
              if (askMatch) {
                askAmount = parseFloat(askMatch[1]);
                askEquity = parseFloat(askMatch[2]);
              }
            }
            
            if ((finalAmount === 0 || finalEquity === 0) && moment.moment_type === 'deal') {
              dealMade = true;
              const content = moment.content || '';
              const dealMatch = content.match(/(\d+(?:\.\d+)?)\s*(?:लाख|lakh|lakhs).*?(\d+(?:\.\d+)?)\s*(?:%|परसेंट|percent)/i);
              if (dealMatch) {
                finalAmount = parseFloat(dealMatch[1]);
                finalEquity = parseFloat(dealMatch[2]);
              }
            }
          });
        }

        // ADDITIONAL FALLBACK: Extract from full segment text if key_moments failed
        if (askAmount === 0 || askEquity === 0) {
          const fullText = this.combineSegmentText(pitch.pitch_sections);
          
          // Look for ask pattern in full text with enhanced regex
          const askPattern = /(?:मांग|asking|ask).*?(\d+(?:\.\d+)?)\s*(?:लाख|lakh|lakhs).*?(\d+(?:\.\d+)?)\s*(?:%|परसेंट|percent)/i;
          const askMatch = fullText.match(askPattern);
          if (askMatch) {
            askAmount = parseFloat(askMatch[1]);
            askEquity = parseFloat(askMatch[2]);
            console.log(`💰 Fallback extraction - Ask: ₹${askAmount}L for ${askEquity}%`);
          }
          
          // Look for deal pattern in full text
          const dealPattern = /(?:offer|डील|deal).*?(\d+(?:\.\d+)?)\s*(?:लाख|lakh|lakhs).*?(\d+(?:\.\d+)?)\s*(?:%|परसेंट|percent)/i;
          const dealMatch = fullText.match(dealPattern);
          if (dealMatch) {
            finalAmount = parseFloat(dealMatch[1]);
            finalEquity = parseFloat(dealMatch[2]);
            console.log(`💰 Fallback extraction - Deal: ₹${finalAmount}L for ${finalEquity}%`);
          }
        }
        
        // Check outcome for deal status if not found in key moments
        if (!dealMade && pitch.pitch_sections?.outcome) {
          dealMade = pitch.pitch_sections.outcome.toLowerCase().includes('deal') && 
                    !pitch.pitch_sections.outcome.toLowerCase().includes('no deal');
        }
        
        return {
          segmentNumber: index + 1,
          companyName: pitch.company_name,
          companyNameTransliterated: this.transliterateCompanyName(pitch.company_name),
          founders: pitch.founders || [],
          startTimestamp: this.extractTimestamp(pitch.pitch_sections?.narrator_intro || ''),
          endTimestamp: this.extractTimestamp(pitch.pitch_sections?.outcome || ''),
          segmentText: this.combineSegmentText(pitch.pitch_sections),
          pitchSections: {
            narratorIntro: pitch.pitch_sections?.narrator_intro,
            founderPresentation: pitch.pitch_sections?.founder_presentation || '',
            qaDiscussion: pitch.pitch_sections?.qa_discussion,
            negotiation: pitch.pitch_sections?.negotiation,
            outcome: pitch.pitch_sections?.outcome || ''
          },
          confidence: pitch.segment_quality?.completeness === 'complete' ? 0.95 : 0.7,
          sharksInvolved: pitch.sharks_involved || [],
          dealMade,
          askAmount,
          askEquity,
          finalAmount: finalAmount || askAmount,
          finalEquity: finalEquity || askEquity
        };
      });
      
      console.log(`✅ Segmented into ${segments.length} companies with ${(response.segmentation_metadata.segmentation_confidence * 100).toFixed(1)}% confidence`);
      
      return {
        transcriptId,
        videoId: transcriptId, // Will be updated by caller if different
        season,
        episode,
        totalCompaniesFound: segments.length,
        segments,
        segmentationConfidence: response.segmentation_metadata.segmentation_confidence,
        errors: []
      };
      
    } catch (error) {
      console.error(`❌ Segmentation failed for ${transcriptId}:`, error);
      return {
        transcriptId,
        videoId: transcriptId,
        season,
        episode,
        totalCompaniesFound: 0,
        segments: [],
        segmentationConfidence: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }
  
  /**
   * Helper: Combine pitch sections into full segment text
   */
  private combineSegmentText(sections: any): string {
    const parts = [];
    if (sections?.narrator_intro) parts.push(sections.narrator_intro);
    if (sections?.founder_presentation) parts.push(sections.founder_presentation);
    if (sections?.qa_discussion) parts.push(sections.qa_discussion);
    if (sections?.negotiation) parts.push(sections.negotiation);
    if (sections?.outcome) parts.push(sections.outcome);
    return parts.join('\n\n');
  }
  
  /**
   * Helper: Extract timestamp from text
   */
  private extractTimestamp(text: string): string | undefined {
    const match = text.match(/\[?(\d{1,2}:\d{2}(?::\d{2})?)\]?/);
    return match ? match[1] : undefined;
  }
  
  /**
   * Helper: Basic transliteration for Hindi company names
   */
  private transliterateCompanyName(name: string): string {
    // Simple transliteration map for common Hindi words in company names
    const transliterations: Record<string, string> = {
      'दादी': 'Dadi',
      'नानी': 'Nani',
      'माँ': 'Maa',
      'बाबा': 'Baba',
      'चाचा': 'Chacha',
      'भाई': 'Bhai',
      'दीदी': 'Didi',
      'जी': 'Ji',
      'श्री': 'Shree',
      'का': 'Ka',
      'की': 'Ki',
      'के': 'Ke'
    };
    
    let result = name;
    Object.entries(transliterations).forEach(([hindi, english]) => {
      result = result.replace(new RegExp(hindi, 'g'), english);
    });
    
    return result;
  }
  
  /**
   * Save segments to database
   */
  async saveSegments(result: SegmentationResult): Promise<void> {
    console.log(`💾 Saving ${result.segments.length} segments to database`);
    
    for (const segment of result.segments) {
      try {
        // First, check if company exists or create it
        let companyId: string;
        
        const existingCompany = await sql`
          SELECT id FROM shark_tank_companies 
          WHERE LOWER(name) = LOWER(${segment.companyName})
            AND season = ${result.season}
            AND episode = ${result.episode}
          LIMIT 1
        `;
        
        if (existingCompany.length > 0) {
          companyId = existingCompany[0].id;
        } else {
          // Create new company entry - use metadata for extra fields to avoid column issues
          const metadataJson = JSON.stringify({
            sharksInvolved: segment.sharksInvolved,
            segmentNumber: segment.segmentNumber,
            confidence: segment.confidence,
            videoId: result.videoId,
            founders: segment.founders
          });
          
          const newCompany = await sql`
            INSERT INTO shark_tank_companies (
              name, season, episode, 
              ask_amount, ask_equity,
              deal_amount, deal_equity, deal_made,
              metadata
            ) VALUES (
              ${segment.companyName},
              ${parseInt(result.season)},
              ${parseInt(result.episode)},
              ${segment.askAmount || 0},
              ${segment.askEquity || 0},
              ${segment.finalAmount || 0},
              ${segment.finalEquity || 0},
              ${segment.dealMade || false},
              ${metadataJson}::jsonb
            )
            RETURNING id
          `;
          companyId = newCompany[0].id;
        }
        
        // Save segment with null-safe values
        await sql`
          INSERT INTO transcript_segments (
            transcript_id, video_id, company_id,
            segment_text, segment_number,
            start_timestamp, end_timestamp
          ) VALUES (
            ${result.transcriptId},
            ${result.videoId},
            ${companyId},
            ${segment.segmentText},
            ${segment.segmentNumber},
            ${segment.startTimestamp || null},
            ${segment.endTimestamp || null}
          )
          ON CONFLICT (transcript_id, company_id, segment_number) 
          DO UPDATE SET
            segment_text = EXCLUDED.segment_text,
            updated_at = NOW()
        `;
        
      } catch (error) {
        console.error(`Failed to save segment for ${segment.companyName}:`, error);
      }
    }
    
    console.log(`✅ Segments saved to database`);
  }
}

// Export singleton instance
export const transcriptSegmentationService = new TranscriptSegmentationService();

// CLI testing
if (import.meta.main) {
  const testTranscriptPath = '/Users/<USER>/giki-ai-workspace/packages/transcript-fetcher/data/transcripts/S4_E008_ks3uwSxlwZ4.text';
  const transcript = readFileSync(testTranscriptPath, 'utf-8');
  
  console.log('🧪 Testing segmentation with S4_E008 (11 companies)...');
  const result = await transcriptSegmentationService.segmentTranscript(
    transcript,
    'S4_E008_ks3uwSxlwZ4',
    '4',
    '008'
  );
  
  console.log('\n📊 Segmentation Results:');
  console.log(`Found ${result.totalCompaniesFound} companies`);
  console.log(`Confidence: ${(result.segmentationConfidence * 100).toFixed(1)}%`);
  
  result.segments.forEach(segment => {
    console.log(`\n${segment.segmentNumber}. ${segment.companyName}`);
    console.log(`   Founders: ${segment.founders.join(', ')}`);
    console.log(`   Ask: ₹${segment.askAmount}L for ${segment.askEquity}%`);
    console.log(`   Deal: ${segment.dealMade ? `✅ ₹${segment.finalAmount}L for ${segment.finalEquity}%` : '❌ No deal'}`);
    console.log(`   Sharks: ${segment.sharksInvolved.join(', ')}`);
  });
  
  await sql.end();
}