/**
 * Unified Similarity Service - Combines best of all similarity services
 * 
 * Consolidates:
 * - modernSimilarityService.ts (Season 4 SQL-based matching)
 * - similarityMatchingService.ts (Drizzle ORM-based matching)
 * - embeddingService.ts (vector-based similarity)
 * 
 * Provides comprehensive similarity matching with multiple strategies
 */

import { sql } from '../database/connection.js';
import { drizzle } from 'drizzle-orm/node-postgres';
import { eq, and, like, desc, sql as drizzleSql } from 'drizzle-orm';
import { Pool } from 'pg';
import { sharkTankCompanies } from '../schema/index.js';
import { enhancedSimilarityScoring } from './enhancedSimilarityScoring.js';

// Database connection for Drizzle ORM
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/pitchprep'
});
const db = drizzle(pool);

export interface UserCompany {
  companyName: string;
  sector: string;
  businessModel?: string;
  revenue_range?: { min: number; max: number };
  target_sharks?: string[];
  askAmount?: number;
  askEquity?: number;
}

export interface SimilarCompanyMatch {
  company_name: string;
  season: number;
  episode: number;
  business_sector: string;
  similarity_score: number;
  deal_outcome: any;
  key_insights: string[];
  why_similar: string;
  context_summary: string;
  match_strategy: 'sql' | 'drizzle' | 'hybrid';
}

export class UnifiedSimilarityService {
  
  /**
   * Find similar companies using multiple strategies
   */
  async findSimilarCompanies(userCompany: UserCompany, limit: number = 8): Promise<SimilarCompanyMatch[]> {
    try {
      console.log(`🔍 [UnifiedSimilarity] Finding matches for: ${userCompany.companyName} (${userCompany.sector})`);
      
      // Strategy 1: SQL-based search (Season 4 data from companies table)
      const sqlMatches = await this.findUsingSQLStrategy(userCompany, limit);
      
      // Strategy 2: Drizzle ORM search (sharkTankCompanies table)
      const drizzleMatches = await this.findUsingDrizzleStrategy(userCompany, limit);
      
      // Combine and deduplicate matches
      const allMatches = this.combineAndDeduplicateMatches(sqlMatches, drizzleMatches);
      
      // Sort by similarity score
      allMatches.sort((a, b) => b.similarity_score - a.similarity_score);
      
      console.log(`🎯 [UnifiedSimilarity] Returning ${Math.min(allMatches.length, limit)} best matches`);
      return allMatches.slice(0, limit);
      
    } catch (error) {
      console.error('❌ [UnifiedSimilarity] Error finding similar companies:', error);
      return [];
    }
  }
  
  /**
   * SQL-based strategy using companies table (ALL SEASONS data)
   */
  private async findUsingSQLStrategy(userCompany: UserCompany, limit: number): Promise<SimilarCompanyMatch[]> {
    try {
      let results: any[] = [];
      
      if (userCompany.sector) {
        // Try exact sector matches first - FROM ALL SEASONS
        results = await sql`
          SELECT 
            company_name,
            season,
            episode,
            business_sector_standardized,
            extraction_final::jsonb,
            context_summary,
            confidence_score
          FROM companies 
          WHERE 
            business_sector_standardized ILIKE ${`%${userCompany.sector}%`}
            AND confidence_score >= 0.7
          ORDER BY confidence_score DESC, season DESC, episode ASC
          LIMIT ${limit * 2}
        `;
      }
      
      // If no sector matches, try broader search - ALL SEASONS
      if (!results || results.length === 0) {
        results = await sql`
          SELECT 
            company_name,
            season,
            episode,  
            business_sector_standardized,
            extraction_final::jsonb,
            context_summary,
            confidence_score
          FROM companies 
          WHERE 
            confidence_score >= 0.7
          ORDER BY confidence_score DESC, season DESC, episode ASC
          LIMIT ${limit * 2}
        `;
      }
      
      // Convert to similarity match format
      return results.map((row: any) => {
        const score = this.calculateSimilarityScore(userCompany, row);
        const dealOutcome = this.extractDealOutcome(row.extraction_final);
        
        return {
          company_name: row.company_name,
          season: row.season,
          episode: row.episode,
          business_sector: row.business_sector_standardized || 'Unknown',
          similarity_score: score,
          deal_outcome: dealOutcome,
          key_insights: this.extractKeyInsights(row.extraction_final),
          why_similar: this.generateSimilarityReason(userCompany, row, 'sql'),
          context_summary: row.context_summary || '',
          match_strategy: 'sql' as const
        };
      });
      
    } catch (error) {
      console.error('SQL strategy error:', error);
      return [];
    }
  }
  
  /**
   * Drizzle ORM strategy using sharkTankCompanies table
   */
  private async findUsingDrizzleStrategy(userCompany: UserCompany, limit: number): Promise<SimilarCompanyMatch[]> {
    try {
      const whereConditions = [];
      
      if (userCompany.sector) {
        whereConditions.push(like(sharkTankCompanies.industry, `%${userCompany.sector}%`));
      }
      
      const baseQuery = db.select({
        id: sharkTankCompanies.id,
        name: sharkTankCompanies.name,
        season: sharkTankCompanies.season,
        episode: sharkTankCompanies.episode,
        industry: sharkTankCompanies.industry,
        askAmount: sharkTankCompanies.askAmount,
        askEquity: sharkTankCompanies.askEquity,
        dealAmount: sharkTankCompanies.dealAmount,
        dealEquity: sharkTankCompanies.dealEquity,
        dealMade: sharkTankCompanies.dealMade,
        sharks: sharkTankCompanies.sharks,
        pitch: sharkTankCompanies.pitch,
        outcome: sharkTankCompanies.outcome
      }).from(sharkTankCompanies);
      
      const finalQuery = whereConditions.length > 0 
        ? baseQuery.where(whereConditions[0])
            .orderBy(desc(sharkTankCompanies.dealMade), desc(sharkTankCompanies.dealAmount))
            .limit(limit * 3)  // Fetch more companies to get better matches
        : baseQuery
            .orderBy(desc(sharkTankCompanies.dealMade), desc(sharkTankCompanies.dealAmount))
            .limit(limit * 3);  // Fetch more companies to get better matches
      
      const similarCompanies = await finalQuery;
      
      // If no matches, get successful companies
      if (similarCompanies.length === 0) {
        const fallbackCompanies = await db.select()
          .from(sharkTankCompanies)
          .where(eq(sharkTankCompanies.dealMade, true))
          .orderBy(desc(sharkTankCompanies.dealAmount))
          .limit(limit);
        
        return this.formatDrizzleResults(fallbackCompanies, userCompany);
      }
      
      return this.formatDrizzleResults(similarCompanies, userCompany);
      
    } catch (error) {
      console.error('Drizzle strategy error:', error);
      return [];
    }
  }
  
  /**
   * Format Drizzle ORM results to match format
   */
  private formatDrizzleResults(companies: any[], userCompany: UserCompany): SimilarCompanyMatch[] {
    return companies.map(company => {
      let score = 0.60; // Base score
      
      // Industry match bonus
      if (userCompany.sector && company.industry) {
        const sectorLower = userCompany.sector.toLowerCase();
        const industryLower = company.industry.toLowerCase();
        if (industryLower.includes(sectorLower) || sectorLower.includes(industryLower)) {
          score += 0.20;
        }
      }
      
      // Successful deal bonus
      if (company.dealMade) {
        score += 0.15;
      }
      
      // Recent season bonus
      if (company.season && company.season >= 3) {
        score += 0.05;
      }
      
      score = Math.min(score, 0.95);
      
      return {
        company_name: company.name,
        season: company.season,
        episode: company.episode,
        business_sector: company.industry || 'Unknown',
        similarity_score: score,
        deal_outcome: {
          deal_closed: company.dealMade,
          initial_ask_amount: company.askAmount ? Number(company.askAmount) : 0,
          initial_equity_offered: company.askEquity ? Number(company.askEquity) : 0,
          final_deal_amount: company.dealAmount ? Number(company.dealAmount) : 0,
          final_equity: company.dealEquity ? Number(company.dealEquity) : 0,
          participating_sharks: company.sharks || []
        },
        key_insights: [
          `Season ${company.season} participant`,
          company.dealMade ? 'Successful deal closed' : 'No deal made',
          `Industry: ${company.industry}`,
          company.dealAmount ? `Deal value: ₹${Number(company.dealAmount).toLocaleString()}` : 'No deal value'
        ],
        why_similar: company.industry === userCompany.sector 
          ? 'Industry and business model match' 
          : 'Business pattern similarity',
        context_summary: company.pitch || '',
        match_strategy: 'drizzle' as const
      };
    });
  }
  
  /**
   * Combine and deduplicate matches from different strategies
   */
  private combineAndDeduplicateMatches(
    sqlMatches: SimilarCompanyMatch[],
    drizzleMatches: SimilarCompanyMatch[]
  ): SimilarCompanyMatch[] {
    const matchMap = new Map<string, SimilarCompanyMatch>();
    
    // Add SQL matches (prefer these as they have richer data)
    sqlMatches.forEach(match => {
      const key = `${match.company_name}_${match.season}_${match.episode}`;
      matchMap.set(key, match);
    });
    
    // Add Drizzle matches if not already present
    drizzleMatches.forEach(match => {
      const key = `${match.company_name}_${match.season}_${match.episode}`;
      if (!matchMap.has(key)) {
        matchMap.set(key, match);
      } else {
        // Merge insights if company exists
        const existing = matchMap.get(key)!;
        existing.key_insights = [...new Set([...existing.key_insights, ...match.key_insights])];
        existing.match_strategy = 'hybrid';
      }
    });
    
    return Array.from(matchMap.values());
  }
  
  /**
   * Get industry insights and patterns
   */
  async getIndustryInsights(sector: string): Promise<any> {
    try {
      // Try SQL strategy first (richer data)
      const sqlInsights = await sql`
        SELECT 
          COUNT(*) as total_companies,
          COUNT(CASE WHEN (extraction_final::jsonb->'funding_deal_outcome'->>'deal_closed')::boolean = true THEN 1 END) as successful_deals,
          AVG((extraction_final::jsonb->'funding_deal_outcome'->>'final_deal_amount_inr')::numeric) as avg_deal_amount,
          AVG((extraction_final::jsonb->'funding_deal_outcome'->>'final_equity_percent')::numeric) as avg_equity
        FROM companies 
        WHERE 
          season = 4 
          AND business_sector_standardized ILIKE ${`%${sector}%`}
      `;
      
      if (sqlInsights[0].total_companies > 0) {
        const totalCompanies = Number(sqlInsights[0].total_companies);
        const successfulDeals = Number(sqlInsights[0].successful_deals);
        
        return {
          sector,
          total_companies: totalCompanies,
          successful_deals: successfulDeals,
          success_rate: `${((successfulDeals / totalCompanies) * 100).toFixed(1)}%`,
          average_deal_amount: sqlInsights[0].avg_deal_amount || 0,
          average_equity: sqlInsights[0].avg_equity || 0,
          data_source: 'Season 4 Pipeline Data'
        };
      }
      
      // Fallback to Drizzle ORM data
      const drizzleCompanies = await db.select()
        .from(sharkTankCompanies)
        .where(like(sharkTankCompanies.industry, `%${sector}%`));
      
      const totalCompanies = drizzleCompanies.length;
      const successfulDeals = drizzleCompanies.filter(c => c.dealMade).length;
      const avgDealAmount = drizzleCompanies
        .filter(c => c.dealMade && c.dealAmount)
        .reduce((sum, c) => sum + Number(c.dealAmount), 0) / Math.max(successfulDeals, 1);
      
      return {
        sector,
        total_companies: totalCompanies,
        successful_deals: successfulDeals,
        success_rate: totalCompanies > 0 ? `${((successfulDeals / totalCompanies) * 100).toFixed(1)}%` : '0%',
        average_deal_amount: avgDealAmount,
        average_equity: 12.5, // Default average
        data_source: 'Historical Shark Tank Data'
      };
      
    } catch (error) {
      console.error('❌ Error getting industry insights:', error);
      return { sector, total_companies: 0, successful_deals: 0, success_rate: '0%' };
    }
  }
  
  /**
   * Enhanced similarity scoring algorithm with multi-factor analysis
   */
  private calculateSimilarityScore(userCompany: UserCompany, dbCompany: any): number {
    // Use enhanced similarity scoring for more accurate matching
    const detailedScore = enhancedSimilarityScoring.calculateDetailedSimilarity(
      userCompany,
      dbCompany,
      null // No shark preferences for basic scoring
    );
    
    // Store detailed analysis for later use
    if (dbCompany && !dbCompany._detailedScore) {
      dbCompany._detailedScore = detailedScore;
    }
    
    // Return normalized score (0-1 range)
    return detailedScore.totalScore / 100;
  }
  
  private calculateSemanticSimilarity(term1: string, term2: string): number {
    // Simple semantic similarity based on common synonyms
    const synonymGroups = [
      ['tech', 'technology', 'it', 'software', 'digital'],
      ['food', 'restaurant', 'culinary', 'cuisine', 'dining'],
      ['health', 'healthcare', 'medical', 'wellness', 'pharma'],
      ['education', 'edtech', 'learning', 'training', 'coaching'],
      ['finance', 'fintech', 'banking', 'payments', 'lending'],
      ['retail', 'ecommerce', 'shopping', 'marketplace', 'store'],
      ['travel', 'tourism', 'hospitality', 'hotel', 'booking']
    ];
    
    for (const group of synonymGroups) {
      const term1InGroup = group.some(word => term1.includes(word));
      const term2InGroup = group.some(word => term2.includes(word));
      
      if (term1InGroup && term2InGroup) {
        return 0.8; // High similarity within same semantic group
      }
    }
    
    // Check for word overlap
    const words1 = term1.split(/\s+/);
    const words2 = term2.split(/\s+/);
    const commonWords = words1.filter(w => words2.includes(w));
    
    if (commonWords.length > 0) {
      return commonWords.length / Math.max(words1.length, words2.length);
    }
    
    return 0;
  }
  
  /**
   * Check for cross-industry patterns
   */
  private checkCrossIndustryPattern(userSector: string, dbSector: string): boolean {
    const crossPatterns = [
      { base: 'tech', variants: ['fintech', 'edtech', 'healthtech', 'agritech', 'proptech'] },
      { base: 'commerce', variants: ['ecommerce', 'social commerce', 'quick commerce'] },
      { base: 'mobility', variants: ['electric mobility', 'shared mobility', 'micro mobility'] }
    ];
    
    for (const pattern of crossPatterns) {
      const userMatchesBase = userSector.includes(pattern.base);
      const dbMatchesVariant = pattern.variants.some(v => dbSector.includes(v));
      
      const userMatchesVariant = pattern.variants.some(v => userSector.includes(v));
      const dbMatchesBase = dbSector.includes(pattern.base);
      
      if ((userMatchesBase && dbMatchesVariant) || (userMatchesVariant && dbMatchesBase)) {
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * Calculate keyword overlap score
   */
  private calculateKeywordOverlap(userCompany: UserCompany, contextText: string): number {
    const keywords = [];
    
    // Extract keywords from user company data
    if (userCompany.sector) keywords.push(...userCompany.sector.toLowerCase().split(/\s+/));
    if (userCompany.businessModel) keywords.push(...userCompany.businessModel.toLowerCase().split(/\s+/));
    
    // Filter out common words
    const stopWords = ['the', 'and', 'or', 'in', 'of', 'for', 'to', 'a', 'an', 'is', 'are', 'was', 'were'];
    const meaningfulKeywords = keywords.filter(k => k.length > 2 && !stopWords.includes(k));
    
    if (meaningfulKeywords.length === 0) return 0;
    
    // Count keyword matches
    const matches = meaningfulKeywords.filter(keyword => contextText.includes(keyword));
    
    return matches.length / meaningfulKeywords.length;
  }
  
  /**
   * Extract deal outcome from company data
   */
  private extractDealOutcome(extractionData: any): any {
    if (!extractionData || typeof extractionData === 'string') {
      return null;
    }
    
    const fundingData = extractionData.funding_deal_outcome;
    if (!fundingData) return null;
    
    return {
      deal_closed: fundingData.deal_closed || false,
      initial_ask_amount: fundingData.initial_ask_amount_inr || 0,
      initial_equity_offered: fundingData.initial_equity_offered_percent || 0,
      final_deal_amount: fundingData.final_deal_amount_inr || 0,
      final_equity: fundingData.final_equity_percent || 0,
      participating_sharks: fundingData.participating_sharks || [],
      negotiation_rounds: fundingData.negotiation_rounds || 0,
      deal_structure: fundingData.deal_structure || 'equity'
    };
  }
  
  /**
   * Extract key insights from company data
   */
  private extractKeyInsights(extractionData: any): string[] {
    const insights: string[] = [];
    
    try {
      if (!extractionData || typeof extractionData === 'string') {
        return ['Season 4 Shark Tank India participant'];
      }
      
      // Add product insights
      if (extractionData.product_offering?.flagship_product_name) {
        insights.push(`Product: ${extractionData.product_offering.flagship_product_name}`);
      }
      
      // Add deal insights
      const funding = extractionData.funding_deal_outcome;
      if (funding?.deal_closed) {
        insights.push(`Secured ₹${funding.final_deal_amount_inr?.toLocaleString() || '0'} deal`);
        if (funding.participating_sharks?.length > 0) {
          insights.push(`Sharks: ${funding.participating_sharks.join(', ')}`);
        }
      } else {
        insights.push(`Pitched for ₹${funding?.initial_ask_amount_inr?.toLocaleString() || '0'}`);
      }
      
      // Add business metrics
      if (extractionData.business_metrics?.monthly_revenue) {
        insights.push(`Monthly revenue: ₹${extractionData.business_metrics.monthly_revenue.toLocaleString()}`);
      }
      
      // Add location
      if (extractionData.founder_team?.city_origin) {
        insights.push(`Based in ${extractionData.founder_team.city_origin}`);
      }
      
      // Add unique value proposition
      if (extractionData.usp) {
        insights.push(`USP: ${extractionData.usp.substring(0, 50)}...`);
      }
      
    } catch (error) {
      console.warn('Warning: Failed to extract insights from company data');
    }
    
    return insights.length > 0 ? insights.slice(0, 5) : ['Season 4 participant'];
  }
  
  /**
   * Generate similarity reasoning
   */
  private generateSimilarityReason(userCompany: UserCompany, dbCompany: any, strategy: string): string {
    const reasons: string[] = [];
    
    if (userCompany.sector && (dbCompany.business_sector_standardized || dbCompany.industry)) {
      const userSector = userCompany.sector.toLowerCase();
      const dbSector = (dbCompany.business_sector_standardized || dbCompany.industry || '').toLowerCase();
      
      if (dbSector.includes(userSector) || userSector.includes(dbSector)) {
        reasons.push(`Same business sector (${dbCompany.business_sector_standardized || dbCompany.industry})`);
      }
    }
    
    if (dbCompany.extraction_final?.funding_deal_outcome?.deal_closed || dbCompany.dealMade) {
      reasons.push('Successful deal pattern');
    }
    
    reasons.push(`Season ${dbCompany.season} experience`);
    
    if (strategy === 'hybrid') {
      reasons.push('Multiple data sources confirm match');
    }
    
    if (dbCompany.confidence_score > 0.8) {
      reasons.push('High-quality data available');
    }
    
    return reasons.join('; ');
  }
}

// Export singleton instance
export const unifiedSimilarityService = new UnifiedSimilarityService();