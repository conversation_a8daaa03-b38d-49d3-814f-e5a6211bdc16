import { GoogleGenAI } from '@google/genai';
import { db } from '../config/database';
import { sharkTankCompanies } from '../schema';
import { eq } from 'drizzle-orm';

interface CompanyProfile {
  name: string;
  industry: string;
  businessModel: string;
  revenue?: number;
  askAmount?: number;
  askEquity?: number;
  description: string;
  websiteData?: any;
}

interface SimilarCompany {
  company: CompanyProfile;
  similarityScore: number;
  similarityReasons: string[];
  sharkInvestments?: {
    dealMade: boolean;
    sharks: string[];
    dealAmount?: number;
    dealEquity?: number;
  };
}

export class SimilarityMatchingService {
  private ai: GoogleGenAI;

  constructor() {
    const apiKey = process.env.GOOGLE_API_KEY || process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('GOOGLE_API_KEY or GEMINI_API_KEY is required for similarity matching');
    }
    
    this.ai = new GoogleGenAI({
      api<PERSON><PERSON>
    });
  }

  /**
   * Find similar Shark Tank companies using inline passages for grounding
   */
  async findSimilarCompanies(
    userCompany: CompanyProfile,
    limit: number = 10
  ): Promise<SimilarCompany[]> {
    try {
      // 1. Fetch all Shark Tank companies from database
      const allCompanies = await db
        .select()
        .from(sharkTankCompanies)
        .limit(500); // Increased to use more of our 1,528 companies

      // 2. Prepare inline passages from database
      const companyPassages = allCompanies.map(company => {
        // Create searchable text combining all relevant fields
        const passage = `
Company: ${company.name}
Season: ${company.season}, Episode: ${company.episode || 'N/A'}
Industry: ${company.industry || 'Not specified'}
Ask: ₹${company.askAmount} lakhs for ${company.askEquity}% equity
Deal: ${company.dealMade ? `₹${company.dealAmount} lakhs for ${company.dealEquity}% with ${JSON.stringify(company.sharks)}` : 'No deal'}
Pitch: ${company.pitch || 'No pitch data'}
Outcome: ${company.outcome || 'No outcome data'}
${company.transcriptData ? `Details: ${JSON.stringify(company.transcriptData).substring(0, 500)}` : ''}
        `.trim();
        
        return {
          id: company.id,
          passage,
          company
        };
      });

      // 3. Use GenAI with inline passages to find similar companies
      const prompt = `
You are a similarity matching expert for Shark Tank India companies.

## User Company Profile:
${JSON.stringify(userCompany, null, 2)}

## Shark Tank Companies Database (Inline Passages):
${companyPassages.map(p => p.passage).join('\n---\n')}

## Task:
Analyze the user's company and find the ${limit} most similar companies from the Shark Tank database.
Consider these factors for similarity:
1. Industry/Sector alignment
2. Business model similarity
3. Revenue range and stage
4. Target market overlap
5. Technology/Innovation level
6. Funding requirements
7. Team composition
8. Growth trajectory

## Required Output Format:
Return a JSON array of similar companies with this structure:
{
  "similar_companies": [
    {
      "company_name": "Name from database",
      "similarity_score": 0.95,
      "similarity_reasons": [
        "Same D2C e-commerce model",
        "Similar revenue range (₹1-5 Cr)",
        "Both targeting millennials",
        "Technology-driven approach"
      ],
      "key_differences": [
        "They focus on beauty, you focus on wellness",
        "They have physical stores, you're online-only"
      ],
      "shark_outcome": {
        "deal_made": true,
        "sharks": ["Aman Gupta", "Vineeta Singh"],
        "deal_amount": 50,
        "deal_equity": 10,
        "key_negotiation_points": ["Valued brand potential", "Liked D2C metrics"]
      },
      "lessons_for_user": [
        "Emphasize your D2C metrics",
        "Show path to profitability",
        "Highlight brand differentiation"
      ]
    }
  ],
  "overall_insights": {
    "common_success_patterns": ["Strong unit economics", "Clear differentiation"],
    "common_failure_reasons": ["High burn rate", "Unclear positioning"],
    "recommended_positioning": "Position as tech-enabled D2C brand with strong metrics"
  }
}

Ensure similarity scores are between 0 and 1, with 1 being identical.
      `;

      // 4. Generate response with structured output and grounding
      const result = await this.ai.models.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: prompt,
        config: {
          temperature: 0.3, // Lower temperature for more consistent matching
          responseMimeType: 'application/json'
        }
      });

      const response = result.text;
      const similarityData = JSON.parse(response);

      // 5. Map results to our interface
      const similarCompanies: SimilarCompany[] = similarityData.similar_companies.map((match: any) => {
        // Find the original company data
        const originalCompany = companyPassages.find(
          p => p.company.name === match.company_name
        )?.company;

        return {
          company: {
            name: match.company_name,
            industry: originalCompany?.industry || 'Unknown',
            businessModel: match.similarity_reasons[0] || 'Similar business',
            revenue: originalCompany?.askAmount ? Number(originalCompany.askAmount) * 20 : undefined, // Rough estimate
            askAmount: originalCompany?.askAmount ? Number(originalCompany.askAmount) : undefined,
            askEquity: originalCompany?.askEquity ? Number(originalCompany.askEquity) : undefined,
            description: originalCompany?.pitch || match.similarity_reasons.join('. ')
          },
          similarityScore: match.similarity_score,
          similarityReasons: match.similarity_reasons,
          sharkInvestments: match.shark_outcome ? {
            dealMade: match.shark_outcome.deal_made,
            sharks: match.shark_outcome.sharks,
            dealAmount: match.shark_outcome.deal_amount,
            dealEquity: match.shark_outcome.deal_equity
          } : undefined
        };
      });

      return similarCompanies;

    } catch (error) {
      console.error('Error in similarity matching:', error);
      throw new Error(`Failed to find similar companies: ${error}`);
    }
  }

  /**
   * Update searchable text for a company in the database
   * This prepares the text that will be used in inline passages
   */
  async updateSearchableText(companyId: string) {
    try {
      const company = await db
        .select()
        .from(sharkTankCompanies)
        .where(eq(sharkTankCompanies.id, companyId))
        .limit(1);

      if (company.length === 0) {
        throw new Error('Company not found');
      }

      const c = company[0];
      
      // Combine all relevant text fields for better search
      const searchableText = `
        ${c.name} ${c.industry || ''} 
        ${c.pitch || ''} 
        ${c.outcome || ''} 
        Season ${c.season} Episode ${c.episode || ''}
        Ask: ₹${c.askAmount} lakhs for ${c.askEquity}%
        ${c.dealMade ? `Deal: ₹${c.dealAmount} lakhs for ${c.dealEquity}%` : 'No deal'}
        ${c.sharks ? `Sharks: ${JSON.stringify(c.sharks)}` : ''}
        ${c.websiteData ? JSON.stringify(c.websiteData).substring(0, 1000) : ''}
        ${c.transcriptData ? JSON.stringify(c.transcriptData).substring(0, 1000) : ''}
      `.replace(/\s+/g, ' ').trim();

      // Update the searchableText column
      await db
        .update(sharkTankCompanies)
        .set({ searchableText })
        .where(eq(sharkTankCompanies.id, companyId));

      return searchableText;
    } catch (error) {
      console.error('Error updating searchable text:', error);
      throw error;
    }
  }

  /**
   * Batch update searchable text for all companies
   * Run this after importing/updating company data
   */
  async updateAllSearchableText() {
    try {
      const companies = await db.select().from(sharkTankCompanies);
      
      console.log(`Updating searchable text for ${companies.length} companies...`);
      
      for (const company of companies) {
        await this.updateSearchableText(company.id);
      }
      
      console.log('Searchable text update complete!');
      return companies.length;
    } catch (error) {
      console.error('Error in batch update:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const similarityMatchingService = new SimilarityMatchingService();