/**
 * Narrative Report Service
 * Generates compelling, story-driven reports from pipeline data
 * Transforms data and story elements into readable, valuable insights
 */

import { GoogleGenAI, Type } from '@google/genai';
import type {
  PipelineCompanyOutput,
  UINarrativeReport
} from '../types/pipeline-contracts.js';
import { dataTransformationService } from './dataTransformationService.js';
import { dramaticStoryService } from './dramaticStoryService.js';
import { storyExtractionService } from './storyExtractionService.js';

export class NarrativeReportService {
  private genai: GoogleGenAI;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY or GOOGLE_API_KEY environment variable is required for NarrativeReportService');
    }
    this.genai = new GoogleGenAI({ apiKey });
  }

  /**
   * Generate complete narrative report for a user company
   */
  async generateNarrativeReport(
    userCompanyData: {
      companyName: string;
      founderName: string;
      businessSector: string;
      businessModel: string;
      websiteUrl: string;
      askAmount: number;
      askEquity: number;
    },
    similarCompanies: Array<{
      pipelineData: PipelineCompanyOutput;
      similarityScore: number;
    }>,
    sharkTankStats: {
      sectorSuccessRate: number;
      avgDealEquity: number;
      avgDealAmount: number;
    }
  ): Promise<UINarrativeReport> {

    // Generate executive letter
    const executiveLetter = await this.generateExecutiveLetter(
      userCompanyData,
      similarCompanies[0], // Top match
      sharkTankStats
    );

    // Process similar company stories with dramatic enhancement
    const similarCompanyStories = await Promise.all(
      similarCompanies.slice(0, 3).map(async (company) => {
        const uiCompany = dataTransformationService.pipelineToUISimilarCompany(
          company.pipelineData,
          company.similarityScore
        );

        // Enhance story with dramatic elements
        let dramaticStory = null;
        if (company.pipelineData.story_elements) {
          try {
            dramaticStory = await dramaticStoryService.enhanceStoryWithDrama(
              company.pipelineData.story_elements,
              company.pipelineData.company_name,
              company.pipelineData.deal_outcome || 'Unknown outcome'
            );
          } catch (error) {
            console.warn(`Failed to enhance story for ${company.pipelineData.company_name}:`, error);
          }
        }

        // Get personalized lessons
        const lessons = await storyExtractionService.extractPersonalizedLessons(
          company.pipelineData.story_elements!,
          company.pipelineData.company_name,
          userCompanyData
        );

        // Generate personalized narrative if dramatic story available
        let personalizedNarrative = null;
        if (dramaticStory) {
          try {
            personalizedNarrative = await dramaticStoryService.createPersonalizedNarrative(
              dramaticStory,
              company.pipelineData.company_name,
              userCompanyData
            );
          } catch (error) {
            console.warn(`Failed to create personalized narrative for ${company.pipelineData.company_name}:`, error);
          }
        }

        return {
          company: uiCompany,
          story: company.pipelineData.story_elements!,
          lessonsForYou: personalizedNarrative?.lessonsExtracted || lessons.lessonsForYou,
          whatToEmulate: personalizedNarrative?.actionableInsights || lessons.whatToEmulate,
          whatToAvoid: lessons.whatToAvoid
        };
      })
    );

    // Generate shark compatibility analysis
    const sharkCompatibility = await this.generateSharkCompatibilityAnalysis(
      userCompanyData,
      similarCompanies
    );

    // Generate benchmark narrative
    const benchmarkNarrative = await this.generateBenchmarkNarrative(
      userCompanyData,
      similarCompanies,
      sharkTankStats
    );

    // Generate question preparation script
    const questionScript = await this.generateQuestionScript(
      userCompanyData,
      similarCompanies
    );

    // Generate action plan
    const actionPlan = await this.generateActionPlan(
      userCompanyData,
      similarCompanyStories
    );

    return {
      executiveLetter,
      similarCompanyStories,
      sharkCompatibility,
      benchmarkNarrative,
      questionScript,
      actionPlan
    };
  }

  /**
   * Generate personalized executive letter
   */
  private async generateExecutiveLetter(
    userCompanyData: any,
    topMatch: { pipelineData: PipelineCompanyOutput; similarityScore: number },
    stats: any
  ): Promise<UINarrativeReport['executiveLetter']> {
    // First, try to generate dramatic opening using enhanced story service
    let enhancedOpening = '';
    try {
      enhancedOpening = await dramaticStoryService.generateOpeningNarrative(
        userCompanyData.founderName,
        userCompanyData.companyName,
        userCompanyData,
        topMatch.pipelineData
      );
    } catch (error) {
      console.warn('Failed to generate enhanced opening, using standard approach:', error);
    }

    const prompt = `You are a trusted business advisor writing a personal letter to ${userCompanyData.founderName}, founder of ${userCompanyData.companyName}.

Write as if you've just finished analyzing their business against 500+ Shark Tank India companies and discovered something important they need to know.

BUSINESS CONTEXT:
- ${userCompanyData.companyName} operates in ${userCompanyData.businessSector}
- Business model: ${userCompanyData.businessModel}
- Seeking: ₹${userCompanyData.askAmount} for ${userCompanyData.askEquity}% equity
- Most similar success: ${topMatch.pipelineData.company_name} (${topMatch.similarityScore}% similarity match)
- Sector data: ${stats.sectorSuccessRate}% success rate, ₹${stats.avgDealAmount} average deal
- Similar company outcome: ${topMatch.pipelineData.deal_outcome}

SIMILAR COMPANY STORY ELEMENTS:
${topMatch.pipelineData.story_elements ? `
- Founder story: ${topMatch.pipelineData.story_elements.founder_background}
- Problem solved: ${topMatch.pipelineData.story_elements.problem_they_solved}
- What worked: ${topMatch.pipelineData.story_elements.what_worked?.join(', ')}
- Key challenge: ${topMatch.pipelineData.story_elements.toughest_challenge}
- Turning point: ${topMatch.pipelineData.story_elements.turning_point_moment}
- Negotiation drama: ${topMatch.pipelineData.story_elements.negotiation_drama}
` : 'Limited story data available'}

${enhancedOpening ? `ENHANCED OPENING INSIGHT: ${enhancedOpening}` : ''}

Write a compelling executive letter with these exact sections:

1. personalizedOpening: ${enhancedOpening || 'Open with a specific insight about their business position or opportunity'} (2-3 sentences)
2. keyInsight: The single most important strategic insight they need to know based on similar company patterns, referencing specific moments or outcomes (2-3 sentences)
3. similarCompanyTeaser: Tease the most compelling story/lesson from similar companies without giving it away - mention specific dramatic moments or breakthroughs (2-3 sentences)
4. callToAction: Specific next steps they should take based on the analysis, with urgency and clear value (2-3 sentences)

TONE: Confident advisor who has insider knowledge from analyzing real pitch moments. Not generic, not salesy, but authoritative and personally relevant.
STYLE: Direct, specific, evidence-based. Reference actual dramatic moments, turning points, and outcomes from similar companies.`;

    const result = await this.genai.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      config: {
        temperature: 0.5,
        maxOutputTokens: 800,
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            founderName: { type: Type.STRING },
            personalizedOpening: { type: Type.STRING },
            keyInsight: { type: Type.STRING },
            similarCompanyTeaser: { type: Type.STRING },
            callToAction: { type: Type.STRING }
          },
          required: ['founderName', 'personalizedOpening', 'keyInsight', 'similarCompanyTeaser', 'callToAction']
        }
      }
    });

    return JSON.parse(result.text || '{}');
  }

  /**
   * Generate shark compatibility analysis with narratives
   */
  private async generateSharkCompatibilityAnalysis(
    userCompanyData: any,
    similarCompanies: Array<{ pipelineData: PipelineCompanyOutput; similarityScore: number }>
  ): Promise<UINarrativeReport['sharkCompatibility']> {
    // Analyze which sharks invested in similar companies
    const sharkInvestments = new Map<string, number>();
    const sharkQuotes = new Map<string, string[]>();

    similarCompanies.forEach(company => {
      if (company.pipelineData.deal_made && company.pipelineData.sharks_involved) {
        company.pipelineData.sharks_involved.forEach(shark => {
          sharkInvestments.set(shark, (sharkInvestments.get(shark) || 0) + 1);
        });
      }

      // Collect quotes if story elements exist
      if (company.pipelineData.story_elements?.shark_quotes) {
        Object.entries(company.pipelineData.story_elements.shark_quotes).forEach(([shark, quotes]) => {
          const existing = sharkQuotes.get(shark) || [];
          sharkQuotes.set(shark, [...existing, ...quotes]);
        });
      }
    });

    // Get top 3 sharks by investment frequency
    const topSharks = Array.from(sharkInvestments.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3);

    // If no investments found, use default sharks
    if (topSharks.length === 0) {
      topSharks.push(['Aman Gupta', 1], ['Namita Thapar', 1], ['Peyush Bansal', 1]);
    }

    // Generate compatibility analysis for each shark
    const sharkCompatibilityArray = await Promise.all(
      topSharks.map(async ([sharkName, investmentCount]) => {
        const compatibilityScore = Math.min(95, 60 + (investmentCount * 15) + Math.random() * 10);
        const quotes = sharkQuotes.get(sharkName) || [];

        const analysis = await this.generateSharkAnalysis(
          sharkName,
          userCompanyData,
          quotes,
          compatibilityScore
        );

        return analysis;
      })
    );

    return sharkCompatibilityArray;
  }

  /**
   * Generate analysis for a specific shark
   */
  private async generateSharkAnalysis(
    sharkName: string,
    userCompanyData: any,
    historicalQuotes: string[],
    compatibilityScore: number
  ): Promise<UINarrativeReport['sharkCompatibility'][0]> {
    const prompt = `Generate strategic insights about ${sharkName} for a ${userCompanyData.businessSector} company.

Historical quotes from ${sharkName}: ${historicalQuotes.slice(0, 5).join('; ')}

Generate:
1. whyGoodFit: 3 specific reasons why this shark would be interested in this business
2. howToWinThem: 3 actionable strategies to appeal to this specific shark
3. questionsTheyWillAsk: 3 questions with context about why they ask and how to answer
4. negotiationStyle: Their typical negotiation approach (1 paragraph)
5. dealHistory: Brief summary of their investment patterns focusing on sector preferences and deal sizes

PSYCHOLOGICAL CONTEXT:
Use this actual dialogue to understand ${sharkName}'s psychology:
${historicalQuotes.slice(0, 3).map(q => `"${q}"`).join('\n')}

Analyze their communication style, what excites them, what concerns them.
Be specific to ${sharkName}'s known preferences and style. Reference actual patterns from their quotes and behavior.

Write as if you've studied hundreds of their investments and can predict their reactions.`;

    const result = await this.genai.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      config: {
        temperature: 0.4,
        maxOutputTokens: 1200,
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            sharkName: { type: Type.STRING },
            compatibilityScore: { type: Type.NUMBER },
            whyGoodFit: {
              type: Type.ARRAY,
              items: { type: Type.STRING }
            },
            howToWinThem: {
              type: Type.ARRAY,
              items: { type: Type.STRING }
            },
            questionsTheyWillAsk: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  question: { type: Type.STRING },
                  whyTheyAskThis: { type: Type.STRING },
                  howToAnswer: { type: Type.STRING }
                },
                required: ['question', 'whyTheyAskThis', 'howToAnswer']
              }
            },
            negotiationStyle: { type: Type.STRING },
            dealHistory: { type: Type.STRING }
          },
          required: ['sharkName', 'compatibilityScore', 'whyGoodFit', 'howToWinThem',
            'questionsTheyWillAsk', 'negotiationStyle', 'dealHistory']
        }
      }
    });

    const parsed = JSON.parse(result.text || '{}');
    parsed.compatibilityScore = Math.round(compatibilityScore);
    parsed.sharkName = sharkName;

    return parsed;
  }

  /**
   * Generate benchmark narrative comparing to peers
   */
  private async generateBenchmarkNarrative(
    userCompanyData: any,
    similarCompanies: any[],
    stats: any
  ): Promise<UINarrativeReport['benchmarkNarrative']> {
    const prompt = `Generate a benchmark narrative for ${userCompanyData.companyName} in ${userCompanyData.businessSector}.

Compare to:
- Sector average success rate: ${stats.sectorSuccessRate}%
- Average deal equity: ${stats.avgDealEquity}%
- Average deal amount: ₹${stats.avgDealAmount}
- Similar companies: ${similarCompanies.map(c => (c.pipelineData?.company_name || c.company_name)).join(', ')}

Generate:
1. whereYouStand: Clear positioning statement (1 paragraph)
2. goodNews: 3 positive indicators or advantages
3. watchOuts: 3 areas of concern or risk
4. opportunities: 3 strategic opportunities to pursue
5. peerComparison: How they compare to the similar companies (1 paragraph)

Be specific and actionable, not generic.`;

    const result = await this.genai.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      config: {
        temperature: 0.4,
        maxOutputTokens: 1000,
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            whereYouStand: { type: Type.STRING },
            goodNews: {
              type: Type.ARRAY,
              items: { type: Type.STRING }
            },
            watchOuts: {
              type: Type.ARRAY,
              items: { type: Type.STRING }
            },
            opportunities: {
              type: Type.ARRAY,
              items: { type: Type.STRING }
            },
            peerComparison: { type: Type.STRING }
          },
          required: ['whereYouStand', 'goodNews', 'watchOuts', 'opportunities', 'peerComparison']
        }
      }
    });

    const baseNarrative = JSON.parse(result.text || '{}');

    // Optionally enhance with sector-level narrative insights
    let sectorNarrative: any = null;
    try {
      const pipelineArray = similarCompanies.map((c: any) => c.pipelineData ? c.pipelineData : c);
      sectorNarrative = await dramaticStoryService.generateSectorNarrative(
        userCompanyData.businessSector,
        pipelineArray,
        stats.sectorSuccessRate
      );
    } catch (error) {
      console.warn('Failed to generate sector narrative insights:', error);
    }

    if (sectorNarrative) {
      try {
        baseNarrative.sectorInsights = {
          sectorStory: sectorNarrative.sectorStory,
          marketPosition: sectorNarrative.successPatterns?.[0] || 'Market validation required',
          competitiveAdvantage: sectorNarrative.sectorSpecificAdvice?.[0] || 'Focus on differentiation'
        };
      } catch (error) {
        console.warn('Failed to integrate sector insights:', error);
      }
    }

    return baseNarrative;
  }

  /**
   * Generate question preparation script
   */
  private async generateQuestionScript(
    userCompanyData: any,
    similarCompanies: any[]
  ): Promise<UINarrativeReport['questionScript']> {
    // Analyze common questions from similar pitches
    const commonQuestions = new Set<string>();
    similarCompanies.forEach(company => {
      if (company.pipelineData.story_elements) {
        const story = company.pipelineData.story_elements;
        if (story.first_shark_question) commonQuestions.add(story.first_shark_question);
        if (story.toughest_challenge) commonQuestions.add(story.toughest_challenge);
      }
    });

    // Extract psychological insights from similar company interactions
    const sharkBehaviorPatterns = new Map<string, string[]>();
    const negotiationTactics = new Set<string>();

    similarCompanies.forEach(company => {
      if (company.pipelineData.story_elements) {
        const story = company.pipelineData.story_elements;

        // Collect shark behavioral patterns
        Object.entries(story.shark_quotes || {}).forEach(([shark, quotes]) => {
          if (!sharkBehaviorPatterns.has(shark)) sharkBehaviorPatterns.set(shark, []);
          sharkBehaviorPatterns.get(shark)!.push(...quotes);
        });

        // Extract negotiation tactics that worked
        if (story.what_worked) {
          story.what_worked.forEach(tactic => negotiationTactics.add(tactic));
        }
      }
    });

    const prompt = `Create a psychological preparation script for ${userCompanyData.founderName} pitching ${userCompanyData.companyName} on Shark Tank India.

BUSINESS CONTEXT:
- Company: ${userCompanyData.companyName} (${userCompanyData.businessSector})
- Model: ${userCompanyData.businessModel}
- Ask: ₹${userCompanyData.askAmount} for ${userCompanyData.askEquity}%
- Founder: ${userCompanyData.founderName}

SHARK BEHAVIORAL PATTERNS FROM SIMILAR PITCHES:
${Array.from(sharkBehaviorPatterns.entries()).map(([shark, quotes]) => `
${shark} patterns:
${quotes.slice(0, 3).map(q => `- "${q}"`).join('\n')}
`).join('\n')}

PROVEN TACTICS FROM SUCCESSFUL COMPANIES:
${Array.from(negotiationTactics).slice(0, 5).map(tactic => `- ${tactic}`).join('\n')}

COMMON QUESTIONS THEY MUST PREPARE FOR:
${Array.from(commonQuestions).slice(0, 5).map(q => `- ${q}`).join('\n')}

Generate a comprehensive question preparation script:

1. openingStrategy: Psychological setup and opening 90 seconds that commands attention and respect
2. anticipatedFlow: 4 time-based phases with specific topics, likely questions, and strategic response frameworks
3. negotiationFramework: Psychological approach to negotiation based on shark behavior patterns
4. contingencyPlans: Specific responses to difficult scenarios with psychological recovery strategies

Focus on PSYCHOLOGICAL PREPARATION - how to read shark reactions, when to push vs. pull back, how to recover from tough moments.
Reference actual shark behavior patterns and what worked for similar companies.`;

    const result = await this.genai.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      config: {
        temperature: 0.4,
        maxOutputTokens: 1500,
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            openingStrategy: { type: Type.STRING },
            anticipatedFlow: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  timeRange: { type: Type.STRING },
                  topic: { type: Type.STRING },
                  likelyQuestions: {
                    type: Type.ARRAY,
                    items: { type: Type.STRING }
                  },
                  yourResponse: { type: Type.STRING }
                },
                required: ['timeRange', 'topic', 'likelyQuestions', 'yourResponse']
              }
            },
            negotiationFramework: { type: Type.STRING },
            contingencyPlans: {
              type: Type.ARRAY,
              items: { type: Type.STRING }
            }
          },
          required: ['openingStrategy', 'anticipatedFlow', 'negotiationFramework', 'contingencyPlans']
        }
      }
    });

    return JSON.parse(result.text || '{}');
  }

  /**
   * Generate story-enhanced 3-week action plan with dramatic motivation
   */
  private async generateActionPlan(
    userCompanyData: any,
    similarCompanyStories: any[]
  ): Promise<UINarrativeReport['actionPlan']> {
    // Extract detailed preparation insights from successful companies
    const successfulPrep = similarCompanyStories
      .filter(story => story.company.outcome.includes('Successful'))
      .map(story => ({
        company: story.company.name,
        whatWorked: story.whatToEmulate,
        whatToAvoid: story.whatToAvoid,
        keyChallenge: story.story.toughest_challenge,
        breakthrough: story.story.turning_point_moment,
        dramaticMoment: story.dramaticStory?.emotionalMoments?.triumph || story.story.turning_point_moment
      }));

    const failedPrep = similarCompanyStories
      .filter(story => !story.company.outcome.includes('Successful'))
      .map(story => ({
        company: story.company.name,
        mistakes: story.whatToAvoid,
        failurePoints: story.story?.toughest_challenge || 'Key challenges not documented',
        criticalError: story.dramaticStory?.conflictElements?.internalConflict || 'Preparation inadequacy'
      }));

    const prompt = `Create a comprehensive 3-week Shark Tank preparation plan for ${userCompanyData.founderName} of ${userCompanyData.companyName}.

LEARN FROM SUCCESSFUL COMPANIES:
${successfulPrep.map(s => `
${s.company} (SUCCESS):
- What worked: ${s.whatWorked.join(', ')}
- Breakthrough moment: ${s.breakthrough}
- Key challenge overcome: ${s.keyChallenge}`).join('\n')}

LEARN FROM FAILED COMPANIES:
${failedPrep.map(f => `
${f.company} (FAILED):
- Critical mistakes: ${f.mistakes.join(', ')}
- Where they struggled: ${f.failurePoints}`).join('\n')}

YOUR CONTEXT:
- Business: ${userCompanyData.companyName} (${userCompanyData.businessSector})
- Ask: ₹${userCompanyData.askAmount} for ${userCompanyData.askEquity}%
- Model: ${userCompanyData.businessModel}

Create a tactical 21-day preparation plan with:

Week 1 (Foundation): 5 daily tasks focusing on what successful companies did right
Week 2 (Refinement): 5 daily tasks addressing what failed companies missed
Week 3 (Polish): 5 daily tasks for final preparation and confidence building

For each daily task provide:
- day: Day of week (Monday, Tuesday, etc.)
- task: Specific, actionable task (30-60 minutes)
- outcome: Measurable result that builds toward pitch success

Plus finalChecklist: 10 specific items to verify before stepping into the tank

BASE ALL TASKS ON ACTUAL SUCCESS/FAILURE PATTERNS from the companies analyzed.`;

    const result = await this.genai.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      config: {
        temperature: 0.3,
        maxOutputTokens: 1500,
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            week1: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  day: { type: Type.STRING },
                  task: { type: Type.STRING },
                  outcome: { type: Type.STRING }
                },
                required: ['day', 'task', 'outcome']
              }
            },
            week2: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  day: { type: Type.STRING },
                  task: { type: Type.STRING },
                  outcome: { type: Type.STRING }
                },
                required: ['day', 'task', 'outcome']
              }
            },
            week3: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  day: { type: Type.STRING },
                  task: { type: Type.STRING },
                  outcome: { type: Type.STRING }
                },
                required: ['day', 'task', 'outcome']
              }
            },
            finalChecklist: {
              type: Type.ARRAY,
              items: { type: Type.STRING }
            }
          },
          required: ['week1', 'week2', 'week3', 'finalChecklist']
        }
      }
    });

    const baseActionPlan = JSON.parse(result.text || '{}');

    // Enhance action plan with story-driven motivation
    try {
      const storyEnhancedPlan = await dramaticStoryService.enhanceActionPlanWithStories(
        baseActionPlan,
        successfulPrep.map(s => ({
          companyName: s.company,
          whatWorked: Array.isArray(s.whatWorked) ? s.whatWorked : [s.whatWorked],
          keyMoment: s.dramaticMoment || s.breakthrough
        })),
        userCompanyData
      );
      return storyEnhancedPlan;
    } catch (error) {
      console.warn('Failed to enhance action plan with stories, using base plan:', error);
      return baseActionPlan;
    }
  }

  /**
   * Generate preview version of the report (with locked sections)
   */
  async generatePreviewReport(
    narrativeReport: UINarrativeReport,
    userCompanyData: {
      companyName: string;
      founderName: string;
      businessSector: string;
    }
  ): Promise<Partial<UINarrativeReport>> {
    // Generate tension-building preview content using actual user data
    let previewTension = null;
    try {
      previewTension = await dramaticStoryService.generatePreviewTension(
        userCompanyData,
        narrativeReport.similarCompanyStories.map(story => ({
          companyName: story.company.name,
          dramaticMoment: story.dramaticStory?.emotionalMoments?.triumph || story.story.turning_point_moment,
          lessonTeaser: story.lessonsForYou[0] || 'Key strategic lesson available'
        }))
      );
    } catch (error) {
      console.warn('Failed to generate preview tension, using standard preview:', error);
    }

    // Return a preview with some sections locked/blurred
    return {
      executiveLetter: {
        ...narrativeReport.executiveLetter,
        keyInsight: previewTension?.tensionBuilder || '[Unlock full report to see strategic insights]',
        callToAction: '[Unlock full report for your action plan]'
      },
      similarCompanyStories: narrativeReport.similarCompanyStories.map((story, index) => {
        if (index === 0) {
          // Show first company fully
          return story;
        }
        // Lock other companies
        return {
          ...story,
          story: undefined as any,
          lessonsForYou: ['[Unlock to see lessons]'],
          whatToEmulate: ['[Unlock to see strategies]'],
          whatToAvoid: ['[Unlock to see pitfalls]']
        };
      }),
      sharkCompatibility: narrativeReport.sharkCompatibility.slice(0, 1), // Show only top shark
      benchmarkNarrative: {
        whereYouStand: narrativeReport.benchmarkNarrative.whereYouStand,
        goodNews: ['[Unlock to see advantages]'],
        watchOuts: ['[Unlock to see risks]'],
        opportunities: ['[Unlock to see opportunities]'],
        peerComparison: '[Unlock for detailed comparison]'
      },
      // Lock question script and action plan completely
      questionScript: undefined,
      actionPlan: undefined
    };
  }
}

export const narrativeReportService = new NarrativeReportService();
