/**
 * Story Extraction Service
 * Extracts narrative elements, dramatic moments, and lessons from Shark Tank transcripts
 * Transforms data into compelling stories for the report
 */

import { GoogleGenAI, Type } from '@google/genai';
import type {
  CompanyStoryElements,
  PipelineCompanyOutput
} from '../types/pipeline-contracts.js';

export class StoryExtractionService {
  private genai: GoogleGenAI;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY or GOOGLE_API_KEY environment variable is required for StoryExtractionService');
    }
    this.genai = new GoogleGenAI({ apiKey });
  }

  /**
   * Extract story elements from a transcript
   * Focus on drama, moments, and lessons - not just data
   */
  async extractStoryElements(
    transcript: string,
    companyName: string
  ): Promise<CompanyStoryElements | null> {
    try {
      const prompt = this.createStoryExtractionPrompt(transcript, companyName);

      const result = await this.genai.models.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: [{
          role: 'user',
          parts: [{ text: prompt }]
        }],
        config: {
          temperature: 0.4, // Higher than data extraction for creativity
          topP: 0.9,
          topK: 50,
          maxOutputTokens: 3000,
          responseMimeType: 'application/json',
          responseSchema: {
            type: Type.OBJECT,
            properties: {
              // The Setup
              founder_background: { type: Type.STRING },
              problem_they_solved: { type: Type.STRING },
              why_they_started: { type: Type.STRING },

              // The Pitch Journey
              opening_hook: { type: Type.STRING },
              first_shark_question: { type: Type.STRING },
              turning_point_moment: { type: Type.STRING },
              toughest_challenge: { type: Type.STRING },
              negotiation_drama: { type: Type.STRING },

              // Key Moments
              gasp_moment: { type: Type.STRING, nullable: true },
              tension_point: { type: Type.STRING, nullable: true },
              breakthrough: { type: Type.STRING, nullable: true },
              emotional_moment: { type: Type.STRING, nullable: true },

              // Shark Reactions (as JSON string)
              shark_quotes: {
                type: Type.STRING
              },
              shark_concerns: {
                type: Type.ARRAY,
                items: { type: Type.STRING }
              },
              shark_excitement: {
                type: Type.ARRAY,
                items: { type: Type.STRING }
              },

              // The Lessons
              what_worked: {
                type: Type.ARRAY,
                items: { type: Type.STRING }
              },
              what_didnt: {
                type: Type.ARRAY,
                items: { type: Type.STRING }
              },
              advice_for_similar: {
                type: Type.ARRAY,
                items: { type: Type.STRING }
              }
            },
            required: [
              'founder_background', 'problem_they_solved', 'why_they_started',
              'opening_hook', 'first_shark_question', 'turning_point_moment',
              'toughest_challenge', 'negotiation_drama',
              'shark_quotes', 'shark_concerns', 'shark_excitement',
              'what_worked', 'what_didnt', 'advice_for_similar'
            ]
          }
        }
      });

      const storyElements = JSON.parse(result.text || '{}');

      // Parse shark_quotes JSON string back to object
      try {
        if (storyElements.shark_quotes && typeof storyElements.shark_quotes === 'string') {
          storyElements.shark_quotes = JSON.parse(storyElements.shark_quotes);
        }
      } catch (error) {
        console.warn('Failed to parse shark_quotes JSON string:', error);
        storyElements.shark_quotes = {};
      }

      return storyElements;

    } catch (error) {
      console.error('Story extraction failed:', error);
      return null;
    }
  }

  /**
   * Create a prompt that focuses on extracting dramatic narrative elements
   */
  private createStoryExtractionPrompt(transcript: string, companyName: string): string {
    return `You are analyzing a Shark Tank India pitch for ${companyName}.
Your goal is to extract the STORY, not just the data. Focus on dramatic moments, emotional beats,
turning points, and valuable lessons. Tell me what HAPPENED, not just what WAS SAID.

Think like a screenwriter or journalist - find the human drama, the tension, the breakthrough moments.

TRANSCRIPT:
${transcript.substring(0, 20000)}

EXTRACT THE FOLLOWING STORY ELEMENTS:

THE SETUP (The Human Story):
- founder_background: Who is this person? What's their personal story? (Not just "IIT graduate" but their journey)
- problem_they_solved: What real problem frustrated them enough to start this? Make it relatable.
- why_they_started: The personal "aha" moment or trigger that made them take action

THE PITCH JOURNEY (The Drama):
- opening_hook: How did they grab attention in the first 30 seconds? What was their opening line/demo?
- first_shark_question: What was the very first question asked and by which shark? This sets the tone.
- turning_point_moment: When did the energy shift? When did sharks lean in or pull back?
- toughest_challenge: What was the hardest question or biggest objection they faced?
- negotiation_drama: Describe the back-and-forth tension during deal negotiation (or rejection)

KEY MOMENTS (The Memorable Beats):
- gasp_moment: When did sharks show genuine surprise or excitement? (null if none)
- tension_point: When did it seem like the deal might fall apart? (null if none)
- breakthrough: What changed shark minds from skeptical to interested? (null if none)
- emotional_moment: Any personal story that created emotional connection? (null if none)

SHARK REACTIONS (What They Actually Said):
- shark_quotes: JSON string containing dictionary of shark names to arrays of their most impactful/memorable quotes (verbatim when possible)
  Example JSON string: '{"Aman Gupta": ["This is a ₹1000 crore brand!", "I\'m out because..."], "Namita Thapar": ["Your numbers don\'t add up"]}'
- shark_concerns: List the main objections/concerns raised (be specific, not generic)
- shark_excitement: What specifically impressed or excited the sharks?

THE LESSONS (Actionable Takeaways):
- what_worked: What specific strategies/answers/demonstrations worked well? Be specific.
- what_didnt: What mistakes or weaknesses hurt their pitch? Be honest but constructive.
- advice_for_similar: What can similar companies learn from this pitch? Actionable advice only.

IMPORTANT INSTRUCTIONS:
1. Write in present tense for dramatic moments ("Aman leans forward and says...")
2. Include specific numbers, quotes, and details when available
3. Focus on MOMENTS and TURNING POINTS, not summaries
4. Make it compelling - would someone want to read this story?
5. Be specific about which shark said/did what - use their full names
6. Capture the emotion and energy of the room, not just facts
7. If something dramatic didn't happen, don't force it - use null for optional fields

Remember: You're extracting a STORY that will help other founders learn and prepare, not writing a business report.`;
  }

  /**
   * Generate a narrative summary from story elements
   * This creates a compelling 1-2 paragraph story
   */
  async generateNarrativeSummary(
    storyElements: CompanyStoryElements,
    companyName: string,
    dealMade: boolean
  ): Promise<string> {
    try {
      const prompt = `Based on these story elements from ${companyName}'s Shark Tank pitch,
write a compelling 1-2 paragraph narrative that captures the drama and key moments.
Focus on what makes this story memorable and what other founders can learn.
${dealMade ? 'They got a deal.' : 'They did not get a deal.'}

Story Elements:
${JSON.stringify(storyElements, null, 2)}

Write in an engaging, magazine-style that would make someone want to read the full story.
Start with a hook and end with the key lesson.`;

      const result = await this.genai.models.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        config: {
          temperature: 0.5,
          maxOutputTokens: 500
        }
      });

      return result.text;
    } catch (error) {
      console.error('Narrative generation failed:', error);
      return `${companyName} pitched on Shark Tank India, ${dealMade ? 'securing' : 'seeking'} investment for their venture.`;
    }
  }

  /**
   * Extract lessons specifically relevant to a user's company
   * This personalizes the story elements for the report reader
   */
  async extractPersonalizedLessons(
    storyElements: CompanyStoryElements,
    similarCompanyName: string,
    userCompanyContext: {
      businessSector: string;
      businessModel: string;
      askAmount: number;
    }
  ): Promise<{
    lessonsForYou: string[];
    whatToEmulate: string[];
    whatToAvoid: string[];
  }> {
    try {
      const prompt = `Based on ${similarCompanyName}'s Shark Tank experience,
provide specific, actionable lessons for a ${userCompanyContext.businessSector} company
with a ${userCompanyContext.businessModel} model seeking ₹${userCompanyContext.askAmount}.

Story Elements from ${similarCompanyName}:
What Worked: ${storyElements?.what_worked?.join(', ') || 'No specific success factors available'}
What Didn't: ${storyElements?.what_didnt?.join(', ') || 'No specific challenges noted'}
Shark Concerns: ${storyElements?.shark_concerns?.join(', ') || 'No specific concerns documented'}

Generate:
1. lessonsForYou: 3-4 specific lessons directly applicable to the user's situation
2. whatToEmulate: 2-3 specific strategies they should copy
3. whatToAvoid: 2-3 specific mistakes they should not repeat

Be specific and actionable, not generic. Reference the actual events from the pitch.`;

      const result = await this.genai.models.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        config: {
          temperature: 0.3,
          maxOutputTokens: 1500,
          responseMimeType: 'application/json',
          responseSchema: {
            type: Type.OBJECT,
            properties: {
              lessonsForYou: {
                type: Type.ARRAY,
                items: { type: Type.STRING }
              },
              whatToEmulate: {
                type: Type.ARRAY,
                items: { type: Type.STRING }
              },
              whatToAvoid: {
                type: Type.ARRAY,
                items: { type: Type.STRING }
              }
            }
          }
        }
      });

      return JSON.parse(result.text || '{}');
    } catch (error) {
      console.error('Personalized lessons extraction failed:', error);
      return {
        lessonsForYou: ['Study their pitch approach', 'Prepare for similar questions'],
        whatToEmulate: ['Their confidence in presenting'],
        whatToAvoid: ['Any preparation gaps they showed']
      };
    }
  }

  /**
   * Generate shark-specific insights from story elements
   */
  async generateSharkInsights(
    storyElements: CompanyStoryElements,
    sharkName: string
  ): Promise<{
    typicalConcerns: string[];
    whatImpressesThem: string[];
    negotiationStyle: string;
  }> {
    const sharkQuotes = storyElements.shark_quotes[sharkName] || [];

    // Use AI to analyze patterns from shark's behavior
    const sharkBehaviorPrompt = `Analyze these shark quotes and match them with concerns and excitement points:

Shark Quotes:
${sharkQuotes.join('\n')}

Concerns from pitch:
${storyElements.shark_concerns.join('\n')}

Excitement points from pitch:
${storyElements.shark_excitement.join('\n')}

Return a JSON object with:
1. concerns: Array of concerns that are reflected in the quotes
2. excitement: Array of excitement points that are reflected in the quotes

Focus on semantic meaning, not keyword matching.`;

    let concerns: string[] = [];
    let excitement: string[] = [];

    try {
      const result = await this.genai.models.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: [{ role: 'user', parts: [{ text: sharkBehaviorPrompt }] }],
        config: {
          temperature: 0.2,
          maxOutputTokens: 500,
          responseMimeType: 'application/json',
          responseSchema: {
            type: Type.OBJECT,
            properties: {
              concerns: { type: Type.ARRAY, items: { type: Type.STRING } },
              excitement: { type: Type.ARRAY, items: { type: Type.STRING } }
            }
          }
        }
      });

      const analysis = JSON.parse(result.text || '{}');
      concerns = analysis.concerns || [];
      excitement = analysis.excitement || [];
    } catch (error) {
      console.warn('Failed to analyze shark behavior patterns:', error);
      // Fallback to empty arrays
    }

    return {
      typicalConcerns: concerns.length > 0 ? concerns : ['Profitability', 'Scalability', 'Market competition'],
      whatImpressesThem: excitement.length > 0 ? excitement : ['Strong execution', 'Clear vision', 'Good numbers'],
      negotiationStyle: this.inferNegotiationStyle(sharkQuotes, sharkName)
    };
  }

  /**
   * Infer negotiation style from quotes
   */
  private async inferNegotiationStyle(sharkName: string, quotes: string[]): Promise<string> {
    // Use AI to analyze negotiation style from actual quotes
    const stylePrompt = `Analyze these actual quotes from ${sharkName} on Shark Tank to determine their negotiation style:

  Quotes:
  ${quotes.map(q => `"${q}"`).join('\n')}

  Based on these quotes, describe ${sharkName}'s negotiation style in 1-2 sentences. Consider:
  1. Are they aggressive, supportive, analytical, or balanced?
  2. What do they focus on (numbers, vision, team, market)?
  3. How should a founder prepare for their style?

  Provide a specific, actionable description of their negotiation approach based on the evidence in these quotes.`;

    try {
      const result = await this.genai.models.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: [{ role: 'user', parts: [{ text: stylePrompt }] }],
        config: {
          temperature: 0.3,
          maxOutputTokens: 200,
          responseMimeType: 'text/plain'
        }
      });

      return result.text?.trim() || `${sharkName} evaluates businesses comprehensively. Be prepared with both numbers and vision.`;
    } catch (error) {
      console.warn('Failed to infer negotiation style:', error);
      return `${sharkName} evaluates businesses comprehensively. Be prepared with both numbers and vision.`;
    }
  }

  /**
   * Enhance existing pipeline output with story elements
   */
  async enhancePipelineWithStory(
    pipelineOutput: PipelineCompanyOutput,
    transcript: string
  ): Promise<PipelineCompanyOutput> {
    const storyElements = await this.extractStoryElements(
      transcript,
      pipelineOutput.company_name
    );

    if (storyElements) {
      return {
        ...pipelineOutput,
        story_elements: storyElements
      };
    }

    return pipelineOutput;
  }
}

export const storyExtractionService = new StoryExtractionService();
