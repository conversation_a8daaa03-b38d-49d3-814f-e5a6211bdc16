/**
 * Unified Report Service
 * Combines the best features from all report generation services:
 * - ReportGenerationService: Template system and comprehensive generation
 * - NarrativeReportService: Story-driven narratives and emotional journeys
 * - PipelineEnhancedReportService: Rich 7-phase pipeline data insights
 */

import '../../../../../lib/env-loader.js';
import { GoogleGenAI } from '@google/genai';
import { sql } from '../database/connection.js';
import { reportCacheService } from './reportCacheService.js';
import { sseProgressService } from './sseProgressService.js';

// Import existing services to leverage their capabilities
import { ReportGenerationService } from './reportGenerationService.js';
import { narrativeReportService } from './narrativeReportService.js';
import { PipelineEnhancedReportService } from './pipelineEnhancedReportService.js';
import { unifiedSimilarityService } from './unifiedSimilarityService.js';
import { promptManager } from './promptManagementService.js';
import { multiGeminiReviewService } from './multiGeminiReviewService.js';
import { sharkCompatibilityService } from './sharkCompatibilityService.js';

// Import types
import type { 
  EnhancedReportRequest,
  EnhancedGeneratedReport,
  EnhancedReportSection 
} from './reportGenerationService.js';
import type { 
  UINarrativeReport,
  PipelineCompanyOutput 
} from '../types/pipeline-contracts.js';

export interface UnifiedReportRequest {
  // Core company information
  user_id: string;
  session_id: string;
  company_name: string;
  business_sector: string;
  business_model: string;
  
  // Business details
  products_services?: string;
  revenue_range?: string;
  target_market?: string;
  website_url?: string;
  website_content?: string;
  
  // Funding information
  funding_sought?: number;
  equity_offered?: number;
  valuation?: number;
  
  // Additional context
  founder_names?: string;
  company_story?: string;
  key_priorities?: string[];
  additional_context?: string;
  
  // Report configuration
  report_type?: 'comprehensive' | 'narrative' | 'pipeline-enhanced' | 'auto';
  include_charts?: boolean;
  detailed_analysis?: boolean;
  export_formats?: string[];
}

export interface UnifiedReport {
  // Core metadata
  report_id: string;
  session_id: string;
  company_name: string;
  generation_timestamp: string;
  report_type: string;
  
  // Executive summary combining all insights
  executive_summary: {
    overview: string;
    key_findings: string[];
    preparation_score: number;
    success_probability: number;
    overall_score: number;
    strategic_recommendations: string[];
  };
  
  // Rich content sections
  sections: {
    // From ReportGenerationService (template-based)
    business_analysis?: EnhancedReportSection;
    website_analysis?: EnhancedReportSection;
    market_positioning?: EnhancedReportSection;
    financial_analysis?: EnhancedReportSection;
    investment_strategy?: EnhancedReportSection;
    risk_assessment?: EnhancedReportSection;
    
    // From NarrativeReportService (story-driven)
    founder_story?: any;
    emotional_journey?: any;
    dramatic_arc?: any;
    pitch_narrative?: any;
    
    // From PipelineEnhancedReportService (7-phase insights)
    similar_companies?: EnhancedReportSection;
    shark_behavior_patterns?: EnhancedReportSection;
    negotiation_insights?: EnhancedReportSection;
    success_patterns?: EnhancedReportSection;
    
    // Combined insights
    shark_compatibility?: EnhancedReportSection;
    pitch_preparation?: EnhancedReportSection;
    question_bank?: EnhancedReportSection;
    next_steps?: EnhancedReportSection;
  };
  
  // Rich appendices
  appendices: {
    preparation_checklist: string[];
    anticipated_questions: any[];
    shark_profiles: any[];
    similar_companies_detailed: any[];
    market_research: any;
    financial_projections?: any;
  };
  
  // Metadata
  metadata: {
    data_sources: string[];
    ai_models_used: string[];
    confidence_score: number;
    processing_time_seconds: number;
    pipeline_data_available: boolean;
    narrative_enrichment: boolean;
    template_coverage: number;
  };
}

export class UnifiedReportService {
  private genAI: GoogleGenAI;
  private reportGenService: ReportGenerationService;
  private pipelineService: PipelineEnhancedReportService;
  
  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY or GOOGLE_API_KEY environment variable is required');
    }
    
    this.genAI = new GoogleGenAI({ apiKey });
    this.reportGenService = new ReportGenerationService();
    this.pipelineService = new PipelineEnhancedReportService();
  }
  
  /**
   * Generate a unified report combining all service capabilities
   */
  async generateUnifiedReport(request: UnifiedReportRequest): Promise<UnifiedReport> {
    const startTime = Date.now();
    console.log(`[UnifiedReport] Starting generation for ${request.company_name}`);
    
    // Check cache first
    const cachedReport = await reportCacheService.getCachedReport(request);
    if (cachedReport) {
      console.log(`[UnifiedReport] Returning cached report for ${request.company_name}`);
      return cachedReport;
    }
    
    // Send SSE progress: Starting
    sseProgressService.sendProgress(
      request.session_id,
      'initialization',
      'Starting report generation',
      5,
      `Preparing comprehensive analysis for ${request.company_name}`,
      1,
      10
    );
    
    // Determine report type if auto
    const reportType = request.report_type === 'auto' 
      ? await this.determineOptimalReportType(request)
      : request.report_type || 'comprehensive';
    
    console.log(`[UnifiedReport] Using report type: ${reportType}`);
    
    // Initialize report structure
    const report: UnifiedReport = {
      report_id: `report_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      session_id: request.session_id,
      company_name: request.company_name,
      generation_timestamp: new Date().toISOString(),
      report_type: reportType,
      
      executive_summary: {
        overview: '',
        key_findings: [],
        preparation_score: 0,
        success_probability: 0,
        overall_score: 0,
        strategic_recommendations: []
      },
      
      sections: {},
      
      appendices: {
        preparation_checklist: [],
        anticipated_questions: [],
        shark_profiles: [],
        similar_companies_detailed: [],
        market_research: {}
      },
      
      metadata: {
        data_sources: [],
        ai_models_used: ['gemini-2.0-flash-001'],
        confidence_score: 0,
        processing_time_seconds: 0,
        pipeline_data_available: false,
        narrative_enrichment: false,
        template_coverage: 0
      }
    };
    
    // Send SSE progress: Generating sections
    sseProgressService.sendProgress(
      request.session_id,
      'section_generation',
      'Generating report sections',
      20,
      'Creating business analysis, narrative, and insights',
      2,
      10
    );
    
    // Parallel generation of different report components
    const [
      templateSections,
      narrativeSections,
      pipelineSections,
      similarCompanies
    ] = await Promise.all([
      this.generateTemplateSections(request),
      this.generateNarrativeSections(request),
      this.generatePipelineSections(request),
      this.findAndAnalyzeSimilarCompanies(request)
    ]);
    
    // Merge sections intelligently
    report.sections = this.mergeSections(
      templateSections,
      narrativeSections,
      pipelineSections
    );
    
    // Send SSE progress: Shark analysis
    sseProgressService.sendProgress(
      request.session_id,
      'shark_analysis',
      'Analyzing shark compatibility',
      50,
      'Identifying best-fit investors and strategies',
      5,
      10
    );
    
    // Generate shark compatibility analysis using combined insights
    const sharkCompatibility = await this.generateSharkCompatibility(
      request,
      similarCompanies,
      pipelineSections
    );
    report.sections.shark_compatibility = sharkCompatibility;
    
    // Send SSE progress: Pitch preparation
    sseProgressService.sendProgress(
      request.session_id,
      'pitch_preparation',
      'Creating pitch preparation guide',
      65,
      'Developing pitch structure and key messages',
      6,
      10
    );
    
    // Generate comprehensive pitch preparation
    const pitchPrep = await this.generatePitchPreparation(
      request,
      report.sections,
      similarCompanies
    );
    report.sections.pitch_preparation = pitchPrep;
    
    // Send SSE progress: Question bank
    sseProgressService.sendProgress(
      request.session_id,
      'question_bank',
      'Preparing anticipated questions',
      75,
      'Creating comprehensive Q&A preparation',
      7,
      10
    );
    
    // Generate question bank from all insights
    const questionBank = await this.generateQuestionBank(
      request,
      report.sections,
      similarCompanies
    );
    report.sections.question_bank = questionBank;
    
    // Send SSE progress: Executive summary
    sseProgressService.sendProgress(
      request.session_id,
      'executive_summary',
      'Creating executive summary',
      85,
      'Synthesizing key findings and recommendations',
      8,
      10
    );
    
    // Generate executive summary from all sections
    report.executive_summary = await this.generateExecutiveSummary(
      request,
      report.sections,
      similarCompanies
    );
    
    // Populate appendices
    report.appendices = await this.generateAppendices(
      request,
      report.sections,
      similarCompanies
    );
    
    // Update metadata
    report.metadata.processing_time_seconds = (Date.now() - startTime) / 1000;
    report.metadata.confidence_score = this.calculateConfidenceScore(report);
    report.metadata.template_coverage = this.calculateTemplateCoverage(report);
    report.metadata.pipeline_data_available = pipelineSections !== null;
    report.metadata.narrative_enrichment = narrativeSections !== null;
    
    // Store report in database
    await this.storeReport(report);
    
    console.log(`[UnifiedReport] Generation complete in ${report.metadata.processing_time_seconds}s`);
    
    // Cache the generated report
    await reportCacheService.cacheReport(request, report);
    
    // Send SSE progress: Complete
    sseProgressService.sendCompletion(
      request.session_id,
      'Report generation complete',
      `/report/${report.report_id}`
    );
    
    return report;
  }
  
  /**
   * Determine optimal report type based on available data
   */
  private async determineOptimalReportType(request: UnifiedReportRequest): Promise<string> {
    // Check if we have rich pipeline data available
    const pipelineDataCount = await sql`
      SELECT COUNT(*) as count
      FROM shark_tank_companies
      WHERE narrative_richness IS NOT NULL
        AND name IS NOT NULL
    `;
    
    if (pipelineDataCount[0].count > 50) {
      return 'pipeline-enhanced';
    }
    
    // Check if narrative style is preferred based on business type
    if (request.company_story || request.founder_names) {
      return 'narrative';
    }
    
    // Default to comprehensive
    return 'comprehensive';
  }
  
  /**
   * Generate template-based sections using ReportGenerationService
   */
  private async generateTemplateSections(request: UnifiedReportRequest): Promise<any> {
    try {
      // Convert to EnhancedReportRequest format
      const enhancedRequest: EnhancedReportRequest = {
        user_id: request.user_id,
        company_name: request.company_name,
        business_sector: request.business_sector,
        business_model: request.business_model,
        user_business_model: request.business_model,
        products_services: request.products_services || '',
        revenue_range: request.revenue_range || 'Pre-revenue',
        target_market: request.target_market || 'India',
        funding_sought: request.funding_sought || 5000000,
        equity_offered: request.equity_offered || 10,
        key_priorities: request.key_priorities || ['growth', 'funding'],
        website_url: request.website_url,
        website_content: request.website_content,
        additional_context: request.additional_context || ''
      };
      
      // Generate enhanced report with templates
      const enhancedReport = await this.reportGenService.generateEnhancedReport(
        enhancedRequest,
        {
          include_charts: request.include_charts || true,
          detailed_analysis: request.detailed_analysis || true,
          export_formats: request.export_formats || ['json']
        }
      );
      
      return enhancedReport.enhanced_sections;
      
    } catch (error) {
      console.error('[UnifiedReport] Error generating template sections:', error);
      return null;
    }
  }
  
  /**
   * Generate narrative sections using NarrativeReportService
   */
  private async generateNarrativeSections(request: UnifiedReportRequest): Promise<any> {
    try {
      // Find similar companies for narrative context
      const similarCompanies = await unifiedSimilarityService.findSimilarCompanies(
        {
          companyName: request.company_name,
          sector: request.business_sector
        },
        3
      );
      
      if (similarCompanies.length === 0) {
        return null;
      }
      
      // Get sector statistics
      const sectorStats = await this.getSectorStatistics(request.business_sector);
      
      // Generate narrative report
      const narrativeReport = await narrativeReportService.generateNarrativeReport(
        {
          companyName: request.company_name,
          founderName: request.founder_names || 'Founder',
          businessSector: request.business_sector,
          businessModel: request.business_model,
          websiteUrl: request.website_url || '',
          askAmount: request.funding_sought || 5000000,
          askEquity: request.equity_offered || 10
        },
        similarCompanies.map(c => ({
          pipelineData: c as any,
          similarityScore: c.similarity_score
        })),
        sectorStats
      );
      
      return {
        founder_story: (narrativeReport as any).sections?.founder_story,
        emotional_journey: (narrativeReport as any).sections?.emotional_journey,
        dramatic_arc: (narrativeReport as any).sections?.dramatic_arc,
        pitch_narrative: (narrativeReport as any).sections?.pitch_narrative
      };
      
    } catch (error) {
      console.error('[UnifiedReport] Error generating narrative sections:', error);
      return null;
    }
  }
  
  /**
   * Generate pipeline-enhanced sections
   */
  private async generatePipelineSections(request: UnifiedReportRequest): Promise<any> {
    try {
      const pipelineReport = await this.pipelineService.generateEnhancedReport(
        request.company_name,
        request.business_sector,
        request.products_services || request.additional_context || ''
      );
      
      return {
        similar_companies: pipelineReport.similarCompaniesAnalysis,
        shark_behavior_patterns: pipelineReport.sharkBehaviorPatterns,
        negotiation_insights: pipelineReport.negotiationInsights,
        success_patterns: {
          title: 'Success Patterns',
          content: {
            success_probability: pipelineReport.successPrediction.content,
            key_learnings: pipelineReport.keyLearnings,
            risk_factors: pipelineReport.riskFactors
          },
          confidence_score: pipelineReport.successPrediction.confidence,
          data_sources: [pipelineReport.successPrediction.dataSource],
          insights: pipelineReport.successPrediction.insights,
          recommendations: pipelineReport.actionableRecommendations
        }
      };
      
    } catch (error) {
      console.error('[UnifiedReport] Error generating pipeline sections:', error);
      return null;
    }
  }
  
  /**
   * Find and analyze similar companies
   */
  private async findAndAnalyzeSimilarCompanies(request: UnifiedReportRequest): Promise<any[]> {
    try {
      const similarCompanies = await unifiedSimilarityService.findSimilarCompanies(
        {
          companyName: request.company_name,
          sector: request.business_sector
        },
        5
      );
      
      return similarCompanies;
      
    } catch (error) {
      console.error('[UnifiedReport] Error finding similar companies:', error);
      return [];
    }
  }
  
  /**
   * Merge sections from different sources intelligently
   */
  private mergeSections(
    templateSections: any,
    narrativeSections: any,
    pipelineSections: any
  ): any {
    const merged: any = {};
    
    // Add template sections if available
    if (templateSections) {
      merged.business_analysis = templateSections.company_analysis;
      merged.website_analysis = templateSections.website_analysis;
      merged.market_positioning = templateSections.market_positioning;
      merged.financial_analysis = templateSections.investment_strategy;
      merged.investment_strategy = templateSections.investment_strategy;
      merged.risk_assessment = templateSections.risk_assessment;
    }
    
    // Add narrative sections if available
    if (narrativeSections) {
      merged.founder_story = narrativeSections.founder_story;
      merged.emotional_journey = narrativeSections.emotional_journey;
      merged.dramatic_arc = narrativeSections.dramatic_arc;
      merged.pitch_narrative = narrativeSections.pitch_narrative;
    }
    
    // Add pipeline sections if available
    if (pipelineSections) {
      merged.similar_companies = pipelineSections.similar_companies;
      merged.shark_behavior_patterns = pipelineSections.shark_behavior_patterns;
      merged.negotiation_insights = pipelineSections.negotiation_insights;
      merged.success_patterns = pipelineSections.success_patterns;
    }
    
    return merged;
  }
  
  /**
   * Generate shark compatibility analysis
   */
  private async generateSharkCompatibility(
    request: UnifiedReportRequest,
    similarCompanies: any[],
    pipelineSections: any
  ): Promise<EnhancedReportSection> {
    // Use shark compatibility service for comprehensive shark analysis
    const fundingAmount = request.funding_sought || 5000000;
    const revenueRange = fundingAmount < 1000000 ? '0-10 Lakhs' : 
                          fundingAmount < 10000000 ? '10 Lakhs - 1 Cr' : '1 Cr+';
    
    const sharkCompatibility = await sharkCompatibilityService.getCompatibilityForUser(
      request.business_sector,
      revenueRange,
      false
    );
    
    // Convert compatibility cache to profiles array
    const sharkProfiles = (sharkCompatibility as any).sharks || [];
    
    // Enhance with pipeline insights if available
    if (pipelineSections?.shark_behavior_patterns) {
      const patterns = pipelineSections.shark_behavior_patterns.content;
      
      // Merge insights
      sharkProfiles.forEach((profile: any) => {
        profile.investment_triggers = patterns.investment_triggers || [];
        profile.typical_concerns = patterns.typical_concerns || [];
        profile.negotiation_style = patterns.negotiation_tactics?.[0] || 'Standard';
      });
    }
    
    return {
      section_id: 'shark_compatibility',
      title: 'Shark Compatibility Analysis',
      content: sharkProfiles,
      confidence_score: 0.85,
      data_sources: ['Historical Investment Data', 'Pattern Analysis', 'Multi-Model Review'],
      insights: [
        `Top 3 sharks identified based on sector and investment patterns`,
        `Negotiation strategies tailored for each shark`,
        `Key talking points to resonate with target sharks`
      ],
      recommendations: [
        'Focus pitch on sharks with highest compatibility scores',
        'Prepare specific answers for each shark\'s typical concerns',
        'Have flexibility in equity negotiation'
      ],
      charts: []
    };
  }
  
  /**
   * Generate comprehensive pitch preparation
   */
  private async generatePitchPreparation(
    request: UnifiedReportRequest,
    sections: any,
    similarCompanies: any[]
  ): Promise<EnhancedReportSection> {
    const preparation = {
      pitch_structure: {
        opening_hook: 'Start with compelling problem statement',
        problem_validation: 'Use customer stories and market data',
        solution_presentation: 'Demonstrate unique value clearly',
        traction_proof: 'Show metrics and validation',
        ask_and_vision: 'Clear funding ask with growth vision'
      },
      
      key_messages: [
        `${request.company_name} solves a real problem in ${request.business_sector}`,
        `Our ${request.business_model} model has proven traction`,
        `Seeking ₹${((request.funding_sought || 5000000) / 100000).toFixed(0)}L for ${request.equity_offered || 10}% equity`,
        'Clear path to 10x growth in 2 years'
      ],
      
      visual_aids: [
        'Product demonstration or prototype',
        'Customer testimonial video',
        'Growth metrics dashboard',
        'Competitive advantage slide'
      ],
      
      timing: {
        total_duration: '8-10 minutes',
        sections: {
          hook: '30 seconds',
          problem: '2 minutes',
          solution: '2 minutes',
          business: '2 minutes',
          ask: '1 minute',
          qa_buffer: '20-30 minutes'
        }
      }
    };
    
    return {
      section_id: 'pitch_preparation',
      title: 'Pitch Preparation Guide',
      content: preparation,
      confidence_score: 0.88,
      data_sources: ['Best Practices', 'Success Pattern Analysis'],
      insights: sections.success_patterns?.insights || [],
      recommendations: [
        'Practice pitch multiple times with timer',
        'Prepare backup slides for deep dives',
        'Have financial data ready for scrutiny'
      ],
      charts: []
    };
  }
  
  /**
   * Generate comprehensive question bank
   */
  private async generateQuestionBank(
    request: UnifiedReportRequest,
    sections: any,
    similarCompanies: any[]
  ): Promise<EnhancedReportSection> {
    // Extract questions from various sections and patterns
    const questionCategories = {
      financial: [
        {
          question: 'What is your current monthly burn rate?',
          suggested_answer: 'Provide specific number with breakdown',
          difficulty: 'high'
        },
        {
          question: 'What are your unit economics?',
          suggested_answer: 'Show CAC, LTV, and contribution margin',
          difficulty: 'high'
        }
      ],
      
      market: [
        {
          question: 'What is your total addressable market?',
          suggested_answer: 'Present TAM, SAM, SOM with sources',
          difficulty: 'medium'
        },
        {
          question: 'Who are your main competitors?',
          suggested_answer: 'Name 3-4 with differentiation points',
          difficulty: 'medium'
        }
      ],
      
      scalability: [
        {
          question: 'How will you scale this nationally?',
          suggested_answer: 'Phased expansion plan with timelines',
          difficulty: 'high'
        },
        {
          question: 'What prevents copying by bigger players?',
          suggested_answer: 'Highlight moats and unique advantages',
          difficulty: 'high'
        }
      ],
      
      team: [
        {
          question: 'Why are you the right team for this?',
          suggested_answer: 'Highlight relevant experience and passion',
          difficulty: 'medium'
        },
        {
          question: 'What happens if you get hit by a bus?',
          suggested_answer: 'Show team depth and succession planning',
          difficulty: 'medium'
        }
      ]
    };
    
    // Add questions from pipeline insights if available
    if (sections.negotiation_insights?.content?.typical_concerns) {
      const concerns = sections.negotiation_insights.content.typical_concerns;
      concerns.forEach((concern: string) => {
        questionCategories.financial.push({
          question: concern,
          suggested_answer: 'Address with data and confidence',
          difficulty: 'medium'
        });
      });
    }
    
    return {
      section_id: 'question_bank',
      title: 'Anticipated Questions & Answers',
      content: questionCategories,
      confidence_score: 0.85,
      data_sources: ['Historical Q&A Patterns', 'Shark Preferences'],
      insights: [
        'Financial questions are most common and critical',
        'Prepare numbers and have them memorized',
        'Practice staying calm under tough questioning'
      ],
      recommendations: [
        'Do mock Q&A sessions with advisors',
        'Have backup data ready on phone/tablet',
        'Never say "I don\'t know" - offer to follow up'
      ],
      charts: []
    };
  }
  
  /**
   * Generate executive summary
   */
  private async generateExecutiveSummary(
    request: UnifiedReportRequest,
    sections: any,
    similarCompanies: any[]
  ): Promise<any> {
    // Calculate scores
    const preparationScore = this.calculatePreparationScore(sections);
    const successProbability = sections.success_patterns?.content?.success_probability || 75;
    const overallScore = Math.round((preparationScore + successProbability) / 2);
    
    return {
      overview: `Comprehensive analysis for ${request.company_name} in ${request.business_sector} sector, seeking ₹${((request.funding_sought || 5000000) / 100000).toFixed(0)}L for ${request.equity_offered || 10}% equity.`,
      
      key_findings: [
        `Success probability: ${successProbability}% based on similar companies`,
        `${similarCompanies.length} similar Shark Tank companies analyzed`,
        sections.shark_behavior_patterns ? 'Rich behavioral patterns identified from pipeline data' : 'Shark preferences analyzed',
        sections.founder_story ? 'Compelling narrative arc developed' : 'Strong business case prepared'
      ],
      
      preparation_score: preparationScore,
      success_probability: successProbability,
      overall_score: overallScore,
      
      strategic_recommendations: [
        'Lead with strong problem-solution narrative',
        'Focus on unit economics and growth metrics',
        'Target sharks with sector alignment',
        'Practice handling valuation negotiations',
        'Prepare product demonstration'
      ]
    };
  }
  
  /**
   * Generate appendices
   */
  private async generateAppendices(
    request: UnifiedReportRequest,
    sections: any,
    similarCompanies: any[]
  ): Promise<any> {
    return {
      preparation_checklist: [
        'Finalize pitch deck (10-12 slides)',
        'Practice 8-minute pitch',
        'Prepare financial statements',
        'Gather customer testimonials',
        'Create product demo',
        'Print backup materials',
        'Plan outfit and appearance',
        'Arrange travel and logistics'
      ],
      
      anticipated_questions: sections.question_bank?.content || [],
      
      shark_profiles: sections.shark_compatibility?.content || [],
      
      similar_companies_detailed: similarCompanies.map(c => ({
        name: c.company_name,
        sector: c.business_sector,
        similarity_score: c.similarity_score,
        outcome: c.deal_outcome,
        lessons: c.key_lessons || []
      })),
      
      market_research: {
        market_size: 'Large and growing',
        growth_rate: '25-30% CAGR',
        key_trends: [
          'Digital transformation acceleration',
          'Increased investor interest',
          'Market consolidation opportunity'
        ]
      }
    };
  }
  
  /**
   * Helper method to get sector statistics
   */
  private async getSectorStatistics(sector: string): Promise<any> {
    try {
      const stats = await sql`
        SELECT
          COUNT(*) as total_companies,
          AVG(CASE WHEN deal_closed = true THEN 1 ELSE 0 END) * 100 as success_rate,
          AVG(final_equity_percent) as avg_equity,
          AVG(final_deal_amount_inr) as avg_deal_amount
        FROM companies
        WHERE business_sector_standardized ILIKE ${'%' + sector + '%'}
      `;
      
      return {
        sectorSuccessRate: stats[0]?.success_rate || 65,
        avgDealEquity: stats[0]?.avg_equity || 12.5,
        avgDealAmount: stats[0]?.avg_deal_amount || 4500000
      };
      
    } catch (error) {
      console.error('[UnifiedReport] Error getting sector stats:', error);
      return {
        sectorSuccessRate: 65,
        avgDealEquity: 12.5,
        avgDealAmount: 4500000
      };
    }
  }
  
  /**
   * Calculate preparation score
   */
  private calculatePreparationScore(sections: any): number {
    let score = 70; // Base score
    
    // Add points for each available section
    if (sections.business_analysis) score += 5;
    if (sections.market_positioning) score += 5;
    if (sections.shark_compatibility) score += 5;
    if (sections.pitch_preparation) score += 5;
    if (sections.question_bank) score += 5;
    if (sections.founder_story) score += 5;
    
    return Math.min(score, 95);
  }
  
  /**
   * Calculate confidence score
   */
  private calculateConfidenceScore(report: UnifiedReport): number {
    const scores: number[] = [];
    
    // Collect confidence scores from all sections
    Object.values(report.sections).forEach((section: any) => {
      if (section?.confidence_score) {
        scores.push(section.confidence_score);
      }
    });
    
    if (scores.length === 0) return 0.75;
    
    return scores.reduce((a, b) => a + b, 0) / scores.length;
  }
  
  /**
   * Calculate template coverage
   */
  private calculateTemplateCoverage(report: UnifiedReport): number {
    const expectedSections = [
      'business_analysis',
      'market_positioning', 
      'shark_compatibility',
      'pitch_preparation',
      'question_bank'
    ];
    
    const availableSections = expectedSections.filter(
      section => report.sections[section as keyof typeof report.sections]
    );
    
    return (availableSections.length / expectedSections.length) * 100;
  }
  
  /**
   * Store report in database
   */
  private async storeReport(report: UnifiedReport): Promise<void> {
    try {
      await sql`
        INSERT INTO analysis_sessions (
          session_id,
          company_name,
          analysis_data,
          status,
          created_at,
          updated_at
        ) VALUES (
          ${report.session_id},
          ${report.company_name},
          ${JSON.stringify(report)},
          'unified_report_generated',
          NOW(),
          NOW()
        )
        ON CONFLICT (session_id)
        DO UPDATE SET
          analysis_data = EXCLUDED.analysis_data,
          status = EXCLUDED.status,
          updated_at = NOW()
      `;
      
      console.log(`[UnifiedReport] Report stored for session ${report.session_id}`);
      
    } catch (error) {
      console.error('[UnifiedReport] Error storing report:', error);
    }
  }
  
  /**
   * Retrieve a previously generated report
   */
  async getReport(sessionId: string): Promise<UnifiedReport | null> {
    try {
      const result = await sql`
        SELECT analysis_data
        FROM analysis_sessions
        WHERE session_id = ${sessionId}
          AND status = 'unified_report_generated'
      `;
      
      if (result.length > 0 && result[0].analysis_data) {
        return result[0].analysis_data as UnifiedReport;
      }
      
    } catch (error) {
      console.error('[UnifiedReport] Error retrieving report:', error);
    }
    
    return null;
  }
}

// Export singleton instance for consistency
export const unifiedReportService = new UnifiedReportService();