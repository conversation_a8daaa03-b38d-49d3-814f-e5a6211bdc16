/**
 * RAG-based Similarity Service
 * Uses Vertex AI RAG Store for grounded similarity matching
 */

import '../../../../../lib/env-loader.js';
import { GoogleGenAI } from '@google/genai';

export class RAGSimilarityService {
  private genai: GoogleGenAI;
  private ragCorpusId: string;
  
  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY or GOOGLE_API_KEY environment variable is required for RAGSimilarityService');
    }
    
    this.genai = new GoogleGenAI({ apiKey });
    
    // RAG corpus ID (created by ragStoreSetup.ts)
    const projectId = process.env.GCP_PROJECT_ID || 'your-project-id';
    const location = process.env.GCP_LOCATION || 'us-central1';
    this.ragCorpusId = `projects/${projectId}/locations/${location}/ragCorpora/shark-tank-companies`;
  }
  
  /**
   * Find similar companies using Vertex AI RAG Store
   * This automatically handles embeddings and similarity search
   */
  async findSimilarCompanies(userCompanyData: {
    company_name: string;
    business_model: string;
    business_sector: string;
    what_you_sell: string;
    target_market?: string;
    description?: string;
  }): Promise<any> {
    
    try {
      // Create a comprehensive query from user's company data
      const query = `
        Find companies similar to:
        Company: ${userCompanyData.company_name}
        Business Model: ${userCompanyData.business_model}
        Sector: ${userCompanyData.business_sector}
        Products/Services: ${userCompanyData.what_you_sell}
        Target Market: ${userCompanyData.target_market || 'Not specified'}
        Description: ${userCompanyData.description || 'Not provided'}
        
        Return the most similar companies from Shark Tank India based on:
        1. Same or similar business sector
        2. Similar business model (B2B/B2C, marketplace, etc.)
        3. Similar product offerings
        4. Similar target market
        5. Similar challenges or opportunities
      `;
      
      console.log('🔍 Searching for similar companies using RAG Store...');
      
      // Use Gemini with RAG Store grounding
      // Note: Based on @google/genai documentation, vertexRagStore is specified in tools array
      const response = await this.genai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: [{ 
          role: 'user', 
          parts: [{ text: query }] 
        }],
        tools: [{
          retrieval: {
            vertexRagStore: {
              ragResources: [{ 
                ragCorpus: this.ragCorpusId 
              }],
              similarityTopK: 10,  // Return top 10 similar companies
              vectorDistanceThreshold: 0.8  // Similarity threshold
            }
          }
        }],
        config: {
          // Response configuration
          temperature: 0.1,
          maxOutputTokens: 4000,
          responseMimeType: 'application/json',
          responseJsonSchema: {
            type: 'object',
            properties: {
              similar_companies: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    name: { type: 'string' },
                    season: { type: 'number' },
                    episode: { type: 'number' },
                    industry: { type: 'string' },
                    similarity_score: { type: 'number' },
                    similarity_reasons: { 
                      type: 'array',
                      items: { type: 'string' }
                    },
                    pitch_summary: { type: 'string' },
                    deal_outcome: { type: 'string' },
                    key_insights: { type: 'string' }
                  },
                  required: ['name', 'similarity_score', 'similarity_reasons']
                }
              },
              analysis_summary: { type: 'string' }
            },
            required: ['similar_companies', 'analysis_summary']
          }
        }
      });
      
      const result = JSON.parse(response.text || '{}');
      
      // Extract grounding metadata for transparency
      const groundingMetadata = response.candidates?.[0]?.groundingMetadata;
      if (groundingMetadata?.groundingChunks) {
        console.log(`✅ Found ${groundingMetadata.groundingChunks.length} grounded matches`);
        
        // Add grounding sources to result
        result.grounding_sources = groundingMetadata.groundingChunks.map(chunk => ({
          text: chunk.retrievedContext?.text?.substring(0, 200) + '...',
          title: chunk.retrievedContext?.title,
          uri: chunk.retrievedContext?.uri
        }));
      }
      
      return result;
      
    } catch (error) {
      console.error('❌ RAG similarity search failed:', error);
      throw error;
    }
  }
  
  /**
   * Get detailed comparison with a specific company
   */
  async compareWithCompany(userCompanyData: any, targetCompanyName: string): Promise<any> {
    if (!this.genai) {
      console.warn('[RAGSimilarityService] Cannot compare companies - Gemini client not configured');
      return 'Company comparison not available due to missing API key configuration.';
    }
    
    try {
      const query = `
        Compare my company with ${targetCompanyName} from Shark Tank India:
        
        My Company:
        ${JSON.stringify(userCompanyData, null, 2)}
        
        Provide a detailed comparison including:
        1. Business model similarities and differences
        2. Market approach comparison
        3. Financial metrics comparison (if available)
        4. Shark feedback relevance
        5. Lessons to learn from their pitch
        6. What worked and what didn't work for them
      `;
      
      const response = await this.genai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: [{ 
          role: 'user', 
          parts: [{ text: query }] 
        }],
        tools: [{
          retrieval: {
            vertexRagStore: {
              ragResources: [{ 
                ragCorpus: this.ragCorpusId 
              }],
              similarityTopK: 1,  // Focus on specific company
              vectorDistanceThreshold: 0.9
            }
          }
        }],
        config: {
          temperature: 0.2,
          maxOutputTokens: 4000
        }
      });
      
      return response.text;
      
    } catch (error) {
      console.error('❌ Company comparison failed:', error);
      throw error;
    }
  }
  
  /**
   * Get shark-specific insights based on similar companies
   */
  async getSharkInsights(userCompanyData: any, targetShark: string): Promise<any> {
    if (!this.genai) {
      console.warn('[RAGSimilarityService] Cannot get shark insights - Gemini client not configured');
      return 'Shark insights not available due to missing API key configuration.';
    }
    
    try {
      const query = `
        Based on companies similar to mine, what are ${targetShark}'s patterns and preferences?
        
        My Company:
        ${JSON.stringify(userCompanyData, null, 2)}
        
        Analyze ${targetShark}'s behavior with similar companies:
        1. Investment patterns in this sector
        2. Common questions they ask
        3. Deal breakers for them
        4. What excites them
        5. Negotiation style
        6. Success stories with similar companies
      `;
      
      const response = await this.genai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: [{ 
          role: 'user', 
          parts: [{ text: query }] 
        }],
        tools: [{
          retrieval: {
            vertexRagStore: {
              ragResources: [{ 
                ragCorpus: this.ragCorpusId 
              }],
              similarityTopK: 15,  // Get more companies for pattern analysis
              vectorDistanceThreshold: 0.7
            }
          }
        }],
        config: {
          temperature: 0.3,
          maxOutputTokens: 4000
        }
      });
      
      return response.text;
      
    } catch (error) {
      console.error('❌ Shark insights generation failed:', error);
      throw error;
    }
  }
}

// Create and export singleton instance
export const ragSimilarityService = new RAGSimilarityService();

export default RAGSimilarityService;