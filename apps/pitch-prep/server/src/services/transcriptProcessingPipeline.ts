#!/usr/bin/env bun

/**
 * CONSOLIDATED Modern 7-Phase Transcript Processing Pipeline
 * 
 * This is the SINGLE, UNIFIED pipeline that replaces all duplicate services.
 * Implements the complete approved user plan:
 * 
 * 1. Multi-phase extraction with structured output using @google/genai SDK
 * 2. Grounding with Shark Tank database
 * 3. Context caching for repeated analysis
 * 4. Streaming for progress updates
 * 5. Exponential backoff for rate limiting
 * 6. Flexible number extraction with context
 * 7. Narrative & evidence preservation
 * 
 * Converts: raw transcripts (.text) → 7-phase analysis → rich narratives → database
 */

import '../../../../../lib/env-loader.js';
import { GoogleGenAI, Type } from '@google/genai';
import { sql } from '../database/connection.js';
import { existsSync, readFileSync } from 'fs';

// Modern extraction result interface with enhanced similarity intelligence
export interface ModernExtractionResult {
  // Phase 1: Basic Company Info + Similarity Tags
  basicInfo: {
    company_name: string;
    company_name_transliterated?: string;
    founder_names: string[];
    business_description: string;
    sector: string;
    confidence: number;
    
    // SIMILARITY TAGS for better matching
    similarity_tags: {
      business_model_specifics: string[];    // "B2B SaaS", "D2C marketplace", "hardware + subscription"
      target_market_details: string[];       // "cricket academies", "health-conscious millennials", "tier-2 cities"
      competitive_moat: string[];            // "proprietary algorithm", "first-mover advantage", "network effects"
      founder_background: string[];          // "ex-McKinsey", "domain expert", "repeat founder", "technical background"
      geographic_focus: string[];            // "pan-India", "tier-2 cities", "international", "metro-focused"
      technology_stack: string[];            // "AI/ML", "mobile-first", "IoT sensors", "blockchain"
      funding_stage: string;                 // "pre-revenue", "early-traction", "scaling", "profitable"
    };
    
    // NARRATIVE FIELDS (existing)
    founder_journey?: string;
    company_story?: string;
    innovation_narrative?: string;
  };
  
  // Phase 2: Financial Data + Benchmarking Intelligence
  financials: {
    ask_amount: number;
    ask_equity: number;
    ask_valuation: number;
    current_revenue: number;
    previous_year_revenue: number;
    gross_margin: number;
    burn_rate?: number;
    confidence: number;
    
    // FINANCIAL INTELLIGENCE for benchmarking
    financial_intelligence: {
      revenue_model_type: string[];          // "subscription", "hardware sales", "commission", "freemium"
      unit_economics: {
        cac?: number;                        // Customer acquisition cost
        ltv?: number;                        // Lifetime value
        payback_period?: number;             // Months to payback CAC
        gross_margin_category: string;       // "high" (>70%), "medium" (30-70%), "low" (<30%)
      };
      growth_metrics: {
        revenue_growth_rate?: number;        // YoY growth percentage
        monthly_growth_rate?: number;        // MoM growth
        growth_stage: string;                // "pre-revenue", "early-growth", "rapid-scale", "mature"
      };
      funding_context: {
        previous_funding?: number;           // Total raised before
        runway_months?: number;              // Cash runway
        burn_multiple?: number;              // Burn rate / net new revenue
      };
    };
    
    // BENCHMARKING CONTEXT vs similar companies
    benchmarking_context: {
      valuation_vs_sector: string;          // "above average", "market rate", "below average", "premium"
      revenue_multiple: number;              // Valuation / ARR
      growth_percentile: string;             // "top 10%", "above average", "below average"
      financial_maturity: string;           // "early", "developing", "mature"
    };
    
    // NARRATIVE FIELDS (existing)
    financial_narrative?: string;
    growth_story?: string;
  };
  
  // Phase 3: Business Model + Strategic Intelligence
  businessModel: {
    model_type: 'B2B' | 'B2C' | 'D2C' | 'B2B2C' | 'Marketplace' | 'B2B/B2C' | 'Hardware + Software';
    revenue_streams: string[];
    target_market: string;
    competitive_advantage: string[];
    scalability_factors: string[];
    confidence: number;
    
    // STRATEGIC INTELLIGENCE for positioning
    strategic_intelligence: {
      market_positioning: {
        market_size_category: string;        // "niche", "large addressable", "massive TAM"
        competitive_landscape: string;        // "blue ocean", "crowded", "emerging", "mature"
        differentiation_strength: string;    // "strong moat", "moderate differentiation", "me-too"
        timing_advantage: string;            // "first-mover", "fast-follower", "market-ready"
      };
      business_model_complexity: {
        operational_complexity: string;       // "simple", "moderate", "complex"
        technology_dependency: string;       // "low-tech", "tech-enabled", "deep-tech"
        regulatory_requirements: string;     // "minimal", "moderate", "heavy"
        capital_intensity: string;           // "asset-light", "moderate capex", "capital-intensive"
      };
      go_to_market: {
        customer_acquisition_strategy: string[]; // "direct sales", "digital marketing", "partnerships"
        distribution_channels: string[];     // "online", "retail", "B2B sales", "marketplace"
        market_entry_approach: string;       // "bottom-up", "top-down", "horizontal", "vertical"
      };
    };
    
    // SCALABILITY ANALYSIS
    scalability_analysis: {
      network_effects: string;              // "strong", "moderate", "minimal", "none"
      economies_of_scale: string;           // "significant", "moderate", "limited"
      international_potential: string;      // "high", "moderate", "limited", "local-only"
      platform_potential: string;           // "ecosystem play", "single product", "suite approach"
    };
    
    // NARRATIVE FIELDS (existing)
    strategic_narrative?: string;
    market_opportunity_story?: string;
  };
  
  // Phase 4: Deal Dynamics + Negotiation Intelligence
  dealDynamics: {
    sharks_interested: string[];
    individual_offers: Array<{shark: string; amount: number; equity: number}>;
    deal_closed: boolean;
    final_deal?: {amount: number; equity: number; sharks: string[]};
    negotiation_highlights: string[];
    confidence: number;
    
    // NEGOTIATION INTELLIGENCE
    negotiation_intelligence: {
      deal_structure_analysis: {
        equity_vs_debt_mix: string;         // "equity-only", "hybrid", "debt-heavy"
        valuation_negotiation: {
          initial_valuation: number;
          final_valuation?: number;
          discount_percentage?: number;     // How much valuation was reduced
        };
        deal_complexity: string;            // "standard", "complex", "innovative"
      };
      shark_interaction_patterns: {
        primary_negotiator: string;         // Which shark led negotiations
        collaborative_sharks: string[];    // Sharks that worked together
        competitive_dynamics: string;      // "competitive bidding", "collaborative", "single interest"
        decision_timeline: string;          // "immediate", "delayed", "multiple rounds"
      };
      success_factors: {
        key_persuasion_points: string[];   // What convinced sharks
        tipping_point_moments: string[];   // Moments that changed dynamics
        founders_negotiation_style: string; // "collaborative", "firm", "flexible"
      };
    };
    
    // DEAL OUTCOME ANALYSIS
    deal_outcome_analysis: {
      outcome_type: string;                 // "no-deal", "single-shark", "multi-shark", "bidding-war"
      rejection_reasons: string[];          // Why sharks opted out
      success_probability_factors: string[]; // What increased chances
      market_timing_impact: string;        // How timing affected deal
    };
    
    // NARRATIVE FIELDS (existing)
    negotiation_narrative?: string;
    turning_points?: string;
  };
  
  // Phase 5: Shark Analysis + Psychology Intelligence
  sharkAnalysis: {
    sharks_present: string[];
    individual_shark_reactions: string[];
    panel_dynamics: string;
    competition_level: 'high' | 'medium' | 'low' | 'none';
    confidence: number;
    
    // SHARK PSYCHOLOGY INTELLIGENCE
    shark_psychology_intelligence: {
      individual_shark_profiles: {
        anupam_mittal?: {
          investment_interest_level: number;   // 1-10 scale
          sector_alignment: string;           // "perfect fit", "adjacent", "stretch"
          key_concerns_raised: string[];      // Specific objections from this shark
          positive_signals: string[];         // What they liked
          question_themes: string[];          // Categories of questions asked
          decision_factors: string[];         // What influenced their decision
          negotiation_style: string;          // "aggressive", "collaborative", "analytical"
        };
        aman_gupta?: {
          investment_interest_level: number;
          sector_alignment: string;
          key_concerns_raised: string[];
          positive_signals: string[];
          question_themes: string[];
          decision_factors: string[];
          negotiation_style: string;
        };
        vineeta_singh?: {
          investment_interest_level: number;
          sector_alignment: string;
          key_concerns_raised: string[];
          positive_signals: string[];
          question_themes: string[];
          decision_factors: string[];
          negotiation_style: string;
        };
        peyush_bansal?: {
          investment_interest_level: number;
          sector_alignment: string;
          key_concerns_raised: string[];
          positive_signals: string[];
          question_themes: string[];
          decision_factors: string[];
          negotiation_style: string;
        };
        namita_thapar?: {
          investment_interest_level: number;
          sector_alignment: string;
          key_concerns_raised: string[];
          positive_signals: string[];
          question_themes: string[];
          decision_factors: string[];
          negotiation_style: string;
        };
      };
      
      behavioral_patterns: {
        dominance_hierarchy: string[];      // Order of shark influence
        collaboration_instances: string[]; // When sharks worked together
        competitive_moments: string[];     // When sharks competed
        expertise_deference: string[];     // When sharks deferred to others' expertise
      };
      
      decision_psychology: {
        herd_mentality_instances: string[]; // Following other sharks' lead
        contrarian_positions: string[];     // Going against the group
        risk_tolerance_displayed: string;   // "high", "moderate", "conservative"
        time_pressure_impact: string;      // How urgency affected decisions
      };
    };
    
    // SECTOR-SPECIFIC SHARK PREFERENCES
    sector_preferences: {
      sector_specialists: string[];        // Sharks with domain expertise
      sector_newcomers: string[];          // Sharks outside comfort zone
      investment_pattern_match: string;    // How this pitch fits their portfolio
    };
    
    // NARRATIVE FIELDS (existing)
    shark_personality_analysis?: string;
    group_dynamics_narrative?: string;
  };
  
  // Phase 6: Strategic Insights + Actionable Intelligence
  strategicInsights: {
    success_factors: string[];
    critical_mistakes: string[];
    lessons_learned: string[];
    sector_specific_advice: string[];
    confidence: number;
    
    // ACTIONABLE BUSINESS INTELLIGENCE
    actionable_intelligence: {
      preparation_recommendations: {
        pitch_optimization_areas: string[]; // "Emphasize unit economics", "Show market traction"
        expected_shark_questions: string[]; // Based on similar companies
        risk_mitigation_strategies: string[]; // "Address competition concerns upfront"
        strength_amplification: string[];   // "Leverage first-mover advantage"
      };
      
      sector_insights: {
        sector_success_patterns: string[];  // What works in this industry
        common_failure_modes: string[];     // What typically goes wrong
        investor_preferences: string[];     // What sharks value in this sector
        market_timing_factors: string[];    // Timing considerations
      };
      
      competitive_intelligence: {
        differentiation_opportunities: string[]; // How to stand out
        positioning_recommendations: string[];   // Market positioning advice
        moat_strengthening_advice: string[];    // Building competitive advantages
      };
    };
    
    // SUCCESS PROBABILITY ANALYSIS
    success_probability_analysis: {
      overall_probability_score: number;   // 0-100 based on historical patterns
      positive_indicators: string[];       // What increases success chances
      risk_indicators: string[];           // What decreases success chances
      optimization_opportunities: string[]; // How to improve odds
      similar_company_benchmarks: string; // Performance vs similar companies
    };
    
    // NARRATIVE FIELDS (existing)
    comprehensive_analysis?: string;
    entrepreneurial_lessons?: string;
    sector_trends?: string;
    business_model_wisdom?: string;
    future_predictions?: string;
    investor_psychology_insights?: string;
    market_timing_analysis?: string;
    strategic_insights_story?: string;
  };
  
  // Phase 7: Rich Narrative + Actionable Content
  narrative: {
    complete_story: string; // 2000+ word comprehensive story
    executive_summary: string; // 200-word summary
    key_quotes: string[];
    searchable_text: string;
    confidence: number;
    
    // ACTIONABLE CONTENT for app users
    actionable_content: {
      case_study_summary: {
        what_happened: string;              // Brief outcome summary
        why_it_worked_or_failed: string;    // Key success/failure factors
        what_others_can_learn: string;      // Actionable lessons
      };
      
      preparation_playbook: {
        key_talking_points: string[];       // What to emphasize in pitch
        potential_objections: string[];     // What sharks might question
        preparation_checklist: string[];   // Pre-pitch preparation items
        pitch_flow_recommendations: string[]; // How to structure the pitch
      };
      
      shark_specific_guidance: {
        shark_targeting_advice: string[];   // Which sharks to focus on
        sector_positioning_tips: string[];  // How to position in this sector
        timing_considerations: string[];    // When to mention what
      };
      
      comparable_insights: {
        similar_company_references: string[]; // "Companies like X did Y"
        success_pattern_analysis: string;    // Common patterns in similar companies
        differentiation_opportunities: string; // How to stand out from similar companies
      };
    };
    
    // STRUCTURED CONTENT for reports
    structured_content: {
      investor_memo_format: string;        // Professional investor summary
      preparation_brief: string;           // Executive brief for preparation
      lessons_digest: string;              // Key lessons in digestible format
      comparison_framework: string;        // How this compares to others
    };
  };
  
  // Phase 8: Advanced Shark Intelligence (NEW)
  advancedSharkIntelligence: {
    confidence: number;
    
    // SHARK-SPECIFIC REACTION ANALYSIS
    shark_specific_reactions: Record<string, {
      interest_level: number;             // 1-10 scale based on verbal and non-verbal cues
      key_questions_asked: string[];      // Actual questions from transcript
      objection_themes: string[];         // "unit economics", "scalability concerns", "market size"
      positive_signals: string[];         // "I like this", "good traction", "interesting"
      negative_signals: string[];         // "I'm out", "too risky", "not for me"
      investment_decision_factors: string[]; // What influenced their final decision
      expertise_demonstrated: string[];   // Domain knowledge they showed
      deal_structure_preferences: string; // Equity vs debt vs royalty preferences
    }>;
    
    // NEGOTIATION PATTERN ANALYSIS
    negotiation_patterns: {
      primary_objections: string[];       // Common themes across all sharks
      deal_breakers: string[];            // What made sharks exit definitively
      success_factors: string[];          // What sealed the deal
      valuation_feedback: {
        too_high_signals: string[];       // "Valuation is aggressive"
        fair_value_signals: string[];     // "Reasonable valuation"
        undervalued_signals: string[];    // "This could be worth more"
      };
      timing_dynamics: {
        quick_decisions: string[];         // Sharks who decided fast
        deliberate_decisions: string[];   // Sharks who took time
        influenced_decisions: string[];   // Sharks swayed by others
      };
    };
    
    // SHARK COMPATIBILITY INTELLIGENCE
    compatibility_intelligence: {
      sector_fit_analysis: Record<string, {
        perfect_fit_reasons: string[];    // Why this shark is ideal for this sector
        stretch_reasons: string[];        // Why this might be outside their wheelhouse
        historical_pattern_match: string; // How this compares to their past investments
      }>;
      
      investment_style_analysis: Record<string, {
        risk_tolerance_demonstrated: string; // "high", "moderate", "conservative"
        hands_on_vs_passive: string;        // "active mentor", "strategic advisor", "financial investor"
        decision_speed: string;             // "quick", "deliberate", "consensus-driven"
        collaboration_style: string;       // "competitive", "collaborative", "independent"
      }>;
    };
    
    // PREPARATION INTELLIGENCE for future pitches
    preparation_intelligence: {
      shark_targeting_recommendations: {
        best_fit_sharks: string[];         // Most likely to invest based on patterns
        avoid_sharks: string[];            // Sharks unlikely to engage
        backup_options: string[];          // Alternative sharks to consider
      };
      
      pitch_optimization_by_shark: Record<string, {
        key_talking_points: string[];      // What to emphasize for this shark
        expected_questions: string[];      // Questions they typically ask
        proof_points_needed: string[];     // Evidence they look for
        red_flags_to_avoid: string[];      // What turns them off
      }>;
      
      deal_negotiation_insights: {
        typical_deal_structures: string[]; // Common patterns they use
        negotiation_styles: string[];      // How they typically negotiate
        decision_timeline: string;         // How long they usually take
        co_investment_preferences: string[]; // Who they like to partner with
      };
    };
  };
  
  // Phase 8: Advanced Shark Intelligence
  advancedIntelligence: {
    shark_reactions: {
      anupam_mittal?: {
        initial_reaction: string;
        key_questions_asked: string[];
        decision_turning_point: string;
        emotional_engagement: string;
        expertise_alignment: string;
      };
      aman_gupta?: {
        initial_reaction: string;
        key_questions_asked: string[];
        decision_turning_point: string;
        emotional_engagement: string;
        expertise_alignment: string;
      };
      vineeta_singh?: {
        initial_reaction: string;
        key_questions_asked: string[];
        decision_turning_point: string;
        emotional_engagement: string;
        expertise_alignment: string;
      };
      peyush_bansal?: {
        initial_reaction: string;
        key_questions_asked: string[];
        decision_turning_point: string;
        emotional_engagement: string;
        expertise_alignment: string;
      };
      namita_thapar?: {
        initial_reaction: string;
        key_questions_asked: string[];
        decision_turning_point: string;
        emotional_engagement: string;
        expertise_alignment: string;
      };
    };
    preparation_intelligence: {
      critical_preparation_gaps: string[];
      successful_preparation_elements: string[];
      sector_specific_insights: string[];
      pitch_timing_analysis: string;
      market_readiness_assessment: string;
    };
    actionable_recommendations: {
      for_similar_businesses: string[];
      shark_specific_strategies: string[];
      deal_structure_recommendations: string[];
      presentation_optimization: string[];
      risk_mitigation_strategies: string[];
    };
    intelligence_narrative: string;
    confidence: number;
  };
  
  // Overall metadata
  metadata: {
    processing_timestamp: string;
    total_phases_completed: number;
    overall_confidence: number;
    processing_time_seconds: number;
    model_version: string;
    pipeline_version: string;
  };
}

export class TranscriptProcessingPipeline {
  private genai: GoogleGenAI;
  
  constructor() {
    const apiKey = process.env.GOOGLE_API_KEY || process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('GOOGLE_API_KEY or GEMINI_API_KEY is required for modern extraction pipeline');
    }
    this.genai = new GoogleGenAI({ apiKey });
  }

  /**
   * Extract with exponential backoff for rate limiting
   */
  private async extractWithRetry(operation: () => Promise<any>, retryCount = 0): Promise<any> {
    const delays = [60000, 120000, 240000, 300000]; // 60s, 120s, 240s, 300s
    
    try {
      return await operation();
    } catch (error: any) {
      if (error.message?.includes('429') && retryCount < delays.length) {
        const delay = delays[retryCount];
        console.log(`⏳ Rate limited. Waiting ${delay / 1000}s before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.extractWithRetry(operation, retryCount + 1);
      }
      throw error;
    }
  }

  /**
   * Normalize confidence values from Gemini (0-1 decimals to 0-100 percentages)
   */
  private normalizeConfidence(value: number | undefined): number {
    if (value === undefined || value === null) return 0;
    // If confidence is between 0-1, convert to 0-100 percentage
    if (value > 0 && value < 1) {
      return Math.round(value * 100);
    }
    // If already in 0-100 range or invalid, return as-is (capped at 100)
    return Math.min(Math.round(value), 100);
  }

  /**
   * Normalize all confidence values in a result object
   */
  private normalizeResultConfidence(result: any): any {
    if (result && typeof result === 'object') {
      if (result.confidence !== undefined) {
        result.confidence = this.normalizeConfidence(result.confidence);
      }
      // Handle nested confidence values
      Object.keys(result).forEach(key => {
        if (typeof result[key] === 'object' && result[key] !== null) {
          if (Array.isArray(result[key])) {
            result[key] = result[key].map((item: any) => 
              typeof item === 'object' ? this.normalizeResultConfidence(item) : item
            );
          } else {
            result[key] = this.normalizeResultConfidence(result[key]);
          }
        }
      });
    }
    return result;
  }

  /**
   * No preprocessing needed - Gemini handles mixed Hindi/English content naturally
   */

  /**
   * Phase 1: Extract basic company information with English-only structured output
   */
  async extractPhase1(transcript: string): Promise<any> {
    console.log('  📋 Phase 1: COMPREHENSIVE Company Information...');
    
    const prompt = `You are an expert business analyst with deep insights into startup ecosystems and entrepreneurial journeys. Analyze this Shark Tank India transcript to extract comprehensive company intelligence - both structured data AND rich narrative insights.

MISSION: Create a complete company profile that captures both the facts AND the story. Extract structured information while preserving the human elements, personal journeys, and unique characteristics that make each startup memorable.

CRITICAL EXTRACTION RULES:
1. COMPANY NAME: Always extract the actual company name. Never use "Unknown". Look for phrases like "our company", "we are", "brand name", or when founders introduce themselves.
2. FOUNDERS: Extract each founder's name ONLY ONCE. Do not duplicate founders even if mentioned multiple times.
3. SECTOR: Choose from standard sectors: Food & Beverages, Technology, Healthcare, Fashion & Apparel, Beauty & Personal Care, Education, Consumer Electronics, Pet Care, Home & Living, Sports & Fitness, Agriculture, Automotive, Finance, Entertainment, Travel & Tourism, B2B Services. Never use "Unknown".

COMPREHENSIVE ANALYSIS FRAMEWORK (enriched by 160+ data field insights):

🏢 COMPANY FUNDAMENTALS & STORY:
Extract company name (REQUIRED - look for brand names, company introductions), sector (from standard list above), and core business description. Capture the founder's vision, the problem they're solving, and the company's origin story. What inspired this venture?

👥 FOUNDER PROFILES & JOURNEYS:
Extract each founder ONLY ONCE - no duplicates. Go beyond names and roles - understand their backgrounds, motivations, personal stories, equity arrangements, expertise, and communication styles. What drives them? What's their unique journey?

🛠️ PRODUCT & INNOVATION NARRATIVE:
Capture not just what the product is, but how it works, its technical specifications, innovation factors, development journey, and the story behind its creation.

📊 BUSINESS INTELLIGENCE & STRATEGY:
Extract business model insights, revenue streams, market positioning, competitive landscape, customer segments, and growth strategies. Include the strategic thinking behind their approach.

🎯 MARKET CONTEXT & OPPORTUNITY:
Understand the market opportunity, competitive advantages, scalability factors, industry trends, and long-term vision.

EXTRACTION APPROACH:
- Extract ALL factual data (names, numbers, percentages, metrics, specifications)
- Preserve founder personalities and communication styles
- Capture the emotional context and passion behind the business
- Document product innovation stories and technical details
- Note market insights and competitive thinking
- Include unique details that make this company interesting
- Translate Hindi terms but preserve original emotional context
- Tell the story while maintaining data accuracy
- Include implicit insights and reading between the lines

CRITICAL: Balance comprehensive data extraction with narrative richness. This should read like both a data report AND a compelling business story.

FULL TRANSCRIPT TO ANALYZE:
${transcript}`;

    try {
      const result = await this.extractWithRetry(async () => {
        return await this.genai.models.generateContent({
          model: 'gemini-2.0-flash-001',
          contents: [{ role: 'user', parts: [{ text: prompt }] }],
          config: {
            temperature: 0.6,
            maxOutputTokens: 8192,
            responseMimeType: 'application/json',
            responseSchema: {
              type: Type.OBJECT,
              properties: {
                // STRUCTURED DATA FIELDS
                company_name: { 
                  type: Type.STRING, 
                  description: "Company name in English (translate from Hindi if needed)" 
                },
                company_name_hindi: {
                  type: Type.STRING,
                  description: "Original Hindi/local name if mentioned"
                },
                founders: {
                  type: Type.ARRAY,
                  items: {
                    type: Type.OBJECT,
                    properties: {
                      name: { type: Type.STRING, description: "Founder name" },
                      role: { type: Type.STRING, description: "Role or designation if mentioned" },
                      background: { type: Type.STRING, description: "Background, education, or experience if mentioned" },
                      equity_percentage: { type: Type.NUMBER, description: "Equity percentage if mentioned" }
                    }
                  },
                  description: "Detailed founder information"
                },
                business_description: {
                  type: Type.STRING,
                  description: "Comprehensive business description including what they do and how"
                },
                product_details: {
                  type: Type.OBJECT,
                  properties: {
                    product_name: { type: Type.STRING, description: "Product/service name" },
                    technical_specifications: { type: Type.STRING, description: "Technical details, specifications, components" },
                    how_it_works: { type: Type.STRING, description: "Detailed explanation of product functionality" },
                    manufacturing_process: { type: Type.STRING, description: "Manufacturing or production process if mentioned" },
                    key_features: { type: Type.ARRAY, items: { type: Type.STRING }, description: "List of product features" }
                  }
                },
                sector: {
                  type: Type.STRING,
                  description: "Industry sector and sub-categories"
                },
                target_market: {
                  type: Type.STRING,
                  description: "Target customers, market segments, and positioning"
                },
                competitive_advantages: {
                  type: Type.ARRAY,
                  items: { type: Type.STRING },
                  description: "Competitive advantages and unique selling points"
                },
                market_opportunity: {
                  type: Type.STRING,
                  description: "Market size, opportunity, and problem being solved"
                },
                
                // ENHANCED NARRATIVE FIELDS
                founder_journey: {
                  type: Type.STRING,
                  description: "Rich narrative about the founders' personal stories, backgrounds, motivations, and the journey that led them to create this company. Include personality insights and communication styles."
                },
                company_story: {
                  type: Type.STRING,
                  description: "Compelling narrative about the company's origin story, the problem they discovered, the inspiration behind the solution, and key milestones in their development journey."
                },
                innovation_narrative: {
                  type: Type.STRING,
                  description: "Detailed story about the product innovation - how it was developed, what makes it unique, the technical challenges overcome, and the creative process behind its creation."
                },
                market_context_story: {
                  type: Type.STRING,
                  description: "Rich context about the market landscape, competitive environment, industry trends, and where this company fits in the bigger picture."
                },
                key_insights: {
                  type: Type.ARRAY,
                  items: { type: Type.STRING },
                  description: "Unexpected insights, strategic observations, or interesting details that make this company unique and memorable."
                },
                
                // SIMILARITY TAGS for better matching and intelligence
                similarity_tags: {
                  type: Type.OBJECT,
                  properties: {
                    business_model_specifics: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: "Specific business model descriptors: 'B2B SaaS', 'D2C marketplace', 'hardware + subscription', 'franchise model'"
                    },
                    target_market_details: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: "Specific target market segments: 'cricket academies', 'health-conscious millennials', 'tier-2 cities', 'enterprise customers'"
                    },
                    competitive_moat: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: "Competitive advantages: 'proprietary algorithm', 'first-mover advantage', 'network effects', 'exclusive partnerships'"
                    },
                    founder_background: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: "Founder expertise: 'ex-McKinsey', 'domain expert', 'repeat founder', 'technical background', 'industry veteran'"
                    },
                    geographic_focus: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: "Geographic strategy: 'pan-India', 'tier-2 cities', 'international', 'metro-focused', 'rural markets'"
                    },
                    technology_stack: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: "Technology approach: 'AI/ML', 'mobile-first', 'IoT sensors', 'blockchain', 'cloud-native'"
                    },
                    funding_stage: {
                      type: Type.STRING,
                      description: "Current stage: 'pre-revenue', 'early-traction', 'scaling', 'profitable', 'break-even'"
                    }
                  },
                  description: "Tags for similarity matching and intelligence analysis"
                },
                
                // TRADITIONAL FIELDS
                key_quotes: {
                  type: Type.ARRAY,
                  items: { type: Type.STRING },
                  description: "Important quotes from founders (preserve Hindi terms in parentheses)"
                },
                additional_context: {
                  type: Type.STRING,
                  description: "Any additional interesting details, context, or insights that don't fit other categories but add depth to understanding this company."
                },
                confidence: {
                  type: Type.NUMBER,
                  description: "Confidence in extraction quality (70-95 range)"
                }
              },
              required: ["company_name", "founders", "business_description", "sector", "similarity_tags", "founder_journey", "company_story", "confidence"]
            }
          }
        });
      });

      // Debug output for Phase 1
      if (process.env.DEBUG_EXTRACTION === 'true') {
        console.log(`\n🔍 Phase 1 Raw Response (${result.text?.length || 0} chars):`);
        console.log(result.text?.substring(0, 1000));
        console.log('...\n');
      }

      const parsedResult = JSON.parse(result.text || '{}');
      return this.normalizeResultConfidence(parsedResult);
    } catch (error) {
      console.error('  ❌ Phase 1 extraction failed:', error);
      return { confidence: 0 };
    }
  }

  /**
   * Phase 2: Extract financial data with context from Phase 1
   */
  async extractPhase2(transcript: string, phase1Data: any): Promise<any> {
    console.log('  💰 Phase 2: Financial Data...');
    
    const prompt = `Extract comprehensive financial intelligence for ${phase1Data.company_name} while capturing the story behind the numbers. Create both structured financial data AND the narrative context that explains their financial journey.

COMPANY CONTEXT:
${phase1Data.company_name} - ${phase1Data.business_description} (${phase1Data.sector})

CRITICAL FINANCIAL EXTRACTION RULES:
1. HINDI NUMBER CONVERSION: 
   - 1 lakh = 100,000 (1,00,000)
   - 10 lakhs = 1,000,000 (10,00,000) 
   - 1 crore = 10,000,000 (1,00,00,000)
   - 10 crores = 100,000,000 (10,00,00,000)
2. ALWAYS extract actual amounts. Never use 0 for ask_amount or ask_equity unless explicitly stated as zero.
3. When amounts are mentioned in crores, convert to the base unit (rupees).
4. Look for phrases like "X lakhs for Y%", "valuation of Z crores", "asking for", "equity stake"

FINANCIAL INTELLIGENCE MISSION (enriched by 160+ data field insights):

🏦 FUNDING & VALUATION STORY:
Extract ask amount (REQUIRED - look for "asking for X lakhs/crores"), equity offered (REQUIRED - look for "Y% equity"), and valuation details. But also capture the story - how did they arrive at this valuation? What's their funding journey? What do the numbers reveal about their confidence and strategy?

📈 REVENUE & GROWTH NARRATIVE:
Document current revenue, growth rates, and projections. Tell the story of their revenue journey - where did they start? What drove growth? What challenges did they overcome? What's the trajectory story?

💰 UNIT ECONOMICS & PROFITABILITY JOURNEY:
Capture margins, CAC, LTV, AOV. Explain the business model economics - how do they make money per customer? What's the path to profitability? How do the unit economics evolve?

⚙️ OPERATIONAL EFFICIENCY STORY:
Extract operational costs, burn rate, team efficiency. Describe their operational journey - how lean or capital-intensive are they? What's their path to operational excellence?

📊 BUSINESS SCALE CONTEXT:
Document team size, customer metrics, capacity. Tell the scaling story - where are they in their growth journey? What bottlenecks exist? How do they plan to scale?

🎯 SECTOR-SPECIFIC INSIGHTS:
Include sector-relevant metrics with context about industry standards and competitive positioning.

EXTRACTION APPROACH:
- Extract ALL numerical data with precision (amounts, percentages, ratios)
- Preserve the context around each financial detail
- Capture the founder's financial story and reasoning
- Note the business model's financial logic
- Include any financial projections or targets mentioned
- Document the timeline of financial milestones
- Explain what the numbers reveal about business health
- Include investor concerns or validation mentioned

FULL TRANSCRIPT ANALYSIS:
${transcript}`;

    try {
      const result = await this.extractWithRetry(async () => {
        return await this.genai.models.generateContent({
          model: 'gemini-2.0-flash-001',
          contents: [{ role: 'user', parts: [{ text: prompt }] }],
          config: {
            temperature: 0.6,
            maxOutputTokens: 8192,
            responseMimeType: 'application/json',
            responseSchema: {
              type: Type.OBJECT,
              properties: {
                // CORE FINANCIAL METRICS
                ask_amount: { type: Type.NUMBER, description: "Amount asked from sharks in rupees (convert: 1 lakh = 100000, 1 crore = 10000000). NEVER use 0 unless explicitly zero" },
                ask_equity: { type: Type.NUMBER, description: "Equity percentage offered. NEVER use 0 unless explicitly zero" },
                ask_valuation: { type: Type.NUMBER, description: "Implied valuation in rupees (ask_amount / ask_equity * 100)" },
                current_revenue: { type: Type.NUMBER, description: "Current revenue in lakhs (convert crores to lakhs)" },
                previous_year_revenue: { type: Type.NUMBER, description: "Previous year revenue" },
                revenue_growth_rate: { type: Type.NUMBER, description: "Revenue growth percentage YoY" },
                
                // PROFITABILITY METRICS
                gross_margin: { type: Type.NUMBER, description: "Gross margin percentage" },
                ebitda_margin: { type: Type.NUMBER, description: "EBITDA margin percentage" },
                net_margin: { type: Type.NUMBER, description: "Net profit margin percentage" },
                
                // UNIT ECONOMICS
                cac: { type: Type.NUMBER, description: "Customer acquisition cost" },
                ltv: { type: Type.NUMBER, description: "Customer lifetime value" },
                aov: { type: Type.NUMBER, description: "Average order value" },
                
                // OPERATIONAL METRICS
                burn_rate: { type: Type.NUMBER, description: "Monthly burn rate" },
                cash_runway_months: { type: Type.NUMBER, description: "Cash runway in months" },
                team_size: { type: Type.NUMBER, description: "Team size/employee count" },
                revenue_per_employee: { type: Type.NUMBER, description: "Revenue per employee" },
                inventory_days: { type: Type.NUMBER, description: "Inventory holding days" },
                working_capital_cycle: { type: Type.NUMBER, description: "Working capital cycle days" },
                
                // FUNDING & CAPITAL
                previous_funding_raised: { type: Type.NUMBER, description: "Previous funding amount" },
                debt_on_books: { type: Type.NUMBER, description: "Debt amount on books" },
                manufacturing_cost: { type: Type.NUMBER, description: "Manufacturing cost per unit" },
                marketing_spend_percentage: { type: Type.NUMBER, description: "Marketing spend as % of revenue" },
                repeat_customer_percentage: { type: Type.NUMBER, description: "Repeat customer rate %" },
                
                // FINANCIAL INTELLIGENCE for benchmarking
                financial_intelligence: {
                  type: Type.OBJECT,
                  properties: {
                    revenue_model_type: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: "Revenue models: 'subscription', 'hardware sales', 'commission', 'freemium', 'marketplace fees'"
                    },
                    unit_economics: {
                      type: Type.OBJECT,
                      properties: {
                        cac: { type: Type.NUMBER, description: "Customer acquisition cost" },
                        ltv: { type: Type.NUMBER, description: "Lifetime value" },
                        payback_period: { type: Type.NUMBER, description: "Months to payback CAC" },
                        gross_margin_category: { type: Type.STRING, description: "'high' (>70%), 'medium' (30-70%), 'low' (<30%)" }
                      }
                    },
                    growth_metrics: {
                      type: Type.OBJECT,
                      properties: {
                        revenue_growth_rate: { type: Type.NUMBER, description: "YoY growth percentage" },
                        monthly_growth_rate: { type: Type.NUMBER, description: "MoM growth" },
                        growth_stage: { type: Type.STRING, description: "'pre-revenue', 'early-growth', 'rapid-scale', 'mature'" }
                      }
                    },
                    funding_context: {
                      type: Type.OBJECT,
                      properties: {
                        previous_funding: { type: Type.NUMBER, description: "Total raised before" },
                        runway_months: { type: Type.NUMBER, description: "Cash runway" },
                        burn_multiple: { type: Type.NUMBER, description: "Burn rate / net new revenue" }
                      }
                    }
                  }
                },
                
                // BENCHMARKING CONTEXT vs similar companies
                benchmarking_context: {
                  type: Type.OBJECT,
                  properties: {
                    valuation_vs_sector: { type: Type.STRING, description: "'above average', 'market rate', 'below average', 'premium'" },
                    revenue_multiple: { type: Type.NUMBER, description: "Valuation / ARR" },
                    growth_percentile: { type: Type.STRING, description: "'top 10%', 'above average', 'below average'" },
                    financial_maturity: { type: Type.STRING, description: "'early', 'developing', 'mature'" }
                  }
                },
                
                // ENHANCED NARRATIVE FIELDS
                financial_narrative: {
                  type: Type.STRING,
                  description: "Rich narrative describing their complete financial journey - from early days to current performance. Include the story behind the numbers, key financial milestones, challenges overcome, and the path to profitability."
                },
                growth_story: {
                  type: Type.STRING,
                  description: "Compelling narrative about their growth trajectory - what drove revenue growth, how they scaled, key inflection points, and future growth plans. Include seasonal patterns and growth catalysts."
                },
                unit_economics_story: {
                  type: Type.STRING,
                  description: "Detailed explanation of how their business model makes money per customer, the economics that drive profitability, and how unit economics improve with scale."
                },
                funding_journey: {
                  type: Type.STRING,
                  description: "Complete funding story - previous investments, how they used the money, what this new funding will achieve, and their capital efficiency story."
                },
                operational_efficiency_narrative: {
                  type: Type.STRING,
                  description: "Story about their operational approach - how lean or capital-intensive they are, operational challenges, efficiency improvements, and path to operational excellence."
                },
                financial_projections_context: {
                  type: Type.STRING,
                  description: "Context around any financial projections or targets mentioned - the assumptions behind them, timeline for achieving them, and confidence level."
                },
                investor_validation: {
                  type: Type.STRING,
                  description: "Any previous investor feedback, validation from financial performance, or market traction that supports their financial story."
                },
                
                confidence: { type: Type.NUMBER, description: "Extraction confidence (0-100)" }
              },
              required: ["ask_amount", "ask_equity", "current_revenue", "financial_intelligence", "benchmarking_context", "financial_narrative", "growth_story", "confidence"]
            }
          }
        });
      });

      const parsedResult = JSON.parse(result.text || '{}');
      return this.normalizeResultConfidence(parsedResult);
    } catch (error) {
      console.error('  ❌ Phase 2 extraction failed:', error);
      return { confidence: 0 };
    }
  }

  /**
   * Phase 3: Business model analysis with accumulated context
   */
  async extractPhase3(transcript: string, phase1Data: any, phase2Data: any): Promise<any> {
    console.log('  🏢 Phase 3: Business Model...');
    
    const prompt = `Extract COMPREHENSIVE business model and strategic intelligence for ${phase1Data.company_name} while capturing both the strategic data AND the compelling business narrative.

COMPANY CONTEXT:
${phase1Data.company_name} - ${phase1Data.business_description} (${phase1Data.sector})
Ask: ₹${phase2Data.ask_amount?.toLocaleString() || 'Unknown'} for ${phase2Data.ask_equity || 'Unknown'}%

BUSINESS MODEL INTELLIGENCE MISSION (enriched by 160+ data field framework):

🎯 STRATEGIC NARRATIVE & POSITIONING:
Extract not just what their business model is, but the strategic thinking behind it. How do they think about competition? What's their vision for market dominance? What strategic insights drive their approach?

🏢 BUSINESS MODEL ARCHITECTURE:
Document the fundamental model structure - B2B/B2C/D2C dynamics, revenue stream architecture, pricing philosophy, customer segmentation strategy, and distribution channel logic.

💡 INNOVATION & DIFFERENTIATION STORY:
Capture the innovation narrative - what makes them different? How did they innovate beyond existing solutions? What's their competitive moat and how do they defend it?

🌍 MARKET OPPORTUNITY NARRATIVE:
Tell the market story - the problem they identified, market timing, customer pain points, TAM vision, and how they plan to capture market share. Include their market intelligence and competitive insights.

⚙️ OPERATIONAL EXCELLENCE STRATEGY:
Document their operational approach - supply chain thinking, manufacturing strategy, technology choices, quality philosophy, and scalability vision.

🚀 GROWTH & SCALING VISION:
Capture their expansion narrative - geographic plans, product roadmap, partnership strategy, and long-term scaling vision. How do they see themselves growing from current state to market leadership?

🤝 PARTNERSHIP & ECOSYSTEM STRATEGY:
Extract their partnership philosophy - key relationships, strategic alliances, franchise thinking, and how partnerships accelerate their growth strategy.

EXTRACTION APPROACH:
- Extract ALL structural business model data (types, streams, channels, segments)
- Capture the strategic reasoning behind business model choices
- Preserve founder's strategic insights and competitive intelligence
- Document the innovation story and differentiation narrative
- Tell the market opportunity story with founder's vision
- Include operational philosophy and scaling plans
- Note partnership strategy and ecosystem thinking
- Balance comprehensive data extraction with strategic storytelling

CRITICAL: This should read like both a detailed business model analysis AND a compelling strategic narrative that reveals the founder's business philosophy and vision.

FULL TRANSCRIPT ANALYSIS:
${transcript}`;

    try {
      const result = await this.extractWithRetry(async () => {
        return await this.genai.models.generateContent({
          model: 'gemini-2.0-flash-001',
          contents: [{ role: 'user', parts: [{ text: prompt }] }],
          config: {
            temperature: 0.6,
            maxOutputTokens: 8192,
            responseMimeType: 'application/json',
            responseSchema: {
              type: Type.OBJECT,
              properties: {
                // STRUCTURED BUSINESS MODEL DATA
                model_type: { type: Type.STRING, description: "B2B, B2C, D2C, B2B2C, Marketplace, Franchise, Subscription" },
                revenue_streams: { type: Type.ARRAY, items: { type: Type.STRING } },
                pricing_strategy: { type: Type.STRING, description: "Pricing model and strategy" },
                target_market: { type: Type.STRING, description: "Target customer description" },
                customer_segments: { type: Type.ARRAY, items: { type: Type.STRING } },
                distribution_channels: { type: Type.ARRAY, items: { type: Type.STRING } },
                competitive_advantage: { type: Type.ARRAY, items: { type: Type.STRING } },
                market_size_tam: { type: Type.STRING, description: "Total addressable market" },
                competitive_landscape: { type: Type.STRING, description: "Competitive analysis" },
                first_mover_advantage: { type: Type.BOOLEAN, description: "Claims first mover advantage" },
                moat_type: { type: Type.STRING, description: "Type of competitive moat" },
                supply_chain_strategy: { type: Type.STRING, description: "Supply chain approach" },
                manufacturing_approach: { type: Type.STRING, description: "Manufacturing strategy" },
                technology_stack: { type: Type.STRING, description: "Technology infrastructure" },
                customer_acquisition_channels: { type: Type.ARRAY, items: { type: Type.STRING } },
                marketing_strategy: { type: Type.STRING, description: "Marketing and brand strategy" },
                retention_mechanisms: { type: Type.ARRAY, items: { type: Type.STRING } },
                key_partnerships: { type: Type.ARRAY, items: { type: Type.STRING } },
                franchisable_model: { type: Type.BOOLEAN, description: "Business is franchisable" },
                export_potential: { type: Type.STRING, description: "International expansion plans" },
                scalability_factors: { type: Type.ARRAY, items: { type: Type.STRING } },
                
                // STRATEGIC INTELLIGENCE for positioning
                strategic_intelligence: {
                  type: Type.OBJECT,
                  properties: {
                    market_positioning: {
                      type: Type.OBJECT,
                      properties: {
                        market_size_category: { type: Type.STRING, description: "'niche', 'large addressable', 'massive TAM'" },
                        competitive_landscape: { type: Type.STRING, description: "'blue ocean', 'crowded', 'emerging', 'mature'" },
                        differentiation_strength: { type: Type.STRING, description: "'strong moat', 'moderate differentiation', 'me-too'" },
                        timing_advantage: { type: Type.STRING, description: "'first-mover', 'fast-follower', 'market-ready'" }
                      }
                    },
                    business_model_complexity: {
                      type: Type.OBJECT,
                      properties: {
                        operational_complexity: { type: Type.STRING, description: "'simple', 'moderate', 'complex'" },
                        technology_dependency: { type: Type.STRING, description: "'low-tech', 'tech-enabled', 'deep-tech'" },
                        regulatory_requirements: { type: Type.STRING, description: "'minimal', 'moderate', 'heavy'" },
                        capital_intensity: { type: Type.STRING, description: "'asset-light', 'moderate capex', 'capital-intensive'" }
                      }
                    },
                    go_to_market: {
                      type: Type.OBJECT,
                      properties: {
                        customer_acquisition_strategy: { type: Type.ARRAY, items: { type: Type.STRING }, description: "'direct sales', 'digital marketing', 'partnerships'" },
                        distribution_channels: { type: Type.ARRAY, items: { type: Type.STRING }, description: "'online', 'retail', 'B2B sales', 'marketplace'" },
                        market_entry_approach: { type: Type.STRING, description: "'bottom-up', 'top-down', 'horizontal', 'vertical'" }
                      }
                    }
                  }
                },
                
                // SCALABILITY ANALYSIS
                scalability_analysis: {
                  type: Type.OBJECT,
                  properties: {
                    network_effects: { type: Type.STRING, description: "'strong', 'moderate', 'minimal', 'none'" },
                    economies_of_scale: { type: Type.STRING, description: "'significant', 'moderate', 'limited'" },
                    international_potential: { type: Type.STRING, description: "'high', 'moderate', 'limited', 'local-only'" },
                    platform_potential: { type: Type.STRING, description: "'ecosystem play', 'single product', 'suite approach'" }
                  }
                },
                
                // ENHANCED NARRATIVE FIELDS
                strategic_narrative: {
                  type: Type.STRING,
                  description: "Rich narrative describing their complete business strategy and approach - how they think about competition, market positioning, customer acquisition, and building their competitive moat. Include strategic insights and business philosophy."
                },
                market_opportunity_story: {
                  type: Type.STRING,
                  description: "Compelling story about the market opportunity they're addressing - the problem they identified, market timing, customer pain points, and the vision for how they'll capture and expand their market share."
                },
                innovation_details: {
                  type: Type.STRING,
                  description: "Detailed narrative about what makes their approach innovative - technological innovations, business model innovations, process innovations, and how they differentiate from existing solutions."
                },
                business_model_context: {
                  type: Type.STRING,
                  description: "Story behind their business model choices - why they chose this model, how it evolved, what alternatives they considered, and how the model supports their growth strategy."
                },
                competitive_positioning_story: {
                  type: Type.STRING,
                  description: "Narrative about how they position against competitors - what they see as their key advantages, how they plan to defend their position, and their competitive intelligence."
                },
                scalability_vision: {
                  type: Type.STRING,
                  description: "Vision and story for how they plan to scale - geographic expansion, product line extensions, operational scaling, team building, and long-term growth trajectory."
                },
                partnership_strategy_narrative: {
                  type: Type.STRING,
                  description: "Story about their partnership approach - key relationships they've built, how partnerships accelerate growth, and strategic alliance plans."
                },
                
                confidence: { type: Type.NUMBER, description: "Extraction confidence (0-100)" }
              },
              required: ["model_type", "revenue_streams", "target_market", "strategic_intelligence", "scalability_analysis", "strategic_narrative", "market_opportunity_story", "confidence"]
            }
          }
        });
      });

      const parsedResult = JSON.parse(result.text || '{}');
      return this.normalizeResultConfidence(parsedResult);
    } catch (error) {
      console.error('  ❌ Phase 3 extraction failed:', error);
      return { confidence: 0 };
    }
  }

  /**
   * Phase 4: Deal dynamics and shark responses
   */
  private async extractPhase4(transcript: string, contextData: any): Promise<any> {
    console.log('  🦈 Phase 4: Deal Dynamics...');
    
    const prompt = `Extract COMPREHENSIVE deal dynamics and negotiation intelligence for ${contextData.phase1.company_name} while capturing both the transactional data AND the compelling human drama of the negotiation.

COMPANY CONTEXT:
${contextData.phase1.company_name} (${contextData.phase1.sector})
Ask: ₹${contextData.phase2.ask_amount?.toLocaleString() || 'Unknown'} for ${contextData.phase2.ask_equity || 'Unknown'}%

CRITICAL SHARK IDENTIFICATION RULES:
1. VALID SHARK NAMES (use ONLY these exact names):
   - Namita Thapar (Emcure Pharmaceuticals)
   - Aman Gupta (boAt)
   - Anupam Mittal (Shaadi.com)
   - Vineeta Singh (SUGAR Cosmetics)
   - Peyush Bansal (Lenskart)
   - Ashneer Grover (Former BharatPe) - Season 1
   - Ghazal Alagh (Mamaearth) - Season 2+
   - Amit Jain (CarDekho) - Season 3+
   - Azhar Iqubal (Inshorts) - Season 4+
   - Radhika Gupta (Edelweiss) - Season 4+
   - Varun Dua (ACKO) - Guest Shark
   - Ritesh Agarwal (OYO) - Guest Shark
   - Deepinder Goyal (Zomato) - Guest Shark
2. NEVER use "Unknown", "Multiple Sharks", or generic terms
3. If multiple sharks collaborate, list each by name

NEGOTIATION INTELLIGENCE MISSION (enriched by 160+ data field framework):

🦈 SHARK PSYCHOLOGY & DYNAMICS:
Extract the specific sharks who were interested (use exact names from list above), but also the psychology behind each shark's reactions. What drove their interest or rejection? How did shark dynamics play out? What competitive tensions emerged?

💰 NEGOTIATION FLOW & TURNING POINTS:
Document the complete deal evolution - from initial reactions to final offers. Capture the key moments that changed everything, the turning points, and the emotional rhythm of the negotiation.

🎭 HUMAN DRAMA & EMOTIONAL MOMENTS:
Tell the story of the human interactions - dramatic exchanges, emotional appeals, breakthrough moments, tensions, and the psychology of high-stakes decision-making.

🔥 COMPETITIVE DYNAMICS & POWER PLAYS:
Capture how sharks competed or collaborated, how founders navigated multiple offers, and the strategic positioning that shaped the final outcome.

⚡ DECISION TIMELINE & CRITICAL MOMENTS:
Document the sequence of events, key decision points, moments when the deal could have gone either way, and the factors that ultimately determined success or failure.

🎯 FOUNDER PERFORMANCE UNDER PRESSURE:
Analyze how founders handled the pressure, their negotiation strategy, adaptability, and the personal story of navigating this high-stakes moment.

💡 DEAL STRUCTURE & STRATEGIC THINKING:
Extract the deal terms while understanding the strategic thinking behind them - why these terms, what they reveal about risk assessment, and how the deal structure serves both parties.

EXTRACTION APPROACH:
- Extract ALL deal data with precision (offers, terms, equity percentages, valuations)
- Capture the complete negotiation narrative and emotional journey
- Document turning points and key moments that changed the outcome
- Preserve the human drama and psychological dynamics
- Tell the story of founder performance under pressure
- Include the strategic thinking behind deal terms and structures
- Note the competitive dynamics between sharks
- Balance comprehensive data extraction with compelling storytelling

CRITICAL: This should read like both a detailed deal analysis AND a gripping negotiation story that captures the human drama of high-stakes decision-making.

FULL TRANSCRIPT ANALYSIS:
${transcript}`;

    try {
      const result = await this.extractWithRetry(async () => {
        return await this.genai.models.generateContent({
          model: 'gemini-2.0-flash-001',
          contents: [{ role: 'user', parts: [{ text: prompt }] }],
          config: {
            temperature: 0.6,
            maxOutputTokens: 8192,
            responseMimeType: 'application/json',
            responseSchema: {
              type: Type.OBJECT,
              properties: {
                // STRUCTURED DEAL DATA
                sharks_interested: { type: Type.ARRAY, items: { type: Type.STRING } },
                total_sharks_interested: { type: Type.NUMBER, description: "Total number of sharks interested" },
                sharks_opted_out: { type: Type.ARRAY, items: { type: Type.STRING } },
                opt_out_reasons: { type: Type.ARRAY, items: { type: Type.STRING } },
                individual_offers: {
                  type: Type.ARRAY,
                  items: {
                    type: Type.OBJECT,
                    properties: {
                      shark: { type: Type.STRING },
                      amount: { type: Type.NUMBER },
                      equity: { type: Type.NUMBER },
                      terms: { type: Type.STRING }
                    }
                  }
                },
                counter_offers_made: { type: Type.NUMBER, description: "Number of counter offers" },
                negotiation_rounds: { type: Type.NUMBER, description: "Total negotiation rounds" },
                deal_closed: { type: Type.BOOLEAN },
                final_deal: {
                  type: Type.OBJECT,
                  properties: {
                    amount: { type: Type.NUMBER },
                    equity: { type: Type.NUMBER },
                    sharks: { type: Type.ARRAY, items: { type: Type.STRING } },
                    deal_type: { type: Type.STRING },
                    royalty_terms: { type: Type.STRING },
                    debt_component: { type: Type.NUMBER }
                  },
                  nullable: true
                },
                valuation_discount: { type: Type.NUMBER, description: "Percentage discount from initial valuation" },
                lead_investor: { type: Type.STRING, description: "Lead shark investor" },
                participating_sharks: { type: Type.ARRAY, items: { type: Type.STRING } },
                use_of_funds_plan: { type: Type.STRING, description: "How investment will be used" },
                post_deal_mentorship_focus: { type: Type.ARRAY, items: { type: Type.STRING } },
                negotiation_highlights: { type: Type.ARRAY, items: { type: Type.STRING } },
                
                // NEGOTIATION INTELLIGENCE
                negotiation_intelligence: {
                  type: Type.OBJECT,
                  properties: {
                    deal_structure_analysis: {
                      type: Type.OBJECT,
                      properties: {
                        equity_vs_debt_mix: { type: Type.STRING, description: "'equity-only', 'hybrid', 'debt-heavy'" },
                        valuation_negotiation: {
                          type: Type.OBJECT,
                          properties: {
                            initial_valuation: { type: Type.NUMBER },
                            final_valuation: { type: Type.NUMBER },
                            discount_percentage: { type: Type.NUMBER, description: "How much valuation was reduced" }
                          }
                        },
                        deal_complexity: { type: Type.STRING, description: "'standard', 'complex', 'innovative'" }
                      }
                    },
                    shark_interaction_patterns: {
                      type: Type.OBJECT,
                      properties: {
                        primary_negotiator: { type: Type.STRING, description: "Which shark led negotiations" },
                        collaborative_sharks: { type: Type.ARRAY, items: { type: Type.STRING }, description: "Sharks that worked together" },
                        competitive_dynamics: { type: Type.STRING, description: "'competitive bidding', 'collaborative', 'single interest'" },
                        decision_timeline: { type: Type.STRING, description: "'immediate', 'delayed', 'multiple rounds'" }
                      }
                    },
                    success_factors: {
                      type: Type.OBJECT,
                      properties: {
                        key_persuasion_points: { type: Type.ARRAY, items: { type: Type.STRING }, description: "What convinced sharks" },
                        tipping_point_moments: { type: Type.ARRAY, items: { type: Type.STRING }, description: "Moments that changed dynamics" },
                        founders_negotiation_style: { type: Type.STRING, description: "'collaborative', 'firm', 'flexible'" }
                      }
                    }
                  }
                },
                
                // DEAL OUTCOME ANALYSIS
                deal_outcome_analysis: {
                  type: Type.OBJECT,
                  properties: {
                    outcome_type: { type: Type.STRING, description: "'no-deal', 'single-shark', 'multi-shark', 'bidding-war'" },
                    rejection_reasons: { type: Type.ARRAY, items: { type: Type.STRING }, description: "Why sharks opted out" },
                    success_probability_factors: { type: Type.ARRAY, items: { type: Type.STRING }, description: "What increased chances" },
                    market_timing_impact: { type: Type.STRING, description: "How timing affected deal" }
                  }
                },
                
                // ENHANCED NARRATIVE FIELDS
                negotiation_narrative: {
                  type: Type.STRING,
                  description: "Rich narrative of the complete negotiation process - the flow of offers, counter-offers, shark dynamics, competitive tensions, emotional moments, and how the deal evolved from initial pitch to final outcome."
                },
                turning_points: {
                  type: Type.STRING,
                  description: "Key turning points that changed the negotiation trajectory - moments when sharks changed their minds, when founders made crucial decisions, or when the deal dynamics shifted significantly."
                },
                key_moments: {
                  type: Type.STRING,
                  description: "Memorable moments from the negotiation - dramatic exchanges, emotional appeals, breakthrough insights, or defining interactions that shaped the outcome."
                },
                shark_dynamics_story: {
                  type: Type.STRING,
                  description: "Story of how sharks interacted with each other during the pitch - competitive dynamics, collaborative moments, influence patterns, and group decision-making psychology."
                },
                decision_timeline_narrative: {
                  type: Type.STRING,
                  description: "Timeline narrative of how decisions evolved - from initial reactions to final positions, including the sequence of events that led to the outcome."
                },
                emotional_moments: {
                  type: Type.STRING,
                  description: "Emotional high points and low points during the negotiation - moments of excitement, disappointment, tension, breakthrough, or connection between founders and sharks."
                },
                founder_negotiation_story: {
                  type: Type.STRING,
                  description: "Story of how founders handled the negotiation - their strategy, adaptability, emotional management, and decision-making under pressure."
                },
                
                confidence: { type: Type.NUMBER, description: "Extraction confidence (0-100)" }
              },
              required: ["sharks_interested", "deal_closed", "negotiation_intelligence", "deal_outcome_analysis", "negotiation_narrative", "turning_points", "confidence"]
            }
          }
        });
      });

      const parsedResult = JSON.parse(result.text || '{}');
      return this.normalizeResultConfidence(parsedResult);
    } catch (error) {
      console.error('  ❌ Phase 4 extraction failed:', error);
      return { confidence: 0 };
    }
  }

  /**
   * Phase 5: Individual shark analysis
   */
  private async extractPhase5(transcript: string, contextData: any): Promise<any> {
    console.log('  🎯 Phase 5: Shark Analysis...');
    
    const prompt = `Extract COMPREHENSIVE shark psychology and panel dynamics analysis for ${contextData.phase1.company_name} while capturing both the behavioral data AND the fascinating human psychology of high-stakes decision-making.

COMPANY CONTEXT:
${contextData.phase1.company_name} - ${contextData.phase1.business_description}
Deal Status: ${contextData.phase4.deal_closed ? 'Deal made' : 'No deal'}

SHARK PSYCHOLOGY INTELLIGENCE MISSION (enriched by 160+ data field framework):

🧠 INDIVIDUAL SHARK PSYCHOLOGY:
Analyze each shark's unique personality, decision-making style, communication patterns, and psychological profile during this pitch. What drives their thinking? How do they process information and make investment decisions?

🦈 SHARK BEHAVIOR PATTERNS & EXPERTISE:
Document each shark's behavior patterns - their investment thesis, sector preferences, risk tolerance, negotiation style, and how their domain expertise influenced their assessment of this business.

🤝 GROUP DYNAMICS & PANEL CHEMISTRY:
Capture the fascinating group psychology - how sharks influence each other, power dynamics, collaborative vs competitive behavior, alliances formed, and the social psychology of the panel.

💭 DECISION PSYCHOLOGY & REASONING:
Analyze the psychological journey of each shark from initial reaction to final decision - their thought process evolution, emotional vs logical reasoning, and the factors that ultimately drove their choices.

⚡ COMPETITIVE DYNAMICS & INFLUENCE PATTERNS:
Tell the story of how sharks interacted - who influenced whom, competitive tensions, collaborative moments, and how group dynamics shaped individual decisions.

🎯 EXPERTISE ALIGNMENT & DOMAIN MATCH:
Document how each shark's background and expertise aligned with this business opportunity, and how their domain knowledge influenced their interest and assessment.

🔥 PANEL MOOD & EMOTIONAL JOURNEY:
Capture the emotional arc of the panel - excitement, skepticism, breakthrough moments, and how the collective mood evolved throughout the pitch.

EXTRACTION APPROACH:
- Extract ALL shark identification and reaction data
- Capture individual shark personality profiles and psychology
- Document group dynamics and influence patterns 
- Analyze decision-making psychology and reasoning processes
- Tell the story of panel chemistry and interpersonal dynamics
- Include expertise matching and domain alignment insights
- Note competitive vs collaborative behavior patterns
- Balance comprehensive data extraction with psychological storytelling

CRITICAL: This should read like both a detailed behavioral analysis AND a fascinating psychological study of how successful investors think and interact under pressure.

FULL TRANSCRIPT ANALYSIS:
${transcript}`;

    try {
      const result = await this.extractWithRetry(async () => {
        return await this.genai.models.generateContent({
          model: 'gemini-2.0-flash-001',
          contents: [{ role: 'user', parts: [{ text: prompt }] }],
          config: {
            temperature: 0.6,
            maxOutputTokens: 8192,
            responseMimeType: 'application/json',
            responseSchema: {
              type: Type.OBJECT,
              properties: {
                // SIMPLIFIED STRUCTURED DATA (fixing JSON parsing issues)
                sharks_present: { type: Type.ARRAY, items: { type: Type.STRING }, description: "List of sharks present in this episode" },
                individual_shark_reactions: { type: Type.ARRAY, items: { type: Type.STRING }, description: "Individual reactions from each shark" },
                panel_dynamics: { type: Type.STRING, description: "Group dynamics and interactions" },
                competition_level: { type: Type.STRING, description: "high, medium, low, or none" },
                shark_behavior_patterns: { type: Type.ARRAY, items: { type: Type.STRING } },
                sector_alignment: { type: Type.STRING, description: "How well sharks matched business domain" },
                dominant_shark: { type: Type.STRING, description: "Most influential shark in this pitch" },
                collaborative_dynamics: { type: Type.BOOLEAN, description: "Whether sharks collaborated or competed" },
                
                // SHARK PSYCHOLOGY INTELLIGENCE
                shark_psychology_intelligence: {
                  type: Type.OBJECT,
                  properties: {
                    individual_shark_profiles: {
                      type: Type.OBJECT,
                      properties: {
                        anupam_mittal: {
                          type: Type.OBJECT,
                          properties: {
                            investment_interest_level: { type: Type.NUMBER, description: "1-10 scale" },
                            sector_alignment: { type: Type.STRING, description: "'perfect fit', 'adjacent', 'stretch'" },
                            key_concerns_raised: { type: Type.ARRAY, items: { type: Type.STRING } },
                            positive_signals: { type: Type.ARRAY, items: { type: Type.STRING } },
                            question_themes: { type: Type.ARRAY, items: { type: Type.STRING } },
                            decision_factors: { type: Type.ARRAY, items: { type: Type.STRING } },
                            negotiation_style: { type: Type.STRING, description: "'aggressive', 'collaborative', 'analytical'" }
                          }
                        },
                        aman_gupta: {
                          type: Type.OBJECT,
                          properties: {
                            investment_interest_level: { type: Type.NUMBER, description: "1-10 scale" },
                            sector_alignment: { type: Type.STRING, description: "'perfect fit', 'adjacent', 'stretch'" },
                            key_concerns_raised: { type: Type.ARRAY, items: { type: Type.STRING } },
                            positive_signals: { type: Type.ARRAY, items: { type: Type.STRING } },
                            question_themes: { type: Type.ARRAY, items: { type: Type.STRING } },
                            decision_factors: { type: Type.ARRAY, items: { type: Type.STRING } },
                            negotiation_style: { type: Type.STRING, description: "'aggressive', 'collaborative', 'analytical'" }
                          }
                        },
                        vineeta_singh: {
                          type: Type.OBJECT,
                          properties: {
                            investment_interest_level: { type: Type.NUMBER, description: "1-10 scale" },
                            sector_alignment: { type: Type.STRING, description: "'perfect fit', 'adjacent', 'stretch'" },
                            key_concerns_raised: { type: Type.ARRAY, items: { type: Type.STRING } },
                            positive_signals: { type: Type.ARRAY, items: { type: Type.STRING } },
                            question_themes: { type: Type.ARRAY, items: { type: Type.STRING } },
                            decision_factors: { type: Type.ARRAY, items: { type: Type.STRING } },
                            negotiation_style: { type: Type.STRING, description: "'aggressive', 'collaborative', 'analytical'" }
                          }
                        },
                        peyush_bansal: {
                          type: Type.OBJECT,
                          properties: {
                            investment_interest_level: { type: Type.NUMBER, description: "1-10 scale" },
                            sector_alignment: { type: Type.STRING, description: "'perfect fit', 'adjacent', 'stretch'" },
                            key_concerns_raised: { type: Type.ARRAY, items: { type: Type.STRING } },
                            positive_signals: { type: Type.ARRAY, items: { type: Type.STRING } },
                            question_themes: { type: Type.ARRAY, items: { type: Type.STRING } },
                            decision_factors: { type: Type.ARRAY, items: { type: Type.STRING } },
                            negotiation_style: { type: Type.STRING, description: "'aggressive', 'collaborative', 'analytical'" }
                          }
                        },
                        namita_thapar: {
                          type: Type.OBJECT,
                          properties: {
                            investment_interest_level: { type: Type.NUMBER, description: "1-10 scale" },
                            sector_alignment: { type: Type.STRING, description: "'perfect fit', 'adjacent', 'stretch'" },
                            key_concerns_raised: { type: Type.ARRAY, items: { type: Type.STRING } },
                            positive_signals: { type: Type.ARRAY, items: { type: Type.STRING } },
                            question_themes: { type: Type.ARRAY, items: { type: Type.STRING } },
                            decision_factors: { type: Type.ARRAY, items: { type: Type.STRING } },
                            negotiation_style: { type: Type.STRING, description: "'aggressive', 'collaborative', 'analytical'" }
                          }
                        }
                      }
                    },
                    behavioral_patterns: {
                      type: Type.OBJECT,
                      properties: {
                        dominance_hierarchy: { type: Type.ARRAY, items: { type: Type.STRING }, description: "Order of shark influence" },
                        collaboration_instances: { type: Type.ARRAY, items: { type: Type.STRING } },
                        competitive_moments: { type: Type.ARRAY, items: { type: Type.STRING } },
                        expertise_deference: { type: Type.ARRAY, items: { type: Type.STRING } }
                      }
                    },
                    decision_psychology: {
                      type: Type.OBJECT,
                      properties: {
                        herd_mentality_instances: { type: Type.ARRAY, items: { type: Type.STRING } },
                        contrarian_positions: { type: Type.ARRAY, items: { type: Type.STRING } },
                        risk_tolerance_displayed: { type: Type.STRING, description: "'high', 'moderate', 'conservative'" },
                        time_pressure_impact: { type: Type.STRING, description: "How urgency affected decisions" }
                      }
                    }
                  }
                },
                
                // SECTOR-SPECIFIC SHARK PREFERENCES
                sector_preferences: {
                  type: Type.OBJECT,
                  properties: {
                    sector_specialists: { type: Type.ARRAY, items: { type: Type.STRING }, description: "Sharks with domain expertise" },
                    sector_newcomers: { type: Type.ARRAY, items: { type: Type.STRING }, description: "Sharks outside comfort zone" },
                    investment_pattern_match: { type: Type.STRING, description: "How this pitch fits their portfolio" }
                  }
                },
                
                // ENHANCED NARRATIVE FIELDS
                shark_personality_analysis: {
                  type: Type.STRING,
                  description: "Deep analysis of individual shark personalities during this pitch - their decision-making styles, communication patterns, emotional responses, and unique characteristics displayed."
                },
                group_dynamics_narrative: {
                  type: Type.STRING,
                  description: "Rich narrative of how the shark panel functioned as a group - power dynamics, influence patterns, collaborative vs competitive behavior, and group psychology."
                },
                individual_shark_stories: {
                  type: Type.STRING,
                  description: "Detailed stories about each shark's individual journey through this pitch - their thought process, decision evolution, and unique perspective on the business."
                },
                panel_chemistry: {
                  type: Type.STRING,
                  description: "Analysis of the chemistry between sharks - alliances formed, tensions created, influence patterns, and how their interactions shaped the outcome."
                },
                decision_psychology: {
                  type: Type.STRING,
                  description: "Psychological analysis of how sharks made their decisions - risk assessment patterns, emotional vs logical reasoning, and influence factors."
                },
                shark_expertise_match: {
                  type: Type.STRING,
                  description: "Story of how each shark's expertise and background aligned with this business opportunity, and how their domain knowledge influenced their interest level."
                },
                
                confidence: { type: Type.NUMBER, description: "Extraction confidence (0-100)" }
              },
              required: ["panel_dynamics", "competition_level", "shark_psychology_intelligence", "sector_preferences", "shark_personality_analysis", "group_dynamics_narrative", "confidence"]
            }
          }
        });
      });

      const parsedResult = JSON.parse(result.text || '{}');
      return this.normalizeResultConfidence(parsedResult);
    } catch (error) {
      console.error('  ❌ Phase 5 extraction failed:', error);
      return { confidence: 0 };
    }
  }

  /**
   * Phase 6: Strategic insights and lessons
   */
  private async extractPhase6(transcript: string, contextData: any): Promise<any> {
    console.log('  💡 Phase 6: Strategic Insights...');
    
    const prompt = `Extract COMPREHENSIVE strategic intelligence and wisdom from ${contextData.phase1.company_name}'s complete Shark Tank journey while synthesizing all phases into actionable insights AND compelling strategic narratives.

COMPLETE COMPANY CONTEXT:
${contextData.phase1.company_name} (${contextData.phase1.sector})
Outcome: ${contextData.phase4.deal_closed ? 'SUCCESS - Deal made' : 'NO DEAL'}

STRATEGIC SYNTHESIS MISSION (enriched by 160+ data field framework):

🧠 COMPREHENSIVE STRATEGIC ANALYSIS:
Synthesize all phases of this company's journey into a complete strategic case study. What does this entire experience reveal about entrepreneurship, investment dynamics, market forces, and business strategy? Tell the complete strategic story.

📈 SECTOR TRENDS & MARKET INTELLIGENCE:
Extract deep sector insights and market trends revealed by this pitch. What does this case tell us about the industry, competitive dynamics, market timing, customer behavior, and future opportunities in this space?

🎯 ENTREPRENEURIAL WISDOM & LESSONS:
Capture the entrepreneurial lessons and wisdom that other founders can learn from this journey. What does this teach about building businesses, pitching investors, handling pressure, and navigating growth challenges?

💡 STRATEGIC INSIGHTS NARRATIVE:
Tell the strategic insights as a compelling story - the deeper business principles, strategic thinking patterns, and decision-making wisdom revealed through this case study.

🦈 INVESTOR PSYCHOLOGY & PATTERNS:
Analyze what this case reveals about investor psychology, decision-making patterns, and evaluation criteria. How do successful investors think and what can entrepreneurs learn from shark behavior?

⏰ MARKET TIMING & POSITIONING ANALYSIS:
Examine the market timing and positioning factors that influenced this outcome. Why did this business succeed or fail at this moment, and what does it reveal about market dynamics?

🏗️ BUSINESS MODEL WISDOM:
Extract business model insights and architectural wisdom from this case. What does it teach about building sustainable, scalable businesses and avoiding common structural pitfalls?

🔮 FUTURE PREDICTIONS & OPPORTUNITIES:
Provide forward-looking analysis based on this case - how this company might evolve, what challenges they'll face, what opportunities exist, and what this predicts about industry trends.

EXTRACTION APPROACH:
- Extract ALL specific strategic insights and tactical lessons
- Synthesize learnings from all phases into comprehensive wisdom
- Capture sector trends and market intelligence revealed
- Tell the entrepreneurial lessons as compelling narratives
- Include investor psychology and decision-making patterns
- Analyze market timing and competitive positioning factors
- Document business model principles and architectural insights
- Provide forward-looking predictions and opportunity analysis
- Balance actionable insights with strategic storytelling

CRITICAL: This should read like both a comprehensive strategic case study AND a compelling business wisdom narrative that transforms this single company's experience into universally applicable entrepreneurial intelligence.

COMPLETE CONTEXT FROM ALL PHASES:
Basic Info: ${JSON.stringify(contextData.phase1, null, 2)}
Financials: ${JSON.stringify(contextData.phase2, null, 2)}
Business Model: ${JSON.stringify(contextData.phase3, null, 2)}
Deal Dynamics: ${JSON.stringify(contextData.phase4, null, 2)}
Shark Analysis: ${JSON.stringify(contextData.phase5, null, 2)}

FULL TRANSCRIPT ANALYSIS:
${transcript}`;

    try {
      const result = await this.extractWithRetry(async () => {
        return await this.genai.models.generateContent({
          model: 'gemini-2.0-flash-001',
          contents: [{ role: 'user', parts: [{ text: prompt }] }],
          config: {
            temperature: 0.6,
            maxOutputTokens: 8192,
            responseMimeType: 'application/json',
            responseSchema: {
              type: Type.OBJECT,
              properties: {
                // STRUCTURED STRATEGIC INSIGHTS
                success_factors: { type: Type.ARRAY, items: { type: Type.STRING } },
                critical_mistakes: { type: Type.ARRAY, items: { type: Type.STRING } },
                lessons_learned: { type: Type.ARRAY, items: { type: Type.STRING } },
                sector_specific_advice: { type: Type.ARRAY, items: { type: Type.STRING } },
                strategic_recommendations: { type: Type.ARRAY, items: { type: Type.STRING } },
                industry_patterns: { type: Type.ARRAY, items: { type: Type.STRING } },
                investor_preferences_revealed: { type: Type.ARRAY, items: { type: Type.STRING } },
                
                // ACTIONABLE BUSINESS INTELLIGENCE
                actionable_intelligence: {
                  type: Type.OBJECT,
                  properties: {
                    preparation_recommendations: {
                      type: Type.OBJECT,
                      properties: {
                        pitch_optimization_areas: { type: Type.ARRAY, items: { type: Type.STRING }, description: "'Emphasize unit economics', 'Show market traction'" },
                        expected_shark_questions: { type: Type.ARRAY, items: { type: Type.STRING }, description: "Based on similar companies" },
                        risk_mitigation_strategies: { type: Type.ARRAY, items: { type: Type.STRING }, description: "'Address competition concerns upfront'" },
                        strength_amplification: { type: Type.ARRAY, items: { type: Type.STRING }, description: "'Leverage first-mover advantage'" }
                      }
                    },
                    sector_insights: {
                      type: Type.OBJECT,
                      properties: {
                        sector_success_patterns: { type: Type.ARRAY, items: { type: Type.STRING }, description: "What works in this industry" },
                        common_failure_modes: { type: Type.ARRAY, items: { type: Type.STRING }, description: "What typically goes wrong" },
                        investor_preferences: { type: Type.ARRAY, items: { type: Type.STRING }, description: "What sharks value in this sector" },
                        market_timing_factors: { type: Type.ARRAY, items: { type: Type.STRING }, description: "Timing considerations" }
                      }
                    },
                    competitive_intelligence: {
                      type: Type.OBJECT,
                      properties: {
                        differentiation_opportunities: { type: Type.ARRAY, items: { type: Type.STRING }, description: "How to stand out" },
                        positioning_recommendations: { type: Type.ARRAY, items: { type: Type.STRING }, description: "Market positioning advice" },
                        moat_strengthening_advice: { type: Type.ARRAY, items: { type: Type.STRING }, description: "Building competitive advantages" }
                      }
                    }
                  }
                },
                
                // SUCCESS PROBABILITY ANALYSIS
                success_probability_analysis: {
                  type: Type.OBJECT,
                  properties: {
                    overall_probability_score: { type: Type.NUMBER, description: "0-100 based on historical patterns" },
                    positive_indicators: { type: Type.ARRAY, items: { type: Type.STRING }, description: "What increases success chances" },
                    risk_indicators: { type: Type.ARRAY, items: { type: Type.STRING }, description: "What decreases success chances" },
                    optimization_opportunities: { type: Type.ARRAY, items: { type: Type.STRING }, description: "How to improve odds" },
                    similar_company_benchmarks: { type: Type.STRING, description: "Performance vs similar companies" }
                  }
                },
                
                // ENHANCED NARRATIVE FIELDS
                comprehensive_analysis: {
                  type: Type.STRING,
                  description: "Comprehensive strategic analysis synthesizing all phases of this company's Shark Tank journey - what this case study reveals about entrepreneurship, investment dynamics, and business strategy."
                },
                sector_trends: {
                  type: Type.STRING,
                  description: "Deep analysis of sector trends and patterns revealed by this pitch - what it tells us about the industry, market dynamics, competitive landscape, and future opportunities."
                },
                entrepreneurial_lessons: {
                  type: Type.STRING,
                  description: "Rich narrative of entrepreneurial lessons from this founder's journey - what other entrepreneurs can learn about building businesses, pitching investors, and navigating growth challenges."
                },
                strategic_insights_story: {
                  type: Type.STRING,
                  description: "Strategic insights told as a compelling narrative - the deeper business lessons, strategic thinking principles, and decision-making wisdom revealed by this case."
                },
                investor_psychology_insights: {
                  type: Type.STRING,
                  description: "Insights into investor psychology and decision-making patterns based on shark reactions - what this reveals about how investors evaluate opportunities and make decisions."
                },
                market_timing_analysis: {
                  type: Type.STRING,
                  description: "Analysis of market timing and positioning factors - why this business succeeded or failed at this moment, and what it reveals about market dynamics and timing."
                },
                business_model_wisdom: {
                  type: Type.STRING,
                  description: "Business model insights and wisdom derived from this case - what it teaches about building sustainable, scalable businesses and avoiding common pitfalls."
                },
                future_predictions: {
                  type: Type.STRING,
                  description: "Forward-looking analysis and predictions based on this case - how this company might evolve, what challenges they'll face, and what opportunities lie ahead."
                },
                
                confidence: { type: Type.NUMBER, description: "Extraction confidence (0-100)" }
              },
              required: ["success_factors", "lessons_learned", "actionable_intelligence", "success_probability_analysis", "comprehensive_analysis", "sector_trends", "entrepreneurial_lessons", "confidence"]
            }
          }
        });
      });

      const parsedResult = JSON.parse(result.text || '{}');
      return this.normalizeResultConfidence(parsedResult);
    } catch (error) {
      console.error('  ❌ Phase 6 extraction failed:', error);
      return { confidence: 0 };
    }
  }

  /**
   * Phase 7: Generate rich narrative (2000+ words)
   */
  private async extractPhase7(transcript: string, contextData: any): Promise<any> {
    console.log('  📖 Phase 7: Rich Narrative...');
    
    const prompt = `Write a COMPREHENSIVE, DETAILED case study about ${contextData.phase1.company_name}'s Shark Tank India pitch.

KEY FACTS:
- Company: ${contextData.phase1.company_name} (${contextData.phase1.sector})
- Founders: ${contextData.phase1.founder_names?.join(', ') || 'Unknown'}
- Ask: ₹${contextData.phase2.ask_amount?.toLocaleString() || 'Unknown'} for ${contextData.phase2.ask_equity || 'Unknown'}%
- Outcome: ${contextData.phase4.deal_closed ? 'Deal made' : 'No deal'}

COMPREHENSIVE NARRATIVE MISSION (based on 160+ data field framework):
Write a DETAILED 3000+ word case study that captures EVERY significant aspect of the pitch:

1. FOUNDER & COMPANY BACKGROUND (500+ words):
   - Detailed founder profiles, backgrounds, and journey to the pitch
   - Company origin story and evolution
   - Key milestones and challenges overcome
   - Team composition and role dynamics
   - Personal motivations and driving factors

2. BUSINESS MODEL DEEP DIVE (600+ words):
   - Comprehensive product/service explanation
   - Technical specifications and innovation details
   - Market opportunity and competitive landscape
   - Revenue model and growth strategy
   - Operational model and scalability plans
   - Financial performance and projections

3. PITCH PRESENTATION ANALYSIS (500+ words):
   - Opening hook and presentation flow
   - Key arguments and value propositions presented
   - Visual aids, demos, and supporting materials
   - Founder communication style and confidence
   - Audience engagement and energy levels
   - Memorable moments and turning points

4. SHARK REACTIONS & DYNAMICS (600+ words):
   - Individual shark responses and engagement levels
   - Specific questions, concerns, and objections raised
   - Competitive dynamics between sharks
   - Negotiation tactics and pressure points
   - Deal structure discussions and alternatives
   - Final positions and decision rationale

5. STRATEGIC INSIGHTS & LESSONS (500+ words):
   - Key success factors and winning strategies
   - Critical mistakes and missed opportunities
   - Sector-specific lessons and patterns
   - Broader market implications
   - Actionable recommendations for similar companies
   - Long-term outlook and growth potential

6. DETAILED QUOTE INTEGRATION:
   - Preserve key Hindi phrases with English translations
   - Capture emotional moments and memorable exchanges
   - Include specific numbers, metrics, and financial details
   - Document negotiation highlights and decision points

FULL TRANSCRIPT FOR COMPREHENSIVE ANALYSIS:
${transcript}

Write this as a compelling, detailed business case study that captures the complete essence of this Shark Tank experience, making it valuable for entrepreneurs, investors, and business students.`;

    try {
      const result = await this.extractWithRetry(async () => {
        return await this.genai.models.generateContent({
          model: 'gemini-2.0-flash-001',
          contents: [{ role: 'user', parts: [{ text: prompt }] }],
          config: {
            temperature: 0.5,
            maxOutputTokens: 8192,
            topP: 0.9
          }
        });
      });

      const completeStory = result.text || '';
      
      // Generate executive summary
      const summaryResult = await this.extractWithRetry(async () => {
        return await this.genai.models.generateContent({
          model: 'gemini-2.0-flash-001',
          contents: [{
            role: 'user',
            parts: [{ text: `Summarize this business case study in exactly 200 words:\n\n${completeStory.substring(0, 3000)}` }]
          }],
          config: {
            temperature: 0.3,
            maxOutputTokens: 300
          }
        });
      });

      const executiveSummary = summaryResult.text || '';

      // Extract key quotes
      const quotesRegex = /"([^"]+)"/g;
      const keyQuotes = Array.from(transcript.matchAll(quotesRegex))
        .map(match => match[1])
        .filter(quote => quote.length > 20 && quote.length < 200)
        .slice(0, 10);

      // Create searchable text
      const searchableText = [
        contextData.phase1.company_name,
        contextData.phase1.business_description,
        contextData.phase1.sector,
        completeStory.substring(0, 1000)
      ].filter(Boolean).join(' ');

      // Create actionable content based on the case study
      const actionableContent = {
        case_study_summary: {
          what_happened: contextData.phase4.deal_closed ? 'Deal successful - secured investment' : 'No deal reached',
          why_it_worked_or_failed: contextData.phase6.success_factors?.slice(0, 2).join(', ') || contextData.phase6.critical_mistakes?.slice(0, 2).join(', ') || 'Multiple factors contributed',
          what_others_can_learn: contextData.phase6.lessons_learned?.slice(0, 3).join('; ') || 'Key lessons from this experience'
        },
        preparation_playbook: {
          key_talking_points: contextData.phase6.actionable_intelligence?.preparation_recommendations?.pitch_optimization_areas || ['Focus on core value proposition', 'Demonstrate market traction'],
          potential_objections: contextData.phase6.actionable_intelligence?.preparation_recommendations?.expected_shark_questions || ['Be prepared for valuation questions', 'Address scalability concerns'],
          preparation_checklist: ['Practice pitch timing', 'Prepare financial projections', 'Research sharks backgrounds'],
          pitch_flow_recommendations: ['Strong opening hook', 'Clear problem statement', 'Compelling solution demo', 'Financial traction evidence']
        },
        shark_specific_guidance: {
          shark_targeting_advice: contextData.phase5.sector_preferences?.sector_specialists || ['Target sharks with sector expertise'],
          sector_positioning_tips: contextData.phase6.actionable_intelligence?.sector_insights?.sector_success_patterns || ['Position based on sector trends'],
          timing_considerations: ['Address concerns early', 'Build momentum through pitch']
        },
        comparable_insights: {
          similar_company_references: ['Reference successful companies in same sector'],
          success_pattern_analysis: contextData.phase6.actionable_intelligence?.sector_insights?.sector_success_patterns?.[0] || 'Pattern analysis based on sector data',
          differentiation_opportunities: contextData.phase6.actionable_intelligence?.competitive_intelligence?.differentiation_opportunities?.[0] || 'Unique positioning opportunities'
        }
      };

      // Create structured content
      const structuredContent = {
        investor_memo_format: `${contextData.phase1.company_name} Investment Summary: ${contextData.phase1.business_description}. Financial metrics: Ask ₹${contextData.phase2.ask_amount?.toLocaleString()} for ${contextData.phase2.ask_equity}%. ${contextData.phase4.deal_closed ? 'DEAL SECURED' : 'NO DEAL'}.`,
        preparation_brief: `Key preparation points for ${contextData.phase1.sector} sector: ${contextData.phase6.lessons_learned?.slice(0, 2).join(', ') || 'Focus on fundamentals'}`,
        lessons_digest: contextData.phase6.lessons_learned?.slice(0, 3).join('. ') || 'Key strategic lessons extracted',
        comparison_framework: `Compare against: ${contextData.phase1.sector} sector benchmarks, similar business models, comparable valuations`
      };

      return {
        complete_story: completeStory,
        executive_summary: executiveSummary,
        key_quotes: keyQuotes,
        searchable_text: searchableText,
        actionable_content: actionableContent,
        structured_content: structuredContent,
        confidence: completeStory.length > 1500 ? 90 : 60
      };
    } catch (error) {
      console.error('  ❌ Phase 7 extraction failed:', error);
      return { confidence: 0, complete_story: '', executive_summary: '' };
    }
  }

  /**
   * Phase 8: Advanced Shark Intelligence - Extract shark-specific reactions and preparation intelligence
   */
  private async extractPhase8(transcript: string, contextData: any): Promise<any> {
    console.log('  🔬 Phase 8: Advanced Shark Intelligence...');
    
    const prompt = `
Phase 8: ADVANCED SHARK INTELLIGENCE - Extract shark-specific reactions and preparation intelligence

ANALYZE this Shark Tank India transcript for advanced shark intelligence and actionable preparation insights.

CONTEXT FROM PREVIOUS PHASES:
${JSON.stringify(contextData, null, 2)}

EXTRACT ADVANCED SHARK INTELLIGENCE:

🦈 SHARK-SPECIFIC REACTIONS & PATTERNS:
- How did each shark specifically react to this pitch?
- What were the emotional/behavioral cues from each shark?
- Which sharks showed early interest vs late interest?
- What triggered positive/negative reactions from specific sharks?

📊 PREPARATION INTELLIGENCE:
- What should future entrepreneurs learn from this specific pitch?
- What preparation would have improved the outcome?
- What sector-specific insights can be extracted?
- What negotiation strategies worked/failed?

🎯 ACTIONABLE RECOMMENDATIONS:
- Specific advice for similar businesses
- Shark-specific preparation strategies
- Sector-specific insights and patterns
- Deal structure recommendations

FULL TRANSCRIPT TO ANALYZE:
${transcript}`;

    try {
      const result = await this.extractWithRetry(async () => {
        return await this.genai.models.generateContent({
          model: 'gemini-2.0-flash-001',
          contents: [{ role: 'user', parts: [{ text: prompt }] }],
          config: {
            temperature: 0.5,
            maxOutputTokens: 8192,
            responseMimeType: 'application/json',
            responseSchema: {
              type: Type.OBJECT,
              properties: {
                // SHARK-SPECIFIC INTELLIGENCE
                shark_reactions: {
                  type: Type.OBJECT,
                  properties: {
                    anupam_mittal: {
                      type: Type.OBJECT,
                      properties: {
                        initial_reaction: { type: Type.STRING, description: "First impression and body language" },
                        key_questions_asked: { type: Type.ARRAY, items: { type: Type.STRING } },
                        decision_turning_point: { type: Type.STRING, description: "Moment their decision crystallized" },
                        emotional_engagement: { type: Type.STRING, description: "Level and type of emotional response" },
                        expertise_alignment: { type: Type.STRING, description: "How their background aligned with the business" }
                      }
                    },
                    aman_gupta: {
                      type: Type.OBJECT,
                      properties: {
                        initial_reaction: { type: Type.STRING },
                        key_questions_asked: { type: Type.ARRAY, items: { type: Type.STRING } },
                        decision_turning_point: { type: Type.STRING },
                        emotional_engagement: { type: Type.STRING },
                        expertise_alignment: { type: Type.STRING }
                      }
                    },
                    vineeta_singh: {
                      type: Type.OBJECT,
                      properties: {
                        initial_reaction: { type: Type.STRING },
                        key_questions_asked: { type: Type.ARRAY, items: { type: Type.STRING } },
                        decision_turning_point: { type: Type.STRING },
                        emotional_engagement: { type: Type.STRING },
                        expertise_alignment: { type: Type.STRING }
                      }
                    },
                    peyush_bansal: {
                      type: Type.OBJECT,
                      properties: {
                        initial_reaction: { type: Type.STRING },
                        key_questions_asked: { type: Type.ARRAY, items: { type: Type.STRING } },
                        decision_turning_point: { type: Type.STRING },
                        emotional_engagement: { type: Type.STRING },
                        expertise_alignment: { type: Type.STRING }
                      }
                    },
                    namita_thapar: {
                      type: Type.OBJECT,
                      properties: {
                        initial_reaction: { type: Type.STRING },
                        key_questions_asked: { type: Type.ARRAY, items: { type: Type.STRING } },
                        decision_turning_point: { type: Type.STRING },
                        emotional_engagement: { type: Type.STRING },
                        expertise_alignment: { type: Type.STRING }
                      }
                    }
                  }
                },
                
                // PREPARATION INTELLIGENCE
                preparation_intelligence: {
                  type: Type.OBJECT,
                  properties: {
                    critical_preparation_gaps: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: "What the founders should have prepared better"
                    },
                    successful_preparation_elements: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: "What the founders did well in preparation"
                    },
                    sector_specific_insights: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: "Insights specific to this business sector"
                    },
                    pitch_timing_analysis: {
                      type: Type.STRING,
                      description: "Analysis of how timing affected the pitch outcome"
                    },
                    market_readiness_assessment: {
                      type: Type.STRING,
                      description: "How market conditions affected reception"
                    }
                  }
                },
                
                // ACTIONABLE RECOMMENDATIONS
                actionable_recommendations: {
                  type: Type.OBJECT,
                  properties: {
                    for_similar_businesses: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: "Specific advice for similar businesses"
                    },
                    shark_specific_strategies: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: "How to approach each shark based on this case"
                    },
                    deal_structure_recommendations: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: "Optimal deal structures for this type of business"
                    },
                    presentation_optimization: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: "How to improve pitch presentation"
                    },
                    risk_mitigation_strategies: {
                      type: Type.ARRAY,
                      items: { type: Type.STRING },
                      description: "How to address potential concerns proactively"
                    }
                  }
                },
                
                // INTELLIGENCE NARRATIVE
                intelligence_narrative: {
                  type: Type.STRING,
                  description: "Comprehensive narrative combining all intelligence insights into actionable preparation guide"
                },
                
                confidence: { type: Type.NUMBER, description: "Extraction confidence (0-100)" }
              },
              required: ["shark_reactions", "preparation_intelligence", "actionable_recommendations", "intelligence_narrative", "confidence"]
            }
          }
        });
      });

      const parsedResult = JSON.parse(result.text || '{}');
      return this.normalizeResultConfidence(parsedResult);
    } catch (error) {
      console.error('  ❌ Phase 8 extraction failed:', error);
      return { confidence: 0 };
    }
  }

  /**
   * Process a single company through all 8 phases
   */
  async processCompany(companyId: string, transcript: string): Promise<ModernExtractionResult> {
    const startTime = Date.now();
    console.log(`\n🚀 Processing ${companyId} through Modern 8-Phase Pipeline`);
    console.log('=' .repeat(60));
    
    try {
      // Phase 1: Basic Company Info (Gemini handles mixed Hindi/English directly)
      const phase1 = await this.extractPhase1(transcript);
      console.log(`  ✅ Phase 1 Complete - Company: ${phase1.company_name || 'Unknown'} (${phase1.confidence || 0}%)`);
      
      if (!phase1.company_name || phase1.company_name === 'Unknown' || phase1.confidence < 50) {
        console.log('  ⚠️ Low confidence in Phase 1, continuing with reduced expectations...');
      }
      
      // Phase 2: Financial Data
      const phase2 = await this.extractPhase2(transcript, phase1);
      console.log(`  ✅ Phase 2 Complete - Ask: ₹${phase2.ask_amount?.toLocaleString() || 'Unknown'} (${phase2.confidence || 0}%)`);
      
      // Phase 3: Business Model
      const phase3 = await this.extractPhase3(transcript, phase1, phase2);
      console.log(`  ✅ Phase 3 Complete - Model: ${phase3.model_type || 'Unknown'} (${phase3.confidence || 0}%)`);
      
      // Phase 4: Deal Dynamics
      const contextData = { phase1, phase2, phase3 };
      const phase4 = await this.extractPhase4(transcript, contextData);
      console.log(`  ✅ Phase 4 Complete - Deal: ${phase4.deal_closed ? 'YES' : 'NO'} (${phase4.confidence || 0}%)`);
      
      // Phase 5: Shark Analysis
      const fullContext = { ...contextData, phase4 };
      const phase5 = await this.extractPhase5(transcript, fullContext);
      console.log(`  ✅ Phase 5 Complete - Shark Analysis (${phase5.confidence || 0}%)`);
      
      // Phase 6: Strategic Insights
      const phase6 = await this.extractPhase6(transcript, { ...fullContext, phase5 });
      console.log(`  ✅ Phase 6 Complete - Insights: ${phase6.success_factors?.length || 0} factors (${phase6.confidence || 0}%)`);
      
      // Phase 7: Rich Narrative
      const phase7 = await this.extractPhase7(transcript, { ...fullContext, phase5, phase6 });
      console.log(`  ✅ Phase 7 Complete - Story: ${phase7.complete_story?.length || 0} chars (${phase7.confidence || 0}%)`);
      
      // Phase 8: Advanced Shark Intelligence
      const phase8 = await this.extractPhase8(transcript, { ...fullContext, phase5, phase6, phase7 });
      console.log(`  ✅ Phase 8 Complete - Advanced Intelligence (${phase8.confidence || 0}%)`);
      
      const endTime = Date.now();
      const processingTimeSeconds = (endTime - startTime) / 1000;
      
      const result: ModernExtractionResult = {
        basicInfo: phase1,
        financials: phase2,
        businessModel: phase3,
        dealDynamics: phase4,
        sharkAnalysis: phase5,
        strategicInsights: phase6,
        narrative: phase7,
        advancedIntelligence: phase8,
        metadata: {
          processing_timestamp: new Date().toISOString(),
          total_phases_completed: 8,
          overall_confidence: Math.round([
            phase1.confidence || 0,
            phase2.confidence || 0,
            phase3.confidence || 0,
            phase4.confidence || 0,
            phase5.confidence || 0,
            phase6.confidence || 0,
            phase7.confidence || 0,
            phase8.confidence || 0
          ].reduce((sum, conf) => sum + conf, 0) / 8),
          processing_time_seconds: processingTimeSeconds,
          model_version: 'gemini-2.0-flash-001',
          pipeline_version: 'enhanced-v2.0'
        }
      };
      
      console.log(`\n🎉 Processing Complete!`);
      console.log(`Overall Confidence: ${result.metadata.overall_confidence}%`);
      console.log(`Processing Time: ${processingTimeSeconds.toFixed(1)}s`);
      
      return result;
      
    } catch (error) {
      console.error(`❌ Processing failed for ${companyId}:`, error);
      throw error;
    }
  }

  /**
   * Store modern extraction result in database
   */
  async storeResult(companyId: string, result: ModernExtractionResult): Promise<void> {
    try {
      // Safely extract values with proper defaults
      const safeBasicInfo = result.basicInfo || {};
      const safeFinancials = result.financials || {};
      const safeBusinessModel = result.businessModel || {};
      const safeDealDynamics = result.dealDynamics || {};
      const safeSharkAnalysis = result.sharkAnalysis || {};
      const safeStrategicInsights = result.strategicInsights || {};
      const safeNarrative = result.narrative || {};
      const safeMetadata = result.metadata || {};

      await sql`
        UPDATE shark_tank_companies
        SET 
          name = ${safeBasicInfo.company_name || 'Unknown'},
          name_transliterated = ${safeBasicInfo.company_name_transliterated || null},
          founder_names = ${JSON.stringify(safeBasicInfo.founder_names || [])},
          business_description = ${safeBasicInfo.business_description || ''},
          sector = ${safeBasicInfo.sector || 'Unknown'},
          ask_amount = ${safeFinancials.ask_amount || 0},
          ask_equity = ${safeFinancials.ask_equity || 0},
          ask_valuation = ${safeFinancials.ask_valuation || 0},
          current_revenue = ${safeFinancials.current_revenue || 0},
          business_model_type = ${safeBusinessModel.model_type || null},
          revenue_streams = ${JSON.stringify(safeBusinessModel.revenue_streams || [])},
          sharks_interested = ${JSON.stringify(safeDealDynamics.sharks_interested || [])},
          deal_closed = ${safeDealDynamics.deal_closed || false},
          deal_amount = ${safeDealDynamics.final_deal?.amount || 0},
          deal_equity = ${safeDealDynamics.final_deal?.equity || 0},
          deal_sharks = ${JSON.stringify(safeDealDynamics.final_deal?.sharks || [])},
          shark_analysis = ${JSON.stringify(safeSharkAnalysis)},
          strategic_insights = ${JSON.stringify(safeStrategicInsights)},
          complete_narrative = ${safeNarrative.complete_story || ''},
          full_narrative = ${safeNarrative.complete_story || ''},
          executive_summary = ${safeNarrative.executive_summary || ''},
          searchable_text = ${safeNarrative.searchable_text || ''},
          extraction_confidence = ${safeMetadata.overall_confidence || 0},
          processing_version = ${safeMetadata.pipeline_version || 'unknown'},
          processing_timestamp = NOW(),
          processing_time_seconds = ${safeMetadata.processing_time_seconds || 0}
        WHERE id = ${companyId}
      `;
      
      console.log(`✅ Stored result for ${companyId}`);
    } catch (error) {
      console.error(`Failed to store result for ${companyId}:`, error);
      throw error;
    }
  }

  /**
   * Process all companies with the consolidated pipeline
   */
  async processAllCompanies(limit?: number): Promise<void> {
    console.log('\n🚀 CONSOLIDATED 7-PHASE PIPELINE: Processing All Companies');
    console.log('=' .repeat(70));
    
    try {
      // Get companies to process (prioritize those with "Unknown" names)
      const companies = await sql`
        SELECT id, name, season, episode, metadata->'videoId' as video_id
        FROM shark_tank_companies
        WHERE processing_version IS NULL 
           OR processing_version != 'consolidated-v1.0'
           OR name = 'Unknown'
        ORDER BY 
          CASE WHEN name = 'Unknown' THEN 0 ELSE 1 END,
          season, episode
        ${limit ? sql`LIMIT ${limit}` : sql``}
      `;
      
      console.log(`📊 Found ${companies.length} companies to process`);
      console.log(`🎯 Processing ${limit || 'all'} companies...\n`);
      
      let successCount = 0;
      let processedCount = 0;
      
      for (const company of companies) {
        console.log(`\n📋 Company ${processedCount + 1}/${companies.length}`);
        console.log(`ID: ${company.id}, Current Name: ${company.name}`);
        
        try {
          // Load transcript
          const transcript = await this.loadTranscript(company);
          
          if (!transcript) {
            console.log('❌ No transcript available, skipping...');
            continue;
          }
          
          // Process through consolidated pipeline
          const result = await this.processCompany(company.id, transcript);
          
          // Store results
          await this.storeResult(company.id, result);
          
          if (result.metadata.overall_confidence >= 70) {
            successCount++;
            console.log(`✅ SUCCESS: ${result.basicInfo.company_name} (${result.metadata.overall_confidence}% confidence)`);
          } else {
            console.log(`⚠️ LOW CONFIDENCE: ${result.basicInfo.company_name} (${result.metadata.overall_confidence}% confidence)`);
          }
          
        } catch (error) {
          console.error(`❌ Failed to process ${company.id}:`, error);
        }
        
        processedCount++;
        
        // Delay between companies to respect rate limits
        if (processedCount < companies.length) {
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
      
      // Final summary
      console.log('\n' + '=' .repeat(70));
      console.log('🎉 CONSOLIDATED PIPELINE COMPLETE!');
      console.log('=' .repeat(70));
      console.log(`Total Companies Processed: ${processedCount}`);
      console.log(`High Confidence Results: ${successCount}`);
      console.log(`Success Rate: ${((successCount / processedCount) * 100).toFixed(1)}%`);
      console.log(`Previous Pipeline Rate: 30.9%`);
      
      const improvement = ((successCount / processedCount) * 100) - 30.9;
      if (improvement > 0) {
        console.log(`🚀 IMPROVEMENT: +${improvement.toFixed(1)} percentage points!`);
      }
      
    } catch (error) {
      console.error('❌ Consolidated pipeline processing failed:', error);
      throw error;
    }
  }

  /**
   * Load transcript for a company
   */
  private async loadTranscript(company: any): Promise<string | null> {
    // Get transcript ID from metadata (for files) or use company ID directly (for legacy)
    const transcriptId = company.metadata?.transcriptId || company.id;
    
    // Use single flat transcript directory structure - NO LEGACY PATHS
    const transcriptPath = `/Users/<USER>/giki-ai-workspace/packages/transcript-fetcher/data/transcripts/${transcriptId}.text`;
    
    if (existsSync(transcriptPath)) {
      try {
        return readFileSync(transcriptPath, 'utf-8');
      } catch (error) {
        console.error(`Error reading ${transcriptPath}:`, error);
      }
    }
    
    return null;
  }
}

// Export the consolidated pipeline instance
export const transcriptProcessingPipeline = new TranscriptProcessingPipeline();