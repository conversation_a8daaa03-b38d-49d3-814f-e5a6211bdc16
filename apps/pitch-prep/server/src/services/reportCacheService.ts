/**
 * Report Cache Service
 * Provides in-memory and database-backed caching for unified reports
 * 
 * Features:
 * - In-memory LRU cache for fast access
 * - PostgreSQL persistence for long-term storage
 * - TTL-based expiration
 * - Cache invalidation strategies
 */

import { sql } from '../database/connection.js';
import type { UnifiedReport, UnifiedReportRequest } from './unifiedReportService.js';

interface CacheEntry {
  key: string;
  report: UnifiedReport;
  timestamp: number;
  hits: number;
  request_hash: string;
}

export class ReportCacheService {
  private memoryCache: Map<string, CacheEntry> = new Map();
  private readonly maxCacheSize = 100; // Maximum number of reports in memory
  private readonly ttlMs = 3600000; // 1 hour TTL for memory cache
  private readonly dbTtlDays = 7; // 7 days TTL for database cache

  /**
   * Generate cache key from request
   */
  private generateCacheKey(request: UnifiedReportRequest): string {
    // Create a deterministic key from request parameters
    const keyComponents = [
      request.company_name.toLowerCase().replace(/\s+/g, '-'),
      request.business_sector.toLowerCase().replace(/\s+/g, '-'),
      (request.business_model || request.business_analysis?.business_model || 'unknown').toLowerCase().replace(/\s+/g, '-'),
      request.report_type || 'comprehensive',
      request.funding_sought?.toString() || '0',
      request.equity_offered?.toString() || '0'
    ];
    
    return `report:${keyComponents.join(':')}`;
  }

  /**
   * Generate request hash for comparison
   */
  private generateRequestHash(request: UnifiedReportRequest): string {
    // Create a hash of the full request for validation
    const requestString = JSON.stringify({
      ...request,
      // Exclude volatile fields
      user_id: undefined,
      session_id: undefined,
      generation_timestamp: undefined
    });
    
    // Simple hash function (in production, use crypto.createHash)
    let hash = 0;
    for (let i = 0; i < requestString.length; i++) {
      const char = requestString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return hash.toString(36);
  }

  /**
   * Get cached report if available
   */
  async getCachedReport(request: UnifiedReportRequest): Promise<UnifiedReport | null> {
    const cacheKey = this.generateCacheKey(request);
    const requestHash = this.generateRequestHash(request);
    
    // Check memory cache first
    const memoryEntry = this.memoryCache.get(cacheKey);
    if (memoryEntry) {
      // Validate TTL
      if (Date.now() - memoryEntry.timestamp < this.ttlMs) {
        // Validate request hasn't changed significantly
        if (memoryEntry.request_hash === requestHash) {
          memoryEntry.hits++;
          console.log(`[Cache] Memory hit for ${cacheKey} (hits: ${memoryEntry.hits})`);
          return memoryEntry.report;
        }
      } else {
        // Expired, remove from memory
        this.memoryCache.delete(cacheKey);
      }
    }
    
    // Check database cache
    try {
      const dbResult = await sql`
        SELECT 
          report_data,
          created_at,
          cache_hits
        FROM report_cache
        WHERE 
          cache_key = ${cacheKey}
          AND request_hash = ${requestHash}
          AND created_at > NOW() - INTERVAL '${this.dbTtlDays} days'
        LIMIT 1
      `;
      
      if (dbResult.length > 0) {
        const report = dbResult[0].report_data as UnifiedReport;
        
        // Update hit count
        await sql`
          UPDATE report_cache 
          SET 
            cache_hits = cache_hits + 1,
            last_accessed = NOW()
          WHERE cache_key = ${cacheKey}
        `;
        
        // Add to memory cache for faster subsequent access
        this.addToMemoryCache(cacheKey, report, requestHash);
        
        console.log(`[Cache] Database hit for ${cacheKey}`);
        return report;
      }
    } catch (error) {
      console.error('[Cache] Database fetch error:', error);
    }
    
    console.log(`[Cache] Miss for ${cacheKey}`);
    return null;
  }

  /**
   * Store report in cache
   */
  async cacheReport(request: UnifiedReportRequest, report: UnifiedReport): Promise<void> {
    const cacheKey = this.generateCacheKey(request);
    const requestHash = this.generateRequestHash(request);
    
    // Add to memory cache
    this.addToMemoryCache(cacheKey, report, requestHash);
    
    // Store in database
    try {
      await sql`
        INSERT INTO report_cache (
          cache_key,
          request_hash,
          company_name,
          business_sector,
          report_type,
          report_data,
          created_at,
          last_accessed,
          cache_hits
        ) VALUES (
          ${cacheKey},
          ${requestHash},
          ${request.company_name},
          ${request.business_sector},
          ${request.report_type || 'comprehensive'},
          ${JSON.stringify(report)},
          NOW(),
          NOW(),
          0
        )
        ON CONFLICT (cache_key) 
        DO UPDATE SET
          request_hash = EXCLUDED.request_hash,
          report_data = EXCLUDED.report_data,
          created_at = NOW(),
          last_accessed = NOW(),
          cache_hits = 0
      `;
      
      console.log(`[Cache] Stored report for ${cacheKey}`);
    } catch (error) {
      console.error('[Cache] Database store error:', error);
    }
  }

  /**
   * Add to memory cache with LRU eviction
   */
  private addToMemoryCache(key: string, report: UnifiedReport, requestHash: string): void {
    // Implement LRU eviction if cache is full
    if (this.memoryCache.size >= this.maxCacheSize) {
      // Find least recently used entry
      let lruKey: string | null = null;
      let lruTime = Date.now();
      
      for (const [k, entry] of this.memoryCache.entries()) {
        if (entry.timestamp < lruTime) {
          lruTime = entry.timestamp;
          lruKey = k;
        }
      }
      
      if (lruKey) {
        this.memoryCache.delete(lruKey);
        console.log(`[Cache] Evicted LRU entry: ${lruKey}`);
      }
    }
    
    this.memoryCache.set(key, {
      key,
      report,
      timestamp: Date.now(),
      hits: 0,
      request_hash: requestHash
    });
  }

  /**
   * Invalidate cache for a specific company
   */
  async invalidateCompanyCache(companyName: string): Promise<void> {
    const pattern = `report:${companyName.toLowerCase().replace(/\s+/g, '-')}:%`;
    
    // Clear from memory cache
    for (const key of this.memoryCache.keys()) {
      if (key.includes(companyName.toLowerCase().replace(/\s+/g, '-'))) {
        this.memoryCache.delete(key);
      }
    }
    
    // Clear from database
    try {
      await sql`
        DELETE FROM report_cache 
        WHERE cache_key LIKE ${pattern}
      `;
      
      console.log(`[Cache] Invalidated cache for company: ${companyName}`);
    } catch (error) {
      console.error('[Cache] Invalidation error:', error);
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{
    memorySize: number;
    memoryHits: number;
    dbSize: number;
    dbHits: number;
    avgResponseTime: number;
  }> {
    let memoryHits = 0;
    for (const entry of this.memoryCache.values()) {
      memoryHits += entry.hits;
    }
    
    const dbStats = await sql`
      SELECT 
        COUNT(*) as db_size,
        SUM(cache_hits) as total_hits,
        AVG(EXTRACT(EPOCH FROM (last_accessed - created_at))) as avg_lifetime
      FROM report_cache
      WHERE created_at > NOW() - INTERVAL '${this.dbTtlDays} days'
    `;
    
    return {
      memorySize: this.memoryCache.size,
      memoryHits,
      dbSize: Number(dbStats[0].db_size || 0),
      dbHits: Number(dbStats[0].total_hits || 0),
      avgResponseTime: Number(dbStats[0].avg_lifetime || 0)
    };
  }

  /**
   * Clean up expired cache entries
   */
  async cleanupExpiredCache(): Promise<void> {
    // Clean memory cache
    const now = Date.now();
    for (const [key, entry] of this.memoryCache.entries()) {
      if (now - entry.timestamp > this.ttlMs) {
        this.memoryCache.delete(key);
      }
    }
    
    // Clean database cache
    try {
      const result = await sql`
        DELETE FROM report_cache 
        WHERE created_at < NOW() - INTERVAL '${this.dbTtlDays} days'
      `;
      
      console.log(`[Cache] Cleaned up expired entries from database`);
    } catch (error) {
      console.error('[Cache] Cleanup error:', error);
    }
  }
}

// Export singleton instance
export const reportCacheService = new ReportCacheService();

// Schedule periodic cleanup (every hour)
setInterval(() => {
  reportCacheService.cleanupExpiredCache();
}, 3600000);