/**
 * SSE Progress Service
 * Provides real-time progress updates for report generation
 * 
 * Features:
 * - Server-Sent Events for real-time updates
 * - Progress tracking for each generation phase
 * - Error handling and recovery
 * - Multiple client support
 */

import { EventEmitter } from 'events';
import type { Context } from 'hono';

export interface ProgressEvent {
  type: 'progress' | 'completed' | 'error' | 'connected';
  sessionId: string;
  step?: string;
  message?: string;
  progress?: number;
  details?: string;
  stepNumber?: number;
  totalSteps?: number;
  timestamp?: string;
  redirectUrl?: string;
  error?: string;
}

export class SSEProgressService extends EventEmitter {
  private connections: Map<string, Set<Context>> = new Map();
  private progressData: Map<string, ProgressEvent[]> = new Map();
  
  /**
   * Register a new SSE connection
   */
  registerConnection(sessionId: string, context: Context): void {
    // Get or create connection set for this session
    if (!this.connections.has(sessionId)) {
      this.connections.set(sessionId, new Set());
    }
    
    const sessionConnections = this.connections.get(sessionId)!;
    sessionConnections.add(context);
    
    console.log(`[SSE] Client connected for session: ${sessionId}`);
    
    // Send initial connection event
    this.sendEvent(sessionId, {
      type: 'connected',
      sessionId,
      message: 'Connected to progress stream',
      timestamp: new Date().toISOString()
    });
    
    // Send any buffered events
    const bufferedEvents = this.progressData.get(sessionId);
    if (bufferedEvents) {
      for (const event of bufferedEvents) {
        this.sendEventToContext(context, event);
      }
    }
    
    // Handle disconnect
    context.req.raw.signal.addEventListener('abort', () => {
      this.removeConnection(sessionId, context);
    });
  }
  
  /**
   * Remove a connection when client disconnects
   */
  removeConnection(sessionId: string, context: Context): void {
    const sessionConnections = this.connections.get(sessionId);
    if (sessionConnections) {
      sessionConnections.delete(context);
      
      if (sessionConnections.size === 0) {
        this.connections.delete(sessionId);
      }
    }
    
    console.log(`[SSE] Client disconnected for session: ${sessionId}`);
  }
  
  /**
   * Send progress update to all connected clients for a session
   */
  sendProgress(
    sessionId: string,
    step: string,
    message: string,
    progress: number,
    details?: string,
    stepNumber?: number,
    totalSteps?: number
  ): void {
    const event: ProgressEvent = {
      type: 'progress',
      sessionId,
      step,
      message,
      progress,
      details,
      stepNumber,
      totalSteps,
      timestamp: new Date().toISOString()
    };
    
    // Store event for late joiners
    this.bufferEvent(sessionId, event);
    
    // Send to all connected clients
    this.sendEvent(sessionId, event);
  }
  
  /**
   * Send completion event
   */
  sendCompletion(sessionId: string, message: string, redirectUrl?: string): void {
    const event: ProgressEvent = {
      type: 'completed',
      sessionId,
      message,
      redirectUrl,
      progress: 100,
      timestamp: new Date().toISOString()
    };
    
    this.bufferEvent(sessionId, event);
    this.sendEvent(sessionId, event);
    
    // Clean up after completion
    setTimeout(() => {
      this.cleanupSession(sessionId);
    }, 30000); // Clean up after 30 seconds
  }
  
  /**
   * Send error event
   */
  sendError(sessionId: string, error: string, details?: string): void {
    const event: ProgressEvent = {
      type: 'error',
      sessionId,
      message: 'An error occurred during generation',
      error,
      details,
      timestamp: new Date().toISOString()
    };
    
    this.bufferEvent(sessionId, event);
    this.sendEvent(sessionId, event);
  }
  
  /**
   * Send event to all connections for a session
   */
  private sendEvent(sessionId: string, event: ProgressEvent): void {
    const sessionConnections = this.connections.get(sessionId);
    if (!sessionConnections || sessionConnections.size === 0) {
      console.log(`[SSE] No active connections for session: ${sessionId}`);
      return;
    }
    
    for (const context of sessionConnections) {
      this.sendEventToContext(context, event);
    }
  }
  
  /**
   * Send event to a specific context
   */
  private sendEventToContext(context: Context, event: ProgressEvent): void {
    try {
      const writer = context.env?.writer;
      if (writer) {
        writer.write(`data: ${JSON.stringify(event)}\n\n`);
      }
    } catch (error) {
      console.error(`[SSE] Failed to send event:`, error);
      // Remove failed connection
      this.removeConnection(event.sessionId, context);
    }
  }
  
  /**
   * Buffer event for late-joining clients
   */
  private bufferEvent(sessionId: string, event: ProgressEvent): void {
    if (!this.progressData.has(sessionId)) {
      this.progressData.set(sessionId, []);
    }
    
    const events = this.progressData.get(sessionId)!;
    events.push(event);
    
    // Keep only last 100 events
    if (events.length > 100) {
      events.shift();
    }
  }
  
  /**
   * Clean up session data
   */
  private cleanupSession(sessionId: string): void {
    // Close any remaining connections
    const sessionConnections = this.connections.get(sessionId);
    if (sessionConnections) {
      for (const context of sessionConnections) {
        try {
          const writer = context.env?.writer;
          if (writer) {
            writer.close();
          }
        } catch (error) {
          // Ignore close errors
        }
      }
    }
    
    this.connections.delete(sessionId);
    this.progressData.delete(sessionId);
    
    console.log(`[SSE] Cleaned up session: ${sessionId}`);
  }
  
  /**
   * Create SSE endpoint handler for Hono
   */
  createSSEHandler() {
    return async (c: Context) => {
      const sessionId = c.req.param('sessionId');
      
      if (!sessionId) {
        return c.json({ error: 'Session ID required' }, 400);
      }
      
      // Set SSE headers
      c.header('Content-Type', 'text/event-stream');
      c.header('Cache-Control', 'no-cache');
      c.header('Connection', 'keep-alive');
      c.header('X-Accel-Buffering', 'no'); // Disable nginx buffering
      
      // Create stream
      const stream = new ReadableStream({
        start: (controller) => {
          // Store writer in context for sending events
          c.env = { ...c.env, writer: controller };
          
          // Register connection
          this.registerConnection(sessionId, c);
          
          // Keep connection alive with periodic heartbeat
          const heartbeat = setInterval(() => {
            try {
              controller.enqueue(new TextEncoder().encode(':heartbeat\n\n'));
            } catch (error) {
              clearInterval(heartbeat);
              this.removeConnection(sessionId, c);
            }
          }, 30000); // Every 30 seconds
          
          // Clean up on close
          c.req.raw.signal.addEventListener('abort', () => {
            clearInterval(heartbeat);
            controller.close();
          });
        }
      });
      
      return new Response(stream, {
        headers: c.res.headers
      });
    };
  }
}

// Export singleton instance
export const sseProgressService = new SSEProgressService();