/**
 * Shark Compatibility Pre-computation Service
 * MODERNIZED: Phase 2 of prompt architecture consolidation
 *
 * Analyzes historical Shark Tank data to pre-compute compatibility scores
 * and investment patterns for instant user journey analysis.
 *
 * MIGRATION COMPLETE: 3 inline prompts → template system integration
 * - analyzeSharkPatterns() → shark-compatibility template
 * - generateRecommendedAsk() → investment-strategy template
 * - generateTalkingPointsAndStrategy() → pitch-preparation template
 *
 * All AI interactions now use centralized template management for consistency,
 * versioning, and maintainability across the service.
 */

import { sql } from '../database/connection';
import { GoogleGenAI } from '@google/genai';
import { PromptManagementService } from './promptManagementService';

export interface SharkProfile {
  shark_name: string;
  total_deals: number;
  success_rate: number;
  average_equity: number;
  average_investment: number;
  preferred_sectors: string[];
  investment_range: { min: number; max: number };
  equity_range: { min: number; max: number };
  deal_patterns: {
    quick_decisions: number;
    negotiation_heavy: number;
    partnership_focused: number;
  };
  red_flags: string[];
  success_factors: string[];
  decision_timeline: 'fast' | 'medium' | 'deliberate';
  risk_tolerance: 'low' | 'medium' | 'high';
}

export interface CompatibilityCache {
  business_sector: string;
  revenue_range: string;
  shark_scores: {
    [sharkName: string]: {
      compatibility_score: number;
      investment_likelihood: number;
      recommended_ask: { amount: number; equity: number };
      key_talking_points: string[];
      potential_concerns: string[];
      negotiation_strategy: string;
      confidence_level: number;
    };
  };
  generated_at: Date;
  expires_at: Date;
}

export class SharkCompatibilityService {
  private geminiClient: GoogleGenAI;
  private promptManagementService: PromptManagementService;

  constructor(promptService?: PromptManagementService) {
    const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      console.warn('[SharkCompatibilityService] WARNING: GOOGLE_API_KEY not configured - service will not function');
      // Create dummy clients to avoid crashes
      this.geminiClient = null as any;
      this.promptManagementService = null as any;
      return;
    }
    this.geminiClient = new GoogleGenAI({ apiKey });

    // Initialize prompt management service
    this.promptManagementService = promptService || new PromptManagementService();
  }

  /**
   * Generate comprehensive shark profiles from historical data
   */
  async generateSharkProfiles(): Promise<SharkProfile[]> {
    console.log('[SharkCompatibilityService] Generating comprehensive shark profiles...');

    try {
      // Since we don't have historical Shark Tank data in the current DB,
      // we'll return default profiles for now
      console.log('[SharkCompatibilityService] Using default shark profiles (no historical data in current DB)');
      const profiles = this.getDefaultSharkProfilesList();

      // Store profiles in database for caching
      await this.storeSharkProfiles(profiles);

      console.log(`[SharkCompatibilityService] ✅ Generated ${profiles.length} default shark profiles`);
      return profiles;

    } catch (error) {
      console.error('[SharkCompatibilityService] Failed to generate shark profiles:', error);
      throw error;
    }
  }

  /**
   * Pre-compute compatibility caches for common business scenarios
   */
  async preComputeCompatibilityCaches(): Promise<{
    generated: number;
    cached_combinations: number;
    processing_time: number;
  }> {
    const startTime = Date.now();
    console.log('[SharkCompatibilityService] Starting pre-computation of compatibility caches...');

    try {
      // Get current shark profiles
      const sharkProfiles = await this.getSharkProfiles();
      if (sharkProfiles.length === 0) {
        console.log('[SharkCompatibilityService] No shark profiles found, generating first...');
        await this.generateSharkProfiles();
      }

      // Define common business scenarios to pre-compute
      const businessScenarios = [
        // Technology sectors
        { sector: 'Technology', revenue_ranges: ['0-100000', '100000-1000000', '1000000-10000000', '10000000+'] },
        { sector: 'E-commerce', revenue_ranges: ['0-100000', '100000-1000000', '1000000-10000000', '10000000+'] },
        { sector: 'SaaS', revenue_ranges: ['0-100000', '100000-1000000', '1000000-10000000', '10000000+'] },

        // Consumer sectors
        { sector: 'Food & Beverage', revenue_ranges: ['0-100000', '100000-1000000', '1000000-10000000'] },
        { sector: 'Fashion & Beauty', revenue_ranges: ['0-100000', '100000-1000000', '1000000-10000000'] },
        { sector: 'Health & Fitness', revenue_ranges: ['0-100000', '100000-1000000', '1000000-10000000'] },

        // Service sectors
        { sector: 'Education', revenue_ranges: ['0-100000', '100000-1000000', '1000000-5000000'] },
        { sector: 'Healthcare', revenue_ranges: ['100000-1000000', '1000000-10000000', '10000000+'] },
        { sector: 'Fintech', revenue_ranges: ['100000-1000000', '1000000-10000000', '10000000+'] }
      ];

      let generatedCaches = 0;
      let totalCombinations = 0;

      for (const scenario of businessScenarios) {
        for (const revenueRange of scenario.revenue_ranges) {
          console.log(`[SharkCompatibilityService] Pre-computing: ${scenario.sector} | ${revenueRange}`);

          const compatibilityCache = await this.generateCompatibilityCache(
            scenario.sector,
            revenueRange
          );

          await this.storeCompatibilityCache(compatibilityCache);

          generatedCaches++;
          totalCombinations++;

          // Brief pause to avoid overwhelming the API
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      const processingTime = Date.now() - startTime;

      console.log(`[SharkCompatibilityService] ✅ Pre-computation complete:`);
      console.log(`   Generated: ${generatedCaches} caches`);
      console.log(`   Total combinations: ${totalCombinations}`);
      console.log(`   Processing time: ${Math.round(processingTime / 1000)}s`);

      return {
        generated: generatedCaches,
        cached_combinations: totalCombinations,
        processing_time: processingTime
      };

    } catch (error) {
      console.error('[SharkCompatibilityService] Pre-computation failed:', error);
      throw error;
    }
  }

  /**
   * Get cached compatibility or generate on-demand
   */
  async getCompatibilityForUser(
    businessSector: string,
    revenueRange: string,
    forceRefresh: boolean = false
  ): Promise<CompatibilityCache> {
    console.log(`[SharkCompatibilityService] Getting compatibility for ${businessSector} | ${revenueRange}`);

    try {
      // Check for existing cache first (unless force refresh)
      if (!forceRefresh) {
        const cached = await this.getCachedCompatibility(businessSector, revenueRange);
        if (cached && !this.isCacheExpired(cached)) {
          console.log(`[SharkCompatibilityService] ✅ Using cached compatibility`);
          return cached;
        }
      }

      // Generate new compatibility cache
      console.log(`[SharkCompatibilityService] Generating new compatibility cache...`);
      const newCache = await this.generateCompatibilityCache(businessSector, revenueRange);
      await this.storeCompatibilityCache(newCache);

      return newCache;

    } catch (error) {
      console.error('[SharkCompatibilityService] Failed to get compatibility:', error);
      throw error;
    }
  }

  /**
   * Analyze shark patterns using template management system
   * MODERNIZED: Template-based approach for shark pattern analysis
   */
  private async analyzeSharkPatterns(sharkName: string, data: any): Promise<{
    patterns: { quick_decisions: number; negotiation_heavy: number; partnership_focused: number };
    red_flags: string[];
    success_factors: string[];
    decision_timeline: 'fast' | 'medium' | 'deliberate';
    risk_tolerance: 'low' | 'medium' | 'high';
  }> {
    try {
      // Prepare template variables for shark pattern analysis
      const templateVariables = {
        company_name: `${sharkName} Pattern Analysis`,
        business_sector: data.sectors?.slice(0, 5).join(', ') || 'General',
        business_model: 'Investor Analysis',
        investment_ask: 'Pattern Study',
        equity_offered: 'N/A',
        key_metrics: `Total Appearances: ${data.total_appearances}, Successful Deals: ${data.successful_deals}, Success Rate: ${data.total_appearances > 0 ? Math.round((data.successful_deals / data.total_appearances) * 100) : 0}%`,
        shark_profiles_data: JSON.stringify({
          name: sharkName,
          total_appearances: data.total_appearances,
          successful_deals: data.successful_deals,
          sectors: data.sectors?.slice(0, 10) || [],
          common_concerns: data.concerns?.slice(0, 5) || [],
          success_factors: data.success_factors?.slice(0, 5) || []
        })
      };

      // Execute shark compatibility template for pattern analysis
      const { result } = await this.promptManagementService.executeTemplate(
        'shark-compatibility',
        templateVariables
      );

      const analysisResult = JSON.parse(result);

      // Extract pattern data from template results
      // Since the template returns comprehensive compatibility analysis,
      // we need to extract the pattern-specific information
      const compatibilityAnalysis = analysisResult.shark_compatibility_analysis;

      // Map template output to expected pattern format
      return {
        patterns: {
          quick_decisions: this.extractQuickDecisionScore(compatibilityAnalysis),
          negotiation_heavy: this.extractNegotiationScore(compatibilityAnalysis),
          partnership_focused: this.extractPartnershipScore(compatibilityAnalysis)
        },
        red_flags: this.extractRedFlags(compatibilityAnalysis),
        success_factors: this.extractSuccessFactors(compatibilityAnalysis),
        decision_timeline: this.extractDecisionTimeline(compatibilityAnalysis),
        risk_tolerance: this.extractRiskTolerance(compatibilityAnalysis)
      };

    } catch (error) {
      console.warn(`[SharkCompatibilityService] Template analysis failed for ${sharkName}, using defaults:`, error);
      return this.getDefaultSharkPatterns(sharkName);
    }
  }

  /**
   * Helper methods to extract pattern data from template results
   */
  private extractQuickDecisionScore(compatibilityAnalysis: any): number {
    try {
      // Extract quick decision tendency from analysis
      const negotiations = compatibilityAnalysis?.individual_shark_analysis;
      if (negotiations) {
        const sharkData = Object.values(negotiations)[0] as any;
        if (sharkData?.negotiation_style && sharkData.negotiation_style.toLowerCase().includes('direct')) {
          return 85;
        }
        if (sharkData?.negotiation_style && sharkData.negotiation_style.toLowerCase().includes('quick')) {
          return 90;
        }
      }
      return 65; // Default moderate score
    } catch {
      return 65;
    }
  }

  private extractNegotiationScore(compatibilityAnalysis: any): number {
    try {
      const negotiations = compatibilityAnalysis?.individual_shark_analysis;
      if (negotiations) {
        const sharkData = Object.values(negotiations)[0] as any;
        if (sharkData?.negotiation_style && sharkData.negotiation_style.toLowerCase().includes('detailed')) {
          return 85;
        }
        if (sharkData?.negotiation_style && sharkData.negotiation_style.toLowerCase().includes('negotiation')) {
          return 80;
        }
      }
      return 70; // Default score
    } catch {
      return 70;
    }
  }

  private extractPartnershipScore(compatibilityAnalysis: any): number {
    try {
      const analysis = compatibilityAnalysis?.individual_shark_analysis;
      if (analysis) {
        const sharkData = Object.values(analysis)[0] as any;
        if (sharkData?.value_beyond_money && sharkData.value_beyond_money.length > 2) {
          return 80;
        }
      }
      return 60; // Default score
    } catch {
      return 60;
    }
  }

  private extractRedFlags(compatibilityAnalysis: any): string[] {
    try {
      const analysis = compatibilityAnalysis?.individual_shark_analysis;
      if (analysis) {
        const sharkData = Object.values(analysis)[0] as any;
        if (sharkData?.potential_concerns && Array.isArray(sharkData.potential_concerns)) {
          return sharkData.potential_concerns.slice(0, 3);
        }
      }
      const risks = compatibilityAnalysis?.risk_mitigation?.potential_objections;
      if (risks && Array.isArray(risks)) {
        return risks.slice(0, 3);
      }
      return ['Unclear business model', 'Market validation concerns', 'Scalability questions'];
    } catch {
      return ['Unclear business model', 'Market validation concerns', 'Scalability questions'];
    }
  }

  private extractSuccessFactors(compatibilityAnalysis: any): string[] {
    try {
      const analysis = compatibilityAnalysis?.strategic_recommendations;
      if (analysis?.preparation_focus?.key_metrics_to_highlight) {
        return analysis.preparation_focus.key_metrics_to_highlight.slice(0, 3);
      }
      const confidence = compatibilityAnalysis?.success_probability?.confidence_factors;
      if (confidence && Array.isArray(confidence)) {
        return confidence.slice(0, 3);
      }
      return ['Strong value proposition', 'Market traction', 'Experienced team'];
    } catch {
      return ['Strong value proposition', 'Market traction', 'Experienced team'];
    }
  }

  private extractDecisionTimeline(compatibilityAnalysis: any): 'fast' | 'medium' | 'deliberate' {
    try {
      const analysis = compatibilityAnalysis?.individual_shark_analysis;
      if (analysis) {
        const sharkData = Object.values(analysis)[0] as any;
        if (sharkData?.negotiation_style) {
          const style = sharkData.negotiation_style.toLowerCase();
          if (style.includes('quick') || style.includes('fast') || style.includes('direct')) {
            return 'fast';
          }
          if (style.includes('detailed') || style.includes('thorough')) {
            return 'deliberate';
          }
        }
      }
      return 'medium';
    } catch {
      return 'medium';
    }
  }

  private extractRiskTolerance(compatibilityAnalysis: any): 'low' | 'medium' | 'high' {
    try {
      const compatibility = compatibilityAnalysis?.individual_shark_analysis;
      if (compatibility) {
        const sharkData = Object.values(compatibility)[0] as any;
        if (sharkData?.compatibility_score) {
          const score = parseFloat(sharkData.compatibility_score);
          if (score > 8) return 'high';
          if (score < 6) return 'low';
        }
      }
      return 'medium';
    } catch {
      return 'medium';
    }
  }

  /**
   * Generate compatibility cache for a specific business scenario
   */
  private async generateCompatibilityCache(
    businessSector: string,
    revenueRange: string
  ): Promise<CompatibilityCache> {
    const sharkProfiles = await this.getSharkProfiles();
    const sharkScores: CompatibilityCache['shark_scores'] = {};

    // Find similar companies for context - simplified for current DB schema
    // Note: The current gikiaidb has limited company data, so using fallback
    const similarCompanies = await sql`
      SELECT name,
             industry as sector,
             stage
      FROM companies
      WHERE
        industry ILIKE '%' || ${businessSector} || '%'
        OR name ILIKE '%' || ${businessSector} || '%'
      ORDER BY RANDOM()
      LIMIT 20
    `.catch(err => {
      console.warn('[SharkCompatibilityService] Could not find similar companies:', err.message);
      return [];
    });

    const contextData = similarCompanies.map(c => ({
      name: c.name,
      sector: c.sector || businessSector,
      business_model: c.stage || 'Unknown',
      extra: {}
    }));

    for (const profile of sharkProfiles) {
      const compatibility = await this.calculateSharkCompatibility(
        profile,
        businessSector,
        revenueRange,
        contextData
      );

      sharkScores[profile.shark_name] = compatibility;
    }

    return {
      business_sector: businessSector,
      revenue_range: revenueRange,
      shark_scores: sharkScores,
      generated_at: new Date(),
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    };
  }

  /**
   * Calculate individual shark compatibility
   */
  private async calculateSharkCompatibility(
    sharkProfile: SharkProfile,
    businessSector: string,
    revenueRange: string,
    contextData: any[]
  ): Promise<CompatibilityCache['shark_scores'][string]> {
    // Base compatibility score
    let compatibilityScore = 50; // Start at neutral

    // Sector preference scoring
    const sectorMatch = sharkProfile.preferred_sectors.some(sector =>
      sector.toLowerCase().includes(businessSector.toLowerCase()) ||
      businessSector.toLowerCase().includes(sector.toLowerCase())
    );
    if (sectorMatch) compatibilityScore += 25;

    // Revenue range alignment
    const [minRevenue, maxRevenue] = this.parseRevenueRange(revenueRange);
    if (minRevenue >= sharkProfile.investment_range.min &&
        maxRevenue <= sharkProfile.investment_range.max * 10) {
      compatibilityScore += 15;
    }

    // Success rate bonus
    compatibilityScore += Math.min(15, sharkProfile.success_rate / 5);

    // Risk tolerance adjustment
    if (revenueRange === '0-100000' && sharkProfile.risk_tolerance === 'high') compatibilityScore += 10;
    if (revenueRange.includes('10000000') && sharkProfile.risk_tolerance === 'low') compatibilityScore -= 10;

    // Cap at 100
    compatibilityScore = Math.min(100, Math.max(0, compatibilityScore));

    // Investment likelihood (based on compatibility and historical patterns)
    const investmentLikelihood = (compatibilityScore + sharkProfile.success_rate) / 2;

    // Generate recommended ask using AI template
    const recommendedAsk = await this.generateRecommendedAsk(
      sharkProfile,
      businessSector,
      revenueRange
    );

    // Generate talking points and negotiation strategy using AI template
    const { talkingPoints, negotiationStrategy } = await this.generateTalkingPointsAndStrategy(
      sharkProfile,
      businessSector
    );

    return {
      compatibility_score: Math.round(compatibilityScore),
      investment_likelihood: Math.round(investmentLikelihood),
      recommended_ask: recommendedAsk,
      key_talking_points: talkingPoints,
      potential_concerns: sharkProfile.red_flags.slice(0, 3),
      negotiation_strategy: negotiationStrategy,
      confidence_level: Math.min(95, Math.max(60, compatibilityScore - 5))
    };
  }

  /**
   * Helper methods
   */
  private extractPreferredSectors(sectors: any[]): string[] {
    if (!sectors || !Array.isArray(sectors)) return [];

    const cleanSectors = sectors
      .filter(s => s && s !== 'null' && typeof s === 'string')
      .map(s => s.trim())
      .filter(s => s.length > 0);

    // Count occurrences and return top sectors
    const sectorCounts: { [key: string]: number } = {};
    cleanSectors.forEach(sector => {
      sectorCounts[sector] = (sectorCounts[sector] || 0) + 1;
    });

    return Object.entries(sectorCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([sector]) => sector);
  }

  private parseRevenueRange(range: string): [number, number] {
    const ranges: { [key: string]: [number, number] } = {
      '0-100000': [0, 100000],
      '100000-1000000': [100000, 1000000],
      '1000000-10000000': [1000000, 10000000],
      '10000000+': [10000000, 100000000]
    };
    return ranges[range] || [0, 1000000];
  }

  private getDefaultSharkPatterns(sharkName: string) {
    const defaultPatterns: { [key: string]: any } = {
      'Anupam Mittal': {
        patterns: { quick_decisions: 70, negotiation_heavy: 80, partnership_focused: 85 },
        red_flags: ['Unrealistic valuations', 'Poor market research', 'Weak business model'],
        success_factors: ['Strong digital strategy', 'Clear monetization', 'Scalable platform'],
        decision_timeline: 'medium',
        risk_tolerance: 'medium'
      },
      'Aman Gupta': {
        patterns: { quick_decisions: 85, negotiation_heavy: 60, partnership_focused: 70 },
        red_flags: ['No brand strategy', 'Poor product quality', 'Weak distribution'],
        success_factors: ['Strong branding', 'Product innovation', 'Market penetration'],
        decision_timeline: 'fast',
        risk_tolerance: 'high'
      }
    };

    return defaultPatterns[sharkName] || {
      patterns: { quick_decisions: 60, negotiation_heavy: 70, partnership_focused: 60 },
      red_flags: ['Unclear business model', 'High burn rate', 'No market validation'],
      success_factors: ['Clear value proposition', 'Market traction', 'Strong team'],
      decision_timeline: 'medium',
      risk_tolerance: 'medium'
    };
  }

  private async generateRecommendedAsk(
    sharkProfile: SharkProfile,
    businessSector: string,
    revenueRange: string
  ): Promise<{ amount: number; equity: number }> {
    try {
      const [minRevenue, maxRevenue] = this.parseRevenueRange(revenueRange);
      const avgRevenue = (minRevenue + maxRevenue) / 2;

      // Prepare template variables for investment strategy analysis
      const templateVariables = {
        company_name: `${businessSector} Company Analysis`,
        business_sector: businessSector,
        current_revenue: `₹${avgRevenue / 100000} Lakhs annually`,
        growth_rate: 'Market standard',
        business_stage: revenueRange.includes('0-') ? 'Early stage' : revenueRange.includes('10000000') ? 'Growth stage' : 'Scaling stage',
        funding_history: 'Seed/Angel funding',
        market_analysis: JSON.stringify({
          shark_profile: sharkProfile,
          investment_range: sharkProfile.investment_range,
          equity_range: sharkProfile.equity_range,
          historical_patterns: sharkProfile.deal_patterns
        })
      };

      // Execute investment strategy template for optimized deal terms
      const { result } = await this.promptManagementService.executeTemplate(
        'investment-strategy',
        templateVariables
      );

      const strategyResult = JSON.parse(result);
      const investmentStrategy = strategyResult.investment_strategy;

      // Extract recommended deal terms from template
      if (investmentStrategy?.optimal_deal_structure?.recommended_ask) {
        const ask = investmentStrategy.optimal_deal_structure.recommended_ask;
        const amount = this.parseAmount(ask.investment_amount);
        const equity = this.parseEquity(ask.equity_percentage);

        if (!amount || !equity) {
          throw new Error('Investment strategy template returned incomplete deal terms');
        }

        return {
          amount: Math.round(amount),
          equity: Math.round(equity)
        };
      }

      throw new Error('Investment strategy template returned no deal structure');

    } catch (error) {
      console.error(`[SharkCompatibilityService] Investment strategy template failed:`, error);
      throw new Error(`Investment strategy analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Helper methods for deal term calculations
   */
  private parseAmount(amountString: string): number | null {
    if (!amountString) return null;
    const matches = amountString.match(/₹?(\d+(?:\.\d+)?)\s*(lakh|crore|L|Cr|lakhs|crores)?/i);
    if (matches) {
      const value = parseFloat(matches[1]);
      const unit = matches[2]?.toLowerCase();
      if (unit?.includes('crore') || unit?.includes('cr')) {
        return value * 10000000; // Convert crores to rupees
      } else if (unit?.includes('lakh') || unit?.includes('l')) {
        return value * 100000; // Convert lakhs to rupees
      }
      return value;
    }
    return null;
  }

  private parseEquity(equityString: string): number | null {
    if (!equityString) return null;
    const matches = equityString.match(/(\d+(?:\.\d+)?)%?/);
    return matches ? parseFloat(matches[1]) : null;
  }

  private calculateFallbackAmount(sharkProfile: SharkProfile, avgRevenue: number): number {
    return Math.max(
      sharkProfile.investment_range.min,
      Math.min(
        sharkProfile.investment_range.max,
        avgRevenue * 0.5 // 0.5x revenue multiple
      )
    );
  }

  private calculateFallbackEquity(sharkProfile: SharkProfile): number {
    return Math.max(
      sharkProfile.equity_range.min,
      Math.min(
        sharkProfile.equity_range.max,
        sharkProfile.average_equity || 15
      )
    );
  }

  private calculateFallbackDealTerms(sharkProfile: SharkProfile, avgRevenue: number): { amount: number; equity: number } {
    return {
      amount: Math.round(this.calculateFallbackAmount(sharkProfile, avgRevenue)),
      equity: Math.round(this.calculateFallbackEquity(sharkProfile))
    };
  }

  private async generateTalkingPointsAndStrategy(
    sharkProfile: SharkProfile,
    businessSector: string
  ): Promise<{ talkingPoints: string[]; negotiationStrategy: string }> {
    try {
      // Prepare template variables for pitch preparation
      const templateVariables = {
        company_name: `${businessSector} Company`,
        founder_names: 'Entrepreneur',
        business_sector: businessSector,
        investment_ask: `₹${sharkProfile.investment_range.min / 100000} lakhs`,
        equity_offer: sharkProfile.equity_range.min.toString(),
        pitch_duration: '4',
        business_summary: `A ${businessSector} company seeking investment from ${sharkProfile.shark_name}`,
        target_sharks: sharkProfile.shark_name
      };

      // Execute pitch preparation template
      const { result } = await this.promptManagementService.executeTemplate(
        'pitch-preparation',
        templateVariables
      );

      const pitchResult = JSON.parse(result);
      const pitchPreparation = pitchResult.pitch_preparation;

      // Extract talking points and negotiation strategy
      const talkingPoints = this.extractTalkingPoints(pitchPreparation);
      const negotiationStrategy = this.extractNegotiationStrategy(pitchPreparation);

      if (!talkingPoints || !negotiationStrategy) {
        throw new Error('Pitch preparation template returned incomplete data');
      }

      return { talkingPoints, negotiationStrategy };

    } catch (error) {
      console.error(`[SharkCompatibilityService] Pitch preparation template failed:`, error);
      throw new Error(`Pitch preparation analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private extractTalkingPoints(pitchPreparation: any): string[] | null {
    try {
      if (pitchPreparation?.shark_specific_strategy?.talking_points) {
        return pitchPreparation.shark_specific_strategy.talking_points.slice(0, 3);
      }
      if (pitchPreparation?.key_talking_points) {
        return pitchPreparation.key_talking_points.slice(0, 3);
      }
      return null;
    } catch {
      return null;
    }
  }

  private extractNegotiationStrategy(pitchPreparation: any): string | null {
    try {
      if (pitchPreparation?.negotiation_strategy?.primary_approach) {
        return pitchPreparation.negotiation_strategy.primary_approach;
      }
      if (pitchPreparation?.shark_specific_strategy?.negotiation_approach) {
        return pitchPreparation.shark_specific_strategy.negotiation_approach;
      }
      return null;
    } catch {
      return null;
    }
  }

  private getFallbackTalkingPoints(sharkProfile: SharkProfile, businessSector: string): string[] {
    const points = [
      `Alignment with ${sharkProfile.shark_name}'s portfolio in ${businessSector}`,
      `Leverage ${sharkProfile.shark_name}'s network and expertise`,
      `Growth potential matches ${sharkProfile.shark_name}'s investment thesis`
    ];

    // Add sector-specific points
    if (sharkProfile.preferred_sectors.includes(businessSector)) {
      points.push(`Direct experience in ${businessSector} sector`);
    }

    return points.slice(0, 3);
  }

  private getFallbackNegotiationStrategy(sharkProfile: SharkProfile): string {
    if (sharkProfile.decision_timeline === 'fast') {
      return 'Present clear value proposition quickly, be prepared for immediate negotiation';
    } else if (sharkProfile.deal_patterns.negotiation_heavy > 70) {
      return 'Expect detailed negotiation, prepare multiple scenarios and compromise options';
    } else {
      return 'Focus on partnership value, emphasize long-term growth potential';
    }
  }

  /**
   * Database operations
   */
  private async storeSharkProfiles(profiles: SharkProfile[]): Promise<void> {
    for (const profile of profiles) {
      await sql`
        INSERT INTO shark_profiles (
          shark_name,
          profile_data,
          generated_at,
          expires_at
        ) VALUES (
          ${profile.shark_name},
          ${profile}::jsonb,
          NOW(),
          NOW() + INTERVAL '30 days'
        )
        ON CONFLICT (shark_name)
        DO UPDATE SET
          profile_data = ${profile}::jsonb,
          generated_at = NOW(),
          expires_at = NOW() + INTERVAL '30 days'
      `;
    }
  }

  private async getSharkProfiles(): Promise<SharkProfile[]> {
    try {
      const results = await sql`
        SELECT profile_data
        FROM shark_profiles
        WHERE expires_at > NOW()
      `;

      if (results.length === 0) {
        console.log('[SharkCompatibilityService] No profiles in database, returning defaults');
        return this.getDefaultSharkProfilesList();
      }

      const profiles = results.map(row => {
        // Handle both string and object formats
        const data = typeof row.profile_data === 'string'
          ? JSON.parse(row.profile_data)
          : row.profile_data;
        return data as SharkProfile;
      });
      console.log(`[SharkCompatibilityService] Loaded ${profiles.length} shark profiles from database`);
      return profiles;
    } catch (error) {
      console.warn('[SharkCompatibilityService] Failed to get shark profiles, returning default profiles:', error.message);
      // Return default shark profiles if table doesn't exist or is empty
      return this.getDefaultSharkProfilesList();
    }
  }

  private getDefaultSharkProfilesList(): SharkProfile[] {
    return [
      {
        shark_name: 'Anupam Mittal',
        total_deals: 45,
        success_rate: 0.75,
        average_equity: 15,
        average_investment: 5000000,
        preferred_sectors: ['Technology', 'Consumer Internet', 'Marketplaces'],
        investment_range: { min: 1000000, max: 10000000 },
        equity_range: { min: 10, max: 30 },
        deal_patterns: {
          quick_decisions: 70,
          negotiation_heavy: 80,
          partnership_focused: 85
        },
        red_flags: ['Unrealistic valuations', 'Poor market research', 'Weak business model'],
        success_factors: ['Strong digital strategy', 'Clear monetization', 'Scalable platform'],
        decision_timeline: 'medium' as const,
        risk_tolerance: 'medium' as const
      },
      {
        shark_name: 'Aman Gupta',
        total_deals: 50,
        success_rate: 0.80,
        average_equity: 12,
        average_investment: 4000000,
        preferred_sectors: ['Consumer Products', 'D2C Brands', 'Electronics'],
        investment_range: { min: 2000000, max: 8000000 },
        equity_range: { min: 8, max: 25 },
        deal_patterns: {
          quick_decisions: 85,
          negotiation_heavy: 60,
          partnership_focused: 70
        },
        red_flags: ['No brand strategy', 'Poor product quality', 'Weak distribution'],
        success_factors: ['Strong branding', 'Product innovation', 'Market penetration'],
        decision_timeline: 'fast' as const,
        risk_tolerance: 'high' as const
      },
      {
        shark_name: 'Vineeta Singh',
        total_deals: 35,
        success_rate: 0.70,
        average_equity: 10,
        average_investment: 3000000,
        preferred_sectors: ['Beauty', 'Fashion', 'Consumer Goods'],
        investment_range: { min: 1500000, max: 6000000 },
        equity_range: { min: 8, max: 20 },
        deal_patterns: {
          quick_decisions: 75,
          negotiation_heavy: 65,
          partnership_focused: 80
        },
        red_flags: ['Poor quality control', 'No differentiation', 'Weak brand'],
        success_factors: ['Product quality', 'Brand potential', 'Target market clarity'],
        decision_timeline: 'medium' as const,
        risk_tolerance: 'medium' as const
      },
      {
        shark_name: 'Namita Thapar',
        total_deals: 40,
        success_rate: 0.72,
        average_equity: 14,
        average_investment: 4500000,
        preferred_sectors: ['Healthcare', 'Pharma', 'Education'],
        investment_range: { min: 2000000, max: 10000000 },
        equity_range: { min: 10, max: 25 },
        deal_patterns: {
          quick_decisions: 60,
          negotiation_heavy: 75,
          partnership_focused: 90
        },
        red_flags: ['No compliance', 'Unproven efficacy', 'Regulatory issues'],
        success_factors: ['Research backing', 'Compliance ready', 'Social impact'],
        decision_timeline: 'deliberate' as const,
        risk_tolerance: 'low' as const
      },
      {
        shark_name: 'Peyush Bansal',
        total_deals: 38,
        success_rate: 0.78,
        average_equity: 11,
        average_investment: 3500000,
        preferred_sectors: ['Technology', 'Healthcare', 'Consumer Services'],
        investment_range: { min: 1500000, max: 7000000 },
        equity_range: { min: 8, max: 20 },
        deal_patterns: {
          quick_decisions: 65,
          negotiation_heavy: 70,
          partnership_focused: 85
        },
        red_flags: ['Poor unit economics', 'No tech advantage', 'High CAC'],
        success_factors: ['Technology innovation', 'Customer focus', 'Scalability'],
        decision_timeline: 'medium' as const,
        risk_tolerance: 'medium' as const
      }
    ];
  }

  private async storeCompatibilityCache(cache: CompatibilityCache): Promise<void> {
    try {
      const cacheKey = `${cache.business_sector}|${cache.revenue_range}`;

      await sql`
        INSERT INTO compatibility_cache (
          cache_key,
          business_sector,
          revenue_range,
          shark_scores,
          generated_at,
          expires_at
        ) VALUES (
          ${cacheKey},
          ${cache.business_sector},
          ${cache.revenue_range},
          ${cache.shark_scores}::jsonb,
          ${cache.generated_at},
          ${cache.expires_at}
        )
        ON CONFLICT (cache_key)
        DO UPDATE SET
          shark_scores = ${cache.shark_scores}::jsonb,
          generated_at = ${cache.generated_at},
          expires_at = ${cache.expires_at}
      `;
    } catch (error) {
      console.warn('[SharkCompatibilityService] Failed to store cache, continuing without caching:', error.message);
      // Continue without caching - the analysis will still work
    }
  }

  private async getCachedCompatibility(
    businessSector: string,
    revenueRange: string
  ): Promise<CompatibilityCache | null> {
    try {
      const cacheKey = `${businessSector}|${revenueRange}`;

      const results = await sql`
        SELECT * FROM compatibility_cache
        WHERE cache_key = ${cacheKey}
        AND expires_at > NOW()
      `;

      if (results.length === 0) return null;

      const row = results[0];
      return {
        business_sector: row.business_sector,
        revenue_range: row.revenue_range,
        shark_scores: row.shark_scores,
        generated_at: row.generated_at,
        expires_at: row.expires_at
      };
    } catch (error) {
      console.warn('[SharkCompatibilityService] Failed to get cached compatibility, will generate new:', error.message);
      return null;
    }
  }

  private isCacheExpired(cache: CompatibilityCache): boolean {
    return new Date() > cache.expires_at;
  }

  /**
   * Get compatibility statistics
   */
  async getCompatibilityStats(): Promise<{
    shark_profiles: number;
    cached_combinations: number;
    cache_hit_rate: number;
    recent_generations: number;
  }> {
    const [profiles, caches, recentGenerations] = await Promise.all([
      sql`SELECT COUNT(*) as count FROM shark_profiles WHERE expires_at > NOW()`,
      sql`SELECT COUNT(*) as count FROM compatibility_cache WHERE expires_at > NOW()`,
      sql`SELECT COUNT(*) as count FROM compatibility_cache WHERE generated_at > NOW() - INTERVAL '24 hours'`
    ]);

    return {
      shark_profiles: parseInt(profiles[0].count),
      cached_combinations: parseInt(caches[0].count),
      cache_hit_rate: 85, // Placeholder - would need request tracking for real metric
      recent_generations: parseInt(recentGenerations[0].count)
    };
  }
}

/**
 * CLI runner for shark compatibility operations
 */
if (require.main === module) {
  const command = process.argv[2];
  const sharkService = new SharkCompatibilityService();

  switch (command) {
    case 'generate-profiles':
      sharkService.generateSharkProfiles()
        .then(profiles => {
          console.log(`✅ Generated ${profiles.length} shark profiles`);
          profiles.forEach(p => {
            console.log(`${p.shark_name}: ${p.total_deals} deals, ${p.success_rate}% success rate`);
          });
          process.exit(0);
        })
        .catch(error => {
          console.error('Profile generation failed:', error);
          process.exit(1);
        });
      break;

    case 'precompute':
      sharkService.preComputeCompatibilityCaches()
        .then(stats => {
          console.log('Pre-computation Stats:', JSON.stringify(stats, null, 2));
          process.exit(0);
        })
        .catch(error => {
          console.error('Pre-computation failed:', error);
          process.exit(1);
        });
      break;

    case 'stats':
      sharkService.getCompatibilityStats()
        .then(stats => {
          console.log('Compatibility Service Statistics:');
          console.log('=' .repeat(40));
          console.log(`Shark Profiles: ${stats.shark_profiles}`);
          console.log(`Cached Combinations: ${stats.cached_combinations}`);
          console.log(`Cache Hit Rate: ${stats.cache_hit_rate}%`);
          console.log(`Recent Generations (24h): ${stats.recent_generations}`);
          process.exit(0);
        })
        .catch(error => {
          console.error('Stats failed:', error);
          process.exit(1);
        });
      break;

    case 'test':
      const sector = process.argv[3] || 'Technology';
      const revenue = process.argv[4] || '1000000-10000000';

      sharkService.getCompatibilityForUser(sector, revenue, true)
        .then(compatibility => {
          console.log(`Compatibility for ${sector} | ${revenue}:`);
          console.log('=' .repeat(50));
          Object.entries(compatibility.shark_scores).forEach(([shark, scores]) => {
            console.log(`${shark}:`);
            console.log(`  Compatibility: ${scores.compatibility_score}%`);
            console.log(`  Investment Likelihood: ${scores.investment_likelihood}%`);
            console.log(`  Recommended Ask: ₹${scores.recommended_ask.amount.toLocaleString()} for ${scores.recommended_ask.equity}%`);
            console.log(`  Strategy: ${scores.negotiation_strategy}`);
            console.log('');
          });
          process.exit(0);
        })
        .catch(error => {
          console.error('Test failed:', error);
          process.exit(1);
        });
      break;

    default:
      console.log('Shark Compatibility Service CLI');
      console.log('Usage: bun src/services/sharkCompatibilityService.ts <command>');
      console.log('');
      console.log('Commands:');
      console.log('  generate-profiles        Generate shark profiles from historical data');
      console.log('  precompute              Pre-compute compatibility caches for common scenarios');
      console.log('  stats                   Show service statistics');
      console.log('  test [sector] [revenue] Test compatibility for specific scenario');
      console.log('');
      console.log('Examples:');
      console.log('  bun src/services/sharkCompatibilityService.ts generate-profiles');
      console.log('  bun src/services/sharkCompatibilityService.ts precompute');
      console.log('  bun src/services/sharkCompatibilityService.ts test "Food & Beverage" "100000-1000000"');
      process.exit(1);
  }
}

// Export singleton instance
export const sharkCompatibilityService = new SharkCompatibilityService();

// Export factory function for compatibility with existing code
export function getSharkCompatibilityService() {
  if (!process.env.GEMINI_API_KEY) {
    throw new Error('GEMINI_API_KEY is required for shark compatibility analysis');
  }

  return new SharkCompatibilityService();
}
