/**
 * Narrative Coherence Service
 * Ensures story consistency, thematic alignment, and narrative flow across all report sections
 * Validates that executive letters, company stories, shark analysis, and action plans work together
 */

import { GoogleGenAI, Type } from '@google/genai';
import type {
  UINarrativeReport
} from '../types/pipeline-contracts.js';

interface CoherenceAnalysis {
  overallCoherence: number; // 0-100 score
  thematicConsistency: {
    score: number;
    themes: string[];
    inconsistencies: string[];
  };
  narrativeFlow: {
    score: number;
    progressionQuality: string;
    transitionIssues: string[];
  };
  tonalConsistency: {
    score: number;
    voiceAnalysis: string;
    tonalShifts: string[];
  };
  evidenceAlignment: {
    score: number;
    claimConsistency: string;
    contradictions: string[];
  };
  recommendations: string[];
}

interface SectionValidation {
  sectionName: string;
  contentQuality: number;
  narrativeStrength: number;
  personalizationLevel: number;
  actionability: number;
  professionalTone: number;
  issues: string[];
  strengths: string[];
}

export class NarrativeCoherenceService {
  private genai: GoogleGenAI;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY or GOOGLE_API_KEY environment variable is required for NarrativeCoherenceService');
    }
    this.genai = new GoogleGenAI({ apiKey });
  }

  /**
   * Analyze complete narrative report for coherence and consistency
   */
  async analyzeNarrativeCoherence(
    narrativeReport: UINarrativeReport,
    userCompanyData: any
  ): Promise<CoherenceAnalysis> {
    const prompt = `Analyze this complete narrative report for ${userCompanyData.companyName} for story coherence, consistency, and professional quality.

COMPLETE NARRATIVE REPORT:

EXECUTIVE LETTER:
- Opening: ${narrativeReport.executiveLetter.personalizedOpening}
- Key Insight: ${narrativeReport.executiveLetter.keyInsight}
- Company Teaser: ${narrativeReport.executiveLetter.similarCompanyTeaser}
- Call to Action: ${narrativeReport.executiveLetter.callToAction}

SIMILAR COMPANY STORIES:
${narrativeReport.similarCompanyStories.map(story => `
${story.company.name}:
- Lessons: ${story.lessonsForYou.join('; ')}
- Emulate: ${story.whatToEmulate.join('; ')}
- Avoid: ${story.whatToAvoid.join('; ')}
`).join('\n')}

SHARK COMPATIBILITY:
${narrativeReport.sharkCompatibility.map(shark => `
${shark.sharkName} (${shark.compatibilityScore}%):
- Why good fit: ${shark.whyGoodFit.join('; ')}
- How to win: ${shark.howToWinThem.join('; ')}
`).join('\n')}

BENCHMARK NARRATIVE:
- Position: ${narrativeReport.benchmarkNarrative.whereYouStand}
- Good news: ${narrativeReport.benchmarkNarrative.goodNews.join('; ')}
- Watch outs: ${narrativeReport.benchmarkNarrative.watchOuts.join('; ')}
- Opportunities: ${narrativeReport.benchmarkNarrative.opportunities.join('; ')}

ACTION PLAN SAMPLE:
Week 1: ${narrativeReport.actionPlan.week1?.slice(0, 2).map(task => `${task.day}: ${task.task} → ${task.outcome}`).join('; ')}

Analyze for:
1. THEMATIC CONSISTENCY: Do all sections reinforce the same core themes and messages?
2. NARRATIVE FLOW: Does the story progress logically from letter to action plan?
3. TONAL CONSISTENCY: Is the voice and tone consistent across all sections?
4. EVIDENCE ALIGNMENT: Are claims and insights consistent across sections?
5. PERSONALIZATION DEPTH: How well is everything tailored to this specific founder/company?

Provide scores (0-100) and specific recommendations for improvement.`;

    const result = await this.genai.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      config: {
        temperature: 0.3,
        maxOutputTokens: 2000,
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            overallCoherence: { type: Type.NUMBER },
            thematicConsistency: {
              type: Type.OBJECT,
              properties: {
                score: { type: Type.NUMBER },
                themes: {
                  type: Type.ARRAY,
                  items: { type: Type.STRING }
                },
                inconsistencies: {
                  type: Type.ARRAY,
                  items: { type: Type.STRING }
                }
              }
            },
            narrativeFlow: {
              type: Type.OBJECT,
              properties: {
                score: { type: Type.NUMBER },
                progressionQuality: { type: Type.STRING },
                transitionIssues: {
                  type: Type.ARRAY,
                  items: { type: Type.STRING }
                }
              }
            },
            tonalConsistency: {
              type: Type.OBJECT,
              properties: {
                score: { type: Type.NUMBER },
                voiceAnalysis: { type: Type.STRING },
                tonalShifts: {
                  type: Type.ARRAY,
                  items: { type: Type.STRING }
                }
              }
            },
            evidenceAlignment: {
              type: Type.OBJECT,
              properties: {
                score: { type: Type.NUMBER },
                claimConsistency: { type: Type.STRING },
                contradictions: {
                  type: Type.ARRAY,
                  items: { type: Type.STRING }
                }
              }
            },
            recommendations: {
              type: Type.ARRAY,
              items: { type: Type.STRING }
            }
          }
        }
      }
    });

    return JSON.parse(result.text || '{}');
  }

  /**
   * Validate individual sections for narrative quality
   */
  async validateSectionQuality(
    sectionName: string,
    sectionContent: any,
    userContext: any
  ): Promise<SectionValidation> {
    const prompt = `Evaluate the narrative quality of this ${sectionName} section for ${userContext.companyName}.

SECTION CONTENT:
${JSON.stringify(sectionContent, null, 2)}

USER CONTEXT:
- Company: ${userContext.companyName}
- Founder: ${userContext.founderName}
- Sector: ${userContext.businessSector}
- Model: ${userContext.businessModel}

Rate (0-100) and provide specific feedback on:
1. CONTENT QUALITY: Is the content substantive, specific, and valuable?
2. NARRATIVE STRENGTH: Does it tell a compelling story vs. just presenting data?
3. PERSONALIZATION LEVEL: How specifically tailored is it to this founder/company?
4. ACTIONABILITY: Can the founder immediately act on this information?
5. PROFESSIONAL TONE: Is it authoritative without being generic or salesy?

Identify specific issues and strengths for improvement.`;

    const result = await this.genai.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      config: {
        temperature: 0.2,
        maxOutputTokens: 1000,
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            sectionName: { type: Type.STRING },
            contentQuality: { type: Type.NUMBER },
            narrativeStrength: { type: Type.NUMBER },
            personalizationLevel: { type: Type.NUMBER },
            actionability: { type: Type.NUMBER },
            professionalTone: { type: Type.NUMBER },
            issues: {
              type: Type.ARRAY,
              items: { type: Type.STRING }
            },
            strengths: {
              type: Type.ARRAY,
              items: { type: Type.STRING }
            }
          }
        }
      }
    });

    return JSON.parse(result.text || '{}');
  }

  /**
   * Generate coherence improvement recommendations
   */
  async generateImprovementPlan(
    coherenceAnalysis: CoherenceAnalysis,
    narrativeReport: UINarrativeReport,
    userCompanyData: any
  ): Promise<{
    priorityIssues: Array<{
      issue: string;
      impact: 'high' | 'medium' | 'low';
      solution: string;
      implementationSteps: string[];
    }>;
    quickWins: string[];
    strategicImprovements: string[];
  }> {
    const prompt = `Based on this coherence analysis for ${userCompanyData.companyName}, create a prioritized improvement plan.

COHERENCE ANALYSIS:
${JSON.stringify(coherenceAnalysis, null, 2)}

CURRENT ISSUES IDENTIFIED:
- Thematic inconsistencies: ${coherenceAnalysis.thematicConsistency.inconsistencies.join('; ')}
- Narrative flow issues: ${coherenceAnalysis.narrativeFlow.transitionIssues.join('; ')}
- Tonal shifts: ${coherenceAnalysis.tonalConsistency.tonalShifts.join('; ')}
- Evidence contradictions: ${coherenceAnalysis.evidenceAlignment.contradictions.join('; ')}

Create an improvement plan that prioritizes:
1. HIGH IMPACT issues that significantly hurt the narrative experience
2. QUICK WINS that can be fixed immediately
3. STRATEGIC IMPROVEMENTS that enhance long-term narrative quality

Provide specific, actionable solutions for each issue.`;

    const result = await this.genai.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      config: {
        temperature: 0.3,
        maxOutputTokens: 1500,
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            priorityIssues: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  issue: { type: Type.STRING },
                  impact: { type: Type.STRING },
                  solution: { type: Type.STRING },
                  implementationSteps: {
                    type: Type.ARRAY,
                    items: { type: Type.STRING }
                  }
                }
              }
            },
            quickWins: {
              type: Type.ARRAY,
              items: { type: Type.STRING }
            },
            strategicImprovements: {
              type: Type.ARRAY,
              items: { type: Type.STRING }
            }
          }
        }
      }
    });

    return JSON.parse(result.text || '{}');
  }

  /**
   * Validate cross-section story consistency
   */
  async validateCrossSectionConsistency(
    narrativeReport: UINarrativeReport
  ): Promise<{
    consistencyIssues: string[];
    themeAlignment: number;
    narrativeGaps: string[];
    strengthenedConnections: string[];
  }> {
    // Extract key themes from each section
    const executiveThemes = this.extractThemes(narrativeReport.executiveLetter.keyInsight);
    const storyThemes = narrativeReport.similarCompanyStories.flatMap(story =>
      this.extractThemes(story.lessonsForYou.join(' '))
    );
    const benchmarkThemes = this.extractThemes(narrativeReport.benchmarkNarrative.whereYouStand);

    // Find theme overlaps and gaps
    const allThemes = [...executiveThemes, ...storyThemes, ...benchmarkThemes];
    const themeFrequency = new Map<string, number>();

    allThemes.forEach(theme => {
      themeFrequency.set(theme, (themeFrequency.get(theme) || 0) + 1);
    });

    // Identify dominant themes (appear in 2+ sections)
    const dominantThemes = Array.from(themeFrequency.entries())
      .filter(([_, frequency]) => frequency >= 2)
      .map(([theme, _]) => theme);

    // Calculate theme alignment score
    const themeAlignment = dominantThemes.length >= 3 ?
      Math.min(100, (dominantThemes.length / 5) * 100) :
      (dominantThemes.length / 3) * 60;

    const consistencyIssues: string[] = [];
    const narrativeGaps: string[] = [];
    const strengthenedConnections: string[] = [];

    // Check for narrative gaps
    if (executiveThemes.filter(theme => storyThemes.includes(theme)).length < 2) {
      narrativeGaps.push('Executive letter themes not reinforced in company stories');
    }

    if (storyThemes.filter(theme => benchmarkThemes.includes(theme)).length < 1) {
      narrativeGaps.push('Company story insights not reflected in benchmark analysis');
    }

    // Identify strengthened connections
    dominantThemes.forEach(theme => {
      strengthenedConnections.push(`Strong thematic consistency around "${theme}" across multiple sections`);
    });

    return {
      consistencyIssues,
      themeAlignment,
      narrativeGaps,
      strengthenedConnections
    };
  }

  /**
   * Generate narrative bridges between sections
   */
  async generateSectionBridges(
    narrativeReport: UINarrativeReport,
    userCompanyData: any
  ): Promise<{
    letterToStories: string;
    storiesToSharks: string;
    sharksToAction: string;
    overallArc: string;
  }> {
    const prompt = `Create narrative bridges that connect the sections of this report for ${userCompanyData.companyName} into a cohesive story.

The report currently has these sections:
1. Executive Letter with key insight: "${narrativeReport.executiveLetter.keyInsight}"
2. Similar Company Stories featuring: ${narrativeReport.similarCompanyStories.map(s => s.company.name).join(', ')}
3. Shark Compatibility focusing on: ${narrativeReport.sharkCompatibility.map(s => s.sharkName).join(', ')}
4. Action Plan starting with: ${narrativeReport.actionPlan.week1?.[0]?.task || 'Preparation tasks'}

Create connecting narratives that:
1. letterToStories: Bridge from executive insights to why these specific company stories matter
2. storiesToSharks: Connect lessons from companies to shark psychology analysis
3. sharksToAction: Link shark strategies to specific daily actions
4. overallArc: The complete story arc from current position to Shark Tank success

Make these feel like natural story progressions, not business document transitions.`;

    const result = await this.genai.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      config: {
        temperature: 0.5,
        maxOutputTokens: 1200,
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            letterToStories: { type: Type.STRING },
            storiesToSharks: { type: Type.STRING },
            sharksToAction: { type: Type.STRING },
            overallArc: { type: Type.STRING }
          }
        }
      }
    });

    return JSON.parse(result.text || '{}');
  }

  /**
   * Validate action plan story alignment
   */
  async validateActionPlanAlignment(
    actionPlan: UINarrativeReport['actionPlan'],
    similarCompanyStories: UINarrativeReport['similarCompanyStories'],
    userCompanyData: any
  ): Promise<{
    alignmentScore: number;
    storyIntegration: {
      tasksWithStoryBacking: number;
      tasksNeedingStorySupport: string[];
      strongStoryConnections: string[];
    };
    improvementSuggestions: string[];
  }> {
    // Analyze which tasks are backed by similar company success patterns
    const allTasks = [
      ...actionPlan.week1 || [],
      ...actionPlan.week2 || [],
      ...actionPlan.week3 || []
    ];

    const storyBasedAdvice = similarCompanyStories.flatMap(story => [
      ...story.lessonsForYou,
      ...story.whatToEmulate
    ]);

    const prompt = `Analyze how well this action plan aligns with the story-based insights for ${userCompanyData.companyName}.

ACTION PLAN TASKS:
${allTasks.map(task => `${task.day}: ${task.task} → ${task.outcome}`).join('\n')}

STORY-BASED ADVICE FROM SIMILAR COMPANIES:
${storyBasedAdvice.join('\n- ')}

Evaluate:
1. How many action plan tasks are directly supported by story insights?
2. Which tasks need stronger story backing or motivation?
3. Are there key story lessons that aren't reflected in the action plan?
4. How can we strengthen the connection between stories and daily tasks?

Provide specific recommendations for improving story-action alignment.`;

    const result = await this.genai.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      config: {
        temperature: 0.3,
        maxOutputTokens: 1000,
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            alignmentScore: { type: Type.NUMBER },
            storyIntegration: {
              type: Type.OBJECT,
              properties: {
                tasksWithStoryBacking: { type: Type.NUMBER },
                tasksNeedingStorySupport: {
                  type: Type.ARRAY,
                  items: { type: Type.STRING }
                },
                strongStoryConnections: {
                  type: Type.ARRAY,
                  items: { type: Type.STRING }
                }
              }
            },
            improvementSuggestions: {
              type: Type.ARRAY,
              items: { type: Type.STRING }
            }
          }
        }
      }
    });

    return JSON.parse(result.text || '{}');
  }

  /**
   * Generate personalization enhancement recommendations
   */
  async enhancePersonalization(
    narrativeReport: UINarrativeReport,
    userCompanyData: any
  ): Promise<{
    currentPersonalizationScore: number;
    enhancementOpportunities: Array<{
      section: string;
      currentLevel: number;
      enhancementSuggestion: string;
      expectedImprovement: number;
    }>;
    personalizedElements: {
      founderSpecific: string[];
      companySpecific: string[];
      sectorSpecific: string[];
      contextSpecific: string[];
    };
  }> {
    const prompt = `Analyze personalization depth and suggest enhancements for ${userCompanyData.founderName} of ${userCompanyData.companyName}.

CURRENT NARRATIVE CONTENT:
Executive Letter: ${narrativeReport.executiveLetter.personalizedOpening}
Key Insight: ${narrativeReport.executiveLetter.keyInsight}
Similar Stories: ${narrativeReport.similarCompanyStories.map(s => s.lessonsForYou[0]).join('; ')}
Benchmark Position: ${narrativeReport.benchmarkNarrative.whereYouStand}

USER SPECIFIC CONTEXT:
- Founder: ${userCompanyData.founderName}
- Company: ${userCompanyData.companyName}
- Sector: ${userCompanyData.businessSector}
- Business Model: ${userCompanyData.businessModel}
- Ask: ₹${userCompanyData.askAmount} for ${userCompanyData.askEquity}%

Evaluate personalization and suggest specific enhancements:
1. How well does each section reference their specific situation?
2. What founder-specific elements could be added?
3. What company-specific insights are missing?
4. How can we make the advice more relevant to their exact circumstances?

Score current personalization (0-100) and provide specific enhancement suggestions.`;

    const result = await this.genai.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      config: {
        temperature: 0.3,
        maxOutputTokens: 1200,
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            currentPersonalizationScore: { type: Type.NUMBER },
            enhancementOpportunities: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  section: { type: Type.STRING },
                  currentLevel: { type: Type.NUMBER },
                  enhancementSuggestion: { type: Type.STRING },
                  expectedImprovement: { type: Type.NUMBER }
                }
              }
            },
            personalizedElements: {
              type: Type.OBJECT,
              properties: {
                founderSpecific: {
                  type: Type.ARRAY,
                  items: { type: Type.STRING }
                },
                companySpecific: {
                  type: Type.ARRAY,
                  items: { type: Type.STRING }
                },
                sectorSpecific: {
                  type: Type.ARRAY,
                  items: { type: Type.STRING }
                },
                contextSpecific: {
                  type: Type.ARRAY,
                  items: { type: Type.STRING }
                }
              }
            }
          }
        }
      }
    });

    return JSON.parse(result.text || '{}');
  }

  /**
   * Ensure evidence consistency across all sections
   */
  validateEvidenceConsistency(narrativeReport: UINarrativeReport): {
    consistentClaims: string[];
    contradictoryClaims: Array<{
      claim1: { section: string; content: string };
      claim2: { section: string; content: string };
      contradiction: string;
    }>;
    unsupportedClaims: Array<{
      section: string;
      claim: string;
      needsEvidence: string;
    }>;
  } {
    const consistentClaims: string[] = [];
    const contradictoryClaims: Array<any> = [];
    const unsupportedClaims: Array<any> = [];

    // Extract claims from each section
    const claims = new Map<string, Array<{ section: string; content: string }>>();

    // Executive letter claims
    this.extractClaims(narrativeReport.executiveLetter.keyInsight, 'Executive Letter')
      .forEach(claim => {
        if (!claims.has(claim)) claims.set(claim, []);
        claims.get(claim)!.push({ section: 'Executive Letter', content: claim });
      });

    // Similar company claims
    narrativeReport.similarCompanyStories.forEach((story, index) => {
      story.lessonsForYou.forEach(lesson => {
        this.extractClaims(lesson, `Company Story ${index + 1}`)
          .forEach(claim => {
            if (!claims.has(claim)) claims.set(claim, []);
            claims.get(claim)!.push({ section: `Company Story ${index + 1}`, content: claim });
          });
      });
    });

    // Benchmark claims
    this.extractClaims(narrativeReport.benchmarkNarrative.whereYouStand, 'Benchmark Analysis')
      .forEach(claim => {
        if (!claims.has(claim)) claims.set(claim, []);
        claims.get(claim)!.push({ section: 'Benchmark Analysis', content: claim });
      });

    // Find consistent vs. contradictory claims
    claims.forEach((instances, claim) => {
      if (instances.length > 1) {
        consistentClaims.push(claim);
      } else if (instances.length === 1) {
        // Check if this claim is supported by evidence
        const hasEvidence = this.hasEvidenceSupport(instances[0].content);
        if (!hasEvidence) {
          unsupportedClaims.push({
            section: instances[0].section,
            claim: instances[0].content,
            needsEvidence: 'Requires data or example support'
          });
        }
      }
    });

    return {
      consistentClaims,
      contradictoryClaims,
      unsupportedClaims
    };
  }

  /**
   * Optimize narrative for target reading time
   */
  async optimizeReadingTime(
    narrativeReport: UINarrativeReport,
    targetMinutes: number = 15
  ): Promise<{
    currentEstimatedTime: number;
    optimizationSuggestions: Array<{
      section: string;
      currentWordCount: number;
      suggestedWordCount: number;
      optimizationStrategy: string;
    }>;
    priorityOptimizations: string[];
  }> {
    // Calculate current reading time (average 200 words per minute)
    const wordCounts = {
      executiveLetter: this.countWords(Object.values(narrativeReport.executiveLetter).join(' ')),
      similarStories: narrativeReport.similarCompanyStories.reduce((total, story) =>
        total + this.countWords(story.lessonsForYou.join(' ')), 0),
      sharkCompatibility: narrativeReport.sharkCompatibility.reduce((total, shark) =>
        total + this.countWords(shark.whyGoodFit.join(' ') + shark.howToWinThem.join(' ')), 0),
      benchmarkNarrative: this.countWords(Object.values(narrativeReport.benchmarkNarrative).join(' ')),
      questionScript: this.countWords(narrativeReport.questionScript.openingStrategy),
      actionPlan: this.countWords(JSON.stringify(narrativeReport.actionPlan))
    };

    const totalWords = Object.values(wordCounts).reduce((sum, count) => sum + count, 0);
    const currentEstimatedTime = totalWords / 200; // 200 words per minute

    const optimizationSuggestions = Object.entries(wordCounts).map(([section, wordCount]) => {
      const targetWordCount = Math.floor((targetMinutes * 200) / 6); // Distribute evenly across 6 sections
      const optimizationStrategy = wordCount > targetWordCount ?
        'Condense while maintaining key insights' :
        'Good length, maintain current depth';

      return {
        section,
        currentWordCount: wordCount,
        suggestedWordCount: targetWordCount,
        optimizationStrategy
      };
    });

    const priorityOptimizations = optimizationSuggestions
      .filter(opt => opt.currentWordCount > opt.suggestedWordCount * 1.3)
      .map(opt => `${opt.section}: Reduce from ${opt.currentWordCount} to ~${opt.suggestedWordCount} words`);

    return {
      currentEstimatedTime: Math.round(currentEstimatedTime),
      optimizationSuggestions,
      priorityOptimizations
    };
  }

  /**
   * Validate mobile reading experience
   */
  validateMobileExperience(narrativeReport: UINarrativeReport): {
    mobileReadabilityScore: number;
    sectionOptimization: Array<{
      section: string;
      mobileScore: number;
      issues: string[];
      recommendations: string[];
    }>;
    overallRecommendations: string[];
  } {
    const sections = [
      { name: 'Executive Letter', content: Object.values(narrativeReport.executiveLetter).join(' ') },
      { name: 'Similar Stories', content: narrativeReport.similarCompanyStories.map(s => s.lessonsForYou.join(' ')).join(' ') },
      { name: 'Shark Compatibility', content: narrativeReport.sharkCompatibility.map(s => s.whyGoodFit.join(' ')).join(' ') },
      { name: 'Benchmark Analysis', content: Object.values(narrativeReport.benchmarkNarrative).join(' ') }
    ];

    const sectionOptimization = sections.map(section => {
      const issues: string[] = [];
      const recommendations: string[] = [];
      let mobileScore = 100;

      // Check paragraph length (mobile-optimized should be <150 words per paragraph)
      const avgSentenceLength = section.content.split('.').reduce((sum, sentence) =>
        sum + sentence.trim().split(' ').length, 0) / section.content.split('.').length;

      if (avgSentenceLength > 25) {
        issues.push('Sentences too long for mobile reading');
        recommendations.push('Break long sentences into shorter, punchier statements');
        mobileScore -= 20;
      }

      // Check list formatting (mobile prefers bullets over long paragraphs)
      const hasBulletPoints = section.content.includes('•') || section.content.includes('-');
      if (!hasBulletPoints && section.content.length > 500) {
        issues.push('Long text blocks without visual breaks');
        recommendations.push('Add bullet points or numbered lists for scanability');
        mobileScore -= 15;
      }

      // Check for mobile-unfriendly elements
      if (section.content.includes('see above') || section.content.includes('as mentioned')) {
        issues.push('References that don\'t work on mobile scrolling');
        recommendations.push('Make each section self-contained');
        mobileScore -= 10;
      }

      return {
        section: section.name,
        mobileScore: Math.max(0, mobileScore),
        issues,
        recommendations
      };
    });

    const averageMobileScore = sectionOptimization.reduce((sum, s) => sum + s.mobileScore, 0) / sectionOptimization.length;

    const overallRecommendations = [
      'Optimize for thumb navigation with larger touch targets',
      'Use progressive disclosure to reduce cognitive load',
      'Implement section bookmarking for interrupted reading sessions',
      'Add estimated reading time for each section'
    ];

    return {
      mobileReadabilityScore: Math.round(averageMobileScore),
      sectionOptimization,
      overallRecommendations
    };
  }

  // Helper methods
  private extractThemes(text: string): string[] {
    const themes: string[] = [];
    const themePatterns = [
      /\b(technology|tech|digital|platform|AI|automation)\b/gi,
      /\b(scaling|growth|expansion|market|customers)\b/gi,
      /\b(healthcare|health|medical|service|care)\b/gi,
      /\b(founder|entrepreneur|leadership|vision)\b/gi,
      /\b(investment|funding|equity|valuation|deal)\b/gi,
      /\b(competition|competitive|advantage|differentiation)\b/gi
    ];

    themePatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const theme = match.toLowerCase();
          if (!themes.includes(theme)) {
            themes.push(theme);
          }
        });
      }
    });

    return themes;
  }

  private extractClaims(text: string, section: string): string[] {
    const claims: string[] = [];
    
    // Extract claims based on assertion patterns
    const claimPatterns = [
      /\b(is|are|will be|should|must|can|cannot)\s+[^.]+\./gi,
      /\b(shows?|proves?|demonstrates?|indicates?)\s+[^.]+\./gi,
      /\b(because|due to|as a result of)\s+[^.]+\./gi
    ];

    claimPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const claim = match.trim();
          if (claim.length > 10 && claim.length < 150) {
            claims.push(claim);
          }
        });
      }
    });

    return claims;
  }

  private hasEvidenceSupport(claim: string): boolean {
    // Check if claim contains evidence indicators
    const evidenceIndicators = [
      /\d+%/,  // Percentages
      /\$[\d,]+|\₹[\d,]+/,  // Currency amounts
      /\d+\s+(companies?|founders?|startups?)/,  // Quantified subjects
      /(study|research|data|survey|analysis)\s+shows?/i,
      /(according to|based on|from our analysis)/i
    ];

    return evidenceIndicators.some(pattern => pattern.test(claim));
  }

  private countWords(text: string): number {
    if (!text || typeof text !== 'string') return 0;
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }
}

// Export singleton instance
export const narrativeCoherenceService = new NarrativeCoherenceService();

