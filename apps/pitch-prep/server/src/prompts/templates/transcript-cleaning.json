{"id": "transcript-cleaning", "name": "Hindi/Hinglish Transcript Cleaning", "description": "Clean and structure raw Hindi/Hinglish Shark Tank India transcripts for extraction", "version": "1.0.0", "category": "transcript_processing", "model": "gemini-2.0-flash-exp", "template": "# Clean and Structure Shark Tank India Transcript\n\nYou are a transcript cleaning specialist for Shark Tank India episodes. Your task is to clean and structure raw Hindi/Hinglish transcripts for further processing.\n\n## Raw Transcript\n{{transcript_content}}\n\n## Cleaning Requirements\n\n### Text Cleaning\n- Remove all [संगीत] (music) markers\n- Remove excessive line breaks and formatting artifacts\n- Fix common transcription errors in Hindi/Hinglish text\n- Normalize speaker names (<PERSON>, Founders, Narrator)\n- Preserve the original language (Hindi/Hinglish/English mix)\n\n### Company Segmentation\n- Identify where each company's pitch begins and ends\n- Mark transitions between different companies\n- Identify company names clearly\n- Separate pre-pitch introductions from actual pitches\n\n### Speaker Identification\n- Identify and label Sharks: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>neer\n- Label founders/entrepreneurs clearly\n- Mark narrator/host segments\n- Identify Q&A sections vs presentation sections\n\n### Quality Assessment\n- Score transcript quality (0-100)\n- Note any missing or unclear sections\n- Identify technical/audio issues affecting quality\n- Flag sections that may need manual review\n\n## Output Format\n\nReturn a JSON object with this structure:\n\n```json\n{\n  \"cleaned_transcript\": {\n    \"full_text\": \"Complete cleaned transcript text\",\n    \"companies\": [\n      {\n        \"company_name\": \"Identified company name\",\n        \"segment_start\": \"Text marker where pitch begins\",\n        \"segment_end\": \"Text marker where pitch ends\",\n        \"transcript_segment\": \"Full cleaned text for this company\",\n        \"speakers_identified\": [\"List of speakers in this segment\"]\n      }\n    ],\n    \"speaker_sections\": [\n      {\n        \"speaker\": \"Speaker name\",\n        \"text\": \"What they said\",\n        \"timestamp_estimate\": \"Approximate position in episode\"\n      }\n    ]\n  },\n  \"quality_metrics\": {\n    \"overall_score\": 0,\n    \"clarity_score\": 0,\n    \"completeness_score\": 0,\n    \"language_mix\": {\n      \"hindi_percentage\": 0,\n      \"english_percentage\": 0,\n      \"hinglish_percentage\": 0\n    },\n    \"issues_found\": [\"List of quality issues\"],\n    \"requires_manual_review\": false\n  },\n  \"metadata\": {\n    \"total_companies\": 0,\n    \"total_speakers\": 0,\n    \"sharks_present\": [\"List of sharks identified\"],\n    \"episode_markers\": {\n      \"has_introduction\": true,\n      \"has_conclusion\": true,\n      \"has_ad_breaks\": false\n    }\n  }\n}\n```\n\n## Cleaning Guidelines\n\n### Language Handling\n- Preserve the original Hindi/Hinglish/English mix\n- Don't translate - keep original language\n- Fix obvious transliteration errors\n- Normalize company names to consistent spelling\n\n### Quality Scoring\n- 90-100: Crystal clear, all speakers identified, complete content\n- 70-89: Good quality, minor issues, mostly complete\n- 50-69: Acceptable, some unclear sections, usable for extraction\n- Below 50: Poor quality, significant issues, may need re-fetching\n\n### Common Patterns to Recognize\n- \"हमारे अगले एंटरप्रेन्योर हैं...\" - New company introduction\n- \"मैं आउट हूं\" / \"I'm out\" - Shark declining\n- \"डील\" / \"Deal\" - Investment agreement\n- \"इक्विटी\" / \"Equity\" - Equity discussion\n- \"वैल्यूएशन\" / \"Valuation\" - Valuation discussion\n\nClean and structure the transcript focusing on accuracy and maintaining the original business context.", "variables": {"transcript_content": {"type": "string", "required": true, "description": "Raw Hindi/Hinglish transcript content to clean", "validation": {"minLength": 100}}}, "output_format": "json", "output_schema": {"type": "object", "required": ["cleaned_transcript", "quality_metrics", "metadata"], "properties": {"cleaned_transcript": {"type": "object", "required": ["full_text", "companies"], "properties": {"full_text": {"type": "string"}, "companies": {"type": "array", "items": {"type": "object", "required": ["company_name", "transcript_segment"]}}}}, "quality_metrics": {"type": "object", "required": ["overall_score", "requires_manual_review"]}}}, "usage_notes": ["Use for cleaning raw Hindi/Hinglish Shark Tank transcripts", "Preserves original language mix without translation", "Identifies company segments for further processing", "Quality score helps filter poor transcripts"], "examples": [{"input": {"transcript_content": "[संगीत]\\nशार्क टैंक इंडिया में आपका स्वागत है\\n[संगीत]\\nहमारे पहले एंटरप्रेन्योर हैं डॉ पॉज़ के फाउंडर..."}, "expected_output": "Cleaned structured JSON with companies identified and quality scored"}], "created_at": "2025-01-13T12:00:00.000Z", "updated_at": "2025-01-13T12:00:00.000Z"}