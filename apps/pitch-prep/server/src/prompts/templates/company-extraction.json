{"id": "company-extraction", "name": "Company Data Extraction from Transcripts", "description": "Extract structured company information from Shark Tank India episode transcripts", "version": "1.0.0", "category": "transcript_processing", "model": "gemini-2.0-flash-exp", "template": "# Company Data Extraction from Shark Tank India Transcripts\n\nYou are a data extraction specialist focused on extracting structured company information from Shark Tank India episode transcripts. Extract comprehensive company profiles with accurate financial data, deal information, and business intelligence.\n\n## Episode Information\n- **Season**: {{season_number}}\n- **Episode**: {{episode_number}}\n- **Episode Title**: {{episode_title}}\n- **Air Date**: {{air_date}}\n\n## Transcript Content\n{{transcript_content}}\n\n## Company Extraction Framework\n\nExtract all company information from the transcript in this JSON format:\n\n```json\n{\n  \"companies_extracted\": [\n    {\n      \"company_name\": \"Exact company name as mentioned\",\n      \"founders\": [\n        {\n          \"name\": \"Founder full name\",\n          \"background\": \"Professional background and education\",\n          \"role\": \"CEO/CTO/Co-founder etc.\"\n        }\n      ],\n      \"business_details\": {\n        \"business_sector\": \"FinTech/EdTech/HealthTech/Beauty/Fashion/Food/Agriculture/Manufacturing/Services/E-commerce/Technology/Healthcare/Education/Entertainment\",\n        \"business_model\": \"B2B/B2C/D2C/Marketplace/SaaS/Subscription/Franchise/Service\",\n        \"product_service_description\": \"Clear description of what the company offers\",\n        \"target_market\": \"Primary customer segment and demographics\",\n        \"geographic_presence\": [\"City 1\", \"City 2\"],\n        \"years_in_business\": \"Number of years operating or since when\",\n        \"business_stage\": \"Startup/Growth/Established\"\n      },\n      \"financial_metrics\": {\n        \"current_revenue\": {\n          \"annual_revenue\": \"₹X Cr/Lakhs or null if not mentioned\",\n          \"monthly_revenue\": \"₹X Lakhs or null if not mentioned\",\n          \"revenue_growth_rate\": \"X% YoY or null if not mentioned\",\n          \"revenue_streams\": [\"Primary revenue source\", \"Secondary source\"]\n        },\n        \"profitability\": {\n          \"gross_margin\": \"X% or null if not mentioned\",\n          \"net_profit_margin\": \"X% or null if not mentioned\",\n          \"break_even_status\": \"Achieved/Not achieved/Timeline mentioned\",\n          \"monthly_profit\": \"₹X or null if not mentioned\"\n        },\n        \"customer_metrics\": {\n          \"total_customers\": \"Number or null if not mentioned\",\n          \"customer_acquisition_cost\": \"₹X or null if not mentioned\",\n          \"customer_lifetime_value\": \"₹X or null if not mentioned\",\n          \"repeat_customer_rate\": \"X% or null if not mentioned\"\n        },\n        \"operational_metrics\": {\n          \"team_size\": \"Number of employees or null\",\n          \"manufacturing_capacity\": \"Production capacity if applicable\",\n          \"inventory_turnover\": \"Inventory metrics if mentioned\",\n          \"unit_economics\": \"Cost per unit, profit per unit if mentioned\"\n        }\n      },\n      \"shark_tank_pitch\": {\n        \"investment_ask\": {\n          \"amount_requested\": \"₹X Lakhs/Cr\",\n          \"equity_offered\": \"X%\",\n          \"valuation_implied\": \"₹X Cr (calculated from ask and equity)\",\n          \"use_of_funds\": [\"Purpose 1\", \"Purpose 2\"]\n        },\n        \"pitch_highlights\": {\n          \"key_strengths_presented\": [\"Strength 1\", \"Strength 2\"],\n          \"unique_selling_points\": [\"USP 1\", \"USP 2\"],\n          \"market_opportunity_claimed\": \"Market size or opportunity mentioned\",\n          \"competitive_advantages\": [\"Advantage 1\", \"Advantage 2\"]\n        },\n        \"shark_questions_concerns\": [\n          {\n            \"shark_name\": \"Shark who asked\",\n            \"question_concern\": \"Specific question or concern raised\",\n            \"founder_response\": \"How founder responded\"\n          }\n        ],\n        \"deal_outcome\": {\n          \"final_status\": \"Deal/No Deal\",\n          \"investing_sharks\": [\"Shark name 1\", \"Shark name 2\"],\n          \"final_deal_terms\": {\n            \"investment_amount\": \"₹X Lakhs/Cr or null if no deal\",\n            \"equity_taken\": \"X% or null if no deal\",\n            \"final_valuation\": \"₹X Cr or null if no deal\",\n            \"additional_terms\": \"Royalty, debt, or other terms if mentioned\"\n          },\n          \"deal_negotiation_summary\": \"Brief summary of how deal was negotiated\",\n          \"reasons_for_no_deal\": [\"Reason 1\", \"Reason 2\"] \n        }\n      },\n      \"business_intelligence\": {\n        \"market_insights\": {\n          \"market_size_mentioned\": \"TAM/SAM if discussed\",\n          \"growth_projections\": \"Future growth plans or projections\",\n          \"expansion_plans\": [\"Geographic expansion\", \"Product expansion\"],\n          \"funding_history\": \"Previous funding rounds if mentioned\"\n        },\n        \"competitive_landscape\": {\n          \"competitors_mentioned\": [\"Competitor 1\", \"Competitor 2\"],\n          \"competitive_positioning\": \"How they position against competitors\",\n          \"market_differentiation\": \"What makes them different\"\n        },\n        \"operational_insights\": {\n          \"business_challenges\": [\"Challenge 1\", \"Challenge 2\"],\n          \"growth_constraints\": [\"Constraint 1\", \"Constraint 2\"],\n          \"success_factors\": [\"Success factor 1\", \"Success factor 2\"],\n          \"future_plans\": [\"Plan 1\", \"Plan 2\"]\n        }\n      },\n      \"extract_quality\": {\n        \"data_completeness\": \"High/Medium/Low based on information available\",\n        \"confidence_level\": \"High/Medium/Low confidence in extracted data\",\n        \"missing_key_data\": [\"What important data is missing\"],\n        \"transcript_quality_notes\": \"Issues with transcript quality if any\"\n      },\n      \"episode_context\": {\n        \"season_number\": {{season_number}},\n        \"episode_number\": {{episode_number}},\n        \"pitch_order\": \"1st/2nd/3rd company in episode\",\n        \"pitch_duration_estimate\": \"Estimated pitch duration in minutes\"\n      }\n    }\n  ],\n  \"extraction_summary\": {\n    \"total_companies_found\": 0,\n    \"companies_with_deals\": 0,\n    \"companies_without_deals\": 0,\n    \"average_data_completeness\": \"Overall data quality assessment\",\n    \"transcript_processing_notes\": \"Any issues or notes about transcript processing\"\n  }\n}\n```\n\n## Extraction Guidelines\n\n### Data Accuracy Priority\n- Extract only information explicitly mentioned in transcript\n- Use exact numbers when stated (don't round or estimate)\n- Mark missing data as null rather than guessing\n- Distinguish between actual data and projections/claims\n\n### Financial Data Extraction\n- Capture revenue figures in exact format mentioned (Cr/Lakhs)\n- Note whether revenue is monthly, annual, or other timeframe\n- Extract growth rates exactly as stated\n- Record profitability metrics precisely\n\n### Deal Information Accuracy\n- Record exact investment ask and equity offer\n- Calculate implied valuation accurately (Investment ÷ Equity × 100)\n- Capture final deal terms precisely\n- Note any additional terms (royalty, debt components)\n\n### Business Intelligence Focus\n- Extract market size claims and growth projections\n- Record competitive positioning statements\n- Capture expansion plans and future strategies\n- Note business challenges and constraints mentioned\n\n### Quality Assessment\n- Assess completeness of extracted data\n- Note confidence level in extraction accuracy\n- Identify missing critical information\n- Flag transcript quality issues that affect extraction\n\n### Multi-Company Episodes\n- Extract data for ALL companies in the episode\n- Maintain clear separation between companies\n- Ensure pitch order is correctly identified\n- Handle overlapping discussions appropriately\n\nFocus on accurate, comprehensive extraction that supports strategic analysis and company database development for Shark Tank India research.", "variables": {"season_number": {"type": "string", "required": true, "description": "Shark Tank India season number", "example": "4", "validation": {"pattern": "^[1-9]$"}}, "episode_number": {"type": "string", "required": true, "description": "Episode number within the season", "example": "15"}, "episode_title": {"type": "string", "required": false, "description": "Episode title if available"}, "air_date": {"type": "string", "required": false, "description": "Episode air date if available", "example": "2024-02-15"}, "transcript_content": {"type": "string", "required": true, "description": "Complete episode transcript content for company extraction", "validation": {"minLength": 500}}}, "output_format": "json", "output_schema": {"type": "object", "required": ["companies_extracted", "extraction_summary"], "properties": {"companies_extracted": {"type": "array", "items": {"type": "object", "required": ["company_name", "founders", "business_details", "shark_tank_pitch"], "properties": {"company_name": {"type": "string"}, "founders": {"type": "array", "items": {"type": "object", "required": ["name"]}}, "business_details": {"type": "object", "required": ["business_sector", "business_model", "product_service_description"]}, "shark_tank_pitch": {"type": "object", "required": ["investment_ask", "deal_outcome"]}}}}, "extraction_summary": {"type": "object", "required": ["total_companies_found", "companies_with_deals", "companies_without_deals"]}}}, "usage_notes": ["Use for extracting company data from Shark Tank India transcripts", "Requires complete transcript content as input", "Focus on accuracy over completeness - mark missing data as null", "Extract all companies in multi-company episodes"], "examples": [{"input": {"season_number": "4", "episode_number": "15", "episode_title": "Healthcare Innovation Special", "air_date": "2024-02-15", "transcript_content": "Welcome to Shark Tank India. Our first company is Dr <PERSON>, founded by Dr. <PERSON>. They are asking for ₹50 lakhs for 10% equity. Dr <PERSON>: We are a veterinary clinic chain with ₹2 crore annual revenue, growing at 150% year over year..."}, "expected_output": "Structured JSON extraction with complete company profile including financial metrics, pitch details, and deal outcome"}], "created_at": "2025-01-09T12:00:00.000Z", "updated_at": "2025-01-09T12:00:00.000Z"}