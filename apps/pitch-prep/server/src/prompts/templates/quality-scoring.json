{"id": "quality-scoring", "name": "Transcript Quality Assessment", "description": "Score transcript quality to determine if it's suitable for extraction", "version": "1.0.0", "category": "transcript_processing", "model": "gemini-2.0-flash-exp", "template": "# Transcript Quality Assessment\n\nYou are a quality assessment specialist evaluating Shark Tank India transcript segments for extraction readiness.\n\n## Transcript Segment to Evaluate\n{{transcript_segment}}\n\n## Company Information\n- Company Name: {{company_name}}\n- Season: {{season_number}}\n- Episode: {{episode_number}}\n\n## Quality Assessment Criteria\n\nEvaluate the transcript segment across multiple dimensions to determine if it contains sufficient information for meaningful extraction.\n\n## Output Format\n\n```json\n{\n  \"quality_scores\": {\n    \"overall_score\": 0,\n    \"content_completeness\": 0,\n    \"information_density\": 0,\n    \"clarity_score\": 0,\n    \"business_data_score\": 0,\n    \"outcome_clarity\": 0\n  },\n  \"content_analysis\": {\n    \"has_company_name\": true,\n    \"has_founder_names\": true,\n    \"has_business_description\": true,\n    \"has_financial_metrics\": true,\n    \"has_investment_ask\": true,\n    \"has_deal_outcome\": true,\n    \"has_shark_feedback\": true\n  },\n  \"extractable_elements\": {\n    \"company_basics\": {\n      \"present\": true,\n      \"quality\": \"high/medium/low\",\n      \"specific_items\": [\"Company name\", \"Founder names\", \"Location\"]\n    },\n    \"business_model\": {\n      \"present\": true,\n      \"quality\": \"high/medium/low\",\n      \"specific_items\": [\"Product/service\", \"Target market\", \"Revenue model\"]\n    },\n    \"financial_data\": {\n      \"present\": true,\n      \"quality\": \"high/medium/low\",\n      \"specific_items\": [\"Revenue\", \"Profit\", \"Growth rate\"]\n    },\n    \"deal_information\": {\n      \"present\": true,\n      \"quality\": \"high/medium/low\",\n      \"specific_items\": [\"Ask amount\", \"Equity offered\", \"Final deal\"]\n    },\n    \"shark_interaction\": {\n      \"present\": true,\n      \"quality\": \"high/medium/low\",\n      \"specific_items\": [\"Questions\", \"Concerns\", \"Feedback\"]\n    }\n  },\n  \"quality_issues\": {\n    \"critical_issues\": [],\n    \"major_issues\": [],\n    \"minor_issues\": [],\n    \"missing_critical_data\": [],\n    \"unclear_sections\": []\n  },\n  \"extraction_readiness\": {\n    \"ready_for_extraction\": true,\n    \"recommended_action\": \"proceed/review/skip/re-fetch\",\n    \"confidence_level\": 0.95,\n    \"expected_extraction_quality\": \"high/medium/low\",\n    \"special_handling_required\": false,\n    \"handling_notes\": \"\"\n  },\n  \"language_analysis\": {\n    \"primary_language\": \"Hindi/English/Hinglish\",\n    \"language_clarity\": \"clear/mixed/unclear\",\n    \"technical_terms_present\": true,\n    \"requires_translation\": false\n  }\n}\n```\n\n## Scoring Guidelines\n\n### Overall Score (0-100)\n- **90-100**: Excellent quality, all information clear and complete\n- **70-89**: Good quality, most information present and clear\n- **50-69**: Acceptable quality, sufficient for basic extraction\n- **30-49**: Poor quality, significant gaps but some value\n- **0-29**: Unusable, too incomplete or unclear\n\n### Content Completeness (0-100)\n- Company identification: 20 points\n- Business description: 20 points\n- Financial information: 20 points\n- Deal information: 20 points\n- Shark interaction: 20 points\n\n### Information Density\n- High: Rich details about business, metrics, and decisions\n- Medium: Adequate information for extraction\n- Low: Sparse information, many gaps\n\n### Critical Elements for Extraction\n**Must Have (Minimum for score ≥50)**:\n- Company name\n- Basic business description\n- Investment ask OR deal outcome\n\n**Should Have (For score ≥70)**:\n- Founder names\n- Revenue or business metrics\n- Shark feedback\n- Clear deal terms\n\n**Nice to Have (For score ≥90)**:\n- Detailed financials\n- Growth metrics\n- Complete Q&A\n- All shark opinions\n\n### Recommended Actions\n- **Proceed**: Score ≥70, ready for extraction\n- **Review**: Score 50-69, may need manual review\n- **Skip**: Score 30-49, too incomplete for reliable extraction\n- **Re-fetch**: Score <30, transcript likely corrupted\n\n## Special Considerations\n\n- Hinglish mixing is normal and shouldn't reduce score\n- Some financial data may be intentionally vague\n- Focus on extractable business intelligence\n- Consider context from other episodes if referenced\n\nAssess the transcript quality to ensure reliable extraction results.", "variables": {"transcript_segment": {"type": "string", "required": true, "description": "Company pitch segment to evaluate", "validation": {"minLength": 100}}, "company_name": {"type": "string", "required": true, "description": "Company name for context"}, "season_number": {"type": "string", "required": true, "description": "Season number for context"}, "episode_number": {"type": "string", "required": true, "description": "Episode number for context"}}, "output_format": "json", "output_schema": {"type": "object", "required": ["quality_scores", "extraction_readiness"], "properties": {"quality_scores": {"type": "object", "required": ["overall_score"], "properties": {"overall_score": {"type": "number", "minimum": 0, "maximum": 100}}}, "extraction_readiness": {"type": "object", "required": ["ready_for_extraction", "recommended_action"]}}}, "usage_notes": ["Use to filter transcripts before expensive extraction", "Score threshold of 70+ recommended for extraction", "Helps identify transcripts needing re-fetching", "Quality scores stored for pipeline optimization"], "created_at": "2025-01-13T12:00:00.000Z", "updated_at": "2025-01-13T12:00:00.000Z"}