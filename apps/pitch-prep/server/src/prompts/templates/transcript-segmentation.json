{"id": "transcript-segmentation", "name": "Company Pitch Segmentation", "description": "Split cleaned transcripts into individual company pitch segments", "version": "1.0.0", "category": "transcript_processing", "model": "gemini-2.0-flash-exp", "template": "# Segment Transcript by Company Pitches\n\nYou are analyzing a Shark Tank India episode transcript to identify and extract individual company pitch segments.\n\n## Cleaned Transcript\n{{cleaned_transcript}}\n\n## Episode Information\n- Season: {{season_number}}\n- Episode: {{episode_number}}\n\n## Segmentation Task\n\nIdentify and extract each company's complete pitch segment from the transcript, including:\n- Pre-pitch introduction by narrator/host\n- Founder's pitch presentation\n- Q&A with sharks\n- Negotiation\n- Final deal outcome\n\n## Output Format\n\n```json\n{\n  \"episode_segments\": {\n    \"introduction\": \"Episode introduction text if present\",\n    \"company_pitches\": [\n      {\n        \"pitch_number\": 1,\n        \"company_name\": \"Exact company name\",\n        \"founders\": [\"Founder names identified\"],\n        \"pitch_sections\": {\n          \"narrator_intro\": \"Host/narrator introduction of company\",\n          \"founder_presentation\": \"Main pitch by founders\",\n          \"qa_discussion\": \"Q&A and discussion with sharks\",\n          \"negotiation\": \"Deal negotiation if occurred\",\n          \"outcome\": \"Final deal or no deal outcome\"\n        },\n        \"key_moments\": [\n          {\n            \"moment_type\": \"ask\",\n            \"speaker\": \"Founder name\",\n            \"content\": \"हम X लाख मांग रहे हैं Y% equity के लिए\",\n            \"financial_data\": {\n              \"amount_lakhs\": \"Extract numeric value only (e.g., 50 for पचास लाख)\",\n              \"equity_percentage\": \"Extract numeric value only (e.g., 5 for पांच परसेंट)\"\n            }\n          },\n          {\n            \"moment_type\": \"deal\",\n            \"speaker\": \"Shark name\",\n            \"content\": \"Final deal terms mentioned\",\n            \"financial_data\": {\n              \"amount_lakhs\": \"Final deal amount in lakhs\",\n              \"equity_percentage\": \"Final equity percentage\"\n            }\n          }\n        ],\n        \"sharks_involved\": [\"Names of sharks who participated\"],\n        \"segment_quality\": {\n          \"completeness\": \"complete/partial/unclear\",\n          \"has_clear_outcome\": true,\n          \"missing_sections\": []\n        }\n      }\n    ],\n    \"conclusion\": \"Episode conclusion if present\"\n  },\n  \"segmentation_metadata\": {\n    \"total_companies_found\": 0,\n    \"successful_segmentations\": 0,\n    \"failed_segmentations\": 0,\n    \"average_pitch_length\": \"Approximate words per pitch\",\n    \"segmentation_confidence\": 0.95\n  },\n  \"validation\": {\n    \"all_pitches_have_outcomes\": true,\n    \"all_founders_identified\": true,\n    \"ambiguous_segments\": [],\n    \"overlapping_segments\": []\n  }\n}\n```\n\n## Segmentation Rules\n\n### Pitch Boundaries\n- **Start Markers**:\n  - \"हमारे अगले एंटरप्रेन्योर हैं\" / \"Our next entrepreneurs are\"\n  - \"आइए मिलते हैं\" / \"Let's meet\"\n  - \"अगली कंपनी है\" / \"Next company is\"\n  - Company name mention with founder introduction\n\n- **End Markers**:\n  - Deal conclusion: \"डील हो गई\" / \"Deal done\"\n  - All sharks out: \"सभी शार्क आउट हैं\"\n  - Transition to next company\n  - Episode conclusion markers\n\n### Key Information to Extract\n- **Investment Ask**: \"X लाख के लिए Y% इक्विटी\"\n- **Deal Terms**: Final investment amount and equity\n- **Shark Decisions**: Who invested, who passed, reasons\n- **Business Metrics**: Revenue, profit, growth mentioned\n\n### Quality Checks\n- Each segment should have clear start and end\n- Company name must be identifiable\n- At least one founder name should be found\n- Outcome (deal/no deal) should be clear\n\n## Special Instructions\n\n- Handle incomplete pitches (e.g., cut off by ad breaks)\n- Mark overlapping or unclear segments for review\n- Preserve the original language in extracted segments\n- Maintain chronological order of pitches\n- Flag any unusual patterns or formats\n\nSegment the transcript carefully to ensure each company's complete story is captured.", "variables": {"cleaned_transcript": {"type": "string", "required": true, "description": "Cleaned transcript from transcript-cleaning template", "validation": {"minLength": 500}}, "season_number": {"type": "string", "required": true, "description": "Season number for context", "example": "4"}, "episode_number": {"type": "string", "required": true, "description": "Episode number for context", "example": "15"}}, "output_format": "json", "output_schema": {"type": "object", "required": ["episode_segments", "segmentation_metadata"], "properties": {"episode_segments": {"type": "object", "required": ["company_pitches"], "properties": {"company_pitches": {"type": "array", "items": {"type": "object", "required": ["company_name", "pitch_sections"]}}}}}}, "usage_notes": ["Use after transcript-cleaning to segment by companies", "Each segment becomes input for company-extraction", "Preserves full context for each company pitch", "Quality metrics help identify problematic segments"], "created_at": "2025-01-13T12:00:00.000Z", "updated_at": "2025-01-13T12:00:00.000Z"}