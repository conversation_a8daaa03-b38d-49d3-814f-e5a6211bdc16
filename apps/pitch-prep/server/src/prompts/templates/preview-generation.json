{"id": "preview-generation", "name": "Report Preview Generation", "description": "Generate a locked preview report with teaser content to drive conversions", "version": "1.0.0", "category": "report_generation", "model": "gemini-2.0-flash-exp", "template": "# Generate Locked Preview Report\n\nYou are creating a preview report for a founder preparing for Shark Tank India. The preview should provide valuable insights while keeping detailed analysis locked to encourage purchase.\n\n## User Company Information\n{{user_company_data}}\n\n## Similar Companies Found\n{{similar_companies_summary}}\n\n## Preview Generation Requirements\n\nCreate a compelling preview that:\n1. Shows enough value to demonstrate report quality\n2. Withholds detailed insights to encourage purchase\n3. Creates urgency and FOMO (fear of missing out)\n4. Highlights what's in the full report\n\n## Output Format\n\n```json\n{\n  \"preview_report\": {\n    \"executive_teaser\": {\n      \"headline\": \"Your Shark Tank Success Analysis\",\n      \"key_insight\": \"One specific, valuable insight about their business type\",\n      \"success_probability_hint\": \"Companies like yours had X% success rate\",\n      \"most_compatible_shark\": \"Name only, details locked\",\n      \"critical_warning\": \"One important thing to avoid based on similar companies\"\n    },\n    \"similar_companies_preview\": {\n      \"total_found\": 0,\n      \"preview_companies\": [\n        {\n          \"company_name\": \"Company name visible\",\n          \"similarity_score\": \"92% match\",\n          \"outcome_teaser\": \"Got deal/No deal\",\n          \"details_locked\": true,\n          \"locked_content_hint\": \"See why they succeeded/failed\"\n        }\n      ],\n      \"locked_insights\": [\n        \"Detailed analysis of what worked\",\n        \"Specific mistakes to avoid\",\n        \"Shark-specific strategies\"\n      ]\n    },\n    \"question_bank_preview\": {\n      \"total_questions\": 25,\n      \"sample_questions\": [\n        {\n          \"question\": \"What is your customer acquisition cost?\",\n          \"asked_by\": \"Commonly asked by <PERSON>eyush\",\n          \"importance\": \"Critical\",\n          \"answer_framework_locked\": true\n        },\n        {\n          \"question\": \"How do you compete with [competitor]?\",\n          \"asked_by\": \"Aman's favorite question\",\n          \"importance\": \"High\",\n          \"answer_framework_locked\": true\n        },\n        {\n          \"question\": \"What's your monthly burn rate?\",\n          \"asked_by\": \"Anupam always asks this\",\n          \"importance\": \"Critical\",\n          \"answer_framework_locked\": true\n        }\n      ],\n      \"locked_content\": [\n        \"22 more critical questions\",\n        \"Answer frameworks for each question\",\n        \"Common follow-up questions\",\n        \"Red flag answers to avoid\"\n      ]\n    },\n    \"shark_compatibility_preview\": {\n      \"teaser_scores\": {\n        \"top_match\": {\n          \"shark_name\": \"Shark name\",\n          \"compatibility_percentage\": \"87%\",\n          \"reason_locked\": true\n        },\n        \"other_sharks_locked\": true\n      },\n      \"locked_insights\": [\n        \"Detailed compatibility analysis for all sharks\",\n        \"Specific pitch strategies for each shark\",\n        \"Investment history in your sector\",\n        \"Deal negotiation patterns\"\n      ]\n    },\n    \"benchmarking_preview\": {\n      \"your_position\": \"Your revenue puts you in top X%\",\n      \"critical_metrics_hint\": \"Most funded companies had X metric\",\n      \"locked_benchmarks\": [\n        \"Detailed financial comparisons\",\n        \"Valuation expectations\",\n        \"Growth rate requirements\",\n        \"Team size benchmarks\"\n      ]\n    },\n    \"call_to_action\": {\n      \"urgency_message\": \"Join 500+ founders who got funded\",\n      \"value_proposition\": [\n        \"Complete 12-page analysis\",\n        \"All 25+ critical questions with answers\",\n        \"Detailed shark strategies\",\n        \"Proven pitch frameworks\"\n      ],\n      \"price\": \"₹1,999\",\n      \"unlock_button_text\": \"Unlock Full Report Now\",\n      \"guarantee\": \"100% based on real Shark Tank data\"\n    }\n  },\n  \"locked_sections_summary\": {\n    \"total_locked_insights\": 0,\n    \"estimated_reading_time\": \"30 minutes\",\n    \"report_pages\": 12,\n    \"data_points_analyzed\": 500\n  },\n  \"conversion_triggers\": [\n    \"You're missing critical information that 73% of funded companies knew\",\n    \"3 sharks have specific interest in your sector - see who\",\n    \"Your valuation expectation may be off by 40% - here's why\"\n  ]\n}\n```\n\n## Preview Generation Strategy\n\n### Value Demonstration\n- Show real, specific insights (not generic advice)\n- Use actual data points and percentages\n- Reference real company names from database\n- Provide one genuinely useful tip they can use immediately\n\n### Creating Urgency\n- Highlight what they don't know\n- Show competitive advantage of having this information\n- Reference success rates and patterns\n- Create FOMO about missing critical insights\n\n### Locking Strategy\n- Show headlines, lock details\n- Provide questions, lock answers\n- Give scores, lock explanations\n- List insights, lock specifics\n\n### Psychological Triggers\n- Social proof: \"500+ founders prepared with this\"\n- Authority: \"Based on analysis of all 500+ companies\"\n- Scarcity: \"Insights your competitors don't have\"\n- Loss aversion: \"Avoid the mistakes that killed 40% of pitches\"\n\n## Quality Guidelines\n\n- Be specific enough to show real value\n- Be vague enough to require purchase for actionable details\n- Use real data and patterns from the database\n- Create legitimate value, not clickbait\n- Focus on founder's specific sector and business model\n\nGenerate a preview that converts browsers into buyers while providing genuine value.", "variables": {"user_company_data": {"type": "object", "required": true, "description": "User's company information from website analysis"}, "similar_companies_summary": {"type": "object", "required": true, "description": "Summary of similar companies found in database"}}, "output_format": "json", "output_schema": {"type": "object", "required": ["preview_report", "locked_sections_summary"], "properties": {"preview_report": {"type": "object", "required": ["executive_teaser", "similar_companies_preview", "call_to_action"]}, "locked_sections_summary": {"type": "object", "required": ["total_locked_insights", "report_pages"]}}}, "usage_notes": ["Use for generating the preview page content", "Balance value delivery with purchase incentive", "Customize based on user's sector and business model", "Update conversion triggers based on A/B testing"], "created_at": "2025-01-13T12:00:00.000Z", "updated_at": "2025-01-13T12:00:00.000Z"}