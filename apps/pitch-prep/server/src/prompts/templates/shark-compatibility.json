{"id": "shark-compatibility", "name": "Detailed Shark Compatibility Analysis", "description": "Analyze shark compatibility based on investment history and company profile", "version": "1.0.0", "category": "shark_compatibility", "model": "gemini-2.0-flash-exp", "template": "# Shark Compatibility Analysis\n\nYou are analyzing shark compatibility for a founder preparing for Shark Tank India. Use investment patterns and shark preferences to provide detailed compatibility scores and strategies.\n\n## User Company Profile\n{{user_company_data}}\n\n## Similar Companies Analysis\n{{similar_companies_with_sharks}}\n\n## Shark Investment Database\n{{shark_investment_patterns}}\n\n## Compatibility Analysis Requirements\n\nAnalyze each shark's compatibility with the user's company based on:\n1. Historical investments in similar sectors\n2. Investment size preferences\n3. Business model alignment\n4. Geographic focus\n5. Stage preferences (early/growth/mature)\n6. Personal interests and expertise\n\n## Output Format\n\n```json\n{\n  \"shark_compatibility_analysis\": {\n    \"compatibility_scores\": [\n      {\n        \"shark_name\": \"<PERSON><PERSON>\",\n        \"compatibility_percentage\": 85,\n        \"score_breakdown\": {\n          \"sector_alignment\": 90,\n          \"investment_range_fit\": 80,\n          \"business_model_match\": 85,\n          \"stage_preference_match\": 85,\n          \"expertise_relevance\": 85\n        },\n        \"investment_history\": {\n          \"total_investments\": 0,\n          \"sector_investments\": 0,\n          \"average_deal_size\": \"₹X lakhs\",\n          \"similar_companies_funded\": [\n            {\n              \"company_name\": \"Company name\",\n              \"deal_amount\": \"₹X lakhs\",\n              \"equity_taken\": \"Y%\",\n              \"outcome\": \"Success/Failure/Ongoing\"\n            }\n          ],\n          \"sector_interest_level\": \"High/Medium/Low\"\n        },\n        \"personality_fit\": {\n          \"known_preferences\": [\n            \"Likes strong branding\",\n            \"Prefers D2C models\",\n            \"Values profitability\"\n          ],\n          \"pet_peeves\": [\n            \"Dislikes unclear unit economics\",\n            \"Avoids heavy capex businesses\"\n          ],\n          \"negotiation_style\": \"Aggressive/Collaborative/Strategic\",\n          \"typical_questions\": [\n            \"What's your CAC?\",\n            \"How do you compete with X?\"\n          ]\n        },\n        \"strategic_value\": {\n          \"network_access\": \"Retail distribution, D2C expertise\",\n          \"mentorship_areas\": [\"Branding\", \"Marketing\", \"Operations\"],\n          \"portfolio_synergies\": [\"Can connect with portfolio company X\"],\n          \"growth_acceleration\": \"Can help with specific growth areas\"\n        },\n        \"pitch_strategy\": {\n          \"key_points_to_emphasize\": [\n            \"Emphasize brand potential\",\n            \"Show D2C traction\",\n            \"Highlight marketing efficiency\"\n          ],\n          \"points_to_avoid\": [\n            \"Don't focus on B2B potential\",\n            \"Avoid discussing high burn rate\"\n          ],\n          \"hook_suggestions\": [\n            \"Start with brand story\",\n            \"Lead with customer testimonials\"\n          ],\n          \"negotiation_tactics\": [\n            \"Be flexible on valuation\",\n            \"Offer advisory shares\",\n            \"Propose performance milestones\"\n          ]\n        },\n        \"deal_likelihood\": {\n          \"probability_of_interest\": 0.85,\n          \"probability_of_offer\": 0.70,\n          \"expected_offer_range\": {\n            \"amount\": \"₹50-75 lakhs\",\n            \"equity\": \"15-20%\"\n          },\n          \"competition_from_other_sharks\": \"High/Medium/Low\"\n        }\n      }\n    ],\n    \"panel_dynamics\": {\n      \"likely_lead_shark\": \"Most interested shark\",\n      \"potential_partnerships\": [\n        {\n          \"sharks\": [\"Aman\", \"Namita\"],\n          \"likelihood\": \"High\",\n          \"reason\": \"Complementary expertise\"\n        }\n      ],\n      \"competitive_bidding_likelihood\": \"High/Medium/Low\",\n      \"group_psychology_insights\": [\n        \"Aman's interest might trigger Peyush\",\n        \"Namita usually follows technical validation\"\n      ]\n    },\n    \"recommended_approach\": {\n      \"primary_target\": {\n        \"shark_name\": \"Best match shark\",\n        \"approach_strategy\": \"Detailed strategy\",\n        \"backup_plan\": \"If they decline\"\n      },\n      \"secondary_targets\": [\n        {\n          \"shark_name\": \"Second choice\",\n          \"trigger_conditions\": \"When to pivot to this shark\"\n        }\n      ],\n      \"avoid_if_possible\": {\n        \"shark_name\": \"Least compatible\",\n        \"reason\": \"Why they're not ideal\",\n        \"mitigation\": \"How to handle if they show interest\"\n      }\n    },\n    \"question_preparation\": {\n      \"shark_specific_questions\": [\n        {\n          \"shark\": \"Aman Gupta\",\n          \"likely_questions\": [\n            {\n              \"question\": \"What's your repeat rate?\",\n              \"why_they_ask\": \"Values customer retention\",\n              \"strong_answer_approach\": \"Share cohort data\"\n            }\n          ]\n        }\n      ]\n    }\n  },\n  \"strategic_recommendations\": {\n    \"pre_pitch_preparation\": [\n      \"Research shark's recent investments\",\n      \"Prepare shark-specific data points\",\n      \"Practice personality-based responses\"\n    ],\n    \"during_pitch_tactics\": [\n      \"Make eye contact with target sharks\",\n      \"Reference their portfolio companies\",\n      \"Use their language and terms\"\n    ],\n    \"negotiation_preparation\": [\n      \"Know your walk-away terms\",\n      \"Prepare counter-offers\",\n      \"Have sweeteners ready\"\n    ]\n  },\n  \"confidence_analysis\": {\n    \"data_quality\": 0.95,\n    \"prediction_confidence\": 0.85,\n    \"factors_affecting_accuracy\": [\n      \"Recent shark behavior changes\",\n      \"Unknown portfolio updates\",\n      \"Panel mood dynamics\"\n    ]\n  }\n}\n```\n\n## Analysis Guidelines\n\n### Compatibility Scoring (0-100)\n- **90-100**: Perfect match - high probability of investment\n- **70-89**: Strong match - likely to show interest\n- **50-69**: Moderate match - depends on pitch quality\n- **30-49**: Weak match - unlikely without exceptional pitch\n- **0-29**: Poor match - probably won't invest\n\n### Key Factors to Consider\n1. **Sector Experience**: Has the shark invested in this sector?\n2. **Check Size**: Does ask fit their typical investment range?\n3. **Stage Preference**: Early stage vs growth stage preference\n4. **Geographic Fit**: Some sharks prefer certain regions\n5. **Expertise Match**: Can they add strategic value?\n\n### Personality Insights (Based on Show Patterns)\n- **Aman Gupta**: D2C focus, brand building, marketing\n- **Namita Mittal**: Healthcare, consumer products, women entrepreneurs\n- **Anupam Mittal**: Tech, marketplaces, scalable models\n- **Vineeta Singh**: Beauty, FMCG, women-led businesses\n- **Peyush Bansal**: Tech, innovation, customer experience\n- **Amit Jain**: Tech, automotive, B2B\n- **Ashneer Grover**: Fintech, unit economics focus\n\n### Strategic Recommendations\n- Identify primary and backup targets\n- Prepare shark-specific pitches\n- Anticipate their specific concerns\n- Have data ready for their typical questions\n- Understand their negotiation patterns\n\nGenerate comprehensive shark compatibility analysis to maximize investment probability.", "variables": {"user_company_data": {"type": "object", "required": true, "description": "Complete user company profile and metrics"}, "similar_companies_with_sharks": {"type": "object", "required": true, "description": "Similar companies with shark investment details"}, "shark_investment_patterns": {"type": "object", "required": true, "description": "Historical shark investment patterns and preferences"}}, "output_format": "json", "output_schema": {"type": "object", "required": ["shark_compatibility_analysis", "strategic_recommendations"], "properties": {"shark_compatibility_analysis": {"type": "object", "required": ["compatibility_scores", "recommended_approach"]}}}, "usage_notes": ["Use for detailed shark matching and strategy", "Incorporates historical investment patterns", "Provides personality-based recommendations", "Helps with negotiation preparation"], "created_at": "2025-01-13T12:00:00.000Z", "updated_at": "2025-01-13T12:00:00.000Z"}