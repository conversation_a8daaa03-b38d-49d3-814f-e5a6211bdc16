{"id": "question-generation", "name": "Shark Tank Question Bank Generation", "description": "Generate comprehensive question bank based on company analysis and shark patterns", "version": "1.0.0", "category": "report_generation", "model": "gemini-2.0-flash-001", "template": "# Generate Shark Tank Question Bank\n\nYou are preparing a founder for Shark Tank India by generating a comprehensive question bank based on historical patterns and company-specific vulnerabilities.\n\n## User Company Profile\n{{user_company_data}}\n\n## Similar Companies Q&A History\n{{similar_companies_questions}}\n\n## Shark Question Patterns\n{{shark_question_database}}\n\n## Question Generation Requirements\n\nGenerate exactly 12 questions total across all categories:\n- Business model questions: exactly 4 questions\n- Financial questions: exactly 4 questions  \n- Market questions: exactly 4 questions\n\nFor each question, include complete answer frameworks and red flags to avoid.\n\n## Output Format\n\nReturn a JSON object with comprehensive question bank structure including:\n- Critical questions by category (business model, financial, market) - EXACTLY 12 TOTAL\n- Answer frameworks with structure and key points\n- Example strong answers and red flags to avoid\n- Shark-specific question patterns and preferences\n- Difficulty levels from easy to trap questions\n- Practice recommendations and preparation guidance\n\nFocus on creating actionable, specific questions tailored to the user's business sector and model. Include realistic answer frameworks that founders can actually use.", "variables": {"user_company_data": {"type": "object", "required": true, "description": "Complete user company profile including metrics, team, and business model"}, "similar_companies_questions": {"type": "object", "required": true, "description": "Q&A patterns from similar companies in database"}, "shark_question_database": {"type": "object", "required": true, "description": "Historical question patterns by each shark with their preferences"}}, "output_format": "json", "output_schema": {"type": "OBJECT", "required": ["question_bank", "practice_recommendations"], "properties": {"question_bank": {"type": "OBJECT", "required": ["critical_questions", "shark_specific_patterns"], "properties": {"critical_questions": {"type": "OBJECT", "required": ["business_model_questions", "financial_questions", "market_questions"], "properties": {"business_model_questions": {"type": "ARRAY", "items": {"type": "OBJECT", "required": ["question", "difficulty", "answer_framework"], "properties": {"question": {"type": "STRING"}, "difficulty": {"type": "STRING"}, "answer_framework": {"type": "OBJECT", "required": ["structure", "key_points"], "properties": {"structure": {"type": "STRING"}, "key_points": {"type": "ARRAY", "items": {"type": "STRING"}}}}}}}, "financial_questions": {"type": "ARRAY", "items": {"type": "OBJECT", "required": ["question", "difficulty", "answer_framework"], "properties": {"question": {"type": "STRING"}, "difficulty": {"type": "STRING"}, "answer_framework": {"type": "OBJECT", "required": ["structure", "key_points"], "properties": {"structure": {"type": "STRING"}, "key_points": {"type": "ARRAY", "items": {"type": "STRING"}}}}}}}, "market_questions": {"type": "ARRAY", "items": {"type": "OBJECT", "required": ["question", "difficulty", "answer_framework"], "properties": {"question": {"type": "STRING"}, "difficulty": {"type": "STRING"}, "answer_framework": {"type": "OBJECT", "required": ["structure", "key_points"], "properties": {"structure": {"type": "STRING"}, "key_points": {"type": "ARRAY", "items": {"type": "STRING"}}}}}}}}}, "shark_specific_patterns": {"type": "ARRAY", "items": {"type": "OBJECT", "required": ["shark_name", "question", "focus_area"], "properties": {"shark_name": {"type": "STRING"}, "question": {"type": "STRING"}, "focus_area": {"type": "STRING"}}}}}}, "practice_recommendations": {"type": "ARRAY", "items": {"type": "STRING"}}}}, "usage_notes": ["Use for comprehensive question preparation", "Includes answer frameworks and examples", "Covers shark-specific patterns", "Provides practice recommendations"], "created_at": "2025-01-13T12:00:00.000Z", "updated_at": "2025-01-13T12:00:00.000Z"}