/**
 * API Router
 * Central routing configuration for all API endpoints
 */

import { Hono } from 'hono';
import { authRouter } from './routes/auth.js';
import { analysisRouter } from './routes/analysis.js';
import { paymentsRouter } from './routes/payments.js';
import similarityRouter from './routes/similarity.js';
import companiesRouter from './routes/companies.js';
import processingStatusRouter from './endpoints/processingStatus.js';
import { narrativeReportRouter } from './endpoints/narrativeReport.js';
import { enhancedReportEndpoint } from './endpoints/enhancedReportEndpoint.js';
import { paymentEndpoint } from './endpoints/paymentEndpoint.js';
import { pipelineReportEndpoint } from './endpoints/pipelineReportEndpoint.js';
import { unifiedReportEndpoint } from './endpoints/unifiedReportEndpoint.js';
import sseProgressEndpoint from './endpoints/sseProgressEndpoint.js';
import healthRouter from './endpoints/health.js';

// Create main API router
const apiRouter = new Hono();

// Mount health check endpoints (no versioning for health checks)
apiRouter.route('/health', healthRouter);

// Mount v1 routes
const v1Router = new Hono();

// Authentication routes
v1Router.route('/auth', authRouter);

// Analysis routes
v1Router.route('/analysis', analysisRouter);

// Payment routes
v1Router.route('/payments', paymentsRouter);

// Similarity matching routes
v1Router.route('/similarity', similarityRouter);

// Company data access routes (NEW - Aug 13 2025)
v1Router.route('/companies', companiesRouter);

// Processing status for batch operations (NEW - Aug 13 2025)
v1Router.route('/processing', processingStatusRouter);

// Narrative report routes (new)
v1Router.route('/narrative-report', narrativeReportRouter);

// Unified report endpoint (NEW - PRIMARY)
v1Router.route('/reports', unifiedReportEndpoint);

// SSE Progress endpoint for real-time updates
v1Router.route('/progress', sseProgressEndpoint);

// Legacy report endpoints (kept for backward compatibility)
v1Router.route('/reports/enhanced', enhancedReportEndpoint);
v1Router.route('/reports/pipeline', pipelineReportEndpoint);

// Legacy endpoint mounts
v1Router.route('/payments-legacy', paymentEndpoint);

// Health check for v1
v1Router.get('/health', (c) => {
  return c.json({
    status: 'healthy',
    version: 'v1',
    timestamp: new Date().toISOString()
  });
});

// Mount v1 under /api/v1
apiRouter.route('/v1', v1Router);

// Root health check
apiRouter.get('/', (c) => {
  return c.json({
    message: 'Pitch Prep API',
    version: '1.0.0',
    docs: '/api/v1/health'
  });
});

export { apiRouter };