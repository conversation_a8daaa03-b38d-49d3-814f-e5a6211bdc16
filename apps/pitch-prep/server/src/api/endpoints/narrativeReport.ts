/**
 * Narrative Report API Endpoint
 * Generates story-driven, personalized reports for users
 * Integrates with the new narrative pipeline
 */

import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { GoogleGenAI } from '@google/genai';
import { sql } from '../../database/connection.js';
import { unifiedReportService } from '../../services/unifiedReportService.js';
import { unifiedSimilarityService } from '../../services/unifiedSimilarityService.js';
import { storyExtractionService } from '../../services/storyExtractionService.js';
import { sharkCompatibilityService } from '../../services/sharkCompatibilityService.js';
import type {
  UINarrativeReport,
  PipelineCompanyOutput
} from '../../types/pipeline-contracts.js';

const narrativeReportRouter = new Hono();

// Request schema for narrative report generation
const generateNarrativeReportSchema = z.object({
  sessionId: z.string().min(1),
  userCompanyData: z.object({
    companyName: z.string().min(1),
    founderN<PERSON>: z.string().min(1),
    businessSector: z.string().min(1),
    businessModel: z.string().min(1),
    websiteUrl: z.string().url(),
    askAmount: z.number().positive(),
    askEquity: z.number().positive().max(100)
  })
});

/**
 * Generate complete narrative report
 * POST /api/v1/narrative-report/generate
 */
narrativeReportRouter.post(
  '/generate',
  zValidator('json', generateNarrativeReportSchema),
  async (c) => {
    try {
      const { sessionId, userCompanyData } = c.req.valid('json');

      console.log(`📖 Generating narrative report for ${userCompanyData.companyName}`);

      // Step 1: Find similar companies using existing similarity service
      const similarCompanies = await unifiedSimilarityService.findSimilarCompanies(
        {
          companyName: userCompanyData.companyName,
          sector: userCompanyData.businessSector
        },
        5 // limit
      );

      if (similarCompanies.length === 0) {
        return c.json({
          success: false,
          error: 'No similar companies found for narrative generation'
        }, 404);
      }

      // Step 2: Get sector statistics for benchmarking
      const sectorStats = await getSectorStatistics(userCompanyData.businessSector);

      // Step 3: Transform similarity results to pipeline format
      const similarCompaniesWithPipeline = await Promise.all(
        similarCompanies.slice(0, 3).map(async (company) => {
          const pipelineData = await transformToPipelineOutput(company);
          return {
            pipelineData,
            similarityScore: company.similarity_score
          };
        })
      );

      // Step 4: Get shark compatibility data
      const sharkCompatibility = await sharkCompatibilityService.getCompatibilityForUser(
        userCompanyData.businessSector,
        'Pre-revenue', // Default to pre-revenue for now
        false // isDealClosed
      );

      // Step 5: Generate narrative report using unified service
      const unifiedRequest = {
        user_id: `user_${Date.now()}`,
        session_id: sessionId,
        company_name: userCompanyData.companyName,
        business_sector: userCompanyData.businessSector,
        business_model: userCompanyData.businessModel,
        products_services: '',
        revenue_range: 'Pre-revenue',
        target_market: 'India',
        funding_sought: userCompanyData.askAmount,
        equity_offered: userCompanyData.askEquity,
        key_priorities: ['funding', 'growth', 'mentorship'],
        website_url: userCompanyData.websiteUrl,
        website_content: '',
        additional_context: '',
        founder_names: userCompanyData.founderName,
        report_type: 'narrative' as const,
        include_charts: true,
        detailed_analysis: true,
        export_formats: ['json', 'html']
      };
      
      const unifiedReport = await unifiedReportService.generateUnifiedReport(unifiedRequest);
      
      // Extract narrative sections from unified report and transform to UINarrativeReport
      const narrativeReport: UINarrativeReport = {
        executiveLetter: {
          founderName: userCompanyData.founderName || userCompanyData.companyName,
          personalizedOpening: unifiedReport.executive_summary.overview,
          keyInsight: unifiedReport.executive_summary.key_findings[0] || '',
          similarCompanyTeaser: `Found ${similarCompaniesWithPipeline.length} similar companies with valuable insights`,
          callToAction: unifiedReport.executive_summary.strategic_recommendations[0] || 'Focus on preparation and practice'
        },
        similarCompanyStories: similarCompaniesWithPipeline.map(c => ({
          company: {
            name: c.pipelineData.company_name,
            season: `Season ${c.pipelineData.season || 'N/A'}`,
            episode: `Episode ${c.pipelineData.episode || 'N/A'}`,
            dealAmount: c.pipelineData.deal_amount ? `₹${c.pipelineData.deal_amount}` : 'No deal',
            equity: c.pipelineData.deal_equity ? `${c.pipelineData.deal_equity}%` : 'N/A',
            sharks: c.pipelineData.sharks_involved || [],
            similarity: c.similarityScore || 0,
            outcome: c.pipelineData.deal_made ? 'Deal' : 'No Deal'
          },
          story: {
            founder_background: c.pipelineData.story_elements?.founder_background || '',
            problem_they_solved: c.pipelineData.story_elements?.problem_they_solved || '',
            why_they_started: c.pipelineData.story_elements?.why_they_started || '',
            opening_hook: '',
            first_shark_question: '',
            turning_point_moment: '',
            toughest_challenge: '',
            negotiation_drama: '',
            shark_quotes: {},
            shark_concerns: [],
            shark_excitement: [],
            what_worked: [],
            what_didnt: [],
            advice_for_similar: []
          },
          lessonsForYou: [],
          whatToEmulate: [],
          whatToAvoid: []
        })),
        sharkCompatibility: Object.entries(sharkCompatibility.shark_scores).map(([sharkName, scores]: [string, any]) => ({
          sharkName: sharkName,
          compatibilityScore: scores.compatibility_score,
          whyGoodFit: scores.key_talking_points || [],
          howToWinThem: scores.negotiation_strategy ? [scores.negotiation_strategy] : [],
          questionsTheyWillAsk: scores.potential_concerns || [],
          negotiationStyle: scores.negotiation_strategy || '',
          dealHistory: `Investment likelihood: ${scores.investment_likelihood}%`
        })),
        benchmarkNarrative: {
          whereYouStand: unifiedReport.executive_summary.overview,
          goodNews: unifiedReport.executive_summary.key_findings.filter(f => f.includes('positive') || f.includes('strength')),
          watchOuts: unifiedReport.executive_summary.key_findings.filter(f => f.includes('concern') || f.includes('risk')),
          opportunities: unifiedReport.executive_summary.strategic_recommendations,
          peerComparison: ''
        },
        questionScript: {
          openingStrategy: '',
          anticipatedFlow: [],
          negotiationFramework: '',
          contingencyPlans: []
        },
        actionPlan: {
          week1: [],
          week2: [],
          week3: [],
          finalChecklist: unifiedReport.appendices.preparation_checklist
        }
      };

      // Step 5: Store the report in session storage
      await storeNarrativeReport(sessionId, narrativeReport, userCompanyData);

      // Step 6: Generate preview version for payment gate
      const previewReport = {
        ...narrativeReport,
        isPreview: true,
        message: 'This is a preview. Full report available after payment.'
      };

      console.log(`✅ Narrative report generated for ${userCompanyData.companyName}`);

      return c.json({
        success: true,
        data: {
          sessionId,
          previewReport,
          similarCompaniesCount: similarCompaniesWithPipeline.length,
          generatedAt: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Narrative report generation failed:', error);
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Report generation failed'
      }, 500);
    }
  }
);

/**
 * Get full narrative report (after payment)
 * GET /api/v1/narrative-report/full/:sessionId
 */
narrativeReportRouter.get(
  '/full/:sessionId',
  async (c) => {
    try {
      const sessionId = c.req.param('sessionId');

      // Check if payment was made
      const paymentStatus = await checkPaymentStatus(sessionId);
      if (!paymentStatus.paid) {
        return c.json({
          success: false,
          error: 'Payment required to access full report'
        }, 402);
      }

      // Retrieve full narrative report
      const narrativeReport = await retrieveNarrativeReport(sessionId);
      if (!narrativeReport) {
        return c.json({
          success: false,
          error: 'Report not found'
        }, 404);
      }

      return c.json({
        success: true,
        data: narrativeReport
      });

    } catch (error) {
      console.error('Failed to retrieve narrative report:', error);
      return c.json({
        success: false,
        error: 'Failed to retrieve report'
      }, 500);
    }
  }
);

/**
 * Get report preview (before payment)
 * GET /api/v1/narrative-report/preview/:sessionId
 */
narrativeReportRouter.get(
  '/preview/:sessionId',
  async (c) => {
    try {
      const sessionId = c.req.param('sessionId');

      // Retrieve full report first
      const narrativeReport = await retrieveNarrativeReport(sessionId);
      if (!narrativeReport) {
        return c.json({
          success: false,
          error: 'Report not found'
        }, 404);
      }

      // Generate preview version
      const previewReport = {
        ...narrativeReport,
        isPreview: true,
        message: 'This is a preview. Full report available after payment.',
        sectionsIncluded: ['heroSection', 'pitchNarrative'],
        sectionsHidden: ['similarCompanyStories', 'sharkCompatibilityNarrative', 'strategyAndRecommendations']
      };

      return c.json({
        success: true,
        data: previewReport
      });

    } catch (error) {
      console.error('Failed to retrieve report preview:', error);
      return c.json({
        success: false,
        error: 'Failed to retrieve preview'
      }, 500);
    }
  }
);

/**
 * Get sector statistics for benchmarking
 */
async function getSectorStatistics(businessSector: string): Promise<{
  sectorSuccessRate: number;
  avgDealEquity: number;
  avgDealAmount: number;
}> {
  try {
    const stats = await sql`
      SELECT
        ROUND(
          (COUNT(CASE WHEN (extraction_final::jsonb->'funding_deal_outcome'->>'deal_closed')::boolean = true THEN 1 END)::float / COUNT(*) * 100)::numeric, 1
        ) as sector_success_rate,
        ROUND(AVG(CASE WHEN (extraction_final::jsonb->'funding_deal_outcome'->>'deal_closed')::boolean = true THEN (extraction_final::jsonb->'funding_deal_outcome'->>'final_equity_percent')::numeric END)::numeric, 1) as avg_deal_equity,
        ROUND(AVG(CASE WHEN (extraction_final::jsonb->'funding_deal_outcome'->>'deal_closed')::boolean = true THEN (extraction_final::jsonb->'funding_deal_outcome'->>'final_deal_amount_inr')::numeric END)::numeric, 0) as avg_deal_amount
      FROM companies
      WHERE business_sector_standardized ILIKE ${'%' + businessSector + '%'}
        AND season = 4
    `;

    if (stats.length > 0) {
      return {
        sectorSuccessRate: stats[0].sector_success_rate || 65,
        avgDealEquity: stats[0].avg_deal_equity || 12.5,
        avgDealAmount: stats[0].avg_deal_amount || 4500000
      };
    }
  } catch (error) {
    console.error('Failed to get sector statistics:', error);
  }

  // Default statistics if query fails
  return {
    sectorSuccessRate: 65,
    avgDealEquity: 12.5,
    avgDealAmount: 4500000
  };
}

/**
 * Transform similarity result to pipeline output format
 */
async function transformToPipelineOutput(similarCompany: any): Promise<PipelineCompanyOutput> {
  // Get story elements if they exist
  let storyElements = null;
  if (similarCompany.transcript_data?.story_elements) {
    storyElements = similarCompany.transcript_data.story_elements;
  }

  // Extract deal outcome data
  const dealOutcome = similarCompany.deal_outcome || {};

  return {
    company_name: similarCompany.company_name,
    season: similarCompany.season,
    episode: similarCompany.episode || 0,
    video_id: `${similarCompany.season}_${similarCompany.episode}`,
    business_sector: similarCompany.business_sector,
    business_model: 'B2B SaaS',
    product_description: similarCompany.context_summary || 'Product description not available',
    target_market: inferTargetMarket(similarCompany.business_sector),
    value_proposition: 'Innovative solution',
    ask_amount: dealOutcome.initial_ask_amount || 0,
    ask_equity: dealOutcome.initial_equity_offered || 0,
    deal_amount: dealOutcome.final_deal_amount || 0,
    deal_equity: dealOutcome.final_equity || 0,
    deal_made: dealOutcome.deal_closed || false,
    sharks_involved: dealOutcome.participating_sharks || [],
    deal_outcome: similarCompany.why_similar || 'Deal outcome not available',
    story_elements: storyElements,
    embedding_text: `${similarCompany.company_name} is a ${similarCompany.business_sector} company`,
    confidence_score: 85,
    extraction_quality_score: 90,
    raw_transcript_path: '',
    pitch_summary: similarCompany.pitch || '',
    shark_feedback: [],
    processed_at: new Date().toISOString(),
    ai_model_version: 'narrative-api-1.0'
  };
}

/**
 * Helper functions for business model inference
 */
function inferBusinessModel(company: any): string {
  // TODO: Implement GenAI integration for business model classification
  // For now, return a default value based on sector
  const sectorModels: Record<string, string> = {
    'Technology': 'SaaS',
    'Healthcare': 'B2B',
    'Food & Beverage': 'D2C',
    'E-commerce': 'Marketplace',
    'Education': 'Platform'
  };
  return sectorModels[company.industry] || 'B2B';
  
  /* Uncomment when GenAI is properly configured:
  const prompt = `Analyze this company and classify its business model...`;
  
  try {
    const genAI = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
    const result = await genAI.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      config: {
        temperature: 0.1,
        maxOutputTokens: 50,
        responseMimeType: 'text/plain'
      }
    });

    const classification = result.text?.trim() || 'B2B';
    // Validate against known models
    const validModels = ['SaaS', 'B2B', 'B2C', 'B2B2C', 'D2C', 'Marketplace', 'Platform'];
    return validModels.includes(classification) ? classification : 'B2B';
  } catch (error) {
    console.error('Business model inference failed:', error);
    return 'B2B'; // Default fallback
  }
  */
}

function inferTargetMarket(businessSector: string): string {
  // Return sector-specific target markets
  const sectorMarkets: Record<string, string> = {
    'Technology': 'Tech-savvy businesses seeking digital transformation',
    'Healthcare': 'Healthcare providers and patients seeking better care',
    'Food & Beverage': 'Health-conscious consumers seeking quality products',
    'E-commerce': 'Online shoppers seeking convenience and value',
    'Education': 'Students and educators seeking learning solutions',
    'Finance': 'Individuals and businesses seeking financial solutions',
    'Retail': 'Cost-conscious consumers seeking value and convenience',
    'Manufacturing': 'Industrial businesses seeking efficiency improvements',
    'Services': 'Businesses and individuals seeking professional services'
  };
  return sectorMarkets[businessSector] || 'General consumers and businesses';
}

function inferValueProposition(company: any): string {
  if (company.pitch) {
    return company.pitch.substring(0, 200) + '...';
  }
  return `Innovative solution in ${company.industry}`;
}

/**
 * Store narrative report in session storage
 */
async function storeNarrativeReport(
  sessionId: string,
  narrativeReport: UINarrativeReport,
  userCompanyData: any
): Promise<void> {
  try {
    await sql`
      INSERT INTO analysis_sessions (
        session_id,
        company_name,
        analysis_data,
        status,
        created_at,
        updated_at
      ) VALUES (
        ${sessionId},
        ${userCompanyData.companyName},
        ${JSON.stringify({ narrativeReport, userCompanyData })},
        'report_generated',
        NOW(),
        NOW()
      )
      ON CONFLICT (session_id)
      DO UPDATE SET
        analysis_data = EXCLUDED.analysis_data,
        status = EXCLUDED.status,
        updated_at = NOW()
    `;
  } catch (error) {
    console.error('Failed to store narrative report:', error);
    throw error;
  }
}

/**
 * Retrieve narrative report from session storage
 */
async function retrieveNarrativeReport(sessionId: string): Promise<UINarrativeReport | null> {
  try {
    const result = await sql`
      SELECT analysis_data
      FROM analysis_sessions
      WHERE session_id = ${sessionId}
    `;

    if (result.length > 0 && result[0].analysis_data?.narrativeReport) {
      return result[0].analysis_data.narrativeReport;
    }
  } catch (error) {
    console.error('Failed to retrieve narrative report:', error);
  }
  return null;
}

/**
 * Check if payment was made for this session
 */
async function checkPaymentStatus(sessionId: string): Promise<{ paid: boolean; paymentId?: string }> {
  try {
    const result = await sql`
      SELECT payment_id, status
      FROM payments
      WHERE session_id = ${sessionId}
      AND status = 'completed'
    `;

    if (result.length > 0) {
      return { paid: true, paymentId: result[0].payment_id };
    }
  } catch (error) {
    console.error('Failed to check payment status:', error);
  }

  return { paid: false };
}

export { narrativeReportRouter };
