/**
 * Unified Report Endpoint
 * Single endpoint for all report generation needs
 * Provides backward compatibility with existing endpoints
 */

import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { unifiedReportService, type UnifiedReportRequest } from '../../services/unifiedReportService.js';

const app = new Hono();

// Comprehensive request schema that supports all report types
const unifiedReportSchema = z.object({
  // Core fields (required)
  user_id: z.string().min(1),
  session_id: z.string().min(1),
  company_name: z.string().min(1),
  business_sector: z.string().min(1),
  business_model: z.string().min(1),
  
  // Business details (optional)
  products_services: z.string().optional(),
  revenue_range: z.string().optional(),
  target_market: z.string().optional(),
  website_url: z.string().url().optional(),
  website_content: z.string().optional(),
  
  // Funding information (optional)
  funding_sought: z.number().positive().optional(),
  equity_offered: z.number().positive().max(100).optional(),
  valuation: z.number().positive().optional(),
  
  // Additional context (optional)
  founder_names: z.string().optional(),
  company_story: z.string().optional(),
  key_priorities: z.array(z.string()).optional(),
  additional_context: z.string().optional(),
  
  // Report configuration (optional)
  report_type: z.enum(['comprehensive', 'narrative', 'pipeline-enhanced', 'auto']).optional(),
  include_charts: z.boolean().optional(),
  detailed_analysis: z.boolean().optional(),
  export_formats: z.array(z.string()).optional()
});

/**
 * Main unified report generation endpoint
 * POST /api/v1/reports/generate
 */
app.post(
  '/generate',
  zValidator('json', unifiedReportSchema),
  async (c) => {
    try {
      const requestData = c.req.valid('json');
      
      console.log(`[UnifiedReportEndpoint] Generating report for ${requestData.company_name}`);
      console.log(`[UnifiedReportEndpoint] Report type: ${requestData.report_type || 'auto'}`);
      
      // Generate unified report
      const report = await unifiedReportService.generateUnifiedReport(requestData);
      
      // Format response for frontend
      const response = {
        success: true,
        report: {
          // Core metadata
          id: report.report_id,
          session_id: report.session_id,
          company_name: report.company_name,
          generation_date: report.generation_timestamp,
          report_type: report.report_type,
          
          // Executive summary with scores
          executive_summary: report.executive_summary,
          
          // All content sections
          sections: report.sections,
          
          // Rich appendices
          appendices: report.appendices,
          
          // Metadata for transparency
          metadata: report.metadata,
          
          // Quick access to key metrics
          metrics: {
            overall_score: report.executive_summary.overall_score,
            preparation_score: report.executive_summary.preparation_score,
            success_probability: report.executive_summary.success_probability,
            confidence_score: report.metadata.confidence_score,
            processing_time: report.metadata.processing_time_seconds
          }
        }
      };
      
      console.log(`[UnifiedReportEndpoint] Report generated successfully in ${report.metadata.processing_time_seconds}s`);
      
      return c.json(response);
      
    } catch (error) {
      console.error('[UnifiedReportEndpoint] Error generating report:', error);
      return c.json({
        success: false,
        error: 'Failed to generate report',
        message: error instanceof Error ? error.message : 'Unknown error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      }, 500);
    }
  }
);

/**
 * Get previously generated report
 * GET /api/v1/reports/:sessionId
 */
app.get('/:sessionId', async (c) => {
  try {
    const sessionId = c.req.param('sessionId');
    
    console.log(`[UnifiedReportEndpoint] Retrieving report for session ${sessionId}`);
    
    const report = await unifiedReportService.getReport(sessionId);
    
    if (!report) {
      return c.json({
        success: false,
        error: 'Report not found'
      }, 404);
    }
    
    return c.json({
      success: true,
      report: {
        id: report.report_id,
        session_id: report.session_id,
        company_name: report.company_name,
        generation_date: report.generation_timestamp,
        report_type: report.report_type,
        executive_summary: report.executive_summary,
        sections: report.sections,
        appendices: report.appendices,
        metadata: report.metadata,
        metrics: {
          overall_score: report.executive_summary.overall_score,
          preparation_score: report.executive_summary.preparation_score,
          success_probability: report.executive_summary.success_probability,
          confidence_score: report.metadata.confidence_score,
          processing_time: report.metadata.processing_time_seconds
        }
      }
    });
    
  } catch (error) {
    console.error('[UnifiedReportEndpoint] Error retrieving report:', error);
    return c.json({
      success: false,
      error: 'Failed to retrieve report'
    }, 500);
  }
});

/**
 * Generate report preview (before payment)
 * GET /api/v1/reports/preview/:sessionId
 */
app.get('/preview/:sessionId', async (c) => {
  try {
    const sessionId = c.req.param('sessionId');
    
    const report = await unifiedReportService.getReport(sessionId);
    
    if (!report) {
      return c.json({
        success: false,
        error: 'Report not found'
      }, 404);
    }
    
    // Create preview version with limited content
    const preview = {
      success: true,
      preview: {
        company_name: report.company_name,
        generation_date: report.generation_timestamp,
        
        // Executive summary is always visible
        executive_summary: report.executive_summary,
        
        // Section titles only (content hidden)
        available_sections: Object.keys(report.sections).map(key => ({
          id: key,
          title: report.sections[key as keyof typeof report.sections]?.title || key,
          preview_text: 'Full content available after payment',
          confidence_score: report.sections[key as keyof typeof report.sections]?.confidence_score
        })),
        
        // Basic metrics
        metrics: {
          overall_score: report.executive_summary.overall_score,
          success_probability: report.executive_summary.success_probability,
          sections_available: Object.keys(report.sections).length,
          data_sources: report.metadata.data_sources.length
        },
        
        // Teaser content
        sample_insights: [
          report.executive_summary.key_findings[0],
          'Full report contains 12+ detailed sections',
          'Personalized recommendations based on your business',
          'Question bank with 25+ anticipated questions'
        ]
      }
    };
    
    return c.json(preview);
    
  } catch (error) {
    console.error('[UnifiedReportEndpoint] Error generating preview:', error);
    return c.json({
      success: false,
      error: 'Failed to generate preview'
    }, 500);
  }
});

/**
 * Check report generation status
 * GET /api/v1/reports/status/:sessionId
 */
app.get('/status/:sessionId', async (c) => {
  try {
    const sessionId = c.req.param('sessionId');
    
    // Check if report exists
    const report = await unifiedReportService.getReport(sessionId);
    
    if (report) {
      return c.json({
        success: true,
        status: 'completed',
        ready: true,
        metadata: {
          generated_at: report.generation_timestamp,
          processing_time: report.metadata.processing_time_seconds,
          report_type: report.report_type
        }
      });
    }
    
    // Report not ready yet
    return c.json({
      success: true,
      status: 'pending',
      ready: false,
      message: 'Report generation in progress'
    });
    
  } catch (error) {
    console.error('[UnifiedReportEndpoint] Error checking status:', error);
    return c.json({
      success: false,
      error: 'Failed to check status'
    }, 500);
  }
});

// ============================================
// BACKWARD COMPATIBILITY ENDPOINTS
// These wrap the unified service for existing integrations
// ============================================

/**
 * Legacy enhanced report endpoint wrapper
 * POST /api/v1/reports/generate-enhanced
 */
app.post('/generate-enhanced', async (c) => {
  try {
    const body = await c.req.json();
    
    // Convert to unified format
    const unifiedRequest: UnifiedReportRequest = {
      user_id: body.user_id || 'legacy_user',
      session_id: body.session_id || `session_${Date.now()}`,
      company_name: body.company_name,
      business_sector: body.business_sector,
      business_model: body.business_model,
      products_services: body.products_services || body.description,
      revenue_range: body.revenue_range,
      target_market: body.target_market,
      funding_sought: body.funding_sought,
      equity_offered: body.equity_offered,
      website_url: body.website_url,
      website_content: body.website_content,
      key_priorities: body.key_priorities,
      additional_context: body.additional_context,
      report_type: 'comprehensive',
      include_charts: true,
      detailed_analysis: true
    };
    
    const report = await unifiedReportService.generateUnifiedReport(unifiedRequest);
    
    // Format as legacy enhanced report response
    return c.json({
      success: true,
      report: {
        id: report.report_id,
        company_name: report.company_name,
        generation_date: report.generation_timestamp,
        status: 'completed',
        executive_summary: report.executive_summary,
        sections: report.sections,
        appendices: report.appendices,
        metadata: report.metadata
      }
    });
    
  } catch (error) {
    console.error('[UnifiedReportEndpoint] Legacy enhanced error:', error);
    return c.json({
      error: 'Failed to generate enhanced report',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

/**
 * Legacy narrative report endpoint wrapper
 * POST /api/v1/reports/generate-narrative
 */
app.post('/generate-narrative', async (c) => {
  try {
    const body = await c.req.json();
    
    // Convert to unified format
    const unifiedRequest: UnifiedReportRequest = {
      user_id: body.user_id || 'legacy_user',
      session_id: body.sessionId,
      company_name: body.userCompanyData?.companyName || body.company_name,
      business_sector: body.userCompanyData?.businessSector || body.business_sector,
      business_model: body.userCompanyData?.businessModel || body.business_model,
      website_url: body.userCompanyData?.websiteUrl,
      funding_sought: body.userCompanyData?.askAmount,
      equity_offered: body.userCompanyData?.askEquity,
      founder_names: body.userCompanyData?.founderName,
      report_type: 'narrative',
      include_charts: false,
      detailed_analysis: true
    };
    
    const report = await unifiedReportService.generateUnifiedReport(unifiedRequest);
    
    // Format as legacy narrative report response
    return c.json({
      success: true,
      data: {
        sessionId: report.session_id,
        narrativeReport: {
          sections: {
            founder_story: report.sections.founder_story,
            emotional_journey: report.sections.emotional_journey,
            dramatic_arc: report.sections.dramatic_arc,
            pitch_narrative: report.sections.pitch_narrative
          }
        },
        generatedAt: report.generation_timestamp
      }
    });
    
  } catch (error) {
    console.error('[UnifiedReportEndpoint] Legacy narrative error:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Report generation failed'
    }, 500);
  }
});

/**
 * Legacy pipeline report endpoint wrapper
 * POST /api/v1/reports/pipeline-enhanced
 */
app.post('/pipeline-enhanced', async (c) => {
  try {
    const body = await c.req.json();
    
    // Convert to unified format
    const unifiedRequest: UnifiedReportRequest = {
      user_id: body.user_id || 'legacy_user',
      session_id: body.session_id || `session_${Date.now()}`,
      company_name: body.companyName,
      business_sector: body.businessSector,
      business_model: body.businessModel || 'B2B',
      products_services: body.businessDescription,
      report_type: 'pipeline-enhanced'
    };
    
    const report = await unifiedReportService.generateUnifiedReport(unifiedRequest);
    
    // Format as legacy pipeline report response
    return c.json({
      success: true,
      report: {
        companyName: report.company_name,
        businessSector: body.businessSector || 'Technology',
        reportDate: report.generation_timestamp,
        overallScore: report.executive_summary.overall_score,
        executiveSummary: report.executive_summary,
        similarCompaniesAnalysis: report.sections.similar_companies,
        sharkBehaviorPatterns: report.sections.shark_behavior_patterns,
        negotiationInsights: report.sections.negotiation_insights,
        emotionalIntelligence: report.sections.emotional_journey,
        pitchOptimization: report.sections.pitch_preparation,
        successPrediction: report.sections.success_patterns,
        keyLearnings: report.executive_summary.key_findings,
        actionableRecommendations: report.executive_summary.strategic_recommendations,
        riskFactors: Array.isArray(report.sections.risk_assessment) ? report.sections.risk_assessment : []
      }
    });
    
  } catch (error) {
    console.error('[UnifiedReportEndpoint] Legacy pipeline error:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Pipeline report generation failed'
    }, 500);
  }
});

export const unifiedReportEndpoint = app;
export default app;