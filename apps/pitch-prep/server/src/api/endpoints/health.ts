/**
 * Health Check Endpoint
 * Provides comprehensive health status for monitoring
 */

import { Hono } from 'hono';
import { sql } from '../../database/connection.js';
import fs from 'fs/promises';
import path from 'path';

const healthRouter = new Hono();

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  checks: {
    database: CheckResult;
    redis?: CheckResult;
    filesystem: CheckResult;
    memory: CheckResult;
    geminiApi?: CheckResult;
    razorpay?: CheckResult;
  };
  version: string;
  environment: string;
}

interface CheckResult {
  status: 'pass' | 'fail' | 'warn';
  message?: string;
  responseTime?: number;
  details?: any;
}

async function checkDatabase(): Promise<CheckResult> {
  const startTime = Date.now();
  try {
    const result = await sql`SELECT 1 as health_check`;
    const responseTime = Date.now() - startTime;
    
    if (result && result[0]?.health_check === 1) {
      return {
        status: 'pass',
        responseTime,
        message: 'Database connection successful'
      };
    }
    return {
      status: 'fail',
      message: 'Database query returned unexpected result',
      responseTime
    };
  } catch (error) {
    return {
      status: 'fail',
      message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      responseTime: Date.now() - startTime
    };
  }
}

async function checkFilesystem(): Promise<CheckResult> {
  const startTime = Date.now();
  try {
    // Check if logs directory exists and is writable
    const logsDir = path.join(process.cwd(), 'logs');
    await fs.access(logsDir, fs.constants.W_OK);
    
    // Check if data directory exists
    const dataDir = path.join(process.cwd(), 'data');
    await fs.access(dataDir, fs.constants.R_OK);
    
    return {
      status: 'pass',
      responseTime: Date.now() - startTime,
      message: 'Filesystem access verified'
    };
  } catch (error) {
    return {
      status: 'warn',
      message: `Filesystem check warning: ${error instanceof Error ? error.message : 'Unknown error'}`,
      responseTime: Date.now() - startTime
    };
  }
}

function checkMemory(): CheckResult {
  const used = process.memoryUsage();
  const heapUsedMB = Math.round(used.heapUsed / 1024 / 1024);
  const heapTotalMB = Math.round(used.heapTotal / 1024 / 1024);
  const rssMB = Math.round(used.rss / 1024 / 1024);
  
  // Warn if memory usage is high
  const heapUsagePercent = (used.heapUsed / used.heapTotal) * 100;
  
  return {
    status: heapUsagePercent > 90 ? 'warn' : 'pass',
    message: `Heap: ${heapUsedMB}MB / ${heapTotalMB}MB (${heapUsagePercent.toFixed(1)}%)`,
    details: {
      heapUsed: heapUsedMB,
      heapTotal: heapTotalMB,
      rss: rssMB,
      external: Math.round(used.external / 1024 / 1024)
    }
  };
}

async function checkGeminiApi(): Promise<CheckResult> {
  if (!process.env.GEMINI_API_KEY) {
    return {
      status: 'warn',
      message: 'Gemini API key not configured'
    };
  }
  
  // Don't actually call the API in health checks to avoid costs
  // Just verify the key format
  const keyLength = process.env.GEMINI_API_KEY.length;
  if (keyLength > 20) {
    return {
      status: 'pass',
      message: 'Gemini API key configured'
    };
  }
  
  return {
    status: 'warn',
    message: 'Gemini API key appears invalid'
  };
}

async function checkRazorpay(): Promise<CheckResult> {
  if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_KEY_SECRET) {
    return {
      status: 'warn',
      message: 'Razorpay credentials not configured'
    };
  }
  
  return {
    status: 'pass',
    message: 'Razorpay credentials configured'
  };
}

// Basic health check endpoint
healthRouter.get('/', async (c) => {
  return c.text('OK', 200);
});

// Detailed health check endpoint
healthRouter.get('/detailed', async (c) => {
  const startTime = Date.now();
  
  // Run all health checks in parallel
  const [database, filesystem, geminiApi, razorpay] = await Promise.all([
    checkDatabase(),
    checkFilesystem(),
    checkGeminiApi(),
    checkRazorpay()
  ]);
  
  const memory = checkMemory();
  
  // Determine overall status
  const checks = {
    database,
    filesystem,
    memory,
    geminiApi,
    razorpay
  };
  
  const hasFailure = Object.values(checks).some(check => check.status === 'fail');
  const hasWarning = Object.values(checks).some(check => check.status === 'warn');
  
  const status: 'healthy' | 'degraded' | 'unhealthy' = 
    hasFailure ? 'unhealthy' : 
    hasWarning ? 'degraded' : 
    'healthy';
  
  const healthStatus: HealthStatus = {
    status,
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    checks,
    version: process.env.APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  };
  
  // Set appropriate status code
  const statusCode = status === 'healthy' ? 200 : status === 'degraded' ? 200 : 503;
  
  return c.json(healthStatus, statusCode);
});

// Readiness check for Kubernetes
healthRouter.get('/ready', async (c) => {
  try {
    const dbCheck = await checkDatabase();
    if (dbCheck.status === 'pass') {
      return c.text('Ready', 200);
    }
    return c.text('Not Ready', 503);
  } catch (error) {
    return c.text('Not Ready', 503);
  }
});

// Liveness check for Kubernetes
healthRouter.get('/live', (c) => {
  // Simple check that the service is responsive
  return c.text('Alive', 200);
});

export default healthRouter;