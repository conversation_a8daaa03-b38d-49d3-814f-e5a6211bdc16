/**
 * SSE Progress Endpoint
 * Provides real-time progress updates for report generation
 */

import { Hono } from 'hono';
import { sseProgressService } from '../../services/sseProgressService.js';

const app = new Hono();

/**
 * SSE endpoint for real-time progress updates
 * GET /api/v1/progress/stream/:sessionId
 */
app.get('/stream/:sessionId', sseProgressService.createSSEHandler());

/**
 * Test endpoint to trigger progress events
 * POST /api/v1/progress/test/:sessionId
 */
app.post('/test/:sessionId', async (c) => {
  const sessionId = c.req.param('sessionId');
  
  // Simulate progress events
  setTimeout(() => {
    sseProgressService.sendProgress(sessionId, 'start', 'Starting analysis', 10, 'Initializing', 1, 5);
  }, 1000);
  
  setTimeout(() => {
    sseProgressService.sendProgress(sessionId, 'analysis', 'Analyzing data', 50, 'Processing', 3, 5);
  }, 3000);
  
  setTimeout(() => {
    sseProgressService.sendCompletion(sessionId, 'Analysis complete', '/report/test-123');
  }, 5000);
  
  return c.json({ message: 'Test events triggered' });
});

export default app;