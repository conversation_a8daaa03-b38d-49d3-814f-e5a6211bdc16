/**
 * Processing Status SSE Endpoint
 * Real-time progress updates for batch processing
 */

import { Hono } from 'hono';
import { progressEmitter } from '../../services/batchProcessingOrchestrator.js';

const processingStatusRouter = new Hono();

// SSE endpoint for real-time processing updates
processingStatusRouter.get('/stream', (c) => {
  // Set SSE headers
  c.header('Content-Type', 'text/event-stream');
  c.header('Cache-Control', 'no-cache');
  c.header('Connection', 'keep-alive');
  c.header('X-Accel-Buffering', 'no'); // Disable Nginx buffering
  
  // Send initial connection message
  const writer = c.env.outgoing.getWriter();
  const encoder = new TextEncoder();
  
  const sendEvent = (data: any) => {
    const message = `data: ${JSON.stringify(data)}\n\n`;
    writer.write(encoder.encode(message));
  };
  
  // Send initial status
  sendEvent({ 
    type: 'connected', 
    message: 'Connected to processing status stream',
    timestamp: new Date().toISOString()
  });
  
  // Listen for progress updates
  const progressHandler = (status: any) => {
    sendEvent({
      type: 'progress',
      ...status,
      timestamp: new Date().toISOString()
    });
  };
  
  progressEmitter.on('progress', progressHandler);
  
  // Clean up on disconnect
  c.req.raw.signal.addEventListener('abort', () => {
    progressEmitter.off('progress', progressHandler);
    writer.close();
  });
  
  // Keep connection alive with heartbeat
  const heartbeat = setInterval(() => {
    sendEvent({ type: 'heartbeat', timestamp: new Date().toISOString() });
  }, 30000);
  
  c.req.raw.signal.addEventListener('abort', () => {
    clearInterval(heartbeat);
  });
  
  // Return streaming response
  return new Response(c.env.outgoing, {
    headers: c.header()
  });
});

// Get current status (non-streaming)
processingStatusRouter.get('/current', async (c) => {
  const { batchProcessingOrchestrator } = await import('../../services/batchProcessingOrchestrator.js');
  const status = batchProcessingOrchestrator.getStatus();
  
  return c.json({
    success: true,
    data: status,
    timestamp: new Date().toISOString()
  });
});

export default processingStatusRouter;