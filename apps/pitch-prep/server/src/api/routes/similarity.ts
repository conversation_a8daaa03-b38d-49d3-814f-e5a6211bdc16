import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { eq } from 'drizzle-orm';
import { similarityMatchingService } from '../../services/similarityMatchingService';
import { db } from '../../config/database';
import { sharkTankCompanies } from '../../schema';

const similarityRouter = new Hono();

// Request validation schema
const findSimilarCompaniesSchema = z.object({
  company: z.object({
    name: z.string().min(1),
    industry: z.string().min(1),
    businessModel: z.string().min(1),
    revenue: z.number().optional(),
    askAmount: z.number().optional(),
    askEquity: z.number().optional(),
    description: z.string().min(1),
    websiteData: z.any().optional()
  }),
  limit: z.number().min(1).max(20).default(10)
});

// Find similar companies endpoint
similarityRouter.post(
  '/find-similar',
  zValidator('json', findSimilarCompaniesSchema),
  async (c) => {
    try {
      const { company, limit } = c.req.valid('json');
      
      console.log('Finding similar companies for:', company.name);
      
      // Call similarity matching service
      const similarCompanies = await similarityMatchingService.findSimilarCompanies(
        company,
        limit
      );
      
      return c.json({
        success: true,
        data: {
          userCompany: company,
          similarCompanies,
          totalMatches: similarCompanies.length,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error finding similar companies:', error);
      return c.json(
        {
          success: false,
          error: {
            message: 'Failed to find similar companies',
            details: error instanceof Error ? error.message : 'Unknown error'
          }
        },
        500
      );
    }
  }
);

// Get Shark Tank company details by ID
similarityRouter.get('/company/:id', async (c) => {
  try {
    const id = c.req.param('id');
    
    const company = await db
      .select()
      .from(sharkTankCompanies)
      .where(eq(sharkTankCompanies.id, id))
      .limit(1);
    
    if (company.length === 0) {
      return c.json(
        {
          success: false,
          error: { message: 'Company not found' }
        },
        404
      );
    }
    
    return c.json({
      success: true,
      data: company[0]
    });
  } catch (error) {
    console.error('Error fetching company:', error);
    return c.json(
      {
        success: false,
        error: {
          message: 'Failed to fetch company details',
          details: error instanceof Error ? error.message : 'Unknown error'
        }
      },
      500
    );
  }
});

// Update searchable text for all companies (admin endpoint)
similarityRouter.post('/update-searchable-text', async (c) => {
  try {
    console.log('Updating searchable text for all companies...');
    
    const updatedCount = await similarityMatchingService.updateAllSearchableText();
    
    return c.json({
      success: true,
      data: {
        message: `Updated searchable text for ${updatedCount} companies`,
        count: updatedCount
      }
    });
  } catch (error) {
    console.error('Error updating searchable text:', error);
    return c.json(
      {
        success: false,
        error: {
          message: 'Failed to update searchable text',
          details: error instanceof Error ? error.message : 'Unknown error'
        }
      },
      500
    );
  }
});

// Get statistics about Shark Tank companies database
similarityRouter.get('/stats', async (c) => {
  try {
    const companies = await db.select().from(sharkTankCompanies);
    
    // Calculate statistics
    const stats = {
      totalCompanies: companies.length,
      companiesWithDeals: companies.filter(c => c.dealMade).length,
      companiesWithoutDeals: companies.filter(c => !c.dealMade).length,
      byIndustry: companies.reduce((acc, c) => {
        const industry = c.industry || 'Unknown';
        acc[industry] = (acc[industry] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      bySeason: companies.reduce((acc, c) => {
        const season = `Season ${c.season}`;
        acc[season] = (acc[season] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };
    
    return c.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching stats:', error);
    return c.json(
      {
        success: false,
        error: {
          message: 'Failed to fetch statistics',
          details: error instanceof Error ? error.message : 'Unknown error'
        }
      },
      500
    );
  }
});

export default similarityRouter;