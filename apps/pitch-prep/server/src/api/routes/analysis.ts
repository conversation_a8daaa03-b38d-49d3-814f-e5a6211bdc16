/**
 * Analysis Routes
 * Core analysis endpoints extracted from main index.ts
 */

import { Hono } from 'hono';
import { contextAwareAnalysisService } from '../../services/contextAwareAnalysisService.js';
import { unifiedSimilarityService } from '../../services/unifiedSimilarityService.js';
import { geminiSemanticSearchService } from '../../services/geminiSemanticSearchService.js';
import { similarityMatchingService } from '../../services/similarityMatchingService.js';
import { sharkCompatibilityService } from '../../services/sharkCompatibilityService.js';
import { reportGenerationService } from '../../services/reportGenerationService.js';
import { sql } from '../../database/connection.js';
import { db } from '../../config/database.js';
import { analysisSessions, companies, users } from '../../schema/index.js';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import postgres from 'postgres';

// Use main database connection for consistency
// Removed separate analysisDb connection to avoid schema inconsistencies

const analysisRouter = new Hono();

// Processing status endpoint for monitoring
analysisRouter.get('/status', async (c) => {
  try {
    // Get analysis sessions count and recent activity
    const sessions = await db.select().from(analysisSessions);
    
    // Get shark tank companies count for processing context
    const companies = await db.select().from(sharkTankCompanies);
    
    const recentSessions = sessions
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5)
      .map(s => ({
        sessionId: s.sessionId,
        companyName: s.companyName,
        status: s.status,
        createdAt: s.createdAt
      }));
    
    return c.json({
      success: true,
      status: {
        totalSessions: sessions.length,
        completedSessions: sessions.filter(s => s.status === 'completed').length,
        totalCompanies: companies.length,
        recentActivity: recentSessions
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Status check failed:', error);
    return c.json({ 
      success: false, 
      error: 'Failed to get processing status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// Quick website analysis endpoint for InputFormPage
analysisRouter.post('/quick-website', async (c) => {
  try {
    const { email, website, linkedin } = await c.req.json();
    
    if (!website) {
      return c.json({ success: false, error: 'Website URL is required' }, 400);
    }
    
    // Generate session ID
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
    const analysisId = `analysis_${Date.now()}_${Math.random().toString(36).substring(2)}`;
    
    console.log(`🚀 Starting quick website analysis: ${website}`);
    
    // Extract company name from URL
    const companyName = website
      .replace(/^https?:\/\//, '')
      .replace(/^www\./, '')
      .split('.')[0]
      .replace(/-/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
    
    // Start analysis with Gemini
    const websiteAnalysis = await contextAwareAnalysisService.analyzeWebsite(website);
    
    // Store session
    await analysisDb`
      INSERT INTO analysis_sessions (
        session_id, 
        website_url, 
        company_name, 
        workflow_id, 
        status,
        user_company_data,
        session_key
      )
      VALUES (
        ${sessionId}, 
        ${website}, 
        ${companyName}, 
        'quick-analysis', 
        'analyzing',
        ${JSON.stringify({ 
          email, 
          website, 
          linkedin,
          companyName,
          analysisStarted: new Date().toISOString()
        })},
        ${sessionId}
      )
    `;
    
    return c.json({
      success: true,
      analysis_id: analysisId,
      sessionId,
      companyName,
      businessModel: websiteAnalysis.businessModel || 'Technology Company',
      businessSector: websiteAnalysis.businessSector || 'Technology',
      whatYouSell: websiteAnalysis.whatYouSell || 'Products and Services',
      valueProposition: websiteAnalysis.valueProposition || '',
      targetMarket: websiteAnalysis.targetMarket || '',
      status: 'analyzing'
    });
  } catch (error) {
    console.error('Quick website analysis failed:', error);
    return c.json({ 
      success: false, 
      error: 'Failed to analyze website',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// Start analysis session
analysisRouter.post('/start', async (c) => {
  try {
    const { websiteUrl, companyName, workflowId } = await c.req.json();
    
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
    
    // Store session (with required fields for NOT NULL constraints)
    await analysisDb`
      INSERT INTO analysis_sessions (
        session_id, 
        website_url, 
        company_name, 
        workflow_id, 
        status,
        user_company_data,
        session_key
      )
      VALUES (
        ${sessionId}, 
        ${websiteUrl}, 
        ${companyName}, 
        ${workflowId}, 
        'started',
        ${JSON.stringify({ companyName, websiteUrl })},
        ${sessionId}
      )
    `;
    
    return c.json({ success: true, sessionId, status: 'started' });
  } catch (error) {
    console.error('Failed to start analysis:', error);
    return c.json({ success: false, error: 'Failed to start analysis session' }, 500);
  }
});

// Website analysis endpoint
analysisRouter.post('/website', async (c) => {
  try {
    const { sessionId, websiteUrl } = await c.req.json();
    
    if (!sessionId || !websiteUrl) {
      return c.json({ success: false, error: 'Session ID and website URL are required' }, 400);
    }
    
    console.log(`🔍 Analyzing website: ${websiteUrl}`);
    
    // For now, use basic analysis until analyzeWebsite is implemented
    const analysis = {
      success: true,
      data: {
        url: websiteUrl,
        businessSummary: 'Website analysis pending implementation',
        keyProducts: [],
        targetMarket: 'To be determined'
      }
    };
    
    // No need to check success since we're always returning true for now
    
    // Update session with website analysis
    await analysisDb`
      UPDATE analysis_sessions 
      SET website_analysis = ${JSON.stringify(analysis.data)},
          status = 'website_analyzed',
          updated_at = NOW()
      WHERE session_id = ${sessionId}
    `;
    
    return c.json({
      success: true,
      data: analysis.data,
      sessionId,
      step: 'website_analysis',
      nextStep: 'business_metrics'
    });
    
  } catch (error) {
    console.error('Website analysis failed:', error);
    return c.json({ success: false, error: 'Website analysis failed' }, 500);
  }
});

// Business metrics extraction
analysisRouter.post('/metrics', async (c) => {
  try {
    const { sessionId, previousContext } = await c.req.json();
    
    if (!sessionId) {
      return c.json({ success: false, error: 'Session ID is required' }, 400);
    }
    
    console.log(`📊 Extracting business metrics for session: ${sessionId}`);
    
    // For now, use basic metrics until extractBusinessMetrics is implemented
    const metrics = {
      success: true,
      data: {
        revenue: 0,
        growth: 0,
        marketSize: 0,
        competition: 'To be analyzed'
      }
    };
    
    // No need to check success since we're always returning true for now
    
    // Update session
    await analysisDb`
      UPDATE analysis_sessions 
      SET business_metrics = ${JSON.stringify(metrics.data)},
          status = 'metrics_extracted',
          updated_at = NOW()
      WHERE session_id = ${sessionId}
    `;
    
    return c.json({
      success: true,
      data: metrics.data,
      sessionId,
      step: 'business_metrics',
      nextStep: 'similar_companies'
    });
    
  } catch (error) {
    console.error('Business metrics extraction failed:', error);
    return c.json({ success: false, error: 'Business metrics extraction failed' }, 500);
  }
});

// Similar companies matching
analysisRouter.post('/similar-companies', async (c) => {
  // Declare variables in outer scope for catch block access
  let sessionId: string = '';
  let companyData: any = {};
  let userContext: any = {};
  
  try {
    const body = await c.req.json();
    sessionId = body.sessionId || `temp_session_${Date.now()}`;
    companyData = body.companyData;
    
    if (!companyData) {
      return c.json({ success: false, error: 'Company data is required' }, 400);
    }
    
    console.log(`🔍 Finding similar companies for session: ${sessionId}`);
    
    // Transform companyData to CompanyProfile format for similarity matching
    const userCompanyProfile = {
      name: companyData.companyName || 'User Company',
      industry: companyData.sector || 'Technology',
      businessModel: companyData.businessModel || 'B2C',
      revenue: companyData.revenue || 0,
      askAmount: companyData.askAmount || 50, // In lakhs
      askEquity: companyData.askEquity || 5,
      description: companyData.whatYouSell || companyData.productDescription || 'Innovative solution',
      websiteData: companyData.websiteData || null
    };
    
    console.log('🚀 Using new SimilarityMatchingService with inline passages...');
    
    // Use our new SimilarityMatchingService with inline passages (optimal approach)
    const searchResults = await similarityMatchingService.findSimilarCompanies(
      userCompanyProfile,
      5 // Get top 5 similar companies
    );
    
    // Transform results to maintain backward compatibility with existing frontend
    const similarCompanies = searchResults.map(match => ({
      company_name: match.company.name,
      season: 'TBD', // Will be populated from company data
      episode: 'TBD', // Will be populated from company data  
      business_sector: match.company.industry,
      similarity_score: match.similarityScore,
      deal_outcome: match.sharkInvestments?.dealMade ? 'Deal Made' : 'No Deal',
      key_insights: match.similarityReasons,
      why_similar: match.similarityReasons.join('. '),
      context_summary: match.company.description,
      
      // Enhanced fields from new similarity service
      enhanced_analysis: {
        whatToEmulate: match.similarityReasons,
        whatToAvoid: [],
        openingStrategy: `Focus on ${match.similarityReasons[0]}`,
        negotiationAdvice: match.sharkInvestments?.sharks?.join(', ') || 'Target compatible sharks',
        relevantStoryExcerpt: match.company.description,
        confidenceLevel: Math.round(match.similarityScore * 100)
      },
      
      // Shark investment details
      shark_investments: match.sharkInvestments
    }));
    
    // Prepare session data for response
    const sessionData = {
      matches: similarCompanies,
      synthesis: `Found ${similarCompanies.length} similar companies using advanced inline passages matching`,
      searchMethod: 'similarity-matching-service',
      timestamp: new Date().toISOString()
    };
    
    console.log(`✅ Successfully found ${similarCompanies.length} similar companies using SimilarityMatchingService`);
    
    return c.json({
      success: true,
      data: similarCompanies,
      synthesis: sessionData.synthesis, // Include synthesis from similarity matching
      sessionId,
      step: 'similar_companies',
      nextStep: 'shark_compatibility'
    });
    
  } catch (error) {
    console.error('SimilarityMatchingService failed:', error);
    
    // Fallback 1: Try enhanced Gemini semantic search
    console.log('🔄 Falling back to enhanced Gemini semantic search...');
    try {
      // Transform companyData to UserBusinessContext format
      const userContext = {
        businessSector: companyData.sector || 'Technology',
        businessModel: companyData.businessModel || 'B2C',
        productDescription: companyData.productDescription || 'Innovative solution',
        targetMarket: companyData.targetMarket || 'Indian consumers',
        currentRevenue: companyData.revenue || 0,
        askAmount: companyData.askAmount || 5000000,
        askEquity: companyData.askEquity || 5,
        stage: companyData.stage || 'early-revenue',
        mainChallenges: companyData.challenges || ['Market penetration', 'Scaling operations'],
        keyStrengths: companyData.strengths || ['Strong team', 'Proven product'],
        competitiveAdvantage: companyData.advantages || ['First mover', 'Unique technology'],
        pitchFocus: companyData.pitchFocus || 'Market opportunity and traction',
        concerns: companyData.concerns || ['Competition', 'Market readiness']
      };
      
      const searchResult = await geminiSemanticSearchService.findSimilarCompanies(
        userContext,
        3 // Get 3 detailed matches
      );
      
      const fallbackResults = searchResult.matches.map(match => ({
        company_name: match.companyName,
        season: match.season,
        episode: match.episode,
        business_sector: match.companyId.split('-')[0] || 'Technology',
        similarity_score: match.relevanceScore / 100,
        deal_outcome: null,
        key_insights: match.specificLessons,
        why_similar: match.relevanceReasoning,
        context_summary: match.executiveSummary,
        enhanced_analysis: {
          whatToEmulate: match.whatToEmulate,
          whatToAvoid: match.whatToAvoid,
          openingStrategy: match.openingStrategy,
          negotiationAdvice: match.negotiationAdvice,
          relevantStoryExcerpt: match.relevantStoryExcerpt,
          confidenceLevel: match.confidenceLevel
        }
      }));
      
      console.log(`✅ Fallback: Found ${fallbackResults.length} similar companies using Gemini semantic search`);
      
      return c.json({
        success: true,
        data: fallbackResults,
        synthesis: searchResult.synthesis,
        sessionId: sessionId,
        step: 'similar_companies',
        nextStep: 'shark_compatibility',
        fallback: 'gemini-semantic'
      });
      
    } catch (fallbackError) {
      console.error('Gemini semantic search also failed:', fallbackError);
      
      // Fallback 2: Try legacy unified similarity service
      console.log('🔄 Final fallback to legacy similarity search...');
      try {
        const finalFallbackResults = await unifiedSimilarityService.findSimilarCompanies(
          userContext,
          5
        );
        
        console.log(`✅ Final fallback: Found ${finalFallbackResults.length} similar companies using unified similarity service`);
        
        return c.json({
          success: true,
          data: finalFallbackResults,
          sessionId: sessionId,
          step: 'similar_companies',
          nextStep: 'shark_compatibility',
          fallback: 'unified-similarity'
        });
        
      } catch (finalError) {
        console.error('All similarity search methods failed:', finalError);
        return c.json({ success: false, error: 'Similar companies matching failed' }, 500);
      }
    }
  }
});

// Shark compatibility analysis
analysisRouter.post('/shark-compatibility', async (c) => {
  try {
    const { sessionId, companyData, similarCompanies } = await c.req.json();
    
    if (!sessionId || !companyData) {
      return c.json({ success: false, error: 'Session ID and company data are required' }, 400);
    }
    
    console.log(`🦈 Analyzing shark compatibility for session: ${sessionId}`);
    
    // Use the correct method name
    const compatibility = await sharkCompatibilityService.getCompatibilityForUser(
      companyData.sector || 'Technology',
      similarCompanies || []
    );
    
    // Update session
    await analysisDb`
      UPDATE analysis_sessions 
      SET shark_compatibility = ${JSON.stringify(compatibility)},
          status = 'shark_compatibility_analyzed',
          updated_at = NOW()
      WHERE session_id = ${sessionId}
    `;
    
    return c.json({
      success: true,
      data: compatibility,
      sessionId,
      step: 'shark_compatibility',
      nextStep: 'report_generation'
    });
    
  } catch (error) {
    console.error('Shark compatibility analysis failed:', error);
    return c.json({ success: false, error: 'Shark compatibility analysis failed' }, 500);
  }
});

// Get analysis report
analysisRouter.get('/report/:sessionId', async (c) => {
  try {
    const sessionId = c.req.param('sessionId');
    
    console.log(`📋 Generating report for session: ${sessionId}`);
    
    // Get session data
    const sessionResult = await analysisDb`
      SELECT * FROM analysis_sessions 
      WHERE session_id = ${sessionId}
    `;
    
    if (sessionResult.length === 0) {
      return c.json({ success: false, error: 'Session not found' }, 404);
    }
    
    const session = sessionResult[0];
    
    // Generate comprehensive report
    const report = await reportGenerationService.generateComprehensiveReport({
      sessionId,
      websiteAnalysis: session.website_analysis,
      businessMetrics: session.business_metrics,
      similarCompanies: session.similar_companies,
      sharkCompatibility: session.shark_compatibility
    });
    
    // Update session with final report
    await analysisDb`
      UPDATE analysis_sessions 
      SET final_report = ${JSON.stringify(report)},
          status = 'completed',
          completed_at = NOW(),
          updated_at = NOW()
      WHERE session_id = ${sessionId}
    `;
    
    return c.json({
      success: true,
      data: report,
      sessionId,
      generatedAt: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Report generation failed:', error);
    return c.json({ success: false, error: 'Report generation failed' }, 500);
  }
});

// Get analysis status
analysisRouter.get('/status/:sessionId', async (c) => {
  try {
    const sessionId = c.req.param('sessionId');
    
    const result = await analysisDb`
      SELECT session_id, status, created_at, updated_at, completed_at
      FROM analysis_sessions 
      WHERE session_id = ${sessionId}
    `;
    
    if (result.length === 0) {
      return c.json({ success: false, error: 'Session not found' }, 404);
    }
    
    return c.json({
      success: true,
      data: result[0]
    });
    
  } catch (error) {
    console.error('Failed to get session status:', error);
    return c.json({ success: false, error: 'Failed to get session status' }, 500);
  }
});

export { analysisRouter };