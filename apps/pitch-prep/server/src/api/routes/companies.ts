/**
 * Company Routes
 * 
 * URGENT: Created Aug 13 2025 for same-day release
 * 
 * Endpoints for searching and accessing Shark Tank company data
 * Supports preview (free) and full access (paid) modes
 */

import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import postgres from 'postgres';

// Use pitch_prep_dev database
const PITCH_PREP_DB = 'postgresql://nikhilsingh@localhost:5432/pitch_prep_dev';
const sql = postgres(PITCH_PREP_DB);

const companiesRouter = new Hono();

// Search schema - coerce strings to numbers for query params
const searchSchema = z.object({
  query: z.string().optional(),
  sector: z.string().optional(),
  season: z.coerce.number().optional(),
  dealMade: z.coerce.boolean().optional(),
  minRevenue: z.coerce.number().optional(),
  maxRevenue: z.coerce.number().optional(),
  sharks: z.array(z.string()).optional(),
  limit: z.coerce.number().min(1).max(100).default(20),
  offset: z.coerce.number().min(0).default(0),
  sortBy: z.enum(['relevance', 'season', 'confidence', 'revenue']).default('relevance')
});

/**
 * Search companies endpoint
 * GET /api/v1/companies/search
 */
companiesRouter.get('/search', zValidator('query', searchSchema), async (c) => {
  try {
    const params = c.req.valid('query');
    console.log(`🔍 Searching companies with params:`, params);
    
    // Build dynamic query
    let conditions = [];
    let values = [];
    let valueIndex = 1;
    
    if (params.query) {
      conditions.push(`(
        name ILIKE $${valueIndex} OR 
        sector ILIKE $${valueIndex} OR 
        industry ILIKE $${valueIndex} OR
        searchable_text ILIKE $${valueIndex}
      )`);
      values.push(`%${params.query}%`);
      valueIndex++;
    }
    
    if (params.sector) {
      conditions.push(`(sector ILIKE $${valueIndex} OR industry ILIKE $${valueIndex})`);
      values.push(`%${params.sector}%`);
      valueIndex++;
    }
    
    if (params.season !== undefined) {
      conditions.push(`season = $${valueIndex}`);
      values.push(params.season);
      valueIndex++;
    }
    
    if (params.dealMade !== undefined) {
      conditions.push(`deal_made = $${valueIndex}`);
      values.push(params.dealMade);
      valueIndex++;
    }
    
    if (params.minRevenue !== undefined) {
      conditions.push(`(metadata->>'currentRevenue')::numeric >= $${valueIndex}`);
      values.push(params.minRevenue);
      valueIndex++;
    }
    
    if (params.maxRevenue !== undefined) {
      conditions.push(`(metadata->>'currentRevenue')::numeric <= $${valueIndex}`);
      values.push(params.maxRevenue);
      valueIndex++;
    }
    
    if (params.sharks && params.sharks.length > 0) {
      const sharkConditions = params.sharks.map(shark => {
        conditions.push(`sharks::text ILIKE $${valueIndex}`);
        values.push(`%${shark}%`);
        valueIndex++;
        return `sharks::text ILIKE $${valueIndex - 1}`;
      });
      // Sharks are already added to conditions above
    }
    
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    
    // Determine sort order
    let orderBy = 'extraction_confidence DESC';
    switch (params.sortBy) {
      case 'season':
        orderBy = 'season DESC, episode DESC';
        break;
      case 'confidence':
        orderBy = 'extraction_confidence DESC';
        break;
      case 'revenue':
        orderBy = "(metadata->>'currentRevenue')::numeric DESC NULLS LAST";
        break;
      case 'relevance':
      default:
        orderBy = 'extraction_confidence DESC, season DESC';
    }
    
    // Execute search query
    const query = `
      SELECT 
        id,
        name,
        season,
        episode,
        sector,
        industry,
        ask_amount,
        ask_equity,
        deal_amount,
        deal_equity,
        deal_made,
        sharks,
        extraction_confidence,
        metadata->>'founders' as founders,
        metadata->>'currentRevenue' as current_revenue,
        metadata->>'businessModel' as business_model,
        metadata->>'videoId' as video_id,
        CASE 
          WHEN executive_summary IS NOT NULL THEN executive_summary
          ELSE SUBSTRING(pitch FROM 1 FOR 200) || '...'
        END as preview_summary
      FROM shark_tank_companies
      ${whereClause}
      ORDER BY ${orderBy}
      LIMIT $${valueIndex} OFFSET $${valueIndex + 1}
    `;
    
    values.push(params.limit, params.offset);
    
    const companies = await sql.unsafe(query, values);
    
    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM shark_tank_companies
      ${whereClause}
    `;
    
    const countResult = await sql.unsafe(countQuery, values.slice(0, -2)); // Exclude limit/offset
    const totalCount = parseInt(countResult[0]?.total || '0');
    
    console.log(`✅ Found ${companies.length} companies (total: ${totalCount})`);
    
    return c.json({
      success: true,
      data: {
        companies,
        pagination: {
          total: totalCount,
          limit: params.limit,
          offset: params.offset,
          hasMore: params.offset + params.limit < totalCount
        }
      }
    });
    
  } catch (error) {
    console.error('Search failed:', error);
    return c.json({ 
      success: false, 
      error: 'Failed to search companies' 
    }, 500);
  }
});

/**
 * Get company preview (free tier)
 * GET /api/v1/companies/:id/preview
 */
companiesRouter.get('/:id/preview', async (c) => {
  try {
    const companyId = c.req.param('id');
    console.log(`👁️ Fetching preview for company: ${companyId}`);
    
    // Get basic company info for preview
    const company = await sql`
      SELECT 
        id,
        name,
        season,
        episode,
        sector,
        industry,
        ask_amount,
        ask_equity,
        deal_amount,
        deal_equity,
        deal_made,
        sharks,
        metadata->>'founders' as founders,
        metadata->>'businessModel' as business_model,
        metadata->>'currentRevenue' as current_revenue,
        metadata->>'videoId' as video_id,
        CASE 
          WHEN executive_summary IS NOT NULL THEN executive_summary
          ELSE SUBSTRING(pitch FROM 1 FOR 500) || '...'
        END as preview_summary,
        extraction_confidence
      FROM shark_tank_companies
      WHERE id = ${companyId}
      LIMIT 1
    `;
    
    if (company.length === 0) {
      return c.json({ 
        success: false, 
        error: 'Company not found' 
      }, 404);
    }
    
    // Log preview access
    const userIp = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown';
    await sql`
      INSERT INTO company_access_log (
        company_id, access_type, ip_address, user_agent
      ) VALUES (
        ${companyId}, 'preview', ${userIp}::inet, ${c.req.header('user-agent')}
      )
    `;
    
    console.log(`✅ Preview delivered for ${company[0].name}`);
    
    return c.json({
      success: true,
      data: {
        company: company[0],
        accessType: 'preview',
        upgradePrompt: 'Unlock full intelligence report with detailed analysis, strategic insights, and actionable recommendations.'
      }
    });
    
  } catch (error) {
    console.error('Preview fetch failed:', error);
    return c.json({ 
      success: false, 
      error: 'Failed to fetch company preview' 
    }, 500);
  }
});

/**
 * Get full company data (paid tier)
 * GET /api/v1/companies/:id/full
 * Requires authentication
 */
companiesRouter.get('/:id/full', async (c) => {
  try {
    const companyId = c.req.param('id');
    
    // TODO: Check authentication/payment status
    const userId = c.req.header('x-user-id'); // Would come from auth middleware
    
    console.log(`🔓 Fetching full data for company: ${companyId}`);
    
    // Get complete company data including all extractions
    const companyData = await sql`
      SELECT 
        c.*,
        ce.phase_1_data,
        ce.phase_2_data,
        ce.phase_3_data,
        ce.confidence_score as extraction_score,
        ce.token_count,
        ts.segment_text
      FROM shark_tank_companies c
      LEFT JOIN company_extractions ce ON ce.company_id = c.id
      LEFT JOIN transcript_segments ts ON ts.company_id = c.id
      WHERE c.id = ${companyId}
      LIMIT 1
    `;
    
    if (companyData.length === 0) {
      return c.json({ 
        success: false, 
        error: 'Company not found' 
      }, 404);
    }
    
    // Get similar companies
    const similarCompanies = await sql`
      SELECT 
        id, name, season, episode, sector, deal_made, 
        ask_amount, deal_amount, extraction_confidence
      FROM shark_tank_companies
      WHERE sector = ${companyData[0].sector}
        AND id != ${companyId}
      ORDER BY extraction_confidence DESC
      LIMIT 5
    `;
    
    // Log full access
    const userIp = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown';
    await sql`
      INSERT INTO company_access_log (
        user_id, company_id, access_type, ip_address, user_agent
      ) VALUES (
        ${userId}, ${companyId}, 'full', ${userIp}::inet, ${c.req.header('user-agent')}
      )
    `;
    
    console.log(`✅ Full data delivered for ${companyData[0].name}`);
    
    return c.json({
      success: true,
      data: {
        company: companyData[0],
        similarCompanies,
        intelligence: {
          core: companyData[0].phase_1_data,
          strategic: companyData[0].phase_2_data,
          actionable: companyData[0].phase_3_data
        },
        accessType: 'full'
      }
    });
    
  } catch (error) {
    console.error('Full data fetch failed:', error);
    return c.json({ 
      success: false, 
      error: 'Failed to fetch full company data' 
    }, 500);
  }
});

/**
 * Get companies by sector
 * GET /api/v1/companies/sectors/:sector
 */
companiesRouter.get('/sectors/:sector', async (c) => {
  try {
    const sector = c.req.param('sector');
    console.log(`📊 Fetching companies in sector: ${sector}`);
    
    const companies = await sql`
      SELECT 
        id, name, season, episode, 
        ask_amount, deal_amount, deal_made,
        extraction_confidence,
        metadata->>'founders' as founders
      FROM shark_tank_companies
      WHERE sector ILIKE ${'%' + sector + '%'}
         OR industry ILIKE ${'%' + sector + '%'}
      ORDER BY extraction_confidence DESC, season DESC
      LIMIT 50
    `;
    
    // Calculate sector statistics
    const stats = {
      totalCompanies: companies.length,
      dealsClosedRate: companies.filter(c => c.deal_made).length / companies.length * 100,
      averageAsk: companies.reduce((sum, c) => sum + (c.ask_amount || 0), 0) / companies.length,
      averageDeal: companies.filter(c => c.deal_made).reduce((sum, c) => sum + (c.deal_amount || 0), 0) / companies.filter(c => c.deal_made).length || 0
    };
    
    console.log(`✅ Found ${companies.length} companies in ${sector}`);
    
    return c.json({
      success: true,
      data: {
        sector,
        companies,
        statistics: stats
      }
    });
    
  } catch (error) {
    console.error('Sector fetch failed:', error);
    return c.json({ 
      success: false, 
      error: 'Failed to fetch sector companies' 
    }, 500);
  }
});

/**
 * Get trending companies
 * GET /api/v1/companies/trending
 */
companiesRouter.get('/trending', async (c) => {
  try {
    console.log(`🔥 Fetching trending companies`);
    
    // Get most accessed companies in last 7 days
    const trending = await sql`
      WITH access_counts AS (
        SELECT 
          company_id,
          COUNT(*) as access_count
        FROM company_access_log
        WHERE accessed_at >= NOW() - INTERVAL '7 days'
        GROUP BY company_id
      )
      SELECT 
        c.id,
        c.name,
        c.season,
        c.episode,
        c.sector,
        c.deal_made,
        c.ask_amount,
        c.deal_amount,
        c.extraction_confidence,
        ac.access_count
      FROM shark_tank_companies c
      JOIN access_counts ac ON ac.company_id = c.id
      ORDER BY ac.access_count DESC
      LIMIT 10
    `;
    
    console.log(`✅ Found ${trending.length} trending companies`);
    
    return c.json({
      success: true,
      data: {
        trending,
        period: 'last_7_days'
      }
    });
    
  } catch (error) {
    console.error('Trending fetch failed:', error);
    return c.json({ 
      success: false, 
      error: 'Failed to fetch trending companies' 
    }, 500);
  }
});

export default companiesRouter;