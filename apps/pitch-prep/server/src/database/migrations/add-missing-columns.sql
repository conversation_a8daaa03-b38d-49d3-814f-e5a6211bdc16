-- Migration: Add missing columns for YAML data import
-- Adds columns expected by import script and analysis services

-- Enable vector extension for embeddings
CREATE EXTENSION IF NOT EXISTS vector;

-- Add missing columns to companies table
ALTER TABLE companies 
ADD COLUMN IF NOT EXISTS company_name <PERSON><PERSON><PERSON><PERSON>(255),
ADD COLUMN IF NOT EXISTS business_sector_standardized VARCHAR(255),
ADD COLUMN IF NOT EXISTS extraction_final JSONB,
ADD COLUMN IF NOT EXISTS context_summary TEXT,
ADD COLUMN IF NOT EXISTS embeddings vector(768), -- 768-dimensional embeddings for similarity
ADD COLUMN IF NOT EXISTS confidence_score NUMERIC DEFAULT 0.8;

-- Create indexes for new columns
CREATE INDEX IF NOT EXISTS idx_companies_company_name ON companies(company_name);
CREATE INDEX IF NOT EXISTS idx_companies_business_sector ON companies(business_sector_standardized);
CREATE INDEX IF NOT EXISTS idx_companies_extraction_final ON companies USING gin(extraction_final);
CREATE INDEX IF NOT EXISTS idx_companies_confidence_score ON companies(confidence_score);

-- Vector similarity index for embeddings
CREATE INDEX IF NOT EXISTS idx_companies_embeddings ON companies 
USING ivfflat (embeddings vector_cosine_ops) WITH (lists = 100);

-- Update existing data to use company_name if name exists
UPDATE companies SET company_name = name WHERE company_name IS NULL AND name IS NOT NULL;
UPDATE companies SET business_sector_standardized = sector WHERE business_sector_standardized IS NULL AND sector IS NOT NULL;

-- Add missing columns for unified report service
ALTER TABLE companies 
ADD COLUMN IF NOT EXISTS narrative_richness NUMERIC DEFAULT 0.7,
ADD COLUMN IF NOT EXISTS searchable_text TEXT,
ADD COLUMN IF NOT EXISTS deal_closed BOOLEAN DEFAULT false;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_companies_narrative_richness ON companies(narrative_richness);
CREATE INDEX IF NOT EXISTS idx_companies_searchable_text ON companies USING gin(to_tsvector('english', searchable_text));
CREATE INDEX IF NOT EXISTS idx_companies_deal_closed ON companies(deal_closed);

-- Update existing records with computed values
UPDATE companies 
SET 
  narrative_richness = COALESCE((extraction_final->>'confidence_score')::numeric, 0.7),
  searchable_text = CONCAT_WS(' ', 
    company_name,
    business_sector_standardized,
    COALESCE(extraction_final->>'business_overview', ''),
    COALESCE(extraction_final->>'what_you_sell', '')
  ),
  deal_closed = COALESCE((extraction_final->'dealDynamics'->>'deal_closed')::boolean, false)
WHERE extraction_final IS NOT NULL;

-- Comments for new columns
COMMENT ON COLUMN companies.company_name IS 'Primary company name from YAML extraction';
COMMENT ON COLUMN companies.business_sector_standardized IS 'Standardized business sector for similarity matching';
COMMENT ON COLUMN companies.extraction_final IS 'Complete YAML extraction data (599 fields)';
COMMENT ON COLUMN companies.context_summary IS 'Natural language summary for embedding generation';
COMMENT ON COLUMN companies.embeddings IS '768-dimensional vector for semantic similarity search';
COMMENT ON COLUMN companies.confidence_score IS 'Extraction confidence score (0.0-1.0)';
COMMENT ON COLUMN companies.narrative_richness IS 'Quality score for narrative content (0.0-1.0)';
COMMENT ON COLUMN companies.searchable_text IS 'Combined searchable text from company data';
COMMENT ON COLUMN companies.deal_closed IS 'Whether the company secured a deal';