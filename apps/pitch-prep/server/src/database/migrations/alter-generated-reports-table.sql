-- Migration: Add missing columns to existing generated_reports table
-- Purpose: Extend existing table to support comprehensive report storage

-- Add missing columns if they don't exist
ALTER TABLE generated_reports 
ADD COLUMN IF NOT EXISTS session_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS website_url VARCHAR(500),
ADD COLUMN IF NOT EXISTS business_sector VARCHAR(255),
ADD COLUMN IF NOT EXISTS analysis_type VARCHAR(100) DEFAULT 'comprehensive',
ADD COLUMN IF NOT EXISTS pdf_url VARCHAR(500),
ADD COLUMN IF NOT EXISTS html_url VARCHAR(500),
ADD COLUMN IF NOT EXISTS generation_time INTEGER,
ADD COLUMN IF NOT EXISTS payment_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS payment_status VARCHAR(50),
ADD COLUMN IF NOT EXISTS customer_email VARCHAR(255),
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS accessed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS access_count INTEGER DEFAULT 0;

-- Rename existing columns to match expected naming
ALTER TABLE generated_reports RENAME COLUMN generated_at TO created_at_backup;
UPDATE generated_reports SET created_at = created_at_backup WHERE created_at IS NULL;

-- Update any NULL values for new columns
UPDATE generated_reports SET 
  session_id = COALESCE(session_id, 'legacy-session'),
  analysis_type = COALESCE(analysis_type, 'comprehensive'),
  access_count = COALESCE(access_count, 0),
  accessed_at = COALESCE(accessed_at, CURRENT_TIMESTAMP)
WHERE session_id IS NULL OR analysis_type IS NULL OR access_count IS NULL OR accessed_at IS NULL;

-- Add new indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_generated_reports_session_id ON generated_reports(session_id);
CREATE INDEX IF NOT EXISTS idx_generated_reports_customer_email ON generated_reports(customer_email);
CREATE INDEX IF NOT EXISTS idx_generated_reports_payment_status ON generated_reports(payment_status);

-- Create function to track report access if it doesn't exist
CREATE OR REPLACE FUNCTION track_report_access(report_id_param VARCHAR)
RETURNS void AS $$
BEGIN
  UPDATE generated_reports 
  SET accessed_at = CURRENT_TIMESTAMP,
      access_count = access_count + 1
  WHERE report_id = report_id_param;
END;
$$ LANGUAGE plpgsql;