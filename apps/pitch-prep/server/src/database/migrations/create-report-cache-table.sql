-- Migration: Create report_cache table for unified report caching
-- Purpose: Store generated reports for faster retrieval and reduced API costs

CREATE TABLE IF NOT EXISTS report_cache (
  id SERIAL PRIMARY KEY,
  cache_key VARCHAR(500) UNIQUE NOT NULL,
  request_hash VARCHAR(100) NOT NULL,
  company_name VARCHAR(255) NOT NULL,
  business_sector VARCHAR(255),
  report_type VARCHAR(50) DEFAULT 'comprehensive',
  report_data JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  cache_hits INTEGER DEFAULT 0
);

-- Create indexes for efficient lookups
CREATE INDEX IF NOT EXISTS idx_cache_key ON report_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_company_name ON report_cache(company_name);
CREATE INDEX IF NOT EXISTS idx_created_at ON report_cache(created_at);
CREATE INDEX IF NOT EXISTS idx_last_accessed ON report_cache(last_accessed);

-- Add comment to table
COMMENT ON TABLE report_cache IS 'Cache storage for unified reports to improve performance and reduce API costs';

-- Add comments to columns
COMMENT ON COLUMN report_cache.cache_key IS 'Unique key generated from request parameters';
COMMENT ON COLUMN report_cache.request_hash IS 'Hash of full request for validation';
COMMENT ON COLUMN report_cache.report_data IS 'Complete unified report JSON data';
COMMENT ON COLUMN report_cache.cache_hits IS 'Number of times this cached report was retrieved';

-- Create cleanup function for expired entries
CREATE OR REPLACE FUNCTION cleanup_expired_report_cache()
RETURNS void AS $$
BEGIN
  DELETE FROM report_cache 
  WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql;

-- Optional: Create a scheduled job to run cleanup daily
-- Note: Requires pg_cron extension
-- SELECT cron.schedule('cleanup-report-cache', '0 2 * * *', 'SELECT cleanup_expired_report_cache();');