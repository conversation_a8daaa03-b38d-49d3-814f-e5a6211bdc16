-- Migration: Create generated_reports table for production report storage
-- Purpose: Store generated comprehensive reports for customer access and analytics

CREATE TABLE IF NOT EXISTS generated_reports (
  id SERIAL PRIMARY KEY,
  report_id VARCHAR(255) UNIQUE NOT NULL,
  session_id VARCHAR(255) NOT NULL,
  company_name VARCHAR(255) NOT NULL,
  website_url VARCHAR(500),
  business_sector VARCHAR(255),
  analysis_type VARCHAR(100) DEFAULT 'comprehensive',
  report_status VARCHAR(50) DEFAULT 'completed',
  report_data JSONB NOT NULL,
  pdf_url VARCHAR(500),
  html_url VARCHAR(500),
  generation_time INTEGER, -- Time in seconds to generate report
  payment_id VARCHAR(255),
  payment_status VARCHAR(50),
  customer_email VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  accessed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  access_count INTEGER DEFAULT 0
);

-- <PERSON>reate indexes for efficient lookups
CREATE INDEX IF NOT EXISTS idx_generated_reports_report_id ON generated_reports(report_id);
CREATE INDEX IF NOT EXISTS idx_generated_reports_session_id ON generated_reports(session_id);
CREATE INDEX IF NOT EXISTS idx_generated_reports_company_name ON generated_reports(company_name);
CREATE INDEX IF NOT EXISTS idx_generated_reports_customer_email ON generated_reports(customer_email);
CREATE INDEX IF NOT EXISTS idx_generated_reports_created_at ON generated_reports(created_at);
CREATE INDEX IF NOT EXISTS idx_generated_reports_payment_status ON generated_reports(payment_status);

-- Add table comment
COMMENT ON TABLE generated_reports IS 'Storage for customer-generated comprehensive reports with payment tracking';

-- Add column comments
COMMENT ON COLUMN generated_reports.report_id IS 'Unique identifier for the generated report';
COMMENT ON COLUMN generated_reports.session_id IS 'Analysis session that generated this report';
COMMENT ON COLUMN generated_reports.report_data IS 'Complete report JSON with all analysis sections';
COMMENT ON COLUMN generated_reports.generation_time IS 'Time taken to generate report in seconds';
COMMENT ON COLUMN generated_reports.access_count IS 'Number of times report was accessed by customer';

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_generated_reports_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER trigger_update_generated_reports_updated_at
  BEFORE UPDATE ON generated_reports
  FOR EACH ROW
  EXECUTE FUNCTION update_generated_reports_updated_at();

-- Create function to track report access
CREATE OR REPLACE FUNCTION track_report_access(report_id_param VARCHAR)
RETURNS void AS $$
BEGIN
  UPDATE generated_reports 
  SET accessed_at = CURRENT_TIMESTAMP,
      access_count = access_count + 1
  WHERE report_id = report_id_param;
END;
$$ LANGUAGE plpgsql;