-- Migration: Add enhanced narrative columns for rich storytelling
-- Purpose: Transform from simple embeddings to comprehensive narratives
-- Date: 2025-01-11

-- Add JSONB columns for multi-layered narrative storage
ALTER TABLE shark_tank_companies 
ADD COLUMN IF NOT EXISTS business_dna JSONB,          -- Structured business model and unit economics
ADD COLUMN IF NOT EXISTS dramatic_arc JSONB,          -- Story elements, turning points, emotional moments
ADD COLUMN IF NOT EXISTS shark_dynamics JSONB,        -- Individual reactions, group dynamics, negotiation patterns
ADD COLUMN IF NOT EXISTS strategic_insights JSONB,    -- Success factors, mistakes, lessons learned
ADD COLUMN IF NOT EXISTS contextual_data JSONB,       -- Market timing, competitive landscape, trends
ADD COLUMN IF NOT EXISTS full_narrative TEXT,         -- 2000-3000 word comprehensive story
ADD COLUMN IF NOT EXISTS executive_summary TEXT,      -- 200-word summary for quick reference
ADD COLUMN IF NOT EXISTS searchable_text TEXT,        -- Optimized text for Gemini processing
ADD COLUMN IF NOT EXISTS processing_version VARCHAR(50) DEFAULT 'v1.0', -- Track pipeline version
ADD COLUMN IF NOT EXISTS narrative_quality_score DECIMAL(3,2), -- 0-1 quality metric
ADD COLUMN IF NOT EXISTS narrative_generated_at TIMESTAMP;     -- When narrative was created

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_business_sector ON shark_tank_companies ((business_dna->>'sector'));
CREATE INDEX IF NOT EXISTS idx_business_model ON shark_tank_companies ((business_dna->>'businessModel'));
CREATE INDEX IF NOT EXISTS idx_deal_outcome ON shark_tank_companies ((dramatic_arc->'outcome'->>'result'));
CREATE INDEX IF NOT EXISTS idx_processing_version ON shark_tank_companies (processing_version);

-- Create full-text search index for narrative content
CREATE INDEX IF NOT EXISTS idx_narrative_fulltext ON shark_tank_companies 
USING gin(to_tsvector('english', COALESCE(full_narrative, '') || ' ' || COALESCE(searchable_text, '')));

-- Add comments for documentation
COMMENT ON COLUMN shark_tank_companies.business_dna IS 'Comprehensive business model including sector, revenue model, unit economics, market position';
COMMENT ON COLUMN shark_tank_companies.dramatic_arc IS 'Story elements including founder journey, turning points, emotional moments, negotiation drama';
COMMENT ON COLUMN shark_tank_companies.shark_dynamics IS 'Individual shark reactions, group dynamics, negotiation patterns, memorable quotes';
COMMENT ON COLUMN shark_tank_companies.strategic_insights IS 'Extracted lessons, success factors, mistakes, sector-specific advice';
COMMENT ON COLUMN shark_tank_companies.contextual_data IS 'Market timing, competitive landscape, regulatory environment, trend alignment';
COMMENT ON COLUMN shark_tank_companies.full_narrative IS 'Complete 2000-3000 word magazine-style case study of the company journey';
COMMENT ON COLUMN shark_tank_companies.executive_summary IS 'Concise 200-word summary for quick reference and matching';
COMMENT ON COLUMN shark_tank_companies.searchable_text IS 'Optimized text combining all relevant data for Gemini semantic search';

-- Note: In a future migration, we may remove the 'embedding' column once Gemini search is fully implemented
-- For now, keeping it for backwards compatibility during transition period