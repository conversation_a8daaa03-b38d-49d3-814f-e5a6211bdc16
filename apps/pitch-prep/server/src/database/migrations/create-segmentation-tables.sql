-- Migration: Create tables for transcript segmentation and company extraction
-- Date: Aug 13, 2025
-- Purpose: Support multi-company transcript processing for same-day release

-- 1. Transcript segments table
CREATE TABLE IF NOT EXISTS transcript_segments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  transcript_id VARCHAR(255) NOT NULL,
  video_id VARCHAR(255) NOT NULL,
  company_id UUID REFERENCES shark_tank_companies(id) ON DELETE CASCADE,
  segment_text TEXT NOT NULL,
  segment_number INTEGER NOT NULL,
  start_timestamp VARCHAR(20),
  end_timestamp VARCHAR(20),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure uniqueness per transcript + company + segment
  UNIQUE(transcript_id, company_id, segment_number)
);

-- 2. Company extractions table (versioned)
CREATE TABLE IF NOT EXISTS company_extractions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES shark_tank_companies(id) ON DELETE CASCADE,
  extraction_version VARCHAR(50) NOT NULL,
  
  -- Three mega-phases of extraction (30K tokens total)
  phase_1_data JSONB, -- Core intelligence (10K tokens)
  phase_2_data JSONB, -- Strategic intelligence (10K tokens)  
  phase_3_data JSONB, -- Actionable intelligence (10K tokens)
  
  -- Metadata
  confidence_score FLOAT,
  token_count INTEGER,
  processing_time_ms INTEGER,
  source_segments TEXT[], -- Array of segment IDs used
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Allow multiple extraction versions per company
  UNIQUE(company_id, extraction_version)
);

-- 3. Company search index for fast lookups
CREATE TABLE IF NOT EXISTS company_search_index (
  company_id UUID PRIMARY KEY REFERENCES shark_tank_companies(id) ON DELETE CASCADE,
  search_vector tsvector,
  
  -- Tags for filtering
  sector_tags TEXT[],
  model_tags TEXT[],
  shark_tags TEXT[],
  season_tags TEXT[],
  
  -- Cached computed values for sorting
  confidence_score FLOAT,
  extraction_completeness FLOAT,
  
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. User access log for tracking preview/full access
CREATE TABLE IF NOT EXISTS company_access_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID, -- Can be NULL for anonymous previews
  company_id UUID REFERENCES shark_tank_companies(id),
  access_type VARCHAR(20) NOT NULL, -- 'preview' or 'full'
  ip_address INET,
  user_agent TEXT,
  accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Add missing columns to shark_tank_companies if they don't exist
ALTER TABLE shark_tank_companies 
ADD COLUMN IF NOT EXISTS video_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS founder_names TEXT[],
ADD COLUMN IF NOT EXISTS processing_version VARCHAR(50),
ADD COLUMN IF NOT EXISTS extraction_confidence FLOAT,
ADD COLUMN IF NOT EXISTS processing_timestamp TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS sector VARCHAR(255),
ADD COLUMN IF NOT EXISTS revenue_streams JSONB,
ADD COLUMN IF NOT EXISTS financial_intelligence JSONB,
ADD COLUMN IF NOT EXISTS shark_psychology_intelligence JSONB,
ADD COLUMN IF NOT EXISTS actionable_intelligence JSONB;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_segments_transcript ON transcript_segments(transcript_id);
CREATE INDEX IF NOT EXISTS idx_segments_company ON transcript_segments(company_id);
CREATE INDEX IF NOT EXISTS idx_segments_video ON transcript_segments(video_id);

CREATE INDEX IF NOT EXISTS idx_extractions_company ON company_extractions(company_id);
CREATE INDEX IF NOT EXISTS idx_extractions_version ON company_extractions(extraction_version);
CREATE INDEX IF NOT EXISTS idx_extractions_confidence ON company_extractions(confidence_score DESC);

CREATE INDEX IF NOT EXISTS idx_search_vector ON company_search_index USING GIN(search_vector);
CREATE INDEX IF NOT EXISTS idx_search_sector ON company_search_index USING GIN(sector_tags);
CREATE INDEX IF NOT EXISTS idx_search_sharks ON company_search_index USING GIN(shark_tags);

CREATE INDEX IF NOT EXISTS idx_access_log_user ON company_access_log(user_id);
CREATE INDEX IF NOT EXISTS idx_access_log_company ON company_access_log(company_id);
CREATE INDEX IF NOT EXISTS idx_access_log_time ON company_access_log(accessed_at DESC);

-- Update trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers
CREATE TRIGGER update_transcript_segments_updated_at 
  BEFORE UPDATE ON transcript_segments 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_company_extractions_updated_at 
  BEFORE UPDATE ON company_extractions 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_company_search_index_updated_at 
  BEFORE UPDATE ON company_search_index 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions (adjust as needed)
GRANT ALL ON transcript_segments TO pitch_prep_user;
GRANT ALL ON company_extractions TO pitch_prep_user;
GRANT ALL ON company_search_index TO pitch_prep_user;
GRANT ALL ON company_access_log TO pitch_prep_user;

COMMENT ON TABLE transcript_segments IS 'Stores individual company segments extracted from multi-company transcripts';
COMMENT ON TABLE company_extractions IS 'Stores versioned AI extractions with 3 mega-phases of intelligence';
COMMENT ON TABLE company_search_index IS 'Optimized search index for fast company discovery';
COMMENT ON TABLE company_access_log IS 'Tracks user access to company data for analytics';