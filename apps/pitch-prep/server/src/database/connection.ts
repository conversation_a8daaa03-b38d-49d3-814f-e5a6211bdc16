// Database connection using postgres client
import postgres from 'postgres';

// Use PITCH_PREP_DATABASE_URL for Pitch Prep, fallback to DATABASE_URL, then to default
const connectionString = process.env.PITCH_PREP_DATABASE_URL || 
                        process.env.DATABASE_URL || 
                        'postgresql://nikhilsingh@localhost:5432/pitch_prep_dev';

export const sql = postgres(connectionString, {
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: 20,
  idle_timeout: 20,
  connect_timeout: 60,
});

export default sql;