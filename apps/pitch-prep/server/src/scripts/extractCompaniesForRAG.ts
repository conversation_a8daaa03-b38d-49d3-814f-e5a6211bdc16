/**
 * Extract companies from database for RAG corpus upload
 */

import '../../../../../lib/env-loader.js';
import { db } from '../config/database.js';
import { sharkTankCompanies } from '../schema/index.js';
import { writeFileSync } from 'fs';
import { isNotNull, ne } from 'drizzle-orm';

async function extractCompanies() {
  console.log('📊 Extracting companies from database...\n');
  
  try {
    // Debug database URL
    console.log('Database URL:', process.env.DATABASE_URL);
    console.log('Pitch Prep URL:', process.env.PITCH_PREP_DATABASE_URL);
    
    // Test database connection
    console.log('Testing database connection...');
    const testQuery = await db.select().from(sharkTankCompanies).limit(1);
    console.log('Connection test result:', testQuery.length);
    
    // Get all companies with names
    console.log('Querying all companies...');
    const companies = await db
      .select()
      .from(sharkTankCompanies)
      .orderBy(sharkTankCompanies.season, sharkTankCompanies.episode);
    
    console.log(`Found ${companies.length} companies with names\n`);
    
    // Save to JSON for upload
    const exportData = {
      timestamp: new Date().toISOString(),
      count: companies.length,
      companies: companies
    };
    
    const outputPath = '/tmp/shark-tank-companies-export.json';
    writeFileSync(outputPath, JSON.stringify(exportData, null, 2));
    
    console.log(`✅ Exported to: ${outputPath}`);
    
    // Show sample
    console.log('\nSample companies:');
    companies.slice(0, 3).forEach(company => {
      console.log(`- ${company.name} (S${company.season}E${company.episode})`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error extracting companies:', error);
    process.exit(1);
  }
}

extractCompanies();
