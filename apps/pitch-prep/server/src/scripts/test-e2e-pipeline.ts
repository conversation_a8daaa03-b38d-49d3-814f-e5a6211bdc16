#!/usr/bin/env bun

/**
 * End-to-End Pipeline Test Script
 * 
 * Tests the complete data pipeline flow with minimal test data:
 * 1. Creates mock transcript
 * 2. Processes with AI extraction
 * 3. Stores in PostgreSQL
 * 4. Validates all fields are populated
 */

// Load environment variables FIRST
import { config } from 'dotenv';
import path from 'path';

// Load .env.development from workspace root
const envPath = path.join(__dirname, '../../../../../.env.development');
config({ path: envPath });

console.log('📦 Environment loaded from:', envPath);
console.log('🔑 API Key status:', process.env.GEMINI_API_KEY ? '✅ Found' : '❌ Not found');

import { TranscriptProcessingPipeline } from '../services/transcriptProcessingPipeline';
import { sql } from '../database/connection';
import fs from 'fs/promises';

// Test configuration
const TEST_CONFIG = {
  season: 4,
  videoId: 'test_e2e_001',
  transcriptDir: '/Users/<USER>/giki-ai-workspace/apps/pitch-prep/server/src/transcript-fetcher/data/transcripts/season_4/raw',
  extractedDir: '/Users/<USER>/giki-ai-workspace/data/pitch-prep/pipeline-output/season_4/extracted'
};

async function runE2ETest() {
  console.log('🚀 Starting End-to-End Pipeline Test');
  console.log('=' . repeat(60));
  
  let service: TranscriptProcessingPipeline;
  
  try {
    // Initialize service (environment already loaded from .env.development)
    if (!process.env.GEMINI_API_KEY) {
      console.log('⚠️  Warning: GEMINI_API_KEY not found in environment');
    }
    service = new TranscriptProcessingPipeline();
    
    // Step 1: Create mock transcript
    console.log('\n📝 Step 1: Creating mock transcript...');
    const mockTranscript = createMockTranscript();
    const transcriptPath = path.join(TEST_CONFIG.transcriptDir, `${TEST_CONFIG.videoId}.txt`);
    
    await fs.mkdir(path.dirname(transcriptPath), { recursive: true });
    await fs.writeFile(transcriptPath, mockTranscript);
    console.log(`✅ Mock transcript created at: ${transcriptPath}`);
    
    // Step 2: Extract company data with REAL AI (no mocks!)
    if (process.env.GEMINI_API_KEY) {
      console.log('\n🤖 Step 2: Extracting company data with REAL Gemini AI...');
      console.log('   Using API key:', process.env.GEMINI_API_KEY.substring(0, 10) + '...');
      const extractionResult = await service['extractCompanyDataFromTranscripts'](TEST_CONFIG.season);
      console.log(`✅ Extracted ${extractionResult.extracted_count} companies using real AI`);
      
      if (extractionResult.errors.length > 0) {
        console.log('⚠️  Extraction warnings:', extractionResult.errors);
      }
    } else {
      console.log('\n⚠️  Step 2: No API key found - creating mock data for testing');
      
      // Create mock extracted data
      const mockExtracted = createMockExtractedData();
      const extractedPath = path.join(TEST_CONFIG.extractedDir, `${TEST_CONFIG.videoId}.json`);
      await fs.mkdir(path.dirname(extractedPath), { recursive: true });
      await fs.writeFile(extractedPath, JSON.stringify(mockExtracted, null, 2));
      console.log('✅ Mock extracted data created');
    }
    
    // Step 3: Store in database
    console.log('\n💾 Step 3: Storing in database...');
    const storageResult = await service['storeCompanyDataInDatabase'](TEST_CONFIG.season);
    console.log(`✅ Stored ${storageResult.stored_count} companies`);
    
    if (storageResult.errors.length > 0) {
      console.log('⚠️  Storage warnings:', storageResult.errors);
    }
    
    // Step 4: Verify database storage
    console.log('\n🔍 Step 4: Verifying database storage...');
    const verification = await verifyDatabaseStorage();
    
    if (verification.success) {
      console.log('✅ All fields properly populated:');
      console.log(`   - Company: ${verification.companyName}`);
      console.log(`   - Has extraction_final: ${verification.hasExtractionFinal}`);
      console.log(`   - Has context_summary: ${verification.hasContextSummary}`);
      console.log(`   - Has embeddings: ${verification.hasEmbeddings}`);
      console.log(`   - Embedding dimensions: ${verification.embeddingDimensions}`);
    } else {
      console.log('❌ Database verification failed');
    }
    
    // Step 5: Test pipeline statistics
    console.log('\n📊 Step 5: Testing pipeline statistics...');
    const stats = await service.getPipelineStats();
    console.log('✅ Pipeline statistics retrieved');
    console.log(`   - Total companies: ${stats.total_companies_processed || 0}`);
    console.log(`   - Seasons with data: ${stats.database_stats?.length || 0}`);
    
    // Cleanup
    console.log('\n🧹 Cleaning up test data...');
    await cleanup();
    
    console.log('\n' + '=' . repeat(60));
    console.log('🎉 End-to-End Pipeline Test Completed Successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    
    // Attempt cleanup even on failure
    try {
      await cleanup();
    } catch (cleanupError) {
      console.error('Cleanup failed:', cleanupError);
    }
    
    process.exit(1);
  } finally {
    await sql.end();
  }
  
  process.exit(0);
}

function createMockTranscript(): string {
  return `
Shark Tank India Season 4 Episode Test

[Anupam Mittal]: Welcome to Shark Tank India! Tell us about your company.

[Entrepreneur]: Namaste Sharks! I'm the founder of E2E Test Technologies. We are revolutionizing 
the EdTech space with our AI-powered personalized learning platform for students aged 10-18.

Our platform uses advanced machine learning algorithms to adapt to each student's learning style 
and pace, creating customized learning paths that improve retention by 40%.

[Namita Thapar]: What are your revenue numbers?

[Entrepreneur]: We have an annual revenue of ₹1.2 crores, growing at 30% month-over-month. 
Our gross margin is 70%, and we have 10,000 active paying users. Our customer acquisition 
cost is ₹300, and the lifetime value is ₹3000, giving us a 10x LTV/CAC ratio.

[Aman Gupta]: That's impressive growth. What's your ask?

[Entrepreneur]: We're seeking ₹50 lakhs for 5% equity, valuing the company at ₹10 crores.

[Vineeta Singh]: The EdTech market is very competitive. What's your unique differentiation?

[Entrepreneur]: Our AI technology is patent-pending, and we have exclusive partnerships with 
100+ schools in tier 2 and tier 3 cities. We're the only platform offering vernacular support 
in 8 Indian languages.

[Anupam Mittal]: I like the technology and the market opportunity. I'll offer you ₹50 lakhs 
for 10% equity with a 2% royalty until I recover my investment.

[Aman Gupta]: I'll make a better offer. ₹50 lakhs for 7.5% equity, no royalty.

[Entrepreneur]: Aman, if you can come down to 6% equity, we have a deal.

[Aman Gupta]: Done! Welcome to the Shark family!

[Narrator]: The entrepreneur successfully closed a deal with Aman Gupta for ₹50 lakhs at 6% 
equity, with promises of marketing support and strategic guidance for scaling the business.
  `;
}

function createMockExtractedData() {
  return {
    company_name: 'E2E Test Technologies',
    season: TEST_CONFIG.season,
    episode: 1,
    video_id: TEST_CONFIG.videoId,
    raw_transcript_path: path.join(TEST_CONFIG.transcriptDir, `${TEST_CONFIG.videoId}.txt`),
    
    business_sector: 'Technology',
    sub_sector: 'EdTech',
    product_description: 'AI-powered personalized learning platform for students aged 10-18',
    unique_selling_points: [
      'Patent-pending AI technology',
      'Vernacular support in 8 languages',
      'Exclusive school partnerships'
    ],
    target_market: 'Students aged 10-18 in tier 2 and tier 3 cities',
    business_model: 'B2C SaaS subscription model',
    competitive_advantages: [
      '40% improvement in retention',
      '10x LTV/CAC ratio',
      'First-mover in vernacular EdTech'
    ],
    
    founders: [
      {
        name: 'Test Founder',
        age: 32,
        background: 'IIT graduate, 10 years in EdTech',
        role: 'CEO & Founder'
      }
    ],
    founding_story: 'Started to democratize quality education in smaller cities',
    team_experience: 'Combined 30+ years in education and technology',
    
    financial_metrics: {
      revenue_amount: 12000000,
      revenue_period: 'annual',
      revenue_currency: 'INR',
      monthly_revenue: 1000000,
      gross_margin: 70,
      customer_acquisition_cost: 300,
      lifetime_value: 3000
    },
    
    ask_amount: '₹50 lakh',
    ask_equity: 5,
    pre_money_valuation: 100000000,
    
    deal_outcome: {
      deal_made: true,
      sharks_involved: ['Aman Gupta'],
      final_equity: 6,
      final_amount: 5000000,
      deal_conditions: ['Marketing support', 'Strategic guidance'],
      negotiation_details: 'Negotiated from 10% to 6% equity'
    },
    
    shark_feedback: [
      'Impressive growth metrics',
      'Strong technology differentiation',
      'Good market opportunity'
    ],
    concerns_raised: [
      'Competitive market',
      'Customer retention challenges'
    ],
    
    extraction_quality_score: 95,
    processed_at: new Date().toISOString(),
    ai_model_version: 'gemini-2.0-flash-001',
    processing_duration: 1200,
    confidence_score: 92
  };
}

async function verifyDatabaseStorage() {
  try {
    const result = await sql`
      SELECT 
        company_name,
        season,
        episode,
        extraction_final,
        context_summary,
        embeddings
      FROM companies
      WHERE company_name = 'E2E Test Technologies'
      AND season = ${TEST_CONFIG.season}
    `;
    
    if (result.length === 0) {
      return { success: false };
    }
    
    const company = result[0];
    
    // For vector type, embeddings will be a string like "[0.1,0.2,...]"
    let embeddingDimensions = 0;
    if (company.embeddings) {
      try {
        // Parse the vector string
        const vectorStr = company.embeddings.toString();
        const vectorArray = vectorStr.slice(1, -1).split(',');
        embeddingDimensions = vectorArray.length;
      } catch (e) {
        console.error('Error parsing embeddings:', e);
      }
    }
    
    return {
      success: true,
      companyName: company.company_name,
      hasExtractionFinal: !!company.extraction_final,
      hasContextSummary: !!company.context_summary,
      hasEmbeddings: !!company.embeddings,
      embeddingDimensions
    };
    
  } catch (error) {
    console.error('Verification error:', error);
    return { success: false };
  }
}

async function cleanup() {
  // Clean test files
  const transcriptPath = path.join(TEST_CONFIG.transcriptDir, `${TEST_CONFIG.videoId}.txt`);
  const extractedPath = path.join(TEST_CONFIG.extractedDir, `${TEST_CONFIG.videoId}.json`);
  
  await fs.unlink(transcriptPath).catch(() => {});
  await fs.unlink(extractedPath).catch(() => {});
  
  // Clean database
  await sql`
    DELETE FROM companies 
    WHERE company_name = 'E2E Test Technologies'
    AND season = ${TEST_CONFIG.season}
  `.catch(() => {});
}

// Run the test
runE2ETest();