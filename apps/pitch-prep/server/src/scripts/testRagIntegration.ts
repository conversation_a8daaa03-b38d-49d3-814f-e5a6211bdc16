#!/usr/bin/env bun

/**
 * Test Vertex AI RAG Store Integration
 * Tests the implementation with real GenAI SDK to ensure correct usage
 */

import '../../../../../lib/env-loader.js';
import { GoogleGenAI } from '@google/genai';

async function testRAGStoreIntegration() {
  console.log('🧪 Testing Vertex AI RAG Store Integration...\n');
  
  const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;
  if (!apiKey) {
    console.error('❌ GEMINI_API_KEY or GOOGLE_API_KEY is required');
    process.exit(1);
  }
  
  const genai = new GoogleGenAI({ apiKey });
  
  // Test configuration based on Context7 documentation
  const projectId = process.env.GCP_PROJECT_ID || 'your-project-id';
  const location = process.env.GCP_LOCATION || 'us-central1';
  const ragCorpusId = `projects/${projectId}/locations/${location}/ragCorpora/shark-tank-companies`;
  
  console.log('📚 RAG Corpus ID:', ragCorpusId);
  console.log('');
  
  try {
    // Test 1: Basic RAG Store query
    console.log('Test 1: Basic RAG Store Query');
    console.log('=============================');
    
    const query = `
      Find companies similar to a food delivery startup that:
      - Operates in urban areas
      - Has a B2C model
      - Uses technology for logistics
    `;
    
    const response1 = await genai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: [{ 
        role: 'user', 
        parts: [{ text: query }] 
      }],
      config: {
        tools: [{
          retrieval: {
            vertexRagStore: {
              ragResources: [{ 
                ragCorpus: ragCorpusId 
              }],
              similarityTopK: 5,
              vectorDistanceThreshold: 0.8
            }
          }
        }],
        temperature: 0.1,
        maxOutputTokens: 2000
      }
    });
    
    console.log('✅ Response received:', response1.text?.substring(0, 200) + '...');
    
    // Check for grounding metadata
    const groundingMetadata = response1.candidates?.[0]?.groundingMetadata;
    if (groundingMetadata?.groundingChunks) {
      console.log(`✅ Found ${groundingMetadata.groundingChunks.length} grounded chunks`);
      console.log('Sample chunks:');
      groundingMetadata.groundingChunks.slice(0, 2).forEach((chunk, i) => {
        console.log(`  ${i + 1}. ${chunk.retrievedContext?.title || 'No title'}`);
        console.log(`     ${chunk.retrievedContext?.text?.substring(0, 100)}...`);
      });
    } else {
      console.log('⚠️ No grounding chunks found in response');
    }
    
    console.log('');
    
    // Test 2: Structured output with RAG
    console.log('Test 2: Structured Output with RAG');
    console.log('===================================');
    
    const response2 = await genai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: [{ 
        role: 'user', 
        parts: [{ 
          text: 'Find EdTech companies from Shark Tank and return as JSON' 
        }] 
      }],
      config: {
        tools: [{
          retrieval: {
            vertexRagStore: {
              ragResources: [{ 
                ragCorpus: ragCorpusId 
              }],
              similarityTopK: 10,
              vectorDistanceThreshold: 0.75,
              ragRetrievalConfig: {
                filter: {
                  metadataFilter: 'industry:education OR sector:edtech'
                }
              }
            }
          }
        }],
        temperature: 0.1,
        maxOutputTokens: 2000,
        responseMimeType: 'application/json',
        responseJsonSchema: {
          type: 'object',
          properties: {
            companies: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  name: { type: 'string' },
                  season: { type: 'number' },
                  description: { type: 'string' }
                },
                required: ['name']
              }
            },
            total_found: { type: 'number' }
          },
          required: ['companies', 'total_found']
        }
      }
    });
    
    const result = JSON.parse(response2.text || '{}');
    console.log('✅ Structured response:', JSON.stringify(result, null, 2).substring(0, 300) + '...');
    
    // Test 3: Advanced retrieval config
    console.log('\nTest 3: Advanced Retrieval Configuration');
    console.log('=========================================');
    
    const response3 = await genai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: [{ 
        role: 'user', 
        parts: [{ 
          text: 'Analyze D2C brands that got funding' 
        }] 
      }],
      config: {
        tools: [{
          retrieval: {
            vertexRagStore: {
              ragResources: [{ 
                ragCorpus: ragCorpusId 
              }],
              similarityTopK: 15,
              vectorDistanceThreshold: 0.7,
              ragRetrievalConfig: {
                topK: 20, // Initial retrieval count
                filter: {
                  vectorSimilarityThreshold: 0.65
                },
                hybridSearch: {
                  alpha: 0.5 // Balance between keyword and vector search
                },
                ranking: {
                  llmRanker: {
                    modelName: 'gemini-2.5-flash' // Use LLM for re-ranking
                  }
                }
              }
            }
          }
        }],
        temperature: 0.2,
        maxOutputTokens: 3000
      }
    });
    
    console.log('✅ Advanced retrieval response:', response3.text?.substring(0, 200) + '...');
    
    console.log('\n🎉 All RAG Store integration tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    
    // Provide helpful error messages
    if (error.message?.includes('corpus')) {
      console.log('\n📝 Note: RAG corpus needs to be created first using:');
      console.log('   gcloud ai rag-corpora create shark-tank-companies \\');
      console.log(`     --project=${projectId} \\`);
      console.log(`     --location=${location} \\`);
      console.log('     --display-name="Shark Tank Companies"');
    }
    
    if (error.message?.includes('API key')) {
      console.log('\n📝 Note: Ensure GEMINI_API_KEY is set with Vertex AI permissions');
    }
    
    process.exit(1);
  }
}

// Run the test
testRAGStoreIntegration();