#!/usr/bin/env bun

/**
 * Process Real Shark Tank Companies Through Complete Pipeline
 * 
 * This script processes real Shark Tank transcripts through the entire pipeline:
 * 1. Fetch transcripts (already have them locally)
 * 2. Extract company data using Gemini AI
 * 3. Generate embeddings for similarity matching
 * 4. Store in PostgreSQL
 * 5. Ready for Dr. Paws-like similarity matching
 */

// Load environment variables FIRST
import { config } from 'dotenv';
import path from 'path';

// Load .env.development from workspace root
const envPath = path.join(__dirname, '../../../../../.env.development');
config({ path: envPath });

console.log('🚀 Real Company Processing Pipeline');
console.log('=' .repeat(60));
console.log('📦 Environment loaded from:', envPath);
console.log('🔑 API Key status:', process.env.GEMINI_API_KEY ? '✅ Found' : '❌ Not found');

import { TranscriptProcessingPipeline } from '../services/transcriptProcessingPipeline';
import { sql } from '../database/connection';
import fs from 'fs/promises';

async function processRealCompanies(limit: number = 10) {
  try {
    const service = new TranscriptProcessingPipeline();
    
    // Check how many transcripts we have
    const transcriptDir = path.join(__dirname, '../transcript-fetcher/data/transcripts/season_4/raw');
    const files = await fs.readdir(transcriptDir);
    const transcriptFiles = files.filter(f => f.endsWith('.txt')).slice(0, limit);
    
    console.log(`\n📄 Found ${files.length} transcript files`);
    console.log(`🎯 Processing first ${limit} companies...`);
    console.log('');
    
    // List the companies we're about to process
    console.log('Companies to process:');
    transcriptFiles.forEach((file, index) => {
      const videoId = file.replace('.txt', '');
      console.log(`  ${index + 1}. ${videoId}`);
    });
    
    console.log('\n' + '=' .repeat(60));
    
    // Step 1: Extract company data from transcripts using Gemini AI
    console.log('\n📝 Step 1: Extracting company data with Gemini AI...');
    const extractionResult = await service['extractCompanyDataFromTranscripts'](4); // Season 4
    
    console.log(`✅ Extracted ${extractionResult.extracted_count} companies`);
    if (extractionResult.errors.length > 0) {
      console.log('⚠️  Extraction errors:', extractionResult.errors);
    }
    
    // Step 2: Store in database with embeddings
    console.log('\n💾 Step 2: Storing in database with embeddings...');
    const storageResult = await service['storeCompanyDataInDatabase'](4);
    
    console.log(`✅ Stored ${storageResult.stored_count} companies`);
    if (storageResult.errors.length > 0) {
      console.log('⚠️  Storage errors:', storageResult.errors);
    }
    
    // Step 3: Verify what we have in the database
    console.log('\n🔍 Step 3: Verifying database contents...');
    const companies = await sql`
      SELECT 
        company_name,
        season,
        business_sector_standardized,
        CASE 
          WHEN extraction_final IS NOT NULL THEN '✅'
          ELSE '❌'
        END as has_extraction,
        CASE 
          WHEN context_summary IS NOT NULL THEN '✅'
          ELSE '❌'
        END as has_context,
        CASE 
          WHEN embeddings IS NOT NULL THEN '✅'
          ELSE '❌'
        END as has_embeddings,
        confidence_score
      FROM companies
      WHERE season = 4
      ORDER BY company_name
      LIMIT ${limit}
    `;
    
    console.log('\nCompanies in database:');
    console.log('=' .repeat(80));
    console.log('Company Name                    | Sector      | Data | Context | Embeddings | Score');
    console.log('-' .repeat(80));
    
    companies.forEach(company => {
      const name = (company.company_name || 'Unknown').padEnd(30).substring(0, 30);
      const sector = (company.business_sector_standardized || 'Unknown').padEnd(10).substring(0, 10);
      console.log(
        `${name} | ${sector} | ${company.has_extraction}    | ${company.has_context}       | ${company.has_embeddings}          | ${company.confidence_score || 0}`
      );
    });
    
    // Step 4: Test similarity matching
    console.log('\n🔍 Step 4: Testing similarity matching capabilities...');
    
    // Check if we can do similarity searches
    const testQuery = await sql`
      SELECT COUNT(*) as embeddings_count
      FROM companies
      WHERE embeddings IS NOT NULL
      AND season = 4
    `;
    
    console.log(`\n✅ ${testQuery[0].embeddings_count} companies ready for similarity matching!`);
    
    // Show pipeline statistics
    console.log('\n📊 Pipeline Statistics:');
    const stats = await service.getPipelineStats();
    
    if (stats.database_stats) {
      stats.database_stats.forEach((seasonStat: any) => {
        if (seasonStat.season === 4) {
          console.log(`  Season ${seasonStat.season}:`);
          console.log(`    - Total companies: ${seasonStat.company_count}`);
          console.log(`    - Average quality score: ${Math.round(seasonStat.avg_quality_score || 0)}`);
          console.log(`    - Average confidence: ${Math.round(seasonStat.avg_confidence_score || 0)}`);
          console.log(`    - Deals made: ${seasonStat.deals_accepted}`);
          console.log(`    - No deals: ${seasonStat.no_deals}`);
        }
      });
    }
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 Pipeline Processing Complete!');
    console.log('\nThese companies are now ready for:');
    console.log('  ✅ Similarity matching (like Dr. Paws analysis)');
    console.log('  ✅ Shark compatibility analysis');
    console.log('  ✅ Market insights generation');
    console.log('  ✅ Report generation');
    
  } catch (error) {
    console.error('\n❌ Pipeline failed:', error);
  } finally {
    await sql.end();
  }
}

// Get command line argument for number of companies to process
const numCompanies = parseInt(process.argv[2] || '10');

// Run the pipeline
processRealCompanies(numCompanies);