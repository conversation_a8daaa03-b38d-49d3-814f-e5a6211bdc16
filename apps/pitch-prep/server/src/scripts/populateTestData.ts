#!/usr/bin/env bun

/**
 * <PERSON><PERSON>t to populate test Shark Tank companies data
 * This creates sample data for testing similarity matching
 */

import '../../../../../lib/env-loader.js';
import { db } from '../config/database';
import { sharkTankCompanies } from '../schema';

const testCompanies = [
  {
    name: 'boAt',
    season: 1,
    episode: 3,
    industry: 'Consumer Electronics',
    askAmount: 7500000, // ₹75 lakhs
    askEquity: 15,
    dealAmount: 7500000,
    dealEquity: 18,
    dealMade: true,
    sharks: ['<PERSON><PERSON>'],
    pitch: 'Affordable premium audio products for Indian millennials. Started in 2016, now valued at over $1.4 billion.',
    outcome: 'Deal with <PERSON><PERSON>. Became one of India\'s fastest growing consumer electronics brands.',
    searchableText: null
  },
  {
    name: 'BluePine Foods',
    season: 1,
    episode: 1,
    industry: 'Food & Beverages',
    askAmount: 5000000, // ₹50 lakhs
    askEquity: 4,
    dealAmount: 5000000,
    dealEquity: 5,
    dealMade: true,
    sharks: ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    pitch: 'Premium frozen momos and dumplings brand. Solving the problem of quality frozen food in India.',
    outcome: 'Deal with three sharks. Expanded distribution across India post-show.',
    searchableText: null
  },
  {
    name: 'Cosiq',
    season: 1,
    episode: 2,
    industry: 'Beauty & Personal Care',
    askAmount: 5000000,
    askEquity: 7,
    dealAmount: 5000000,
    dealEquity: 25,
    dealMade: true,
    sharks: ['Vineeta Singh'],
    pitch: 'Intelligent skincare using AI to provide personalized recommendations based on skin analysis.',
    outcome: 'Deal with Vineeta Singh at higher equity but gained valuable mentorship.',
    searchableText: null
  },
  {
    name: 'Urban Animal',
    season: 4,
    episode: 34,
    industry: 'Pet Care',
    askAmount: 4500000, // ₹45 lakhs
    askEquity: 5,
    dealAmount: null,
    dealEquity: null,
    dealMade: false,
    sharks: [],
    pitch: 'Dog DNA testing kit that screens for 134 genetic diseases. First of its kind in India.',
    outcome: 'No deal. Sharks felt market was too early stage and niche for investment.',
    searchableText: null
  },
  {
    name: 'Tagz Foods',
    season: 1,
    episode: 5,
    industry: 'Food & Beverages',
    askAmount: 7000000, // ₹70 lakhs
    askEquity: 1,
    dealAmount: 7000000,
    dealEquity: 2.75,
    dealMade: true,
    sharks: ['Ashneer Grover'],
    pitch: 'Urban millennials snacking brand with healthier potato chips made from real vegetables.',
    outcome: 'Deal with Ashneer. Post-show, expanded to 6000+ retail stores.',
    searchableText: null
  },
  {
    name: 'Heart Up My Sleeves',
    season: 1,
    episode: 4,
    industry: 'Fashion & Apparel',
    askAmount: 2500000, // ₹25 lakhs
    askEquity: 10,
    dealAmount: 2500000,
    dealEquity: 30,
    dealMade: true,
    sharks: ['Vineeta Singh', 'Anupam Mittal'],
    pitch: 'Detachable sleeves and collars to transform any outfit. Sustainable fashion solution.',
    outcome: 'Deal with Vineeta and Anupam. Gained visibility but faced scaling challenges.',
    searchableText: null
  },
  {
    name: 'Peeschute',
    season: 1,
    episode: 2,
    industry: 'Health & Hygiene',
    askAmount: 7500000,
    askEquity: 4,
    dealAmount: 7500000,
    dealEquity: 6,
    dealMade: true,
    sharks: ['Aman Gupta'],
    pitch: 'Disposable urine bags for women. Solving the problem of lack of clean public toilets.',
    outcome: 'Deal with Aman Gupta. Product gained traction in travel and healthcare sectors.',
    searchableText: null
  },
  {
    name: 'The Yarn Bazaar',
    season: 1,
    episode: 6,
    industry: 'B2B Marketplace',
    askAmount: 5000000,
    askEquity: 2,
    dealAmount: 10000000, // ₹1 crore
    dealEquity: 10,
    dealMade: true,
    sharks: ['Anupam Mittal', 'Aman Gupta', 'Vineeta Singh', 'Peyush Bansal'],
    pitch: 'B2B marketplace for yarn connecting manufacturers with buyers. Tech-enabled textile trading.',
    outcome: 'Biggest deal of season 1 with all sharks except Ashneer. Platform grew 10x post-show.',
    searchableText: null
  },
  {
    name: 'Revamp Moto',
    season: 1,
    episode: 7,
    industry: 'Automotive',
    askAmount: 10000000, // ₹1 crore
    askEquity: 1,
    dealAmount: 10000000,
    dealEquity: 1.5,
    dealMade: true,
    sharks: ['Anupam Mittal', 'Aman Gupta'],
    pitch: 'Electric bike conversion kits. Convert any petrol bike to electric in 3 hours.',
    outcome: 'Deal with Anupam and Aman. Pioneering sustainable mobility solutions.',
    searchableText: null
  },
  {
    name: 'Skippi Ice Pops',
    season: 1,
    episode: 8,
    industry: 'Food & Beverages',
    askAmount: 4500000,
    askEquity: 5,
    dealAmount: 10000000,
    dealEquity: 15,
    dealMade: true,
    sharks: ['All five sharks'],
    pitch: 'Natural ice pops brand targeting kids and adults. No artificial colors or flavors.',
    outcome: 'Historic deal with all 5 sharks. Became fastest growing ice pop brand in India.',
    searchableText: null
  }
];

async function populateTestData() {
  console.log('🗄️ Populating test Shark Tank companies...\n');
  
  try {
    // Insert test companies
    for (const company of testCompanies) {
      console.log(`Adding: ${company.name} (Season ${company.season})`);
      
      // Generate searchable text
      const searchableText = `
        ${company.name} ${company.industry} 
        ${company.pitch} 
        ${company.outcome} 
        Season ${company.season} Episode ${company.episode}
        Ask: ₹${company.askAmount / 100000} lakhs for ${company.askEquity}%
        ${company.dealMade ? `Deal: ₹${company.dealAmount! / 100000} lakhs for ${company.dealEquity}%` : 'No deal'}
        ${company.sharks.length > 0 ? `Sharks: ${company.sharks.join(', ')}` : ''}
      `.replace(/\s+/g, ' ').trim();
      
      await db.insert(sharkTankCompanies).values({
        ...company,
        searchableText,
        websiteData: {},
        transcriptData: {},
        metadata: {
          source: 'manual_entry',
          addedAt: new Date().toISOString()
        }
      });
    }
    
    console.log(`\n✅ Successfully added ${testCompanies.length} test companies!`);
    
    // Verify insertion
    const allCompanies = await db.select().from(sharkTankCompanies);
    console.log(`\n📊 Total companies in database: ${allCompanies.length}`);
    
    // Show industry distribution
    const industries = allCompanies.reduce((acc, c) => {
      const industry = c.industry || 'Unknown';
      acc[industry] = (acc[industry] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    console.log('\n📈 Industry Distribution:');
    Object.entries(industries).forEach(([industry, count]) => {
      console.log(`   ${industry}: ${count} companies`);
    });
    
  } catch (error) {
    console.error('❌ Error populating test data:', error);
    process.exit(1);
  }
}

// Run the script
populateTestData().then(() => {
  console.log('\n✨ Test data population complete!');
  process.exit(0);
}).catch(console.error);