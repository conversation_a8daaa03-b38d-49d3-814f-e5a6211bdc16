#!/usr/bin/env bun

/**
 * Pre-fill database with raw transcript records
 * Creates basic company records for all transcript files
 */

import '../../../../../lib/env-loader.js';
import { sql } from '../database/connection.js';
import { readdirSync, statSync } from 'fs';
import { join, basename } from 'path';

interface TranscriptFile {
  id: string;
  season: number;
  episode?: number;
  videoId?: string;
  filePath: string;
}

function getAllTranscriptFiles(dir: string): TranscriptFile[] {
  const files: TranscriptFile[] = [];
  
  function scanDirectory(currentDir: string) {
    const items = readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = join(currentDir, item);
      if (statSync(fullPath).isDirectory()) {
        scanDirectory(fullPath);
      } else if (item.endsWith('.text')) {
        const id = basename(item, '.text');
        
        // Extract season from filename (S1_E001_xyz -> season 1)
        const seasonMatch = id.match(/S(\d+)_/);
        const episodeMatch = id.match(/_E(\d+)_/);
        const videoIdMatch = id.match(/_([A-Za-z0-9_-]{11})$/);
        
        if (seasonMatch) {
          files.push({
            id,
            season: parseInt(seasonMatch[1]),
            episode: episodeMatch ? parseInt(episodeMatch[1]) : undefined,
            videoId: videoIdMatch ? videoIdMatch[1] : undefined,
            filePath: fullPath
          });
        }
      }
    }
  }
  
  scanDirectory(dir);
  return files;
}

async function prefillDatabase() {
  console.log('🚀 Pre-filling database with transcript records...');
  
  try {
    // Get all transcript files
    const transcriptFiles = getAllTranscriptFiles('data/transcripts');
    console.log(`📂 Found ${transcriptFiles.length} transcript files`);
    
    // Check existing companies with transcript IDs
    const existingCompanies = await sql`
      SELECT metadata->>'transcriptId' as transcript_id 
      FROM shark_tank_companies 
      WHERE metadata ? 'transcriptId'
    `;
    const existingTranscriptIds = new Set(existingCompanies.map(c => c.transcript_id).filter(Boolean));
    console.log(`💾 Database has ${existingTranscriptIds.size} existing transcript-based companies`);
    
    // Filter out files that already have database entries
    const newFiles = transcriptFiles.filter(file => !existingTranscriptIds.has(file.id));
    console.log(`➕ Need to create ${newFiles.length} new records`);
    
    if (newFiles.length === 0) {
      console.log('✅ All transcript files already have database entries');
      return;
    }
    
    // Insert new company records with basic info
    let insertedCount = 0;
    const batchSize = 50;
    
    for (let i = 0; i < newFiles.length; i += batchSize) {
      const batch = newFiles.slice(i, i + batchSize);
      
      console.log(`📝 Inserting batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(newFiles.length / batchSize)} (${batch.length} records)...`);
      
      // Prepare batch insert - generate UUIDs and store transcript filename in metadata
      const values = batch.map(file => ({
        season: file.season,
        episode: file.episode || 0,
        name: 'Unknown', // Will be extracted by pipeline
        founder_names: '[]',
        business_description: '',
        sector: '',
        metadata: JSON.stringify({
          transcriptId: file.id,  // Store original transcript filename
          videoId: file.videoId,
          transcriptPath: file.filePath,
          dataSource: 'transcript_file'
        })
      }));
      
      // Insert batch - let database generate UUIDs
      for (const value of values) {
        await sql`
          INSERT INTO shark_tank_companies (
            season, episode, name, founder_names, 
            business_description, sector, metadata
          ) VALUES (
            ${value.season}, ${value.episode}, ${value.name}, 
            ${value.founder_names}, ${value.business_description}, ${value.sector}, ${value.metadata}
          )
        `;
      }
      
      insertedCount += batch.length;
      console.log(`  ✅ Inserted ${batch.length} records (${insertedCount}/${newFiles.length} total)`);
      
      // Small delay between batches
      if (i + batchSize < newFiles.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    console.log(`\n🎉 Pre-fill complete!`);
    console.log(`📊 Summary:`);
    console.log(`  - Transcript files found: ${transcriptFiles.length}`);
    console.log(`  - Already in database: ${existingTranscriptIds.size}`);
    console.log(`  - New records created: ${insertedCount}`);
    console.log(`  - Total database records: ${existingTranscriptIds.size + insertedCount}`);
    
    // Group by season for summary
    const seasonCounts = transcriptFiles.reduce((acc, file) => {
      acc[file.season] = (acc[file.season] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);
    
    console.log(`\n📈 By Season:`);
    for (const [season, count] of Object.entries(seasonCounts).sort()) {
      console.log(`  Season ${season}: ${count} files`);
    }
    
  } catch (error) {
    console.error('❌ Pre-fill failed:', error);
    throw error;
  } finally {
    await sql.end();
  }
}

// Run if called directly
if (import.meta.main) {
  prefillDatabase().catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}

export { prefillDatabase };