#!/usr/bin/env bun

/**
 * Upload Shark Tank Company Data to Vertex AI RAG Corpus
 * This script reads company data and uploads it to the created RAG corpus
 */

import '../../../../../lib/env-loader.js';
import { GoogleGenAI } from '@google/genai';
import { readFileSync } from 'fs';
import path from 'path';

// The RAG corpus ID from our creation
const RAG_CORPUS_ID = 'projects/273348121056/locations/us-central1/ragCorpora/7186056155423047680';

async function uploadDataToRAGCorpus() {
  console.log('📚 Uploading Shark Tank Company Data to RAG Corpus...\n');
  
  const accessToken = await getAccessToken();
  const companiesData = loadCompanyData();
  
  console.log(`Found ${companiesData.length} companies to upload\n`);
  
  // Upload companies in batches
  const batchSize = 10;
  for (let i = 0; i < companiesData.length; i += batchSize) {
    const batch = companiesData.slice(i, i + batchSize);
    console.log(`Uploading batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(companiesData.length/batchSize)}...`);
    
    await uploadBatch(batch, accessToken);
    
    // Small delay between batches
    if (i + batchSize < companiesData.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  console.log('\n✅ All company data uploaded successfully!');
  console.log('\nNext steps:');
  console.log('1. Test RAG integration with queries');
  console.log('2. Update ragSimilarityService to use corpus ID:', RAG_CORPUS_ID);
}

async function getAccessToken(): Promise<string> {
  return new Promise((resolve, reject) => {
    const { exec } = require('child_process');
    exec('gcloud auth print-access-token', (error, stdout, stderr) => {
      if (error) {
        reject(error);
      } else {
        resolve(stdout.trim());
      }
    });
  });
}

function loadCompanyData(): any[] {
  // Load the exported JSON data from the extraction script
  const dataPath = '/tmp/shark-tank-companies-export.json';
  
  try {
    const rawData = readFileSync(dataPath, 'utf-8');
    const data = JSON.parse(rawData);
    
    console.log(`Loading ${data.companies.length} companies from ${dataPath}`);
    
    // Transform to RAG format
    return data.companies.map((company: any) => ({
      id: `company-${company.name?.toLowerCase().replace(/\s+/g, '-') || Math.random().toString(36).substr(2, 9)}`,
      content: {
        text: generateCompanyText(company),
        metadata: {
          companyName: company.name || 'Unknown',
          season: company.season,
          episode: company.episode,
          industry: company.industry || 'Unknown',
          askAmount: company.askAmount || company.ask_amount,
          askEquity: company.askEquity || company.ask_equity,
          dealAmount: company.dealAmount || company.deal_amount,
          dealEquity: company.dealEquity || company.deal_equity,
          dealMade: company.dealMade || company.deal_made,
          sharks: company.sharks,
          pitch: company.pitch || '',
          outcome: company.outcome || ''
        }
      }
    }));
  } catch (error) {
    console.error('Error loading company data:', error);
    // Return sample data for testing
    return getSampleCompanies();
  }
}

function generateCompanyText(company: any): string {
  const parts = [
    `Company: ${company.name || 'Unknown Company'}`,
    `Season ${company.season}, Episode ${company.episode || 'Unknown'}`,
    company.industry ? `Industry: ${company.industry}` : '',
    company.pitch ? `Pitch: ${company.pitch}` : '',
    company.ask_amount ? `Ask Amount: ₹${company.ask_amount}` : '',
    company.ask_equity ? `Equity Offered: ${company.ask_equity}%` : '',
    company.deal_amount ? `Deal Amount: ₹${company.deal_amount}` : '',
    company.deal_equity ? `Deal Equity: ${company.deal_equity}%` : '',
    company.deal_made !== undefined ? `Deal: ${company.deal_made ? 'Deal Made' : 'No Deal'}` : '',
    company.sharks && typeof company.sharks === 'object' ? `Sharks: ${JSON.stringify(company.sharks)}` : '',
    company.outcome ? `Outcome: ${company.outcome}` : ''
  ];
  
  return parts.filter(Boolean).join('\n');
}

function getSampleCompanies(): any[] {
  // Sample companies for testing if main data not available
  return [
    {
      id: 'company-hoovu-fresh',
      content: {
        text: `Company: Hoovu Fresh
Season 1, Episode 1
Industry: AgriTech
Category: Flower Supply Chain
Business Model: B2B marketplace for fresh flowers
Pitch: Direct flower sourcing platform connecting farmers to retailers
Ask Amount: ₹50,00,000
Equity Offered: 5%
Valuation: ₹10,00,00,000
Deal: Closed
Sharks: Aman Gupta, Anupam Mittal`,
        metadata: {
          companyName: 'Hoovu Fresh',
          season: 1,
          episode: 1,
          industry: 'AgriTech',
          businessModel: 'B2B marketplace',
          askAmount: 5000000,
          equity: 5,
          valuation: *********,
          dealClosed: true
        }
      }
    },
    {
      id: 'company-booz-scooters',
      content: {
        text: `Company: Booz Scooters
Season 1, Episode 1  
Industry: Mobility
Category: Electric Vehicles
Business Model: Rental electric scooters
Pitch: App-based electric scooter rental service for urban mobility
Ask Amount: ₹40,00,000
Equity Offered: 15%
Valuation: ₹2,66,66,667
Deal: No Deal`,
        metadata: {
          companyName: 'Booz Scooters',
          season: 1,
          episode: 1,
          industry: 'Mobility',
          businessModel: 'Rental service',
          askAmount: 4000000,
          equity: 15,
          valuation: 26666667,
          dealClosed: false
        }
      }
    }
  ];
}

async function uploadBatch(documents: any[], accessToken: string) {
  const projectId = process.env.GCP_PROJECT_ID || 'rezolve-poc';
  const location = process.env.GCP_LOCATION || 'us-central1';
  
  for (const doc of documents) {
    try {
      const requestBody = {
        ragFile: {
          displayName: doc.id,
          description: `Shark Tank company: ${doc.content.metadata.companyName}`,
          directUploadSource: {
            content: Buffer.from(doc.content.text).toString('base64'),
            mimeType: 'text/plain'
          }
        }
      };
      
      const response = await fetch(
        `https://${location}-aiplatform.googleapis.com/v1beta1/${RAG_CORPUS_ID}/ragFiles`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        }
      );
      
      if (!response.ok) {
        const error = await response.text();
        console.error(`Failed to upload ${doc.id}:`, error);
      } else {
        console.log(`  ✅ Uploaded: ${doc.content.metadata.companyName}`);
      }
      
      // Small delay between uploads
      await new Promise(resolve => setTimeout(resolve, 200));
      
    } catch (error) {
      console.error(`Error uploading ${doc.id}:`, error);
    }
  }
}

// Run the upload
uploadDataToRAGCorpus().catch(console.error);