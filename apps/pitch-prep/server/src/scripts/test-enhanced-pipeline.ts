#!/usr/bin/env bun

/**
 * Test Enhanced Pipeline Service
 * Validates that the pipeline produces data in the exact format expected by the UI
 */

// Load environment variables FIRST
import { config } from 'dotenv';
import path from 'path';

const envPath = path.join(__dirname, '../../../../../.env.development');
config({ path: envPath });

console.log('🧪 Testing Enhanced Pipeline Service');
console.log('=' .repeat(60));
console.log('📦 Environment loaded from:', envPath);
console.log('🔑 API Key status:', process.env.GEMINI_API_KEY ? '✅ Found' : '❌ Not found');

// import { EnhancedPipelineService } from '../services/enhancedPipelineService'; // Service removed in consolidation
import { PipelineEnhancedReportService } from '../services/pipelineEnhancedReportService';
import { dataTransformationService } from '../services/dataTransformationService';
import { sql } from '../database/connection';
import type { 
  PipelineCompanyOutput, 
  UISimilarCompany,
  SharkTankCompanyDB 
} from '../types/pipeline-contracts';
import { 
  validatePipelineOutput, 
  validateUIData 
} from '../types/pipeline-contracts';

async function testEnhancedPipeline() {
  try {
    const pipeline = new PipelineEnhancedReportService();
    
    console.log('\n🚀 Step 1: Process 3 test transcripts');
    console.log('-' .repeat(60));
    
    // Process a small batch for testing
    const result = await pipeline.processTranscriptsToUI(4, 3); // Season 4, 3 transcripts
    
    console.log(`\n📊 Processing Results:`);
    console.log(`  - Processed: ${result.processed}`);
    console.log(`  - Stored: ${result.stored}`);
    if (result.errors.length > 0) {
      console.log(`  - Errors: ${result.errors.join(', ')}`);
    }
    
    // Step 2: Validate database storage
    console.log('\n🔍 Step 2: Validate Database Storage');
    console.log('-' .repeat(60));
    
    const dbRecords = await sql<SharkTankCompanyDB[]>`
      SELECT * FROM shark_tank_companies 
      WHERE season = 4 
      ORDER BY created_at DESC 
      LIMIT 3
    `;
    
    console.log(`Found ${dbRecords.length} records in shark_tank_companies table`);
    
    if (dbRecords.length > 0) {
      const record = dbRecords[0];
      console.log('\nSample record structure:');
      console.log(`  - name: ${record.name}`);
      console.log(`  - season: ${record.season}`);
      console.log(`  - episode: ${record.episode}`);
      console.log(`  - industry: ${record.industry}`);
      console.log(`  - askAmount: ₹${record.askAmount?.toLocaleString('en-IN')}`);
      console.log(`  - askEquity: ${record.askEquity}%`);
      console.log(`  - dealMade: ${record.dealMade}`);
      console.log(`  - sharks: ${JSON.stringify(record.sharks)}`);
      console.log(`  - Has embedding: ${record.embedding ? '✅' : '❌'}`);
      console.log(`  - Has transcript data: ${record.transcriptData ? '✅' : '❌'}`);
    }
    
    // Step 3: Test transformation to UI format
    console.log('\n🎨 Step 3: Test UI Transformation');
    console.log('-' .repeat(60));
    
    if (dbRecords.length > 0) {
      const uiData: UISimilarCompany[] = dbRecords.map(record => 
        dataTransformationService.databaseToUISimilarCompany(record, 0.85)
      );
      
      console.log('\nUI Format Output:');
      uiData.forEach((company, index) => {
        console.log(`\n${index + 1}. ${company.name}`);
        console.log(`   ${company.season}, ${company.episode}`);
        console.log(`   Deal: ${company.dealAmount} for ${company.equity}`);
        console.log(`   Sharks: ${company.sharks.join(', ') || 'None'}`);
        console.log(`   Similarity: ${company.similarity}%`);
        console.log(`   Outcome: ${company.outcome}`);
        
        // Validate UI data structure
        const isValid = validateUIData(company);
        console.log(`   Valid UI format: ${isValid ? '✅' : '❌'}`);
      });
    }
    
    // Step 4: Test similarity matching
    console.log('\n🔍 Step 4: Test Similarity Matching');
    console.log('-' .repeat(60));
    
    // Simulate a user company
    const userCompany = {
      companyName: 'Test Tech Startup',
      sector: 'Technology',
      businessModel: 'SaaS',
      whatYouSell: 'Software solutions'
    };
    
    console.log('User company:', userCompany);
    
    // Query for similar companies
    const similarCompanies = await sql`
      SELECT 
        name, season, episode, industry,
        ask_amount, ask_equity, deal_amount, deal_equity,
        deal_made, sharks, outcome,
        CASE 
          WHEN industry ILIKE '%tech%' THEN 0.90
          WHEN industry ILIKE '%software%' THEN 0.85
          ELSE 0.70
        END as similarity_score
      FROM shark_tank_companies
      WHERE season = 4
      ORDER BY similarity_score DESC
      LIMIT 4
    `;
    
    console.log(`\nFound ${similarCompanies.length} similar companies`);
    
    // Transform to UI format
    const uiSimilarCompanies = similarCompanies.map(company => ({
      name: company.name,
      season: `Season ${company.season}`,
      episode: `Episode ${company.episode || 0}`,
      dealAmount: dataTransformationService.formatCurrency(
        company.deal_made ? company.deal_amount : company.ask_amount
      ),
      equity: dataTransformationService.formatEquity(
        company.deal_made ? company.deal_equity : company.ask_equity
      ),
      sharks: company.sharks || [],
      similarity: Math.round(company.similarity_score * 100),
      outcome: company.outcome || 'Pitched on Shark Tank'
    }));
    
    console.log('\nSimilar companies for UI:');
    uiSimilarCompanies.forEach((company, index) => {
      console.log(`${index + 1}. ${company.name} - ${company.similarity}% match`);
    });
    
    // Step 5: Validate complete data flow
    console.log('\n✅ Step 5: Data Flow Validation');
    console.log('-' .repeat(60));
    
    const validationResults = {
      pipelineExtraction: result.processed > 0,
      databaseStorage: dbRecords.length > 0,
      uiTransformation: uiSimilarCompanies.length > 0,
      fieldMapping: uiSimilarCompanies.every(validateUIData),
      similarityMatching: similarCompanies.length > 0
    };
    
    console.log('Validation Results:');
    Object.entries(validationResults).forEach(([key, value]) => {
      console.log(`  - ${key}: ${value ? '✅ PASS' : '❌ FAIL'}`);
    });
    
    const allPassed = Object.values(validationResults).every(v => v);
    
    console.log('\n' + '=' .repeat(60));
    if (allPassed) {
      console.log('🎉 SUCCESS: Pipeline produces correct format for UI!');
    } else {
      console.log('⚠️  WARNING: Some validation checks failed');
    }
    
    // Get pipeline stats
    const stats = await pipeline.getPipelineStats();
    console.log('\n📊 Pipeline Statistics:');
    console.log(JSON.stringify(stats, null, 2));
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
  } finally {
    await sql.end();
  }
}

// Run the test
testEnhancedPipeline();