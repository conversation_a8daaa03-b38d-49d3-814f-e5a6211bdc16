import { pgTable, uuid, text, timestamp, decimal, boolean, integer, jsonb, pgEnum } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Enums
export const userRoleEnum = pgEnum('user_role', ['free', 'basic', 'premium']);
export const analysisStatusEnum = pgEnum('analysis_status', ['pending', 'processing', 'completed', 'failed']);
export const paymentStatusEnum = pgEnum('payment_status', ['pending', 'processing', 'completed', 'failed', 'refunded']);
export const reportTierEnum = pgEnum('report_tier', ['basic', 'comprehensive', 'consultation']);

// Users table
export const users = pgTable('users', {
  id: uuid('id').defaultRandom().primaryKey(),
  email: text('email').unique().notNull(),
  passwordHash: text('password_hash').notNull(),
  name: text('name'),
  phone: text('phone'),
  role: userRoleEnum('role').default('free').notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  isEmailVerified: boolean('is_email_verified').default(false).notNull(),
  lastLoginAt: timestamp('last_login_at'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Companies table (user's business)
export const companies = pgTable('companies', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  name: text('name').notNull(),
  website: text('website'),
  industry: text('industry'),
  stage: text('stage'),
  description: text('description'),
  revenue: decimal('revenue', { precision: 15, scale: 2 }),
  fundingRaised: decimal('funding_raised', { precision: 15, scale: 2 }),
  teamSize: integer('team_size'),
  location: text('location'),
  searchableText: text('searchable_text'), // Combined text for GenAI semantic search
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Shark Tank Companies Database
export const sharkTankCompanies = pgTable('shark_tank_companies', {
  id: uuid('id').defaultRandom().primaryKey(),
  name: text('name').notNull(),
  season: integer('season').notNull(),
  episode: integer('episode'),
  industry: text('industry'),
  askAmount: decimal('ask_amount', { precision: 15, scale: 2 }),
  askEquity: decimal('ask_equity', { precision: 5, scale: 2 }),
  dealAmount: decimal('deal_amount', { precision: 15, scale: 2 }),
  dealEquity: decimal('deal_equity', { precision: 5, scale: 2 }),
  dealMade: boolean('deal_made').default(false),
  sharks: jsonb('sharks'), // Array of shark names who invested
  pitch: text('pitch'),
  outcome: text('outcome'),
  websiteData: jsonb('website_data'),
  transcriptData: jsonb('transcript_data'),
  searchableText: text('searchable_text'), // Combined text for GenAI semantic retrieval
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Analysis Sessions
export const analysisSessions = pgTable('analysis_sessions', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  companyId: uuid('company_id').references(() => companies.id).notNull(),
  status: analysisStatusEnum('status').default('pending').notNull(),
  tier: reportTierEnum('tier').default('basic').notNull(),
  
  // Analysis Data
  websiteAnalysis: jsonb('website_analysis'),
  businessMetrics: jsonb('business_metrics'),
  similarCompanies: jsonb('similar_companies'),
  sharkCompatibility: jsonb('shark_compatibility'),
  pitchAnalysis: jsonb('pitch_analysis'),
  
  // Processing
  startedAt: timestamp('started_at'),
  completedAt: timestamp('completed_at'),
  processingTime: integer('processing_time'), // in seconds
  errorMessage: text('error_message'),
  
  // Report
  reportUrl: text('report_url'),
  reportData: jsonb('report_data'),
  
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Payments
export const payments = pgTable('payments', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  sessionId: uuid('session_id').references(() => analysisSessions.id),
  
  // Payment Details
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  currency: text('currency').default('INR').notNull(),
  status: paymentStatusEnum('status').default('pending').notNull(),
  
  // Razorpay Integration
  razorpayOrderId: text('razorpay_order_id'),
  razorpayPaymentId: text('razorpay_payment_id'),
  razorpaySignature: text('razorpay_signature'),
  
  // Metadata
  description: text('description'),
  metadata: jsonb('metadata'),
  
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Shark Preferences (extracted from transcripts)
export const sharkPreferences = pgTable('shark_preferences', {
  id: uuid('id').defaultRandom().primaryKey(),
  sharkName: text('shark_name').notNull(),
  industries: jsonb('industries'), // Preferred industries
  dealRanges: jsonb('deal_ranges'), // Typical investment ranges
  equityPreferences: jsonb('equity_preferences'), // Equity percentage preferences
  commonQuestions: jsonb('common_questions'), // Frequently asked questions
  dealPatterns: jsonb('deal_patterns'), // Historical deal patterns
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Practice Sessions (voice-based practice)
export const practiceSessions = pgTable('practice_sessions', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  sessionId: uuid('session_id').references(() => analysisSessions.id),
  
  // Practice Data
  questions: jsonb('questions'), // Array of practice questions
  responses: jsonb('responses'), // User's recorded responses
  feedback: jsonb('feedback'), // AI-generated feedback
  score: decimal('score', { precision: 5, scale: 2 }),
  duration: integer('duration'), // in seconds
  
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Sessions table for auth
export const sessions = pgTable('sessions', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  token: text('token').unique().notNull(),
  refreshToken: text('refresh_token').unique(),
  expiresAt: timestamp('expires_at').notNull(),
  ipAddress: text('ip_address'),
  userAgent: text('user_agent'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Define relations
export const usersRelations = relations(users, ({ many }) => ({
  companies: many(companies),
  analysisSessions: many(analysisSessions),
  payments: many(payments),
  practiceSessions: many(practiceSessions),
  sessions: many(sessions)
}));

export const companiesRelations = relations(companies, ({ one, many }) => ({
  user: one(users, {
    fields: [companies.userId],
    references: [users.id]
  }),
  analysisSessions: many(analysisSessions)
}));

export const analysisSessionsRelations = relations(analysisSessions, ({ one, many }) => ({
  user: one(users, {
    fields: [analysisSessions.userId],
    references: [users.id]
  }),
  company: one(companies, {
    fields: [analysisSessions.companyId],
    references: [companies.id]
  }),
  payments: many(payments),
  practiceSessions: many(practiceSessions)
}));

export const paymentsRelations = relations(payments, ({ one }) => ({
  user: one(users, {
    fields: [payments.userId],
    references: [users.id]
  }),
  session: one(analysisSessions, {
    fields: [payments.sessionId],
    references: [analysisSessions.id]
  })
}));

export const practiceSessionsRelations = relations(practiceSessions, ({ one }) => ({
  user: one(users, {
    fields: [practiceSessions.userId],
    references: [users.id]
  }),
  analysisSession: one(analysisSessions, {
    fields: [practiceSessions.sessionId],
    references: [analysisSessions.id]
  })
}));