import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from '../schema/index.js';

// Database connection configuration - Use Pitch Prep specific URL
const connectionString = process.env.PITCH_PREP_DATABASE_URL || 
  process.env.DATABASE_URL || 
  'postgresql://nikhilsingh@localhost:5432/pitch_prep_dev';

// Create postgres connection
const queryClient = postgres(connectionString, {
  max: 20, // Connection pool size
  idle_timeout: 20,
  connect_timeout: 10,
  prepare: false // Required for some cloud environments
});

// Create drizzle instance with schema
export const db = drizzle(queryClient, { schema });

// Connection health check
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await queryClient`SELECT 1`;
    console.log('✅ Database connected successfully');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

// Graceful shutdown
export async function closeDatabaseConnection(): Promise<void> {
  await queryClient.end();
  console.log('Database connection closed');
}