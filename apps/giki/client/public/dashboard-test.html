<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Design Test - Giki AI</title>
    <style>
        /* Import the exact same design system CSS that React components use */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Brand Colors - Giki AI Green Theme */
            --brand-primary: #295343;
            --brand-secondary: #1A3F5F;
            --brand-accent: #10b981;
            --brand-warning: #f59e0b;
            
            /* Gradients */
            --gradient-primary: linear-gradient(135deg, #295343 0%, #1A3F5F 100%);
            --gradient-hover: linear-gradient(135deg, #1e3d30 0%, #132d45 100%);
            
            /* Text Colors */
            --text-primary: #1a1a1a;
            --text-secondary: #6b7280;
            --text-inverse: #ffffff;
            --text-muted: #9ca3af;
            
            /* Background Colors */
            --bg-white: #ffffff;
            --bg-light: #f9fafb;
            --bg-gray: #f3f4f6;
            
            /* Semantic Colors */
            --color-success: #10b981;
            --color-warning: #f59e0b;
            --color-error: #ef4444;
            --color-info: #3b82f6;
            
            /* Borders */
            --border-light: #e5e7eb;
            --border-medium: #d1d5db;
            --border-dark: #9ca3af;
            
            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 25px rgba(41, 83, 67, 0.3);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Inter', sans-serif;
            background: var(--bg-light);
            color: var(--text-primary);
        }

        /* Dashboard Styles - Direct copy from prototype dashboard.html */
        .app-container {
            display: flex;
            height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 260px;
            background: var(--bg-white);
            border-right: 1px solid var(--border-light);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 24px;
            border-bottom: 1px solid var(--border-light);
        }

        .logo {
            font-size: 28px;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-menu {
            padding: 24px 16px;
            flex: 1;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            margin-bottom: 4px;
            border-radius: 8px;
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.2s;
            font-weight: 500;
        }

        .nav-item:hover {
            background: var(--bg-light);
            color: var(--text-primary);
        }

        .nav-item.active {
            background: var(--gradient-primary);
            color: var(--text-inverse);
        }

        .nav-icon {
            margin-right: 12px;
            font-size: 20px;
        }

        .sidebar-footer {
            padding: 24px;
            border-top: 1px solid var(--border-light);
        }

        .quick-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .quick-action {
            display: flex;
            align-items: center;
            padding: 10px;
            background: var(--bg-light);
            border-radius: 8px;
            text-decoration: none;
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .quick-action:hover {
            background: var(--gradient-primary);
            color: var(--text-inverse);
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Top Bar */
        .topbar {
            background: var(--bg-white);
            border-bottom: 1px solid var(--border-light);
            padding: 16px 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .topbar-left {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 700;
        }

        .date-selector {
            padding: 8px 16px;
            border: 1px solid var(--border-light);
            border-radius: 8px;
            background: var(--bg-white);
            font-size: 14px;
            color: var(--text-secondary);
        }

        .topbar-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .notification-btn, .help-btn {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: 1px solid var(--border-light);
            background: var(--bg-white);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 20px;
            transition: all 0.2s;
        }

        .notification-btn:hover, .help-btn:hover {
            background: var(--bg-light);
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 16px;
            border: 1px solid var(--border-light);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .user-menu:hover {
            background: var(--bg-light);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-inverse);
            font-weight: 600;
        }

        /* Dashboard Content */
        .dashboard-content {
            flex: 1;
            padding: 32px;
            overflow-y: auto;
        }

        /* Welcome Section */
        .welcome-section {
            background: var(--gradient-primary);
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 32px;
            color: var(--text-inverse);
        }

        .welcome-title {
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 8px;
        }

        .welcome-subtitle {
            font-size: 18px;
            opacity: 0.9;
        }

        /* Metrics Grid */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .metric-card {
            background: var(--bg-white);
            border-radius: 12px;
            padding: 24px;
            border: 1px solid var(--border-light);
            transition: all 0.3s;
        }

        .metric-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 500;
        }

        .metric-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .trend-up {
            color: var(--color-success);
        }

        .trend-down {
            color: var(--color-error);
        }

        .metric-value {
            font-size: 32px;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }

        .metric-subtitle {
            color: var(--text-secondary);
            font-size: 12px;
        }

        /* Main Dashboard Panels */
        .dashboard-panels {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
        }

        /* Panels */
        .panel {
            background: var(--bg-white);
            border-radius: 12px;
            border: 1px solid var(--border-light);
            overflow: hidden;
        }

        .panel-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 700;
        }

        .panel-action {
            color: var(--brand-primary);
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: opacity 0.2s;
        }

        .panel-action:hover {
            opacity: 0.8;
        }

        .panel-content {
            padding: 24px;
        }

        /* Transaction List */
        .transaction-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: var(--bg-light);
            border-radius: 8px;
            transition: all 0.2s;
        }

        .transaction-item:hover {
            background: var(--bg-gray);
        }

        .transaction-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .icon-food {
            background: #fef3c7;
            color: var(--color-warning);
        }

        .icon-transport {
            background: #dbeafe;
            color: var(--color-info);
        }

        .icon-income {
            background: #d1fae5;
            color: var(--color-success);
        }

        .transaction-details {
            display: flex;
            flex-direction: column;
        }

        .transaction-name {
            font-weight: 600;
            font-size: 14px;
        }

        .transaction-category {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .transaction-amount {
            font-weight: 700;
            font-size: 16px;
        }

        .amount-debit {
            color: var(--color-error);
        }

        .amount-credit {
            color: var(--color-success);
        }

        /* Quick Stats Panel */
        .quick-stats {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-light);
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-name {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .stat-value {
            font-weight: 700;
            font-size: 16px;
        }

        /* Progress Bar */
        .progress-section {
            margin-top: 24px;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .progress-label {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .progress-value {
            font-size: 14px;
            font-weight: 600;
            color: var(--brand-primary);
        }

        .progress-bar {
            height: 8px;
            background: var(--bg-light);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: var(--gradient-primary);
            border-radius: 4px;
            transition: width 0.3s;
        }

        /* Mobile Responsive */
        @media (max-width: 1024px) {
            .dashboard-panels {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                display: none;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .dashboard-content {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">GIKI.AI</div>
            </div>
            
            <nav class="nav-menu">
                <a href="#" class="nav-item active">
                    <span class="nav-icon">📊</span>
                    Dashboard
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-icon">📁</span>
                    Upload
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-icon">💳</span>
                    Transactions
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-icon">📈</span>
                    Reports
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-icon">🏷️</span>
                    Categories
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-icon">⚙️</span>
                    Settings
                </a>
            </nav>
            
            <div class="sidebar-footer">
                <div class="quick-actions">
                    <a href="#" class="quick-action">
                        <span style="margin-right: 8px;">📤</span>
                        Export Data
                    </a>
                    <a href="#" class="quick-action">
                        <span style="margin-right: 8px;">🎯</span>
                        Train AI
                    </a>
                </div>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="topbar">
                <div class="topbar-left">
                    <h1 class="page-title">Dashboard</h1>
                    <select class="date-selector">
                        <option>Last 30 days</option>
                        <option>Last 3 months</option>
                        <option>Last 6 months</option>
                        <option>Last year</option>
                        <option>All time</option>
                    </select>
                </div>
                
                <div class="topbar-right">
                    <button class="notification-btn">🔔</button>
                    <button class="help-btn">❓</button>
                    <div class="user-menu">
                        <div class="user-avatar">JD</div>
                        <span>John Doe</span>
                        <span>▼</span>
                    </div>
                </div>
            </header>
            
            <!-- Dashboard Content -->
            <div class="dashboard-content">
                <!-- Welcome Section -->
                <div class="welcome-section">
                    <h2 class="welcome-title">Your Books Are Getting Organized!</h2>
                    <p class="welcome-subtitle">12,450 transactions processed • 87.3% accuracy achieved</p>
                </div>
                
                <!-- Metrics Grid -->
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-header">
                            <span class="metric-label">Total Transactions</span>
                            <span class="metric-trend trend-up">↑ 12%</span>
                        </div>
                        <div class="metric-value">12,450</div>
                        <div class="metric-subtitle">Across 5 bank accounts</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-header">
                            <span class="metric-label">Auto-Categorized</span>
                            <span class="metric-trend trend-up">↑ 5%</span>
                        </div>
                        <div class="metric-value">10,853</div>
                        <div class="metric-subtitle">87.3% accuracy rate</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-header">
                            <span class="metric-label">Total Revenue</span>
                            <span class="metric-trend trend-up">↑ 18%</span>
                        </div>
                        <div class="metric-value">₹24.5L</div>
                        <div class="metric-subtitle">This financial year</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-header">
                            <span class="metric-label">Total Expenses</span>
                            <span class="metric-trend trend-down">↓ 3%</span>
                        </div>
                        <div class="metric-value">₹18.2L</div>
                        <div class="metric-subtitle">Well controlled</div>
                    </div>
                </div>
                
                <!-- Dashboard Panels -->
                <div class="dashboard-panels">
                    <!-- Recent Activity Panel -->
                    <div class="panel">
                        <div class="panel-header">
                            <h3 class="panel-title">Recent Transactions</h3>
                            <a href="#" class="panel-action">View All →</a>
                        </div>
                        <div class="panel-content">
                            <div class="transaction-list">
                                <div class="transaction-item">
                                    <div class="transaction-info">
                                        <div class="transaction-icon icon-food">🍔</div>
                                        <div class="transaction-details">
                                            <span class="transaction-name">Swiggy Food Order</span>
                                            <span class="transaction-category">Food & Dining</span>
                                        </div>
                                    </div>
                                    <span class="transaction-amount amount-debit">-₹458</span>
                                </div>
                                
                                <div class="transaction-item">
                                    <div class="transaction-info">
                                        <div class="transaction-icon icon-transport">🚗</div>
                                        <div class="transaction-details">
                                            <span class="transaction-name">Uber India</span>
                                            <span class="transaction-category">Transportation</span>
                                        </div>
                                    </div>
                                    <span class="transaction-amount amount-debit">-₹234</span>
                                </div>
                                
                                <div class="transaction-item">
                                    <div class="transaction-info">
                                        <div class="transaction-icon icon-income">💰</div>
                                        <div class="transaction-details">
                                            <span class="transaction-name">Client Payment</span>
                                            <span class="transaction-category">Business Income</span>
                                        </div>
                                    </div>
                                    <span class="transaction-amount amount-credit">+₹85,000</span>
                                </div>
                                
                                <div class="transaction-item">
                                    <div class="transaction-info">
                                        <div class="transaction-icon icon-food">☕</div>
                                        <div class="transaction-details">
                                            <span class="transaction-name">Starbucks</span>
                                            <span class="transaction-category">Food & Dining</span>
                                        </div>
                                    </div>
                                    <span class="transaction-amount amount-debit">-₹320</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Stats Panel -->
                    <div class="panel">
                        <div class="panel-header">
                            <h3 class="panel-title">Quick Stats</h3>
                            <a href="#" class="panel-action">Details →</a>
                        </div>
                        <div class="panel-content">
                            <div class="quick-stats">
                                <div class="stat-item">
                                    <span class="stat-name">Pending Review</span>
                                    <span class="stat-value">234</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-name">This Month</span>
                                    <span class="stat-value">1,234</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-name">Categories Used</span>
                                    <span class="stat-value">47</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-name">Accuracy Rate</span>
                                    <span class="stat-value">87.3%</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-name">Time Saved</span>
                                    <span class="stat-value">20 hrs</span>
                                </div>
                            </div>
                            
                            <div class="progress-section">
                                <div class="progress-header">
                                    <span class="progress-label">Monthly Goal</span>
                                    <span class="progress-value">87%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 87%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>