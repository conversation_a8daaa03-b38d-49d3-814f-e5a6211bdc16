/**
 * Login Page - Two Panel Layout
 * Following prototype design: /design/giki-ai/prototype/login.html
 */
import React, { useEffect, useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Loader2, Eye, EyeOff } from 'lucide-react';

export default function LoginPage() {
  const navigate = useNavigate();
  const { login, isAuthenticated, isLoading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // If user is already authenticated, redirect to dashboard
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  // Don't render the login form if user is already authenticated
  if (isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--giki-primary)] mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    
    try {
      const result = await login(email, password);
      
      if (result.success) {
        navigate('/dashboard', { replace: true });
      } else {
        setError(result.error || 'Login failed');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="login-container">
      {/* Left Panel - Branding */}
      <div className="branding-panel">
        <div className="branding-content">
          <div className="logo-large">GIKI.AI</div>
          <h1 className="text-hero">Welcome Back</h1>
          <p className="text-body">Transform your financial chaos into organized books with AI-powered categorization.</p>
          
          <div className="features-list">
            <div className="feature-item">
              <span className="feature-icon">✓</span>
              <span>87% Accuracy Rate</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">✓</span>
              <span>60 Second Processing</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">✓</span>
              <span>10+ Export Formats</span>
            </div>
          </div>
        </div>
        
        {/* Background Pattern */}
        <div className="pattern-overlay"></div>
      </div>
      
      {/* Right Panel - Login Form */}
      <div className="login-panel">
        <div className="login-form-container">
          <div className="form-header">
            <h2 className="text-heading">Sign In</h2>
            <p className="text-secondary">Enter your credentials to access your account</p>
          </div>
          
          <form className="login-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="email">Email Address</label>
              <input 
                type="email" 
                id="email" 
                name="email" 
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>" 
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="password">Password</label>
              <div className="password-input-container">
                <input 
                  type={showPassword ? 'text' : 'password'} 
                  id="password" 
                  name="password" 
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password" 
                  required
                />
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="password-toggle"
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {showPassword ? (
                    <EyeOff className="w-5 h-5" />
                  ) : (
                    <Eye className="w-5 h-5" />
                  )}
                </button>
              </div>
            </div>
            
            <div className="form-options">
              <label className="checkbox-container">
                <input 
                  type="checkbox" 
                  name="remember" 
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                />
                <span>Remember me</span>
              </label>
              <Link to="/forgot-password" className="forgot-link">Forgot password?</Link>
            </div>

            {error && (
              <div className="error-message" style={{
                background: '#fef2f2',
                border: '1px solid #fca5a5',
                color: '#dc2626',
                padding: '12px',
                borderRadius: '8px',
                fontSize: '0.875rem',
                marginBottom: '16px'
              }}>
                {error}
              </div>
            )}
            
            <button type="submit" className="btn btn-primary btn-full" disabled={loading || isLoading}>
              {loading || isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Signing In...
                </>
              ) : (
                'Sign In'
              )}
            </button>
            
            <div className="divider">
              <span>OR</span>
            </div>
            
            <button type="button" className="btn btn-secondary btn-full">
              <span>🔷</span> Continue with Google
            </button>
          </form>
          
          <div className="form-footer">
            <p>Don't have an account? <Link to="/signup" className="link-primary">Sign up</Link></p>
          </div>
        </div>
      </div>
    </div>
  );
}