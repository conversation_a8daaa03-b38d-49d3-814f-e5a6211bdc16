import React from 'react';
import { Link } from 'react-router-dom';

export default function LandingPage() {
  return (
    <>
      {/* Skip to Content for Accessibility */}
      <a href="#main" className="skip-to-content">Skip to main content</a>
      
      {/* Navigation */}
      <nav>
        <div className="nav-container">
          <div className="logo">GIKI.AI</div>
          <div className="nav-links">
            <a href="#features">Features</a>
            <a href="#how-it-works">How It Works</a>
            <a href="#pricing">Pricing</a>
            <a href="#about">About</a>
            <Link to="/login" className="btn btn-primary">Sign In</Link>
          </div>
        </div>
      </nav>
      
      {/* Hero Section */}
      <section className="hero" id="main">
        <div className="hero-container">
          <div className="hero-content">
            <h1>Turn Messy Statements Into Organized Books</h1>
            <p>AI-powered financial categorization that transforms your chaotic bank statements into clean, organized accounting books in under 60 seconds.</p>
            
            <div className="hero-buttons">
              <Link to="/signup" className="btn-hero">Start Free Trial</Link>
              <a href="#demo" className="btn-hero-secondary">Watch Demo</a>
            </div>
            
            <div className="hero-stats">
              <div className="stat">
                <span className="stat-value">87%</span>
                <span className="stat-label">Accuracy Rate</span>
              </div>
              <div className="stat">
                <span className="stat-value">60 Sec</span>
                <span className="stat-label">Processing Time</span>
              </div>
              <div className="stat">
                <span className="stat-value">10+</span>
                <span className="stat-label">Export Formats</span>
              </div>
            </div>
          </div>
          
          <div className="hero-visual">
            <div className="dashboard-preview">
              <div className="preview-header">
                <span className="preview-title">Financial Dashboard</span>
                <span className="preview-badge">Live Demo</span>
              </div>
              <div className="preview-metrics">
                <div className="metric-card">
                  <div className="metric-label">Total Transactions</div>
                  <div className="metric-value">12,450</div>
                </div>
                <div className="metric-card">
                  <div className="metric-label">Categorized</div>
                  <div className="metric-value">10,853</div>
                </div>
                <div className="metric-card">
                  <div className="metric-label">Accuracy</div>
                  <div className="metric-value">87.3%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Features Section */}
      <section className="features" id="features">
        <div className="features-container">
          <div className="section-header">
            <h2>Powerful Financial Organization</h2>
            <p>Everything you need to transform messy data into professional books</p>
          </div>
          
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">🤖</div>
              <h3 className="feature-title">AI-Powered Categorization</h3>
              <p className="feature-description">Advanced machine learning algorithms automatically categorize transactions with 87%+ accuracy, learning from your corrections.</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">⚡</div>
              <h3 className="feature-title">60-Second Processing</h3>
              <p className="feature-description">Process thousands of transactions in under a minute. No more manual data entry or hours of categorization work.</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">📊</div>
              <h3 className="feature-title">Smart Reports</h3>
              <p className="feature-description">Generate P&L statements, balance sheets, and custom reports instantly. Export to Tally, QuickBooks, and more.</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">🔒</div>
              <h3 className="feature-title">Bank-Grade Security</h3>
              <p className="feature-description">End-to-end encryption, SOC 2 compliance, and secure data handling. Your financial data is always protected.</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">🎯</div>
              <h3 className="feature-title">Industry-Specific Rules</h3>
              <p className="feature-description">Pre-configured categorization rules for retail, services, manufacturing, and more Indian business types.</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">📱</div>
              <h3 className="feature-title">Multi-Format Support</h3>
              <p className="feature-description">Upload PDFs, Excel files, CSV exports from any bank. We handle all formats seamlessly.</p>
            </div>
          </div>
        </div>
      </section>
      
      {/* How It Works */}
      <section className="how-it-works" id="how-it-works">
        <div className="features-container">
          <div className="section-header">
            <h2>How It Works</h2>
            <p>From messy statements to organized books in 4 simple steps</p>
          </div>
          
          <div className="steps-container">
            <div className="step">
              <div className="step-number">1</div>
              <h3 className="step-title">Upload Statements</h3>
              <p className="step-description">Drag and drop your bank statements in any format</p>
            </div>
            
            <div className="step">
              <div className="step-number">2</div>
              <h3 className="step-title">AI Categorizes</h3>
              <p className="step-description">Our AI automatically categorizes all transactions</p>
            </div>
            
            <div className="step">
              <div className="step-number">3</div>
              <h3 className="step-title">Review & Adjust</h3>
              <p className="step-description">Quickly review and make any necessary adjustments</p>
            </div>
            
            <div className="step">
              <div className="step-number">4</div>
              <h3 className="step-title">Export Books</h3>
              <p className="step-description">Download clean, organized books in your preferred format</p>
            </div>
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="cta">
        <h2>Ready to Organize Your Books?</h2>
        <p>Join thousands of Indian businesses already saving 20+ hours every month</p>
        <Link to="/signup" className="btn-hero">Start Your Free Trial</Link>
      </section>
      
      {/* Footer */}
      <footer>
        <div className="footer-container">
          <div className="footer-column">
            <h4>Product</h4>
            <a href="#features">Features</a>
            <a href="#pricing">Pricing</a>
            <a href="#security">Security</a>
            <a href="#api">API</a>
          </div>
          
          <div className="footer-column">
            <h4>Company</h4>
            <a href="#about">About Us</a>
            <a href="#careers">Careers</a>
            <a href="#blog">Blog</a>
            <a href="#contact">Contact</a>
          </div>
          
          <div className="footer-column">
            <h4>Resources</h4>
            <a href="#docs">Documentation</a>
            <a href="#help">Help Center</a>
            <a href="#tutorials">Video Tutorials</a>
            <a href="#templates">Templates</a>
          </div>
          
          <div className="footer-column">
            <h4>Legal</h4>
            <a href="/privacy">Privacy Policy</a>
            <a href="/terms">Terms of Service</a>
            <a href="#cookies">Cookie Policy</a>
            <a href="#gdpr">GDPR</a>
          </div>
        </div>
        
        <div className="footer-bottom">
          <p>&copy; 2024 Giki AI. All rights reserved. Made with ❤️ in India</p>
        </div>
      </footer>
    </>
  );
}