/**
 * Dashboard Page - Prototype Design Implementation
 * Following prototype design: /design/giki-ai/prototype/dashboard.html
 */
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

export default function DashboardPage() {
  const [dateRange, setDateRange] = useState('Last 30 days');
  const [metrics, setMetrics] = useState({
    totalTransactions: 12450,
    autoCategorized: 10853,
    totalRevenue: 2450000,
    totalExpenses: 1820000,
    accuracy: 87.3,
    pendingReview: 234,
    thisMonth: 1234,
    categoriesUsed: 47,
    timeSaved: 20
  });

  const [recentTransactions] = useState([
    {
      id: 1,
      icon: '🍔',
      iconClass: 'icon-food',
      name: 'Swiggy Food Order',
      category: 'Food & Dining',
      amount: -458,
      type: 'debit'
    },
    {
      id: 2,
      icon: '🚗',
      iconClass: 'icon-transport',
      name: 'Uber India',
      category: 'Transportation',
      amount: -234,
      type: 'debit'
    },
    {
      id: 3,
      icon: '💰',
      iconClass: 'icon-income',
      name: 'Client Payment',
      category: 'Business Income',
      amount: 85000,
      type: 'credit'
    },
    {
      id: 4,
      icon: '☕',
      iconClass: 'icon-food',
      name: 'Starbucks',
      category: 'Food & Dining',
      amount: -320,
      type: 'debit'
    }
  ]);

  const formatCurrency = (amount: number) => {
    if (amount >= 100000) {
      return `₹${(amount / 100000).toFixed(1)}L`;
    }
    return `₹${amount.toLocaleString()}`;
  };

  const formatAmount = (amount: number, type: string) => {
    const prefix = type === 'credit' ? '+' : '-';
    return `${prefix}${formatCurrency(Math.abs(amount))}`;
  };

  return (
    <div className="app-container">
      {/* Sidebar */}
      <aside className="sidebar">
        <div className="sidebar-header">
          <div className="logo">GIKI.AI</div>
        </div>
        
        <nav className="nav-menu">
          <Link to="/dashboard" className="nav-item active">
            <span className="nav-icon">📊</span>
            Dashboard
          </Link>
          <Link to="/upload" className="nav-item">
            <span className="nav-icon">📁</span>
            Upload
          </Link>
          <Link to="/transactions" className="nav-item">
            <span className="nav-icon">💳</span>
            Transactions
          </Link>
          <Link to="/reports" className="nav-item">
            <span className="nav-icon">📈</span>
            Reports
          </Link>
          <Link to="/categories" className="nav-item">
            <span className="nav-icon">🏷️</span>
            Categories
          </Link>
          <Link to="/settings" className="nav-item">
            <span className="nav-icon">⚙️</span>
            Settings
          </Link>
        </nav>
        
        <div className="sidebar-footer">
          <div className="quick-actions">
            <a href="#" className="quick-action">
              <span style={{marginRight: '8px'}}>📤</span>
              Export Data
            </a>
            <a href="#" className="quick-action">
              <span style={{marginRight: '8px'}}>🎯</span>
              Train AI
            </a>
          </div>
        </div>
      </aside>
      
      {/* Main Content */}
      <main className="main-content">
        {/* Top Bar */}
        <header className="topbar">
          <div className="topbar-left">
            <h1 className="page-title">Dashboard</h1>
            <select 
              className="date-selector"
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
            >
              <option>Last 30 days</option>
              <option>Last 3 months</option>
              <option>Last 6 months</option>
              <option>Last year</option>
              <option>All time</option>
            </select>
          </div>
          
          <div className="topbar-right">
            <button className="notification-btn">🔔</button>
            <button className="help-btn">❓</button>
            <div className="user-menu">
              <div className="user-avatar">JD</div>
              <span>John Doe</span>
              <span>▼</span>
            </div>
          </div>
        </header>
        
        {/* Dashboard Content */}
        <div className="dashboard-content">
          {/* Welcome Section */}
          <div className="welcome-section">
            <h2 className="welcome-title">Your Books Are Getting Organized!</h2>
            <p className="welcome-subtitle">
              {metrics.totalTransactions.toLocaleString()} transactions processed • {metrics.accuracy}% accuracy achieved
            </p>
          </div>
          
          {/* Metrics Grid */}
          <div className="metrics-grid">
            <div className="metric-card">
              <div className="metric-header">
                <span className="metric-label">Total Transactions</span>
                <span className="metric-trend trend-up">↑ 12%</span>
              </div>
              <div className="metric-value">{metrics.totalTransactions.toLocaleString()}</div>
              <div className="metric-subtitle">Across 5 bank accounts</div>
            </div>
            
            <div className="metric-card">
              <div className="metric-header">
                <span className="metric-label">Auto-Categorized</span>
                <span className="metric-trend trend-up">↑ 5%</span>
              </div>
              <div className="metric-value">{metrics.autoCategorized.toLocaleString()}</div>
              <div className="metric-subtitle">{metrics.accuracy}% accuracy rate</div>
            </div>
            
            <div className="metric-card">
              <div className="metric-header">
                <span className="metric-label">Total Revenue</span>
                <span className="metric-trend trend-up">↑ 18%</span>
              </div>
              <div className="metric-value">{formatCurrency(metrics.totalRevenue)}</div>
              <div className="metric-subtitle">This financial year</div>
            </div>
            
            <div className="metric-card">
              <div className="metric-header">
                <span className="metric-label">Total Expenses</span>
                <span className="metric-trend trend-down">↓ 3%</span>
              </div>
              <div className="metric-value">{formatCurrency(metrics.totalExpenses)}</div>
              <div className="metric-subtitle">Well controlled</div>
            </div>
          </div>
          
          {/* Dashboard Panels */}
          <div className="dashboard-panels">
            {/* Recent Activity Panel */}
            <div className="panel">
              <div className="panel-header">
                <h3 className="panel-title">Recent Transactions</h3>
                <Link to="/transactions" className="panel-action">View All →</Link>
              </div>
              <div className="panel-content">
                <div className="transaction-list">
                  {recentTransactions.map((transaction) => (
                    <div key={transaction.id} className="transaction-item">
                      <div className="transaction-info">
                        <div className={`transaction-icon ${transaction.iconClass}`}>
                          {transaction.icon}
                        </div>
                        <div className="transaction-details">
                          <span className="transaction-name">{transaction.name}</span>
                          <span className="transaction-category">{transaction.category}</span>
                        </div>
                      </div>
                      <span className={`transaction-amount ${transaction.type === 'credit' ? 'amount-credit' : 'amount-debit'}`}>
                        {formatAmount(transaction.amount, transaction.type)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            {/* Quick Stats Panel */}
            <div className="panel">
              <div className="panel-header">
                <h3 className="panel-title">Quick Stats</h3>
                <Link to="/reports" className="panel-action">Details →</Link>
              </div>
              <div className="panel-content">
                <div className="quick-stats">
                  <div className="stat-item">
                    <span className="stat-name">Pending Review</span>
                    <span className="stat-value">{metrics.pendingReview}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-name">This Month</span>
                    <span className="stat-value">{metrics.thisMonth.toLocaleString()}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-name">Categories Used</span>
                    <span className="stat-value">{metrics.categoriesUsed}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-name">Accuracy Rate</span>
                    <span className="stat-value">{metrics.accuracy}%</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-name">Time Saved</span>
                    <span className="stat-value">{metrics.timeSaved} hrs</span>
                  </div>
                </div>
                
                <div className="progress-section">
                  <div className="progress-header">
                    <span className="progress-label">Monthly Goal</span>
                    <span className="progress-value">87%</span>
                  </div>
                  <div className="progress-bar">
                    <div className="progress-fill" style={{width: '87%'}}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}