import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';

export default function SignupPage() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }
    
    if (!formData.agreeToTerms) {
      setError('Please agree to the terms and conditions');
      return;
    }

    setLoading(true);
    
    try {
      // TODO: Implement actual signup API call
      console.log('Signup attempt:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Redirect to login on success
      navigate('/login');
    } catch (err) {
      setError('An error occurred during signup');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  return (
    <div className="auth-layout">
      {/* Left Side - Brand & Features */}
      <div className="auth-hero">
        {/* Logo */}
        <div className="flex items-center mb-12">
          <div className="w-8 h-8 rounded-full mr-3" style={{
            background: 'linear-gradient(45deg, #00ff87, #60efff, #ff6b6b, #feca57)'
          }}></div>
          <h1 className="text-2xl font-semibold">GIKI.AI</h1>
        </div>

        {/* Main Heading */}
        <h2 className="hero-title">
          Join Thousands of Smart Businesses
        </h2>
        
        <p className="hero-subtitle">
          Start organizing your finances with AI today.
        </p>

        {/* Feature List */}
        <div className="feature-list">
          {[
            '87% accuracy rate guaranteed',
            '60-second processing time', 
            '10+ export formats',
            'Bank-grade security',
            '24/7 customer support'
          ].map((feature, index) => (
            <div key={index} className="feature-item">
              <div className="feature-icon">✓</div>
              <span>{feature}</span>
            </div>
          ))}
        </div>

        <p className="text-lg mb-8" style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
          No setup fees. Cancel anytime.
        </p>

        {/* Testimonial */}
        <div className="testimonial">
          <blockquote>
            "Saved us 20+ hours every month on bookkeeping"
          </blockquote>
          <cite>— Business Owner</cite>
        </div>
      </div>

      {/* Right Side - Auth Form */}
      <div className="auth-card">
        <div className="auth-card-header">
          <h3 className="auth-card-title">
            Create Your Account
          </h3>
          <p className="auth-card-subtitle">
            Start your free trial today
          </p>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-4">
            <div className="form-group">
              <label htmlFor="firstName" className="form-label">
                First Name
              </label>
              <input
                id="firstName"
                name="firstName"
                type="text"
                required
                value={formData.firstName}
                onChange={handleInputChange}
                className="form-input"
                placeholder="John"
              />
            </div>

            <div className="form-group">
              <label htmlFor="lastName" className="form-label">
                Last Name
              </label>
              <input
                id="lastName"
                name="lastName"
                type="text"
                required
                value={formData.lastName}
                onChange={handleInputChange}
                className="form-input"
                placeholder="Doe"
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="email" className="form-label">
              Email Address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              required
              value={formData.email}
              onChange={handleInputChange}
              className="form-input"
              placeholder="<EMAIL>"
            />
          </div>

          <div className="form-group">
            <label htmlFor="password" className="form-label">
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              required
              value={formData.password}
              onChange={handleInputChange}
              className="form-input"
              placeholder="••••••••••••••••"
              minLength={8}
            />
          </div>

          <div className="form-group">
            <label htmlFor="confirmPassword" className="form-label">
              Confirm Password
            </label>
            <input
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              required
              value={formData.confirmPassword}
              onChange={handleInputChange}
              className="form-input"
              placeholder="••••••••••••••••"
            />
          </div>

          <div className="checkbox-group">
            <input
              id="agreeToTerms"
              name="agreeToTerms"
              type="checkbox"
              checked={formData.agreeToTerms}
              onChange={handleInputChange}
              className="checkbox-input"
              required
            />
            <label htmlFor="agreeToTerms" className="text-sm">
              I agree to the <a href="/terms" className="auth-link">Terms of Service</a> and{' '}
              <a href="/privacy" className="auth-link">Privacy Policy</a>
            </label>
          </div>

          {error && (
            <div style={{
              background: '#fef2f2',
              border: '1px solid #fca5a5',
              color: '#dc2626',
              padding: '12px',
              borderRadius: '8px',
              fontSize: '0.875rem',
              marginBottom: '16px'
            }}>
              {error}
            </div>
          )}
          
          <button 
            type="submit" 
            className="btn-primary"
            disabled={loading}
            style={{
              opacity: loading ? 0.6 : 1,
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? 'Creating Account...' : 'Start Free Trial'}
          </button>

          <div className="auth-links" style={{ marginTop: '16px' }}>
            <span className="text-sm" style={{ color: '#6b7280' }}>Already have an account?</span>{' '}
            <Link to="/login" className="auth-link font-medium">
              Sign in →
            </Link>
          </div>

          <div className="auth-links" style={{ marginTop: '16px' }}>
            <Link to="/" className="auth-link">
              ← Back to Home
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}