/**
 * Upload Page - Prototype Design Implementation 
 * Following prototype design: /design/giki-ai/prototype/upload.html
 */
import React, { useState, useCallback } from 'react';
import { Link } from 'react-router-dom';

interface FileUpload {
  file: File;
  id: string;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
  transactionCount?: number;
}

export default function UploadPage() {
  const [dragActive, setDragActive] = useState(false);
  const [files, setFiles] = useState<FileUpload[]>([
    // Sample files for demonstration
    {
      file: new File([''], 'HDFC_Statement_Jan2024.pdf', { type: 'application/pdf' }),
      id: '1',
      status: 'completed',
      progress: 100,
      transactionCount: 147
    },
    {
      file: new File([''], 'ICICI_Credit_Card.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }),
      id: '2',
      status: 'completed',
      progress: 100,
      transactionCount: 89
    },
    {
      file: new File([''], 'transactions_export.csv', { type: 'text/csv' }),
      id: '3',
      status: 'error',
      progress: 0,
      error: 'Format needs review'
    }
  ]);
  const [categorizationMode, setCategorizationMode] = useState('smart');
  const [businessType, setBusinessType] = useState('retail');
  const [priority, setPriority] = useState('normal');
  const [options, setOptions] = useState({
    detectDuplicates: true,
    gstCategorization: true,
    glCodes: true,
    monthlyReport: false
  });

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  }, []);

  const handleFiles = (fileList: FileList) => {
    const newFiles = Array.from(fileList).map(file => ({
      file,
      id: Math.random().toString(36).substr(2, 9),
      status: 'pending' as const,
      progress: 0
    }));
    
    setFiles(prev => [...prev, ...newFiles]);
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files);
    }
  };

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(file => file.id !== id));
  };

  const processFiles = async () => {
    // Demo processing for prototype
    console.log('Processing files with config:', {
      mode: categorizationMode,
      businessType,
      priority,
      options
    });
  };

  const getFileStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✓';
      case 'error': return '⚠';
      case 'processing': return '⏳';
      default: return '📄';
    }
  };

  const getFileStatusClass = (status: string) => {
    switch (status) {
      case 'completed': return 'status-success';
      case 'error': return 'status-warning';
      case 'processing': return 'status-processing';
      default: return 'status-success';
    }
  };

  const getFileDetails = (file: FileUpload) => {
    if (file.file.name.includes('HDFC')) {
      return 'Bank: HDFC | Period: Jan 2024 | Size: 2.3MB';
    }
    if (file.file.name.includes('ICICI')) {
      return 'Type: Credit Card | Auto-detected | Size: 1.8MB';
    }
    if (file.file.name.includes('transactions')) {
      return 'Format needs review | Size: 456KB';
    }
    return `Size: ${(file.file.size / 1024 / 1024).toFixed(2)}MB`;
  };

  const totalFiles = files.length;
  const totalSize = files.reduce((sum, f) => sum + f.file.size, 0);
  const totalSizeMB = (totalSize / 1024 / 1024).toFixed(1);

  return (
    <div className="app-container">
      {/* Sidebar */}
      <aside className="sidebar">
        <div className="sidebar-header">
          <div className="logo">GIKI.AI</div>
        </div>
        
        <nav className="nav-menu">
          <Link to="/dashboard" className="nav-item">
            <span className="nav-icon">📊</span>
            Dashboard
          </Link>
          <Link to="/upload" className="nav-item active">
            <span className="nav-icon">📁</span>
            Upload
          </Link>
          <Link to="/transactions" className="nav-item">
            <span className="nav-icon">💳</span>
            Transactions
          </Link>
          <Link to="/reports" className="nav-item">
            <span className="nav-icon">📈</span>
            Reports
          </Link>
          <Link to="/categories" className="nav-item">
            <span className="nav-icon">🏷️</span>
            Categories
          </Link>
          <Link to="/settings" className="nav-item">
            <span className="nav-icon">⚙️</span>
            Settings
          </Link>
        </nav>
      </aside>
      
      {/* Main Content */}
      <main className="main-content">
        {/* Top Bar */}
        <header className="topbar">
          <h1 className="page-title">Upload Files</h1>
          
          <div className="topbar-right">
            <div className="user-menu">
              <div className="user-avatar">JD</div>
              <span>John Doe</span>
              <span>▼</span>
            </div>
          </div>
        </header>
        
        {/* Upload Content */}
        <div className="upload-content">
          {/* Hero Section */}
          <div className="upload-hero">
            <h2>Upload Your Messy Statements</h2>
            <p>We'll organize them into clean books with 87% accuracy in under 60 seconds</p>
          </div>
          
          {/* Upload Zone */}
          <div 
            className={`upload-zone ${dragActive ? 'active' : ''}`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <div className="upload-icon">📁</div>
            <h3 className="upload-title">Drop Files Here</h3>
            <p className="upload-subtitle">or click to browse from your computer</p>
            <button className="browse-btn" onClick={() => document.getElementById('file-input')?.click()}>
              Browse Files
            </button>
            <p className="supported-formats">Supported: PDF, Excel, CSV (Max 50MB each)</p>
            <input
              id="file-input"
              type="file"
              multiple
              accept=".csv,.xlsx,.xls,.pdf"
              onChange={handleFileInput}
              style={{ display: 'none' }}
            />
          </div>
          
          {/* Files List */}
          {files.length > 0 && (
            <div className="files-section">
              <div className="section-header">
                <h3 className="section-title">Files Ready to Process</h3>
                <span className="file-count">{totalFiles} files, {totalSizeMB}MB total</span>
              </div>
              
              <div className="files-list">
                {files.map((fileUpload) => (
                  <div key={fileUpload.id} className="file-item">
                    <div className={`file-status ${getFileStatusClass(fileUpload.status)}`}>
                      {getFileStatusIcon(fileUpload.status)}
                    </div>
                    <div className="file-info">
                      <div className="file-name">{fileUpload.file.name}</div>
                      <div className="file-details">{getFileDetails(fileUpload)}</div>
                    </div>
                    <div className="file-actions">
                      {fileUpload.status === 'error' ? (
                        <button className="file-action">Fix Format</button>
                      ) : (
                        <button className="file-action">Edit</button>
                      )}
                      <button className="file-action danger" onClick={() => removeFile(fileUpload.id)}>
                        Remove
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {/* AI Configuration */}
          <div className="config-section">
            <h3 className="section-title">AI Configuration</h3>
            
            <div className="config-grid">
              <div className="config-group">
                <label className="config-label">Categorization Mode</label>
                <div className="radio-group">
                  <div className={`radio-option ${categorizationMode === 'smart' ? 'selected' : ''}`}
                       onClick={() => setCategorizationMode('smart')}>
                    <input 
                      type="radio" 
                      className="radio-input" 
                      name="mode" 
                      checked={categorizationMode === 'smart'}
                      onChange={() => {}}
                    />
                    <div className="radio-label">
                      <div className="radio-title">Smart Auto (Recommended)</div>
                      <div className="radio-description">AI learns from your patterns</div>
                    </div>
                  </div>
                  <div className={`radio-option ${categorizationMode === 'industry' ? 'selected' : ''}`}
                       onClick={() => setCategorizationMode('industry')}>
                    <input 
                      type="radio" 
                      className="radio-input" 
                      name="mode"
                      checked={categorizationMode === 'industry'}
                      onChange={() => {}}
                    />
                    <div className="radio-label">
                      <div className="radio-title">Industry-Specific</div>
                      <div className="radio-description">Pre-configured for your industry</div>
                    </div>
                  </div>
                  <div className={`radio-option ${categorizationMode === 'custom' ? 'selected' : ''}`}
                       onClick={() => setCategorizationMode('custom')}>
                    <input 
                      type="radio" 
                      className="radio-input" 
                      name="mode"
                      checked={categorizationMode === 'custom'}
                      onChange={() => {}}
                    />
                    <div className="radio-label">
                      <div className="radio-title">Custom Rules</div>
                      <div className="radio-description">Use your own categorization rules</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="config-group">
                <label className="config-label">Business Type</label>
                <select 
                  className="dropdown" 
                  value={businessType}
                  onChange={(e) => setBusinessType(e.target.value)}
                >
                  <option value="retail">Retail Business</option>
                  <option value="service">Service Provider</option>
                  <option value="manufacturing">Manufacturing</option>
                  <option value="ecommerce">E-commerce</option>
                  <option value="consulting">Consulting</option>
                  <option value="other">Other</option>
                </select>
                
                <label className="config-label" style={{marginTop: '16px'}}>Processing Priority</label>
                <select 
                  className="dropdown"
                  value={priority}
                  onChange={(e) => setPriority(e.target.value)}
                >
                  <option value="normal">Normal (60 seconds)</option>
                  <option value="priority">Priority (30 seconds)</option>
                  <option value="express">Express (15 seconds)</option>
                </select>
              </div>
              
              <div className="config-group">
                <label className="config-label">Additional Options</label>
                <div className="checkbox-group">
                  <label className="checkbox-option">
                    <input 
                      type="checkbox" 
                      className="checkbox-input" 
                      checked={options.detectDuplicates}
                      onChange={(e) => setOptions({...options, detectDuplicates: e.target.checked})}
                    />
                    <span>Auto-detect duplicate transactions</span>
                  </label>
                  <label className="checkbox-option">
                    <input 
                      type="checkbox" 
                      className="checkbox-input" 
                      checked={options.gstCategorization}
                      onChange={(e) => setOptions({...options, gstCategorization: e.target.checked})}
                    />
                    <span>Apply GST categorization</span>
                  </label>
                  <label className="checkbox-option">
                    <input 
                      type="checkbox" 
                      className="checkbox-input" 
                      checked={options.glCodes}
                      onChange={(e) => setOptions({...options, glCodes: e.target.checked})}
                    />
                    <span>Generate GL codes automatically</span>
                  </label>
                  <label className="checkbox-option">
                    <input 
                      type="checkbox" 
                      className="checkbox-input"
                      checked={options.monthlyReport}
                      onChange={(e) => setOptions({...options, monthlyReport: e.target.checked})}
                    />
                    <span>Create monthly summary report</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="action-buttons">
            <button className="btn btn-cancel">Cancel</button>
            <button className="btn btn-process" onClick={processFiles}>
              Start Processing →
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}