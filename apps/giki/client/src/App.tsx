import React, { Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';

// Direct imports - lazy loading disabled to fix TypeScript errors
import LandingPage from './pages/LandingPage';
import LoginPage from './pages/LoginPage';
import SignupPage from './pages/SignupPage';
import EnhancedAppLayout from './components/layout/EnhancedAppLayout';
import { DashboardPage } from './pages/DashboardPage';
import { UploadPage } from './pages/UploadPage';
import { TransactionsPage } from './pages/TransactionsPage';
import { CategoriesPage } from './pages/CategoriesPage';
import { ReportsPage } from './pages/ReportsPage';

// Loading component
const LoadingFallback = () => (
  <div style={{ 
    padding: '20px', 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    minHeight: '50vh',
    background: 'linear-gradient(135deg, #295343 0%, #1A3F5F 100%)',
    color: 'white'
  }}>
    <div>Loading...</div>
  </div>
);

function App() {
  return (
    <AuthProvider>
      <Routes>
          {/* Public Routes */}
          <Route path="/" element={<LandingPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/signup" element={<SignupPage />} />
          
          {/* Application Routes with Enhanced Layout */}
          <Route path="/app/*" element={
            <EnhancedAppLayout>
              <Routes>
                <Route path="dashboard" element={<DashboardPage />} />
                <Route path="upload" element={<UploadPage />} />
                <Route path="transactions" element={<TransactionsPage />} />
                <Route path="categories" element={<CategoriesPage />} />
                <Route path="reports" element={<ReportsPage />} />
                <Route path="" element={<DashboardPage />} />
              </Routes>
            </EnhancedAppLayout>
          } />
        </Routes>
    </AuthProvider>
  );
}

export default App;