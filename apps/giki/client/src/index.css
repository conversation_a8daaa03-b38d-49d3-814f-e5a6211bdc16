/* Import design tokens first */
@import './styles/design-tokens.css';
@import './styles/auth-styles.css';
/* CRITICAL: Import prototype design system for beautiful UI */
@import './styles/giki-prototype-design.css';

/* Basic CSS for technical validation */
* {
  box-sizing: border-box;
}

/* === GLASSMORPHISM DESIGN SYSTEM === */
.bg-glass {
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

.bg-glass-darker {
  background: rgba(10, 15, 30, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.border-glass-border {
  border-color: rgba(148, 163, 184, 0.1);
}

/* Glass hover effects */
.bg-glass:hover {
  background: rgba(15, 23, 42, 0.7);
}

/* Text colors for dark theme */
.text-text-light {
  color: rgba(226, 232, 240, 0.9);
}

.text-text-muted {
  color: rgba(148, 163, 184, 0.8);
}

/* === CRITICAL MOBILE RESPONSIVE FOUNDATION === */

/* Prevent horizontal scroll on all devices */
html, body {
  overflow-x: hidden;
  width: 100%;
  max-width: 100%;
}

/* Mobile-first responsive breakpoints */
:root {
  --mobile-max: 767px;
  --tablet-min: 768px;
  --tablet-max: 1023px;
  --desktop-min: 1024px;
  --desktop-large-min: 1440px;
}

/* Touch target minimum sizes for mobile */
@media (max-width: 767px) {
  /* Ensure all interactive elements meet 44px minimum touch target */
  button, 
  a, 
  input, 
  select, 
  textarea,
  [role="button"],
  [tabindex="0"] {
    min-height: 44px;
    min-width: 44px;
    padding: 12px;
  }
  
  /* Special handling for inline links */
  a:not(.btn):not(.button):not([role="button"]) {
    min-height: unset;
    min-width: unset;
    padding: 4px 8px;
    display: inline-block;
  }
  
  /* Mobile typography improvements */
  body {
    font-size: 16px; /* Prevent zoom on iOS */
    line-height: 1.5;
  }
  
  /* Larger text for better mobile readability */
  h1 {
    font-size: 2rem; /* 32px */
    line-height: 1.2;
  }
  
  h2 {
    font-size: 1.75rem; /* 28px */
    line-height: 1.3;
  }
  
  h3 {
    font-size: 1.5rem; /* 24px */
    line-height: 1.4;
  }
  
  p {
    font-size: 16px;
    line-height: 1.6;
  }
  
  /* Enhanced text contrast for mobile */
  .text-on-gradient {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  
  .text-on-dark {
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
  
  /* Ensure readable contrast on all backgrounds */
  .hero-text {
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
  }
  
  /* Better button contrast on mobile */
  .mobile-button {
    background-color: #ffffff;
    color: #295343;
    border: 2px solid #295343;
    font-weight: 600;
  }
  
  .mobile-button:hover {
    background-color: #295343;
    color: #ffffff;
  }
}

/* Professional application styling using consolidated design tokens */
html {
  scroll-behavior: smooth;
  font-size: 100%; /* 16px base */
  -webkit-tap-highlight-color: transparent;
  /* Mobile viewport handling */
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  background: linear-gradient(135deg, #295343 0%, #1a3d2e 100%); /* Premium dark gradient like approved prototype */
  color: white; /* White text on dark gradient background */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; /* Modern Inter font */
  line-height: 1.5; /* Good readability */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  min-height: 100vh;
  font-feature-settings:
    'cv02', 'cv03', 'cv04', 'cv11', 'ss01'; /* Inter font features + stylistic sets */
  font-optical-sizing: auto; /* Variable font optimization */
  overflow-x: hidden; /* Prevent horizontal scroll on body */
  
  /* Enhanced contrast for mobile */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Enhanced Typography Hierarchy - Using Design Tokens */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--giki-font-family-sans);
  color: hsl(var(--giki-text-primary));
  margin-bottom: var(--giki-space-3); /* 12px consistent spacing */
}

/* === MODERN TYPOGRAPHY FOUNDATION === */

h1 {
  /* Modern page titles: 30px, Bold, Tight spacing */
  font-family: var(--giki-font-family-display);
  font-size: var(--giki-text-4xl); /* 30px */
  font-weight: var(--giki-font-weight-bold);
  letter-spacing: -0.025em; /* Tighter modern spacing */
  line-height: 1.2; /* Tighter for impact */
  color: hsl(var(--giki-text-primary));
  margin-bottom: var(--giki-space-6);
}

h2 {
  /* Modern section headers: 24px, Semibold */
  font-family: var(--giki-font-family-display);
  font-size: var(--giki-text-3xl); /* 24px */
  font-weight: var(--giki-font-weight-semibold);
  letter-spacing: -0.02em;
  line-height: 1.3;
  color: hsl(var(--giki-text-primary));
  margin-bottom: var(--giki-space-4);
}

h3 {
  /* Modern subsection headers: 20px, Semibold */
  font-family: var(--giki-font-family-display);
  font-size: var(--giki-text-2xl); /* 20px */
  font-weight: var(--giki-font-weight-semibold);
  letter-spacing: -0.015em;
  line-height: 1.4;
  color: hsl(var(--giki-text-primary));
  margin-bottom: var(--giki-space-3);
}

h4 {
  /* Modern card titles: 18px, Medium */
  font-family: var(--giki-font-family-display);
  font-size: var(--giki-text-xl); /* 18px */
  font-weight: var(--giki-font-weight-medium);
  letter-spacing: -0.01em;
  line-height: 1.4;
  color: hsl(var(--giki-text-primary));
  margin-bottom: var(--giki-space-2);
}

h5 {
  /* Modern labels: 16px, Medium */
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-lg); /* 16px */
  font-weight: var(--giki-font-weight-medium);
  line-height: 1.4;
  color: hsl(var(--giki-text-primary));
  margin-bottom: var(--giki-space-2);
}

h6 {
  /* Modern small headers: 14px, Medium */
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-base); /* 14px */
  font-weight: var(--giki-font-weight-medium);
  line-height: 1.4;
  color: hsl(var(--giki-text-secondary));
  margin-bottom: var(--giki-space-2);
}

/* === MODERN BODY TEXT === */
p {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-base); /* 14px - Modern standard */
  font-weight: var(--giki-font-weight-normal);
  line-height: 1.6; /* Better readability */
  color: hsl(var(--giki-text-primary));
  margin-bottom: var(--giki-space-4); /* Better spacing */
}

/* Professional link styling - Using Design Tokens */
a {
  /* DRD Link Text: 15px, Medium (500), Primary color */
  color: hsl(var(--giki-primary));
  text-decoration: none;
  font-weight: var(--giki-font-weight-medium);
  font-size: var(--giki-text-base); /* 15px */
  transition: var(--giki-transition-normal);
}

a:hover {
  color: hsl(var(--giki-brand-purple)); /* Brand purple for hover */
  text-decoration: underline;
}

/* Professional scrollbar styling using design tokens */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--giki-bg-muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--giki-text-muted));
  border-radius: var(--giki-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--giki-primary));
}

/* Professional utility classes for giki.ai branding */
.text-gradient {
  background: linear-gradient(
    120deg,
    hsl(var(--giki-dark-green)) 0%,
    hsl(var(--giki-brand-pink-dark)) 33%,
    hsl(var(--giki-brand-blue-dark)) 66%,
    hsl(var(--giki-dark-purple)) 100%
  ) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.text-gradient-ai {
  background: linear-gradient(
    125deg,
    hsl(var(--giki-dark-green)) 0%,
    hsl(var(--giki-brand-pink-dark)) 33%,
    hsl(var(--giki-brand-blue-dark)) 66%,
    hsl(var(--giki-dark-purple)) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-premium {
  background: linear-gradient(
    115deg,
    hsl(var(--giki-dark-green)) 0%,
    hsl(var(--giki-brand-blue-dark)) 50%,
    hsl(var(--giki-dark-purple)) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-glow {
  box-shadow: var(--giki-shadow-glow-primary);
}

/* AI-specific gradient utilities for modern AI branding */
.text-gradient-ai-subtle {
  background: linear-gradient(
    110deg,
    hsl(var(--giki-dark-green)) 0%,
    hsl(var(--giki-brand-pink-dark)) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-ai {
  background: linear-gradient(
    120deg,
    hsl(var(--giki-dark-green)) 0%,
    hsl(var(--giki-brand-pink-dark)) 33%,
    hsl(var(--giki-brand-blue-dark)) 66%,
    hsl(var(--giki-dark-purple)) 100%
  );
}

.border-gradient-ai {
  border-image: linear-gradient(
      120deg,
      hsl(var(--giki-dark-green)) 0%,
      hsl(var(--giki-brand-pink-dark)) 33%,
      hsl(var(--giki-brand-blue-dark)) 66%,
      hsl(var(--giki-dark-purple)) 100%
    )
    1;
}

.shadow-premium {
  box-shadow: var(--giki-card-shadow-premium);
}

/* === LOGO-BASED GRADIENT UTILITIES === */

/* Subtle logo gradient for buttons and interactive elements */
.bg-gradient-logo-subtle {
  background: linear-gradient(
    135deg,
    hsl(var(--giki-primary)) 0%,
    hsl(var(--giki-dark-green)) 100%
  );
}

/* Full logo gradient for special elements */
.bg-gradient-logo {
  background: linear-gradient(
    135deg,
    hsl(var(--giki-dark-green)) 0%,
    hsl(var(--giki-brand-pink-dark)) 33%,
    hsl(var(--giki-brand-blue-dark)) 66%,
    hsl(var(--giki-dark-purple)) 100%
  );
}

/* Subtle logo border gradient */
.border-gradient-logo {
  border: 1px solid transparent;
  background:
    linear-gradient(hsl(var(--giki-bg-primary)), hsl(var(--giki-bg-primary)))
      padding-box,
    linear-gradient(
        135deg,
        hsl(var(--giki-primary)),
        hsl(var(--giki-dark-green)),
        hsl(var(--giki-brand-blue-dark))
      )
      border-box;
}

/* Very subtle logo gradient for cards */
.bg-gradient-logo-card {
  background: linear-gradient(
    145deg,
    hsla(var(--giki-bg-primary), 0.98) 0%,
    hsla(var(--giki-brand-green-dark), 0.02) 30%,
    hsla(var(--giki-brand-blue-dark), 0.02) 70%,
    hsla(var(--giki-bg-primary), 0.98) 100%
  );
  backdrop-filter: blur(8px);
  border: 1px solid hsla(var(--giki-border-primary), 0.5);
}

/* === MODERN TYPOGRAPHY SYSTEM === */

/* Semantic Text Colors */
.text-primary {
  color: hsl(var(--giki-text-primary)) !important;
  font-weight: var(--giki-font-weight-normal);
}
.text-secondary {
  color: hsl(var(--giki-text-secondary)) !important;
  font-weight: var(--giki-font-weight-normal);
}
.text-muted {
  color: hsl(var(--giki-text-muted)) !important;
  font-weight: var(--giki-font-weight-normal);
}
.text-disabled {
  color: hsl(var(--giki-text-disabled)) !important;
}
.text-success {
  color: hsl(var(--giki-text-success)) !important;
}
.text-error {
  color: hsl(var(--giki-text-error)) !important;
}
.text-warning {
  color: hsl(var(--giki-text-warning)) !important;
}
.text-link {
  color: hsl(var(--giki-text-link)) !important;
  text-decoration: none;
  transition: color 150ms ease;
}
.text-link:hover {
  color: hsl(var(--giki-text-link-hover)) !important;
}

/* Modern Typography Scale */
.text-display {
  font-family: var(--giki-font-family-display);
  font-size: var(--giki-text-6xl); /* 48px */
  font-weight: var(--giki-font-weight-extrabold);
  line-height: 1.1;
  letter-spacing: -0.04em;
}

.text-hero {
  font-family: var(--giki-font-family-display);
  font-size: var(--giki-text-5xl); /* 36px */
  font-weight: var(--giki-font-weight-bold);
  line-height: 1.15;
  letter-spacing: -0.03em;
}

.text-h1 {
  font-family: var(--giki-font-family-display);
  font-size: var(--giki-text-4xl); /* 30px */
  font-weight: var(--giki-font-weight-bold);
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.text-h2 {
  font-family: var(--giki-font-family-display);
  font-size: var(--giki-text-3xl); /* 24px */
  font-weight: var(--giki-font-weight-semibold);
  line-height: 1.3;
  letter-spacing: -0.02em;
}

.text-h3 {
  font-family: var(--giki-font-family-display);
  font-size: var(--giki-text-2xl); /* 20px */
  font-weight: var(--giki-font-weight-semibold);
  line-height: 1.4;
  letter-spacing: -0.015em;
}

.text-h4 {
  font-family: var(--giki-font-family-display);
  font-size: var(--giki-text-xl); /* 18px */
  font-weight: var(--giki-font-weight-medium);
  line-height: 1.4;
  letter-spacing: -0.01em;
}

.text-h5 {
  font-size: var(--giki-text-lg); /* 16px */
  font-weight: var(--giki-font-weight-medium);
  line-height: 1.4;
}

.text-h6 {
  font-size: var(--giki-text-base); /* 14px */
  font-weight: var(--giki-font-weight-medium);
  line-height: 1.4;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.text-body-large {
  font-size: var(--giki-text-md); /* 15px */
  font-weight: var(--giki-font-weight-normal);
  line-height: 1.6;
}

.text-body {
  font-size: var(--giki-text-base); /* 14px */
  font-weight: var(--giki-font-weight-normal);
  line-height: 1.6;
}

.text-body-small {
  font-size: var(--giki-text-sm); /* 13px */
  font-weight: var(--giki-font-weight-normal);
  line-height: 1.5;
}

.text-caption {
  font-size: var(--giki-text-xs); /* 12px */
  font-weight: var(--giki-font-weight-normal);
  line-height: 1.4;
}

.text-fine-print {
  font-size: var(--giki-text-2xs); /* 11px */
  font-weight: var(--giki-font-weight-normal);
  line-height: 1.3;
}

/* Modern Text Alignment */
.text-left {
  text-align: left !important;
}
.text-center {
  text-align: center !important;
}
.text-right {
  text-align: right !important;
}
.text-justify {
  text-align: justify !important;
}

/* Modern Component Typography */
.page-title {
  font-family: var(--giki-font-family-display);
  font-size: var(--giki-text-4xl); /* 30px */
  font-weight: var(--giki-font-weight-bold);
  line-height: 1.2;
  letter-spacing: -0.025em;
  margin-bottom: var(--giki-space-8);
}

.section-title {
  font-family: var(--giki-font-family-display);
  font-size: var(--giki-text-3xl); /* 24px */
  font-weight: var(--giki-font-weight-semibold);
  line-height: 1.3;
  letter-spacing: -0.02em;
  margin-bottom: var(--giki-space-6);
  color: hsl(var(--giki-text-primary));
}

.card-title {
  font-family: var(--giki-font-family-display);
  font-size: var(--giki-text-2xl); /* 20px */
  font-weight: var(--giki-font-weight-semibold);
  line-height: 1.4;
  letter-spacing: -0.015em;
  margin-bottom: var(--giki-space-4);
  color: hsl(var(--giki-text-primary));
}

.form-label {
  font-size: var(--giki-text-sm); /* 13px */
  font-weight: var(--giki-font-weight-medium);
  line-height: 1.4;
  color: hsl(var(--giki-text-secondary));
  margin-bottom: var(--giki-space-2);
}

.help-text {
  font-size: var(--giki-text-xs); /* 12px */
  font-weight: var(--giki-font-weight-normal);
  line-height: 1.5;
  color: hsl(var(--giki-text-muted));
  margin-top: var(--giki-space-1);
}

.nav-text {
  font-size: var(--giki-text-sm); /* 13px */
  font-weight: var(--giki-font-weight-medium);
  line-height: 1.4;
}

.button-text {
  font-size: var(--giki-text-sm); /* 13px */
  font-weight: var(--giki-font-weight-semibold);
  line-height: 1.2;
  letter-spacing: 0.01em;
}

/* Modern Status Text */
.text-status {
  font-size: var(--giki-text-xs);
  font-weight: var(--giki-font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Modern Metric Text */
.text-metric {
  font-family: var(--giki-font-family-mono);
  font-size: var(--giki-text-xl);
  font-weight: var(--giki-font-weight-semibold);
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.shadow-elevated {
  box-shadow: var(--giki-card-shadow-elevated);
}

.border-gradient {
  border: 1px solid transparent;
  background:
    linear-gradient(hsl(var(--giki-bg-primary)), hsl(var(--giki-bg-primary)))
      padding-box,
    linear-gradient(
        135deg,
        hsl(var(--giki-primary)),
        hsl(var(--giki-dark-green)),
        hsl(var(--giki-dark-purple))
      )
      border-box;
}

.bg-gradient-premium {
  background: linear-gradient(
    135deg,
    hsl(var(--giki-bg-primary)),
    hsl(var(--giki-bg-secondary)),
    hsl(var(--giki-bg-primary))
  );
}

.bg-gradient-card {
  background: linear-gradient(
    145deg,
    hsla(var(--giki-bg-primary), 0.98) 0%,
    hsla(var(--giki-bg-secondary), 0.95) 50%,
    hsla(var(--giki-bg-primary), 0.98) 100%
  );
  backdrop-filter: blur(8px);
  border: 1px solid hsla(var(--giki-border-primary), 0.4);
}

.glass-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: var(--giki-shadow-lg);
}

.glass-panel {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(0, 0, 0, 0.08);
}

/* Focus styles for accessibility using design tokens */
*:focus-visible {
  outline: 2px solid hsl(var(--giki-border-focus));
  outline-offset: 2px;
}

/* Smooth transitions for interactive elements */
button,
input,
select,
textarea {
  transition: var(--giki-transition-normal);
}

/* Excel-inspired card styling using design tokens */
.excel-card {
  background-color: hsl(var(--giki-card-bg));
  border: 1px solid hsl(var(--giki-card-border));
  border-radius: var(--giki-radius-lg);
  box-shadow: var(--giki-card-shadow);
}

/* Navigation styling using design tokens */
.nav-item {
  color: hsl(var(--giki-nav-text));
  background-color: transparent;
  border-radius: var(--giki-radius-md);
  transition: var(--giki-transition-normal);
}

.nav-item:hover {
  background-color: hsl(var(--giki-nav-bg-hover));
  color: hsl(var(--giki-nav-text-active));
}

.nav-item.active {
  color: hsl(var(--giki-nav-text-active));
  background-color: hsl(var(--giki-bg-accent));
}

/* Agent panel styling using design tokens */
.agent-panel {
  background-color: hsl(var(--giki-agent-bg));
  border: 1px solid hsl(var(--giki-agent-border));
}

.agent-accent {
  background-color: hsl(var(--giki-agent-accent));
  color: hsl(var(--giki-agent-accent-foreground));
}

/* Form styling using design tokens */
.form-input {
  background-color: hsl(var(--giki-input-bg));
  border: 1px solid hsl(var(--giki-input-border));
  color: hsl(var(--giki-input-text));
  border-radius: var(--giki-radius-md);
  transition: var(--giki-transition-normal);
}

.form-input:focus {
  border-color: hsl(var(--giki-input-border-focus));
  outline: none;
  box-shadow: var(--giki-shadow-focus);
}

.form-input::placeholder {
  color: hsl(var(--giki-input-placeholder));
}

/* Status indicator styles using semantic colors */
.status-success {
  background-color: hsl(var(--giki-success));
  color: hsl(var(--giki-success-foreground));
}

.status-error {
  background-color: hsl(var(--giki-destructive));
  color: hsl(var(--giki-destructive-foreground));
}

.status-warning {
  background-color: hsl(var(--giki-warning));
  color: hsl(var(--giki-warning-foreground));
}

.status-info {
  background-color: hsl(var(--giki-info));
  color: hsl(var(--giki-info-foreground));
}

/* DRD-TYPO-01: Monospace fonts for data display (Excel-inspired requirement) */
.font-mono-data {
  font-family: var(--giki-font-family-mono);
  font-feature-settings: 'tnum' 1; /* Tabular numbers for better alignment */
  font-variant-numeric: tabular-nums;
}

.text-financial {
  font-family: var(--giki-font-family-mono);
  font-feature-settings: 'tnum' 1; /* Tabular numbers for better alignment */
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em; /* Slight letter spacing for better readability */
}

.text-data-table {
  font-family: var(--giki-font-family-mono);
  font-feature-settings: 'tnum' 1; /* Tabular numbers for better alignment */
  font-variant-numeric: tabular-nums;
  font-size: var(--giki-text-sm); /* 14px for table data */
}

/* === ENHANCED FINANCIAL DISPLAY UTILITIES === */

/* Enterprise financial number display - perfect for tables */
.financial-number {
  font-family: var(--giki-font-family-mono);
  font-feature-settings: 'tnum' 1, 'lnum' 1; /* Tabular and lining numbers */
  font-variant-numeric: tabular-nums lining-nums;
  text-align: right;
  letter-spacing: 0.02em;
}

/* Currency amounts with consistent width */
.currency-amount {
  font-family: var(--giki-font-family-mono);
  font-feature-settings: 'tnum' 1, 'lnum' 1;
  font-variant-numeric: tabular-nums lining-nums;
  text-align: right;
  letter-spacing: 0.02em;
  min-width: 80px; /* Consistent minimum width for alignment */
}

/* Large financial metrics for dashboards */
.financial-metric {
  font-family: var(--giki-font-family-mono);
  font-feature-settings: 'tnum' 1, 'lnum' 1;
  font-variant-numeric: tabular-nums lining-nums;
  text-align: right;
  letter-spacing: -0.01em; /* Tighter for large sizes */
  font-weight: 600;
}

/* Financial table headers */
.financial-header {
  font-family: var(--giki-font-family-sans);
  font-weight: 600;
  text-align: right;
  font-size: var(--giki-text-sm);
  color: hsl(var(--giki-text-muted));
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Financial percentage display */
.financial-percentage {
  font-family: var(--giki-font-family-mono);
  font-feature-settings: 'tnum' 1;
  font-variant-numeric: tabular-nums;
  text-align: right;
}

/* Responsive financial display */
@media (max-width: 640px) {
  .currency-amount {
    min-width: 70px;
    font-size: var(--giki-text-sm);
  }
  
  .financial-metric {
    font-size: var(--giki-text-lg);
  }
}

/* === MODERN SCROLLBAR STYLES === */

/* Base scrollbar styles for webkit browsers */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--giki-border-secondary));
  border-radius: 5px;
  border: 2px solid transparent;
  background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--giki-border-primary));
  border: 2px solid transparent;
  background-clip: padding-box;
}

/* Thin scrollbar variant */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  border: 1px solid transparent;
}

/* Hidden scrollbar variant */
.scrollbar-hide {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Opera */
}

/* Custom thumb color variants */
.scrollbar-thumb-primary::-webkit-scrollbar-thumb {
  background: hsl(var(--giki-primary) / 0.5);
}

.scrollbar-thumb-primary::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--giki-primary) / 0.7);
}

/* Rounded scrollbar */
.scrollbar-thumb-rounded::-webkit-scrollbar-thumb {
  border-radius: 9999px;
}

/* Track variants */
.scrollbar-track-transparent::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-track-muted::-webkit-scrollbar-track {
  background: hsl(var(--giki-bg-muted) / 0.5);
}

/* Firefox scrollbar support */
* {
  scrollbar-width: auto;
  scrollbar-color: hsl(var(--giki-border-secondary)) transparent;
}

.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-hide {
  scrollbar-width: none;
}

/* Scroll behavior utilities */
.scroll-smooth {
  scroll-behavior: smooth;
}

.scroll-auto {
  scroll-behavior: auto;
}

/* Scroll padding for fixed headers */
.scroll-pt-20 {
  scroll-padding-top: 5rem;
}

.scroll-pt-16 {
  scroll-padding-top: 4rem;
}

/* Overflow utilities */
.overflow-y-scroll {
  overflow-y: scroll !important;
}

.overflow-x-scroll {
  overflow-x: scroll !important;
}

/* Custom scroll container styles */
.scroll-container {
  position: relative;
  overflow: auto;
}

.scroll-container-fade-top::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(
    to bottom,
    hsl(var(--giki-bg-primary)),
    transparent
  );
  pointer-events: none;
  z-index: 10;
}

.scroll-container-fade-bottom::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to top, hsl(var(--giki-bg-primary)), transparent);
  pointer-events: none;
  z-index: 10;
}
