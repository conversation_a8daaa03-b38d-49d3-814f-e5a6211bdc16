import React, { Suspense, useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import useAuthStore from '../../shared/services/auth/authStore';
import { PerformanceProvider } from '../../shared/components/performance/PerformanceProvider';
import { PageLoading } from '../../shared/components/ui/app-loading';

// App.tsx: Phase 6 COMPLETE - Full MIS Platform Restored with Comprehensive Backend Integration!

// Auth & Landing Pages - Phase 1: Landing & Authentication
const LandingPage = React.lazy(() => import('../../pages/LandingPage'));
const LoginPage = React.lazy(() => import('../../pages/LoginPage'));
const RegisterPageWorking = React.lazy(() => import('../../features/auth/pages/RegisterPageWorking'));
const GetStartedPageWorking = React.lazy(() => import('../../features/auth/pages/GetStartedPageWorking'));

// Onboarding Pages - Phase 2: Onboarding Experience
const OnboardingPage = React.lazy(() => import('../../features/auth/onboarding/pages/OnboardingPage'));

// Phase 3: File Upload & Processing
const UploadPage = React.lazy(() => import('../../features/files/pages/EnhancedUploadPageWithProgress'));
const SchemaInterpretationPage = React.lazy(() => import('../../features/files/pages/SchemaInterpretationPage'));
const ProcessingPage = React.lazy(() => import('../../features/files/pages/ProcessingPage'));

// Phase 4: Categorization & Review  
const TransactionsPage = React.lazy(() => import('../../features/transactions/pages/TransactionsPage'));

// Phase 5: MIS & Reporting
const DashboardPage = React.lazy(() => import('../../features/reports/dashboard/pages/DashboardPage'));
const ReportsPage = React.lazy(() => import('../../features/reports/pages/ReportsPage'));
const ModernReportsPage = React.lazy(() => import('../../pages/ModernReportsPage'));

// Phase 7: Category Hierarchy & Structure
const CategoriesPage = React.lazy(() => import('../../features/categories/pages/CategoriesPage'));
const AutomationRulesPage = React.lazy(() => import('../../features/categories/pages/AutomationRulesPage'));
const MISStructurePage = React.lazy(() => import('../../features/categories/pages/MISStructurePage'));
const VendorClusteringPage = React.lazy(() => import('../../features/categories/pages/VendorClusteringPage'));

// System Management
const SettingsPage = React.lazy(() => import('../../features/system/settings/pages/SettingsPage'));

// Layout and Route Protection
import PrivateRouteSimple from '../../shared/components/routing/PrivateRouteSimple';
const EnhancedAppLayout = React.lazy(() => import('../../shared/components/layout/EnhancedAppLayout'));


function App() {
  // App: PHASE 6 COMPLETE - Full MIS Platform with Comprehensive Backend Integration!
  const { checkAuth, startAuthCheck } = useAuthStore();

  useEffect(() => {
    // Initialize auth checking on app load
    checkAuth();
    startAuthCheck();
    
    // Cleanup on unmount
    return () => {
      const { stopAuthCheck } = useAuthStore.getState();
      stopAuthCheck();
    };
  }, [checkAuth, startAuthCheck]);
  
  return (
    <PerformanceProvider>
      <div className="min-h-screen">
        <Routes>
        {/* PHASE 1: Landing & Authentication - Public Routes */}
        <Route path="/login" element={
          <Suspense fallback={<PageLoading message="Loading login..." />}>
            <LoginPage />
          </Suspense>
        } />
        <Route path="/register" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading register...</div>}>
            <RegisterPageWorking />
          </Suspense>
        } />
        <Route path="/signup" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading register...</div>}>
            <RegisterPageWorking />
          </Suspense>
        } />
        <Route path="/get-started" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading get started...</div>}>
            <GetStartedPageWorking />
          </Suspense>
        } />
        
        {/* PHASE 2: Onboarding Experience - Protected Routes */}
        <Route path="/onboarding" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading onboarding...</div>}>
            <PrivateRouteSimple>
              <OnboardingPage />
            </PrivateRouteSimple>
          </Suspense>
        } />
        
        {/* PHASE 3: File Upload & Processing - Protected Routes */}
        <Route path="/upload" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading upload...</div>}>
            <PrivateRouteSimple>
              <EnhancedAppLayout>
                <UploadPage />
              </EnhancedAppLayout>
            </PrivateRouteSimple>
          </Suspense>
        } />
        
        <Route path="/schema-interpretation" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading schema interpretation...</div>}>
            <PrivateRouteSimple>
              <EnhancedAppLayout>
                <SchemaInterpretationPage />
              </EnhancedAppLayout>
            </PrivateRouteSimple>
          </Suspense>
        } />
        
        <Route path="/processing" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading processing...</div>}>
            <PrivateRouteSimple>
              <EnhancedAppLayout>
                <ProcessingPage />
              </EnhancedAppLayout>
            </PrivateRouteSimple>
          </Suspense>
        } />
        
        {/* PHASE 4: Categorization & Review - Protected Routes */}
        <Route path="/transactions" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading transactions...</div>}>
            <PrivateRouteSimple>
              <EnhancedAppLayout>
                <TransactionsPage />
              </EnhancedAppLayout>
            </PrivateRouteSimple>
          </Suspense>
        } />
        
        <Route path="/transactions/review" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading review...</div>}>
            <PrivateRouteSimple>
              <EnhancedAppLayout>
                <TransactionsPage />
              </EnhancedAppLayout>
            </PrivateRouteSimple>
          </Suspense>
        } />
        
        {/* PHASE 5: MIS & Reporting - Protected Routes */}
        <Route path="/dashboard" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading dashboard...</div>}>
            <PrivateRouteSimple>
              <EnhancedAppLayout>
                <DashboardPage />
              </EnhancedAppLayout>
            </PrivateRouteSimple>
          </Suspense>
        } />
        
        <Route path="/reports" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading reports...</div>}>
            <PrivateRouteSimple>
              <EnhancedAppLayout>
                <ReportsPage />
              </EnhancedAppLayout>
            </PrivateRouteSimple>
          </Suspense>
        } />
        
        <Route path="/reports-modern" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading modern reports...</div>}>
            <PrivateRouteSimple>
              <ModernReportsPage />
            </PrivateRouteSimple>
          </Suspense>
        } />
        
        {/* PHASE 7: Category Hierarchy & Structure - Protected Routes */}
        <Route path="/categories" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading categories...</div>}>
            <PrivateRouteSimple>
              <EnhancedAppLayout>
                <CategoriesPage />
              </EnhancedAppLayout>
            </PrivateRouteSimple>
          </Suspense>
        } />
        <Route path="/automation-rules" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading automation rules...</div>}>
            <PrivateRouteSimple>
              <EnhancedAppLayout>
                <AutomationRulesPage />
              </EnhancedAppLayout>
            </PrivateRouteSimple>
          </Suspense>
        } />
        <Route path="/mis-structure" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading MIS structure...</div>}>
            <PrivateRouteSimple>
              <EnhancedAppLayout>
                <MISStructurePage />
              </EnhancedAppLayout>
            </PrivateRouteSimple>
          </Suspense>
        } />
        <Route path="/vendor-clustering" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading vendor clustering...</div>}>
            <PrivateRouteSimple>
              <EnhancedAppLayout>
                <VendorClusteringPage />
              </EnhancedAppLayout>
            </PrivateRouteSimple>
          </Suspense>
        } />
        
        {/* System Management - Protected Routes */}
        <Route path="/settings" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading settings...</div>}>
            <PrivateRouteSimple>
              <EnhancedAppLayout>
                <SettingsPage />
              </EnhancedAppLayout>
            </PrivateRouteSimple>
          </Suspense>
        } />
        
        
        {/* Default route - Landing page */}
        <Route path="/" element={
          <Suspense fallback={<div style={{ padding: '20px' }}>Loading...</div>}>
            <LandingPage />
          </Suspense>
        } />
        
        {/* Catch all other routes */}
        <Route path="*" element={
          <div style={{ padding: '20px' }}>
            <h1>□ PHASE 14 COMPLETE: User Journey Optimization!</h1>
            <p>Frontend organized around 7-phase user journey with consolidated backend:</p>
            <ul style={{ marginLeft: '20px', lineHeight: '1.6' }}>
              <li><strong>PHASE 1: Landing & Authentication</strong></li>
              <li><a href="/">/ (Get Started - Marketing landing)</a></li>
              <li><a href="/login">/login (Professional auth)</a></li>
              <li><a href="/register">/register (User registration)</a></li>
              <li><strong>PHASE 2: Onboarding Experience</strong></li>
              <li><a href="/onboarding">/onboarding (AI-powered setup & training)</a></li>
              <li><strong>PHASE 3: File Upload & Processing</strong></li>
              <li><a href="/upload">/upload (File upload with schema interpretation)</a></li>
              <li><a href="/schema-interpretation">/schema-interpretation (Confirm column mappings)</a></li>
              <li><a href="/processing">/processing (Real-time AI processing status)</a></li>
              <li><strong>PHASE 4: Categorization & Review</strong></li>
              <li><a href="/transactions">/transactions (Transaction management & review)</a></li>
              <li><a href="/transactions/review">/transactions/review (AI-assisted categorization)</a></li>
              <li><strong>PHASE 5: MIS & Reporting</strong></li>
              <li><a href="/dashboard">/dashboard (MIS metrics & recent activity)</a></li>
              <li><a href="/reports">/reports (Professional export & reports)</a></li>
              <li><strong>PHASE 7: Category Hierarchy & Structure</strong></li>
              <li><a href="/categories">/categories (Hierarchical category management)</a></li>
              <li><a href="/mis-structure">/mis-structure (Complete MIS structure viewing)</a></li>
              <li><strong>System Management</strong></li>
              <li><a href="/settings">/settings (User preferences & configuration)</a></li>
            </ul>
            <div style={{ marginTop: '20px', padding: '15px', backgroundColor: 'var(--ui-surface)', border: '1px solid var(--status-info)', borderRadius: '8px' }}>
              <strong>□ Complete User Journey Ready:</strong><br/>
              Authentication → Onboarding → Upload → Processing → Review → Categorization → MIS Reports
            </div>
          </div>
        } />
      </Routes>
      </div>
    </PerformanceProvider>
  );
}

export default App;