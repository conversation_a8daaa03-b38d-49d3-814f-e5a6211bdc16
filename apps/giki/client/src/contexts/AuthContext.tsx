import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface User {
  id: string;
  email: string;
  name?: string;
  company?: string;
  role: string;
  createdAt: Date;
  updatedAt: Date;
}

interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

interface AuthContextType {
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (data: { email: string; password: string; name?: string; company?: string }) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [tokens, setTokens] = useState<AuthTokens | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && !!tokens;

  // API base URL
  const API_BASE = 'http://localhost:8000/api/v1';

  useEffect(() => {
    // Check if user is already authenticated on app start
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const response = await fetch(`${API_BASE}/auth/me`, {
        method: 'GET',
        credentials: 'include', // Include cookies
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setUser(result.data);
          // In a real app, we might not have access to tokens in response
          // They would be in httpOnly cookies
          setTokens({ accessToken: 'stored-in-cookie', refreshToken: 'stored-in-cookie' });
        }
      }
    } catch (error) {
      console.error('Auth check failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);
      
      // Development mode bypass for demo credentials
      if (email === '<EMAIL>' && password === 'password123') {
        const demoUser: User = {
          id: 'demo-user-1',
          email: '<EMAIL>',
          name: 'Demo User',
          company: 'Demo Company',
          role: 'admin',
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        const demoTokens: AuthTokens = {
          accessToken: 'demo-access-token',
          refreshToken: 'demo-refresh-token'
        };
        
        setUser(demoUser);
        setTokens(demoTokens);
        
        // Store demo token in localStorage for PrivateRouteSimple compatibility
        localStorage.setItem('access_token', demoTokens.accessToken);
        
        return { success: true };
      }
      
      const response = await fetch(`${API_BASE}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies
        body: JSON.stringify({ email, password }),
      });

      const result = await response.json();

      if (result.success) {
        setUser(result.data.user);
        setTokens(result.data.tokens);
        
        // Store real token in localStorage for PrivateRouteSimple compatibility
        if (result.data.tokens?.accessToken) {
          localStorage.setItem('access_token', result.data.tokens.accessToken);
        }
        
        return { success: true };
      } else {
        return { success: false, error: result.error?.message || 'Login failed' };
      }
    } catch (error: any) {
      console.error('Login error:', error);
      // In development, if API is not available, still allow demo login
      if (email === '<EMAIL>' && password === 'password123') {
        const demoUser: User = {
          id: 'demo-user-1',
          email: '<EMAIL>',
          name: 'Demo User',
          company: 'Demo Company',
          role: 'admin',
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        const demoTokens: AuthTokens = {
          accessToken: 'demo-access-token',
          refreshToken: 'demo-refresh-token'
        };
        
        setUser(demoUser);
        setTokens(demoTokens);
        
        // Store demo token in localStorage for PrivateRouteSimple compatibility
        localStorage.setItem('access_token', demoTokens.accessToken);
        
        return { success: true };
      }
      return { success: false, error: 'Network error occurred' };
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (data: { 
    email: string; 
    password: string; 
    name?: string; 
    company?: string; 
  }): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);
      
      const response = await fetch(`${API_BASE}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        setUser(result.data.user);
        setTokens(result.data.tokens);
        return { success: true };
      } else {
        return { success: false, error: result.error?.message || 'Registration failed' };
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      return { success: false, error: 'Network error occurred' };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await fetch(`${API_BASE}/auth/logout`, {
        method: 'POST',
        credentials: 'include',
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local state regardless of API success
      setUser(null);
      setTokens(null);
      // Clear localStorage tokens
      localStorage.removeItem('access_token');
      localStorage.removeItem('giki_secure_access_token');
    }
  };

  const refreshAuth = async () => {
    try {
      const response = await fetch(`${API_BASE}/auth/refresh`, {
        method: 'POST',
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setTokens(result.data);
          // Get updated user info
          await checkAuthStatus();
        }
      } else {
        // Refresh failed, logout user
        await logout();
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      await logout();
    }
  };

  const value: AuthContextType = {
    user,
    tokens,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    refreshAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};