/*
 * Giki AI Prototype Design System
 * Imported from /design/giki-ai/prototype/styles/design-system.css
 * This ensures React implementation matches the beautiful prototype design
 */

/* ============================================
   1. CSS CUSTOM PROPERTIES (Design Tokens)
   ============================================ */

:root {
  /* Brand Colors - Giki AI Green Theme */
  --brand-primary: #295343;
  --brand-secondary: #1A3F5F;
  --brand-accent: #10b981;
  --brand-warning: #f59e0b;
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #295343 0%, #1A3F5F 100%);
  --gradient-hover: linear-gradient(135deg, #1e3d30 0%, #132d45 100%);
  --gradient-success: linear-gradient(135deg, #10b981 0%, #34d399 100%);
  --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
  
  /* Text Colors */
  --text-primary: #1a1a1a;
  --text-secondary: #6b7280;
  --text-inverse: #ffffff;
  --text-muted: #9ca3af;
  
  /* Background Colors */
  --bg-white: #ffffff;
  --bg-light: #f9fafb;
  --bg-gray: #f3f4f6;
  --bg-dark: #1a1a1a;
  
  /* Semantic Colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  /* Borders */
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
  --border-dark: #9ca3af;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 25px rgba(41, 83, 67, 0.3);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.1);
  
  /* Typography Scale (Perfect Fourth - 1.333) */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.333rem;   /* 21px */
  --font-size-2xl: 1.777rem;  /* 28px */
  --font-size-3xl: 2.369rem;  /* 38px */
  --font-size-4xl: 3.157rem;  /* 51px */
  --font-size-5xl: 4.209rem;  /* 67px */
  
  /* Responsive Typography with Clamp */
  --font-hero: clamp(2.5rem, 8vw, 4rem);
  --font-section: clamp(2rem, 5vw, 3rem);
  --font-heading: clamp(1.5rem, 4vw, 2.5rem);
  --font-subheading: clamp(1.25rem, 3vw, 1.75rem);
  --font-body: clamp(1rem, 2vw, 1.125rem);
  --font-small: clamp(0.875rem, 1.5vw, 1rem);
  
  /* Font Weights */
  --font-light: 300;
  --font-regular: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  
  /* Line Heights */
  --line-tight: 1.1;
  --line-snug: 1.3;
  --line-normal: 1.6;
  --line-relaxed: 1.8;
  --line-loose: 2;
  
  /* Letter Spacing */
  --tracking-tight: -0.02em;
  --tracking-normal: 0;
  --tracking-wide: 0.02em;
  --tracking-wider: 0.04em;
  
  /* Spacing Scale (8px base) */
  --space-xs: 0.5rem;   /* 8px */
  --space-sm: 1rem;     /* 16px */
  --space-md: 1.5rem;   /* 24px */
  --space-lg: 2rem;     /* 32px */
  --space-xl: 3rem;     /* 48px */
  --space-2xl: 4rem;    /* 64px */
  --space-3xl: 5rem;    /* 80px */
  --space-4xl: 6rem;    /* 96px */
  
  /* Border Radius */
  --radius-sm: 0.25rem;  /* 4px */
  --radius-md: 0.5rem;   /* 8px */
  --radius-lg: 0.75rem;  /* 12px */
  --radius-xl: 1rem;     /* 16px */
  --radius-2xl: 1.5rem;  /* 24px */
  --radius-full: 9999px;
  
  /* Animation Durations */
  --duration-instant: 100ms;
  --duration-fast: 200ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  
  /* Animation Easings */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-Index Scale */
  --z-base: 0;
  --z-above: 1;
  --z-dropdown: 10;
  --z-sticky: 20;
  --z-fixed: 30;
  --z-modal-backdrop: 40;
  --z-modal: 50;
  --z-tooltip: 60;
  --z-max: 9999;
  
  /* Breakpoints */
  --breakpoint-xs: 375px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1440px;
}

/* ============================================
   2. RESET & BASE STYLES
   ============================================ */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Inter', sans-serif;
  font-size: var(--font-size-base);
  line-height: var(--line-normal);
  color: var(--text-primary);
  background-color: var(--bg-white);
  min-height: 100vh;
  overflow-x: hidden;
}

/* ============================================
   3. TYPOGRAPHY UTILITIES
   ============================================ */

.text-hero {
  font-size: var(--font-hero);
  font-weight: var(--font-extrabold);
  line-height: var(--line-tight);
  letter-spacing: var(--tracking-tight);
}

.text-section {
  font-size: var(--font-section);
  font-weight: var(--font-bold);
  line-height: var(--line-tight);
  letter-spacing: var(--tracking-tight);
}

.text-heading {
  font-size: var(--font-heading);
  font-weight: var(--font-bold);
  line-height: var(--line-snug);
}

.text-subheading {
  font-size: var(--font-subheading);
  font-weight: var(--font-semibold);
  line-height: var(--line-snug);
}

.text-body {
  font-size: var(--font-body);
  font-weight: var(--font-regular);
  line-height: var(--line-normal);
}

.text-small {
  font-size: var(--font-small);
  font-weight: var(--font-regular);
  line-height: var(--line-normal);
}

/* Text Colors */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-inverse { color: var(--text-inverse); }

/* Gradient Text */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* ============================================
   4. COMPONENT STYLES
   ============================================ */

/* Navigation */
nav {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-light);
  position: fixed;
  width: 100%;
  top: 0;
  z-index: var(--z-fixed);
  padding: var(--space-sm) 0;
}

.nav-container {
  max-width: var(--breakpoint-2xl);
  margin: 0 auto;
  padding: 0 var(--space-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-extrabold);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-links {
  display: flex;
  gap: var(--space-lg);
  align-items: center;
}

.nav-links a {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: var(--font-medium);
  transition: color var(--duration-normal);
}

.nav-links a:hover {
  color: var(--text-primary);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: var(--font-semibold);
  transition: all var(--duration-normal);
  cursor: pointer;
  border: none;
  min-width: 150px;
  text-align: center;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--text-inverse);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: transparent;
  color: var(--brand-primary);
  border: 2px solid var(--brand-primary);
}

.btn-secondary:hover {
  background: var(--gradient-primary);
  color: var(--text-inverse);
  border-color: transparent;
}

.btn-hero {
  background: var(--bg-white);
  color: var(--brand-primary);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: var(--font-semibold);
  font-size: var(--font-size-lg);
  transition: all var(--duration-normal);
  display: inline-block;
}

.btn-hero:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.btn-hero-secondary {
  background: transparent;
  color: var(--text-inverse);
  border: 2px solid var(--text-inverse);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: var(--font-semibold);
  font-size: var(--font-size-lg);
  transition: all var(--duration-normal);
  display: inline-block;
}

.btn-full {
  width: 100%;
}

/* Hero Section */
.hero {
  background: var(--gradient-primary);
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: calc(var(--space-4xl) + 60px) var(--space-md) var(--space-3xl);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
}

.hero-container {
  max-width: var(--breakpoint-2xl);
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3xl);
  align-items: center;
  position: relative;
  z-index: var(--z-above);
}

.hero-content h1 {
  font-size: var(--font-hero);
  font-weight: var(--font-extrabold);
  color: var(--text-inverse);
  line-height: var(--line-tight);
  margin-bottom: var(--space-md);
}

.hero-content p {
  font-size: var(--font-size-xl);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-lg);
  line-height: var(--line-normal);
}

.hero-buttons {
  display: flex;
  gap: var(--space-sm);
  margin-bottom: var(--space-xl);
}

.hero-stats {
  display: flex;
  gap: var(--space-lg);
}

.stat {
  color: var(--text-inverse);
}

.stat-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-extrabold);
  display: block;
}

.stat-label {
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
    text-align: center;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-stats {
    justify-content: center;
    gap: var(--space-md);
  }
  
  .nav-links {
    display: none;
  }
  
  .btn {
    min-height: 44px;
  }
}

@media (max-width: 640px) {
  .hero {
    padding: calc(var(--space-2xl) + 60px) var(--space-sm) var(--space-xl);
  }
  
  .hero-content h1 {
    font-size: clamp(2rem, 8vw, 3rem);
  }
  
  .hero-content p {
    font-size: var(--font-size-lg);
  }
  
  .stat-value {
    font-size: var(--font-size-2xl);
  }
}

/* ============================================
   LOGIN PAGE SPECIFIC STYLES
   ============================================ */

/* Login Container */
.login-container {
    display: flex;
    width: 100%;
    min-height: 100vh;
}

/* Branding Panel */
.branding-panel {
    flex: 1;
    background: var(--gradient-primary);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: var(--space-xl);
    position: relative;
    overflow: hidden;
}

.branding-content {
    position: relative;
    z-index: var(--z-above);
    max-width: 500px;
    text-align: center;
}

.logo-large {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-extrabold);
    color: var(--text-inverse);
    margin-bottom: var(--space-lg);
}

.branding-panel h1 {
    color: var(--text-inverse);
    margin-bottom: var(--space-md);
}

.branding-panel p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--space-xl);
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
    text-align: left;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    color: var(--text-inverse);
}

.feature-icon {
    width: 24px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-bold);
}

.pattern-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
}

/* Login Panel */
.login-panel {
    flex: 1;
    background: var(--bg-white);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--space-xl);
}

.login-form-container {
    width: 100%;
    max-width: 400px;
}

.form-header {
    text-align: center;
    margin-bottom: var(--space-xl);
}

.form-header h2 {
    margin-bottom: var(--space-xs);
}

/* Login Form */
.login-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.form-group label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
}

.form-group input {
    padding: var(--space-sm);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    transition: all var(--duration-fast);
}

.form-group input:focus {
    outline: none;
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(41, 83, 67, 0.1);
}

.form-group input.error {
    border-color: var(--color-error);
}

.error-message {
    color: var(--color-error);
    font-size: var(--font-size-sm);
    margin-top: var(--space-xs);
}

.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
}

.password-toggle:hover {
    color: var(--text-primary);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    cursor: pointer;
    font-size: var(--font-size-sm);
}

.checkbox-container input {
    width: 16px;
    height: 16px;
}

.forgot-link {
    color: var(--brand-primary);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: var(--font-medium);
}

.forgot-link:hover {
    text-decoration: underline;
}

/* Divider */
.divider {
    position: relative;
    text-align: center;
    margin: var(--space-md) 0;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-light);
    transform: translateY(-50%);
}

.divider span {
    position: relative;
    background: var(--bg-white);
    padding: 0 var(--space-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Form Footer */
.form-footer {
    text-align: center;
    margin-top: var(--space-lg);
    padding-top: var(--space-lg);
    border-top: 1px solid var(--border-light);
}

.form-footer p {
    color: var(--text-secondary);
}

.link-primary {
    color: var(--brand-primary);
    text-decoration: none;
    font-weight: var(--font-medium);
}

.link-primary:hover {
    text-decoration: underline;
}

/* Login page mobile responsive */
@media (max-width: 768px) {
  .branding-panel {
    display: none;
  }
  
  .login-panel {
    flex: 1;
  }
}

/* ============================================
   DASHBOARD PAGE SPECIFIC STYLES
   ============================================ */

/* Layout Container */
.app-container {
    display: flex;
    height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 260px;
    background: var(--bg-white);
    border-right: 1px solid var(--border-light);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 24px;
    border-bottom: 1px solid var(--border-light);
}

.sidebar .logo {
    font-size: 28px;
    font-weight: var(--font-extrabold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    padding: 24px 16px;
    flex: 1;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 4px;
    border-radius: 8px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--duration-fast);
    font-weight: var(--font-medium);
}

.nav-item:hover {
    background: var(--bg-light);
    color: var(--text-primary);
}

.nav-item.active {
    background: var(--gradient-primary);
    color: var(--text-inverse);
}

.nav-icon {
    margin-right: 12px;
    font-size: 20px;
}

.sidebar-footer {
    padding: 24px;
    border-top: 1px solid var(--border-light);
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.quick-action {
    display: flex;
    align-items: center;
    padding: 10px;
    background: var(--bg-light);
    border-radius: 8px;
    text-decoration: none;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-medium);
    transition: all var(--duration-fast);
}

.quick-action:hover {
    background: var(--gradient-primary);
    color: var(--text-inverse);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Top Bar */
.topbar {
    background: var(--bg-white);
    border-bottom: 1px solid var(--border-light);
    padding: 16px 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.topbar-left {
    display: flex;
    align-items: center;
    gap: 24px;
}

.page-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-bold);
}

.date-selector {
    padding: 8px 16px;
    border: 1px solid var(--border-light);
    border-radius: 8px;
    background: var(--bg-white);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.notification-btn, .help-btn {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 1px solid var(--border-light);
    background: var(--bg-white);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 20px;
    transition: all var(--duration-fast);
}

.notification-btn:hover, .help-btn:hover {
    background: var(--bg-light);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    border: 1px solid var(--border-light);
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--duration-fast);
}

.user-menu:hover {
    background: var(--bg-light);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-weight: var(--font-semibold);
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    padding: 32px;
    overflow-y: auto;
}

/* Welcome Section */
.welcome-section {
    background: var(--gradient-primary);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 32px;
    color: var(--text-inverse);
}

.welcome-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-extrabold);
    margin-bottom: 8px;
}

.welcome-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.metric-card {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: 24px;
    border: 1px solid var(--border-light);
    transition: all 0.3s;
}

.metric-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.metric-label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-medium);
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: var(--font-size-xs);
    font-weight: var(--font-semibold);
}

.trend-up {
    color: var(--color-success);
}

.trend-down {
    color: var(--color-error);
}

.metric-value {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-extrabold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
}

.metric-subtitle {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
}

/* Main Dashboard Panels */
.dashboard-panels {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
}

/* Recent Activity Panel */
.panel {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.panel-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-bold);
}

.panel-action {
    color: var(--brand-primary);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: var(--font-medium);
    transition: opacity var(--duration-fast);
}

.panel-action:hover {
    opacity: 0.8;
}

.panel-content {
    padding: 24px;
}

/* Transaction List */
.transaction-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: var(--bg-light);
    border-radius: 8px;
    transition: all var(--duration-fast);
}

.transaction-item:hover {
    background: var(--bg-gray);
}

.transaction-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.icon-food {
    background: #fef3c7;
    color: var(--color-warning);
}

.icon-transport {
    background: #dbeafe;
    color: var(--color-info);
}

.icon-income {
    background: #d1fae5;
    color: var(--color-success);
}

.transaction-details {
    display: flex;
    flex-direction: column;
}

.transaction-name {
    font-weight: var(--font-semibold);
    font-size: var(--font-size-sm);
}

.transaction-category {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.transaction-amount {
    font-weight: var(--font-bold);
    font-size: var(--font-size-base);
}

.amount-debit {
    color: var(--color-error);
}

.amount-credit {
    color: var(--color-success);
}

/* Quick Stats Panel */
.quick-stats {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-light);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-name {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.stat-value {
    font-weight: var(--font-bold);
    font-size: var(--font-size-base);
}

/* Progress Bar */
.progress-section {
    margin-top: 24px;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.progress-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.progress-value {
    font-size: var(--font-size-sm);
    font-weight: var(--font-semibold);
    color: var(--brand-primary);
}

.progress-bar {
    height: 8px;
    background: var(--bg-light);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 4px;
    transition: width 0.3s;
}

/* Dashboard Mobile Responsive */
@media (max-width: 1024px) {
    .dashboard-panels {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        display: none;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-content {
        padding: 16px;
    }
    
    .topbar {
        padding: 12px 16px;
    }
    
    .topbar-left {
        gap: 16px;
    }
}

/* ============================================
   UPLOAD PAGE SPECIFIC STYLES
   ============================================ */

/* Upload Content */
.upload-content {
    flex: 1;
    padding: 32px;
    overflow-y: auto;
}

/* Hero Section */
.upload-hero {
    background: var(--gradient-primary);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 32px;
    color: var(--text-inverse);
    text-align: center;
}

.upload-hero h2 {
    font-size: 32px;
    font-weight: 800;
    margin-bottom: 8px;
}

.upload-hero p {
    font-size: 18px;
    opacity: 0.9;
}

/* Upload Zone */
.upload-zone {
    background: var(--bg-white);
    border: 2px dashed var(--brand-primary);
    border-radius: 16px;
    padding: 64px 32px;
    text-align: center;
    margin-bottom: 32px;
    transition: all 0.3s;
    cursor: pointer;
    position: relative;
}

.upload-zone:hover {
    border-color: var(--brand-secondary);
    background: rgba(41, 83, 67, 0.02);
}

.upload-zone.active {
    border-color: var(--brand-primary);
    border-style: solid;
    background: rgba(41, 83, 67, 0.05);
}

.upload-icon {
    font-size: 64px;
    margin-bottom: 16px;
}

.upload-title {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.upload-subtitle {
    color: var(--text-secondary);
    margin-bottom: 24px;
}

.browse-btn {
    background: var(--gradient-primary);
    color: var(--text-inverse);
    padding: 12px 32px;
    border-radius: 8px;
    border: none;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
}

.browse-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.supported-formats {
    margin-top: 16px;
    color: var(--text-secondary);
    font-size: 14px;
}

/* Files List */
.files-section {
    background: var(--bg-white);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 32px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.section-title {
    font-size: 20px;
    font-weight: 700;
}

.file-count {
    color: var(--text-secondary);
    font-size: 14px;
}

.files-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: var(--bg-light);
    border-radius: 12px;
    transition: all 0.2s;
}

.file-item:hover {
    background: var(--bg-gray);
}

.file-status {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-right: 16px;
}

.status-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--color-success);
}

.status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--color-warning);
}

.status-processing {
    background: rgba(59, 130, 246, 0.1);
    color: var(--color-info);
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 600;
    margin-bottom: 4px;
}

.file-details {
    font-size: 12px;
    color: var(--text-secondary);
}

.file-actions {
    display: flex;
    gap: 8px;
}

.file-action {
    padding: 8px 12px;
    border: 1px solid var(--border-light);
    border-radius: 6px;
    background: var(--bg-white);
    color: var(--text-secondary);
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.file-action:hover {
    background: var(--bg-light);
    color: var(--text-primary);
}

.file-action.danger {
    color: var(--color-error);
}

/* AI Configuration */
.config-section {
    background: var(--bg-white);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 32px;
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-top: 24px;
}

.config-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.config-label {
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.radio-option {
    display: flex;
    align-items: center;
    padding: 12px;
    background: var(--bg-light);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.radio-option:hover {
    background: var(--bg-gray);
}

.radio-option.selected {
    background: rgba(41, 83, 67, 0.1);
    border: 1px solid var(--brand-primary);
}

.radio-input {
    margin-right: 12px;
}

.radio-label {
    flex: 1;
}

.radio-title {
    font-weight: 500;
    margin-bottom: 2px;
}

.radio-description {
    font-size: 12px;
    color: var(--text-secondary);
}

.dropdown {
    padding: 12px;
    border: 1px solid var(--border-light);
    border-radius: 8px;
    background: var(--bg-white);
    font-size: 14px;
    cursor: pointer;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.checkbox-option {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox-input {
    margin-right: 12px;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding: 32px 0;
}

.btn {
    padding: 14px 32px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    border: none;
}

.btn-cancel {
    background: var(--bg-white);
    color: var(--text-secondary);
    border: 1px solid var(--border-light);
}

.btn-cancel:hover {
    background: var(--bg-light);
}

.btn-process {
    background: var(--gradient-primary);
    color: var(--text-inverse);
}

.btn-process:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Upload Mobile Responsive */
@media (max-width: 768px) {
    .upload-content {
        padding: 16px;
    }
    
    .config-grid {
        grid-template-columns: 1fr;
    }
    
    .upload-zone {
        padding: 32px 16px;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
}