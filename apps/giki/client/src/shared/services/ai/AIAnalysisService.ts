/**
 * AI Analysis Service - Client-side integration with Vercel AI SDK backend
 * 
 * Handles all AI-powered operations including vendor extraction,
 * categorization, conversation, and schema interpretation.
 */

interface VendorExtractionResult {
  vendor: string | null;
  vendorType?: string;
  confidence: number;
}

interface CategorizationResult {
  categoryId: string;
  categoryName: string;
  confidence: number;
  reasoning: string;
  alternativeCategories?: Array<{
    categoryId: string;
    categoryName: string;
    confidence: number;
  }>;
}

interface CategoryHierarchy {
  industry: string;
  businessType: string;
  categories: any[];
  totalCategories: number;
  maxDepth: number;
  metadata: {
    generatedAt: string;
    version: string;
    compliance?: string[];
  };
}

interface ConversationResponse {
  message: string;
  intent: 'help' | 'action' | 'clarification' | 'confirmation' | 'error';
  suggestedActions?: Array<{
    label: string;
    action: string;
    description?: string;
  }>;
  confidence: number;
  requiresInput?: boolean;
}

interface SchemaInterpretation {
  fileType: string;
  hasHeaders: boolean;
  dataStartRow: number;
  dateFormat: string;
  columns: Array<{
    columnIndex: number;
    columnName: string;
    mappedTo: string;
    confidence: number;
    examples: string[];
  }>;
  transactionType: 'unified' | 'separate_debit_credit' | 'signed_amount';
  confidence: number;
  warnings?: string[];
}

interface AnalysisProgress {
  phase: 'vendor_extraction' | 'categorization' | 'category_generation' | 'complete';
  progress: number;
  message: string;
  partialResults?: any;
}

export class AIAnalysisService {
  private baseUrl: string;
  private accessToken: string | null = null;

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
    this.accessToken = localStorage.getItem('access_token');
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    
    if (this.accessToken) {
      headers['Authorization'] = `Bearer ${this.accessToken}`;
    }
    
    return headers;
  }

  /**
   * Extract vendor from transaction description
   */
  async extractVendor(description: string): Promise<VendorExtractionResult> {
    const response = await fetch(`${this.baseUrl}/api/v1/ai/extract-vendor`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({ description })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Vendor extraction failed');
    }

    return result.data;
  }

  /**
   * Batch vendor extraction
   */
  async extractVendorsBatch(descriptions: string[]): Promise<VendorExtractionResult[]> {
    const response = await fetch(`${this.baseUrl}/api/v1/ai/extract-vendors-batch`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({ descriptions })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Batch vendor extraction failed');
    }

    return result.data;
  }

  /**
   * Categorize a single transaction
   */
  async categorizeTransaction(
    transaction: any,
    categories: any[]
  ): Promise<CategorizationResult> {
    const response = await fetch(`${this.baseUrl}/api/v1/ai/categorize-transaction`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({ transaction, categories })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Categorization failed');
    }

    return result.data;
  }

  /**
   * Stream batch analysis with SSE
   */
  async *streamBatchAnalysis(
    transactions: any[],
    businessProfile: any,
    onProgress?: (progress: AnalysisProgress) => void
  ): AsyncGenerator<AnalysisProgress, any> {
    const response = await fetch(`${this.baseUrl}/api/v1/ai/analyze-batch-stream`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({ transactions, businessProfile })
    });

    if (!response.ok) {
      throw new Error('Failed to start batch analysis');
    }

    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (!reader) {
      throw new Error('Response body is not readable');
    }

    let buffer = '';
    
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') continue;
          
          try {
            const progress = JSON.parse(data);
            if (onProgress) {
              onProgress(progress);
            }
            yield progress;
            
            if (progress.event === 'complete') {
              return progress.data;
            }
          } catch (e) {
            console.error('Failed to parse SSE data:', e);
          }
        }
      }
    }
  }

  /**
   * Process conversation message
   */
  async processConversation(
    message: string,
    sessionId?: string,
    context?: any
  ): Promise<ConversationResponse & { sessionId: string }> {
    const response = await fetch(`${this.baseUrl}/api/v1/ai/conversation`, {
      method: 'POST',
      headers: {
        ...this.getHeaders(),
        'x-user-id': localStorage.getItem('user_id') || 'anonymous'
      },
      body: JSON.stringify({ message, sessionId, context })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Conversation processing failed');
    }

    return {
      ...result.data,
      sessionId: result.sessionId
    };
  }

  /**
   * Stream conversation response
   */
  async *streamConversation(
    message: string,
    sessionId?: string,
    context?: any
  ): AsyncGenerator<string, ConversationResponse> {
    const response = await fetch(`${this.baseUrl}/api/v1/ai/conversation-stream`, {
      method: 'POST',
      headers: {
        ...this.getHeaders(),
        'x-user-id': localStorage.getItem('user_id') || 'anonymous'
      },
      body: JSON.stringify({ message, sessionId, context })
    });

    if (!response.ok) {
      throw new Error('Failed to start conversation stream');
    }

    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (!reader) {
      throw new Error('Response body is not readable');
    }

    let buffer = '';
    
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          if (line.includes('event: chunk')) {
            yield data; // Yield text chunks
          } else if (line.includes('event: complete')) {
            return JSON.parse(data); // Return final response
          }
        }
      }
    }

    throw new Error('Stream ended without complete event');
  }

  /**
   * Interpret file schema
   */
  async interpretSchema(filePreview: any): Promise<SchemaInterpretation> {
    const response = await fetch(`${this.baseUrl}/api/v1/ai/interpret-schema`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(filePreview)
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Schema interpretation failed');
    }

    return result.data;
  }

  /**
   * Generate category hierarchy for business
   */
  async generateCategories(
    businessProfile: any,
    sampleTransactions?: any[]
  ): Promise<CategoryHierarchy> {
    const response = await fetch(`${this.baseUrl}/api/v1/ai/generate-categories`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({ businessProfile, sampleTransactions })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Category generation failed');
    }

    return result.data;
  }

  /**
   * Suggest new categories based on uncategorized transactions
   */
  async suggestCategories(
    uncategorizedTransactions: Array<{ description: string; amount: number }>,
    existingCategories?: any[]
  ): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/v1/ai/suggest-categories`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({ uncategorizedTransactions, existingCategories })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Category suggestion failed');
    }

    return result.data;
  }

  /**
   * Analyze category health
   */
  async analyzeCategoryHealth(categories: any[]): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/v1/ai/analyze-category-health`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({ categories })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Category health analysis failed');
    }

    return result.data;
  }

  /**
   * Get contextual help for a page
   */
  async getContextualHelp(page: string, topic?: string): Promise<ConversationResponse> {
    const url = new URL(`${this.baseUrl}/api/v1/ai/help/${page}`);
    if (topic) {
      url.searchParams.set('topic', topic);
    }

    const response = await fetch(url.toString(), {
      headers: this.getHeaders()
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Failed to get help');
    }

    return result.data;
  }

  /**
   * Get smart suggestions
   */
  async getSmartSuggestions(
    context: any,
    recentActivity?: string[]
  ): Promise<string[]> {
    const response = await fetch(`${this.baseUrl}/api/v1/ai/smart-suggestions`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({ context, recentActivity })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Failed to get suggestions');
    }

    return result.data;
  }
}

// Export singleton instance
export const aiAnalysisService = new AIAnalysisService();