{"name": "@workspace/giki-client", "version": "1.0.0", "description": "Giki AI BHVR Client - React + TypeScript + Vite", "type": "module", "scripts": {"dev": "vite --port 3000 --host", "dev:strict": "vite --port 3000 --strictPort", "build": "vite build", "build:strict": "tsc && vite build", "start": "vite preview --port 3000", "preview": "vite preview --port 3000", "test": "vitest ../tests/unit/", "test:ui": "vitest --ui ../tests/unit/", "test:unit": "vitest ../tests/unit/", "test:integration": "vitest ../tests/integration/", "test:e2e": "bunx playwright test ../tests/e2e/", "test:watch": "vitest --watch", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "format": "prettier --write src/**/*.{ts,tsx}"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.3.0", "axios": "^1.6.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.0", "react-router-dom": "^6.20.0", "tailwind-merge": "^2.0.0", "zod": "^3.22.0", "zustand": "^4.4.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6"}}