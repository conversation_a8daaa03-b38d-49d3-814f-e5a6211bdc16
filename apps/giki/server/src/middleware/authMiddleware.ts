import { Context, Next } from 'hono';
import { getAuthService } from '../services/authService.js';
import { getCookie } from '../utils/cookieParser.js';
import type { ApiResponse, User } from '@shared/types/index';

// Extend Hono's Context type to include user
declare module 'hono' {
  interface ContextVariableMap {
    user: User;
  }
}

/**
 * Authentication middleware for protecting API routes
 */
export const authMiddleware = async (c: Context, next: Next) => {
  try {
    // Extract token from Authorization header or cookie
    const authHeader = c.req.header('Authorization');
    const accessToken = authHeader?.startsWith('Bearer ') 
      ? authHeader.slice(7)
      : getCookie(c.req.header('Cookie'), 'accessToken');

    if (!accessToken) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Access token required'
        }
      }, 401);
    }

    // Verify token and get user
    const authService = getAuthService();
    const user = await authService.getCurrentUser(accessToken);

    if (!user) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid or expired access token'
        }
      }, 401);
    }

    // Add user to context
    c.set('user', user);

    await next();
  } catch (error: any) {
    console.error('Auth middleware error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'AUTH_ERROR',
        message: 'Authentication failed'
      }
    }, 500);
  }
};

/**
 * Optional authentication middleware - sets user if token is valid, but doesn't block
 */
export const optionalAuthMiddleware = async (c: Context, next: Next) => {
  try {
    const authHeader = c.req.header('Authorization');
    const accessToken = authHeader?.startsWith('Bearer ') 
      ? authHeader.slice(7)
      : getCookie(c.req.header('Cookie'), 'accessToken');

    if (accessToken) {
      const authService = getAuthService();
      const user = await authService.getCurrentUser(accessToken);
      
      if (user) {
        c.set('user', user);
      }
    }

    await next();
  } catch (error) {
    // Ignore errors in optional auth
    await next();
  }
};

/**
 * Role-based access control middleware
 */
export const requireRole = (requiredRole: string) => {
  return async (c: Context, next: Next) => {
    const user = c.get('user');
    
    if (!user) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      }, 401);
    }

    if (user.role !== requiredRole && user.role !== 'admin') {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Insufficient permissions'
        }
      }, 403);
    }

    await next();
  };
};