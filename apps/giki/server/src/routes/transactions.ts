import { Hono } from 'hono';
import type { ApiResponse, Transaction, CategorizationRequest, CategorizationResult } from '@shared/types/index';
import { getGenAIService } from '../services/genAIService.js';
import { db } from '../config/database.js';
import { transactions, categories } from '../schema/index.js';
import { eq, and, like, desc, asc, count, sql } from 'drizzle-orm';
import { authMiddleware } from '../middleware/authMiddleware.js';

const transactionRoutes = new Hono();

// Apply auth middleware to all routes
transactionRoutes.use('*', authMiddleware);
const genAIService = getGenAIService();

// GET /api/v1/transactions
transactionRoutes.get('/', async (c) => {
  try {
    const userId = c.get('userId') as string;
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '50');
    const search = c.req.query('search');
    const status = c.req.query('status');
    const category = c.req.query('category');
    const dateFrom = c.req.query('dateFrom');
    const dateTo = c.req.query('dateTo');
    
    const offset = (page - 1) * limit;
    
    // Build where conditions
    const conditions = [eq(transactions.userId, userId)];
    
    if (search) {
      conditions.push(like(transactions.description, `%${search}%`));
    }
    
    if (dateFrom) {
      conditions.push(sql`${transactions.date} >= ${dateFrom}`);
    }
    
    if (dateTo) {
      conditions.push(sql`${transactions.date} <= ${dateTo}`);
    }
    
    // Get transactions with category info
    const transactionQuery = db
      .select({
        id: transactions.id,
        userId: transactions.userId,
        fileId: transactions.fileId,
        date: transactions.date,
        description: transactions.description,
        amount: transactions.amount,
        type: transactions.type,
        categoryId: transactions.categoryId,
        confidence: transactions.confidence,
        isReviewed: transactions.isReviewed,
        vendor: transactions.vendor,
        notes: transactions.notes,
        createdAt: transactions.createdAt,
        updatedAt: transactions.updatedAt,
        category: categories.name,
        glCode: categories.glCode
      })
      .from(transactions)
      .leftJoin(categories, eq(transactions.categoryId, categories.id))
      .where(and(...conditions))
      .orderBy(desc(transactions.date))
      .limit(limit)
      .offset(offset);
    
    const transactionResults = await transactionQuery;
    
    // Get total count
    const [totalResult] = await db
      .select({ count: count() })
      .from(transactions)
      .where(and(...conditions));
    
    const total = totalResult.count;
    
    // Transform to match frontend interface
    const transformedTransactions = transactionResults.map(t => ({
      id: t.id,
      userId: t.userId,
      fileId: t.fileId,
      date: t.date,
      description: t.description,
      amount: parseFloat(t.amount),
      type: t.type,
      categoryId: t.categoryId,
      category: t.category,
      glCode: t.glCode,
      confidence: t.confidence ? parseFloat(t.confidence) : undefined,
      isReviewed: t.isReviewed,
      vendor: t.vendor,
      notes: t.notes,
      status: t.isReviewed ? 'confirmed' : (t.confidence && parseFloat(t.confidence) < 0.6 ? 'review' : 'pending'),
      createdAt: t.createdAt,
      updatedAt: t.updatedAt
    }));
    
    return c.json<ApiResponse<Transaction[]>>({
      success: true,
      data: transformedTransactions,
      meta: {
        page,
        limit,
        total,
        hasMore: offset + limit < total
      }
    });
  } catch (error: any) {
    console.error('Error fetching transactions:', error);
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'FETCH_TRANSACTIONS_ERROR',
        message: 'Failed to fetch transactions',
        details: error.message
      }
    }, 500);
  }
});

// GET /api/v1/transactions/categorization-status
transactionRoutes.get('/categorization-status', async (c) => {
  try {
    const userId = c.get('userId') as string;
    
    const [totalResult] = await db
      .select({ count: count() })
      .from(transactions)
      .where(eq(transactions.userId, userId));
    
    const [categorizedResult] = await db
      .select({ count: count() })
      .from(transactions)
      .where(and(
        eq(transactions.userId, userId),
        sql`${transactions.categoryId} IS NOT NULL`
      ));
    
    const total = totalResult.count;
    const categorized = categorizedResult.count;
    const uncategorized = total - categorized;
    
    return c.json<ApiResponse>({
      success: true,
      data: {
        total,
        categorized,
        uncategorized,
        percentage: total > 0 ? Math.round((categorized / total) * 100) : 0
      }
    });
  } catch (error: any) {
    console.error('Error fetching categorization status:', error);
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'CATEGORIZATION_STATUS_ERROR',
        message: 'Failed to fetch categorization status',
        details: error.message
      }
    }, 500);
  }
});

// GET /api/v1/transactions/review-status
transactionRoutes.get('/review-status', async (c) => {
  try {
    const userId = c.get('userId') as string;
    
    const [totalResult] = await db
      .select({ count: count() })
      .from(transactions)
      .where(eq(transactions.userId, userId));
    
    const [reviewedResult] = await db
      .select({ count: count() })
      .from(transactions)
      .where(and(
        eq(transactions.userId, userId),
        eq(transactions.isReviewed, true)
      ));
    
    const total = totalResult.count;
    const reviewed = reviewedResult.count;
    const pending = total - reviewed;
    
    return c.json<ApiResponse>({
      success: true,
      data: {
        total,
        reviewed,
        pending,
        percentage: total > 0 ? Math.round((reviewed / total) * 100) : 0
      }
    });
  } catch (error: any) {
    console.error('Error fetching review status:', error);
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'REVIEW_STATUS_ERROR',
        message: 'Failed to fetch review status',
        details: error.message
      }
    }, 500);
  }
});

// POST /api/v1/transactions/categorize
transactionRoutes.post('/categorize', async (c) => {
  try {
    const userId = c.get('userId') as string;
    const body = await c.req.json<CategorizationRequest>();
    
    const results: CategorizationResult[] = [];
    
    // Get available categories from database
    const availableCategories = await db
      .select({
        id: categories.id,
        name: categories.name,
        glCode: categories.glCode
      })
      .from(categories)
      .where(eq(categories.isActive, true));
    
    if (body.useAI) {
      // Use real AI categorization
      for (const transactionId of body.transactionIds) {
        const [transaction] = await db
          .select()
          .from(transactions)
          .where(and(
            eq(transactions.id, transactionId),
            eq(transactions.userId, userId)
          ));
        
        if (transaction) {
          const aiResult = await genAIService.categorizeTransaction({
            description: transaction.description,
            amount: parseFloat(transaction.amount),
            type: transaction.type,
            vendor: transaction.vendor,
            categories: availableCategories
          });
          
          results.push({
            transactionId,
            categoryId: aiResult.categoryId,
            confidence: aiResult.confidence,
            method: 'ai'
          });
          
          // Update transaction in database
          await db
            .update(transactions)
            .set({
              categoryId: aiResult.categoryId,
              confidence: aiResult.confidence.toString(),
              updatedAt: new Date()
            })
            .where(eq(transactions.id, transactionId));
        }
      }
    } else {
      // Rule-based categorization fallback
      for (const transactionId of body.transactionIds) {
        const [transaction] = await db
          .select()
          .from(transactions)
          .where(and(
            eq(transactions.id, transactionId),
            eq(transactions.userId, userId)
          ));
          
        if (transaction) {
          // Simple rule-based logic
          let categoryId = availableCategories.find(c => c.name === 'Other Income')?.id || availableCategories[0]?.id;
          let confidence = 0.6;
          
          const desc = transaction.description.toLowerCase();
          if (desc.includes('software') || desc.includes('subscription')) {
            const cat = availableCategories.find(c => c.name.includes('Software'));
            if (cat) {
              categoryId = cat.id;
              confidence = 0.8;
            }
          } else if (desc.includes('travel') || desc.includes('uber') || desc.includes('flight')) {
            const cat = availableCategories.find(c => c.name.includes('Travel'));
            if (cat) {
              categoryId = cat.id;
              confidence = 0.85;
            }
          } else if (desc.includes('office') || desc.includes('supplies')) {
            const cat = availableCategories.find(c => c.name.includes('Office'));
            if (cat) {
              categoryId = cat.id;
              confidence = 0.75;
            }
          }
          
          results.push({
            transactionId,
            categoryId,
            confidence,
            method: 'rule'
          });
          
          // Update transaction in database
          await db
            .update(transactions)
            .set({
              categoryId,
              confidence: confidence.toString(),
              updatedAt: new Date()
            })
            .where(eq(transactions.id, transactionId));
        }
      }
    }
    
    return c.json<ApiResponse<CategorizationResult[]>>({
      success: true,
      data: results
    });
  } catch (error: any) {
    console.error('Categorization error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'CATEGORIZATION_ERROR',
        message: 'Failed to categorize transactions',
        details: error.message
      }
    }, 500);
  }
});

// POST /api/v1/transactions/upload
transactionRoutes.post('/upload', async (c) => {
  try {
    const userId = c.get('userId') as string;
    const body = await c.req.json();
    
    // Create mock transactions (in real app, this would parse uploaded file)
    const count = body.count || 10;
    const transactionsToInsert = [];
    
    for (let i = 0; i < count; i++) {
      transactionsToInsert.push({
        userId,
        fileId: body.fileId || crypto.randomUUID(),
        date: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000), // Random date within last 90 days
        description: `Transaction ${i + 1} - ${['Office supplies', 'Travel expense', 'Software license', 'Marketing campaign', 'Utility bill'][Math.floor(Math.random() * 5)]}`,
        amount: (Math.random() * 10000).toFixed(2),
        type: Math.random() > 0.5 ? 'debit' as const : 'credit' as const,
        vendor: ['Amazon', 'Microsoft', 'Google', 'Apple', 'Uber'][Math.floor(Math.random() * 5)],
        isReviewed: false
      });
    }
    
    const insertedTransactions = await db
      .insert(transactions)
      .values(transactionsToInsert)
      .returning();
    
    return c.json<ApiResponse>({
      success: true,
      data: {
        count: insertedTransactions.length,
        transactions: insertedTransactions.map(t => ({
          ...t,
          amount: parseFloat(t.amount),
          confidence: t.confidence ? parseFloat(t.confidence) : undefined,
          status: 'pending' as const
        }))
      }
    });
  } catch (error: any) {
    console.error('Upload error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'UPLOAD_ERROR',
        message: 'Failed to upload transactions',
        details: error.message
      }
    }, 500);
  }
});

// GET /api/v1/transactions/:id
transactionRoutes.get('/:id', async (c) => {
  try {
    const userId = c.get('userId') as string;
    const transactionId = c.req.param('id');
    
    const [result] = await db
      .select({
        id: transactions.id,
        userId: transactions.userId,
        fileId: transactions.fileId,
        date: transactions.date,
        description: transactions.description,
        amount: transactions.amount,
        type: transactions.type,
        categoryId: transactions.categoryId,
        confidence: transactions.confidence,
        isReviewed: transactions.isReviewed,
        vendor: transactions.vendor,
        notes: transactions.notes,
        createdAt: transactions.createdAt,
        updatedAt: transactions.updatedAt,
        category: categories.name,
        glCode: categories.glCode
      })
      .from(transactions)
      .leftJoin(categories, eq(transactions.categoryId, categories.id))
      .where(and(
        eq(transactions.id, transactionId),
        eq(transactions.userId, userId)
      ));
    
    if (!result) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'TRANSACTION_NOT_FOUND',
          message: 'Transaction not found'
        }
      }, 404);
    }
    
    const transformedTransaction = {
      id: result.id,
      userId: result.userId,
      fileId: result.fileId,
      date: result.date,
      description: result.description,
      amount: parseFloat(result.amount),
      type: result.type,
      categoryId: result.categoryId,
      category: result.category,
      glCode: result.glCode,
      confidence: result.confidence ? parseFloat(result.confidence) : undefined,
      isReviewed: result.isReviewed,
      vendor: result.vendor,
      notes: result.notes,
      status: result.isReviewed ? 'confirmed' as const : (result.confidence && parseFloat(result.confidence) < 0.6 ? 'review' as const : 'pending' as const),
      createdAt: result.createdAt,
      updatedAt: result.updatedAt
    };
    
    return c.json<ApiResponse<Transaction>>({
      success: true,
      data: transformedTransaction
    });
  } catch (error: any) {
    console.error('Error fetching transaction:', error);
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'FETCH_TRANSACTION_ERROR',
        message: 'Failed to fetch transaction',
        details: error.message
      }
    }, 500);
  }
});

// PUT /api/v1/transactions/:id
transactionRoutes.put('/:id', async (c) => {
  try {
    const userId = c.get('userId') as string;
    const transactionId = c.req.param('id');
    const updates = await c.req.json();
    
    // Check if transaction exists and belongs to user
    const [existingTransaction] = await db
      .select()
      .from(transactions)
      .where(and(
        eq(transactions.id, transactionId),
        eq(transactions.userId, userId)
      ));
    
    if (!existingTransaction) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'TRANSACTION_NOT_FOUND',
          message: 'Transaction not found'
        }
      }, 404);
    }
    
    // Prepare updates
    const updateData: any = {
      updatedAt: new Date()
    };
    
    if (updates.description !== undefined) updateData.description = updates.description;
    if (updates.amount !== undefined) updateData.amount = updates.amount.toString();
    if (updates.type !== undefined) updateData.type = updates.type;
    if (updates.categoryId !== undefined) updateData.categoryId = updates.categoryId;
    if (updates.confidence !== undefined) updateData.confidence = updates.confidence.toString();
    if (updates.isReviewed !== undefined) updateData.isReviewed = updates.isReviewed;
    if (updates.vendor !== undefined) updateData.vendor = updates.vendor;
    if (updates.notes !== undefined) updateData.notes = updates.notes;
    
    // Update transaction
    const [updatedTransaction] = await db
      .update(transactions)
      .set(updateData)
      .where(eq(transactions.id, transactionId))
      .returning();
    
    // Get updated transaction with category info
    const [result] = await db
      .select({
        id: transactions.id,
        userId: transactions.userId,
        fileId: transactions.fileId,
        date: transactions.date,
        description: transactions.description,
        amount: transactions.amount,
        type: transactions.type,
        categoryId: transactions.categoryId,
        confidence: transactions.confidence,
        isReviewed: transactions.isReviewed,
        vendor: transactions.vendor,
        notes: transactions.notes,
        createdAt: transactions.createdAt,
        updatedAt: transactions.updatedAt,
        category: categories.name,
        glCode: categories.glCode
      })
      .from(transactions)
      .leftJoin(categories, eq(transactions.categoryId, categories.id))
      .where(eq(transactions.id, transactionId));
    
    const transformedTransaction = {
      id: result.id,
      userId: result.userId,
      fileId: result.fileId,
      date: result.date,
      description: result.description,
      amount: parseFloat(result.amount),
      type: result.type,
      categoryId: result.categoryId,
      category: result.category,
      glCode: result.glCode,
      confidence: result.confidence ? parseFloat(result.confidence) : undefined,
      isReviewed: result.isReviewed,
      vendor: result.vendor,
      notes: result.notes,
      status: result.isReviewed ? 'confirmed' as const : (result.confidence && parseFloat(result.confidence) < 0.6 ? 'review' as const : 'pending' as const),
      createdAt: result.createdAt,
      updatedAt: result.updatedAt
    };
    
    return c.json<ApiResponse<Transaction>>({
      success: true,
      data: transformedTransaction
    });
  } catch (error: any) {
    console.error('Error updating transaction:', error);
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'UPDATE_TRANSACTION_ERROR',
        message: 'Failed to update transaction',
        details: error.message
      }
    }, 500);
  }
});

// PUT /api/v1/transactions/bulk-update
transactionRoutes.put('/bulk-update', async (c) => {
  try {
    const userId = c.get('userId') as string;
    const body = await c.req.json();
    const { transactionIds, updates } = body;
    
    if (!transactionIds || !Array.isArray(transactionIds) || transactionIds.length === 0) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: 'Transaction IDs are required'
        }
      }, 400);
    }
    
    // Prepare bulk updates
    const updateData: any = {
      updatedAt: new Date()
    };
    
    if (updates.categoryId !== undefined) updateData.categoryId = updates.categoryId;
    if (updates.confidence !== undefined) updateData.confidence = updates.confidence.toString();
    if (updates.isReviewed !== undefined) updateData.isReviewed = updates.isReviewed;
    if (updates.status !== undefined) {
      // Convert status to isReviewed flag
      updateData.isReviewed = updates.status === 'confirmed';
    }
    
    // Update transactions
    await db
      .update(transactions)
      .set(updateData)
      .where(and(
        sql`${transactions.id} = ANY(${transactionIds})`,
        eq(transactions.userId, userId)
      ));
    
    return c.json<ApiResponse>({
      success: true,
      data: {
        updatedCount: transactionIds.length,
        message: 'Transactions updated successfully'
      }
    });
  } catch (error: any) {
    console.error('Error bulk updating transactions:', error);
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'BULK_UPDATE_ERROR',
        message: 'Failed to update transactions',
        details: error.message
      }
    }, 500);
  }
});

export { transactionRoutes };