import { Hono } from 'hono';
import type { ApiResponse } from '@shared/types/index';

const healthRoutes = new Hono();

// GET /api/v1/health
healthRoutes.get('/', (c) => {
  return c.json<ApiResponse>({
    success: true,
    data: {
      status: 'healthy',
      service: 'giki-ai-api',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development'
    }
  });
});

// GET /api/v1/health/ready
healthRoutes.get('/ready', async (c) => {
  // Check database connection and other dependencies
  const checks = {
    database: true, // TODO: Implement actual DB check
    redis: true,    // TODO: Implement actual Redis check
    ai_service: true // TODO: Check Vertex AI connection
  };
  
  const allHealthy = Object.values(checks).every(v => v === true);
  
  return c.json<ApiResponse>({
    success: allHealthy,
    data: {
      ready: allHealthy,
      checks,
      timestamp: new Date().toISOString()
    }
  }, allHealthy ? 200 : 503);
});

// GET /api/v1/health/live
healthRoutes.get('/live', (c) => {
  return c.json<ApiResponse>({
    success: true,
    data: {
      alive: true,
      timestamp: new Date().toISOString()
    }
  });
});

export { healthRoutes };