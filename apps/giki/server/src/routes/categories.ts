import { Hono } from 'hono';
import type { ApiResponse, Category, MISStructure } from '@shared/types/index';
import { db } from '../config/database.js';
import { categories, transactions } from '../schema/index.js';
import { eq, and, like, count, desc, asc } from 'drizzle-orm';
import { getGenAIService } from '../services/genAIService.js';
import { authMiddleware } from '../middleware/authMiddleware.js';

const categoryRoutes = new Hono();
const genAIService = getGenAIService();

// Apply auth middleware to all routes
categoryRoutes.use('*', authMiddleware);

// GET /api/v1/categories - Get comprehensive default category structure
categoryRoutes.get('/', async (c) => {
  try {
    const search = c.req.query('search');
    const level = c.req.query('level');
    const parentId = c.req.query('parentId');
    
    // Build where conditions
    const conditions = [eq(categories.isActive, true)];
    
    if (search) {
      conditions.push(like(categories.name, `%${search}%`));
    }
    
    if (level) {
      conditions.push(eq(categories.level, parseInt(level)));
    }
    
    if (parentId) {
      conditions.push(eq(categories.parentId, parentId));
    }
    
    const categoryResults = await db
      .select({
        id: categories.id,
        name: categories.name,
        parentId: categories.parentId,
        level: categories.level,
        path: categories.path,
        glCode: categories.glCode,
        description: categories.description,
        isSystem: categories.isSystem,
        sortOrder: categories.sortOrder,
        createdAt: categories.createdAt,
        updatedAt: categories.updatedAt
      })
      .from(categories)
      .where(and(...conditions))
      .orderBy(asc(categories.level), asc(categories.sortOrder), asc(categories.name));
    
    return c.json<ApiResponse<Category[]>>({
      success: true,
      data: categoryResults,
      meta: {
        total: categoryResults.length,
        message: `MIS structure with ${categoryResults.length} categories`
      }
    });
  } catch (error: any) {
    console.error('Error fetching categories:', error);
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'GET_CATEGORIES_ERROR',
        message: error.message || 'Failed to get categories'
      }
    }, 500);
  }
});

// GET /api/v1/categories/mis-structure - Get complete MIS structure
categoryRoutes.get('/mis-structure', async (c) => {
  try {
    const user = c.get('user');
    const misStructure = categoryService.getMISStructure(user.id);
    
    return c.json<ApiResponse<MISStructure>>({
      success: true,
      data: misStructure,
      meta: {
        categoriesCount: misStructure.categories.length,
        message: 'Comprehensive business MIS structure with 130+ categories'
      }
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'MIS_STRUCTURE_ERROR',
        message: error.message || 'Failed to get MIS structure'
      }
    }, 500);
  }
});

// GET /api/v1/categories/mis-structure/category/:id/drill-down
categoryRoutes.get('/mis-structure/category/:id/drill-down', async (c) => {
  try {
    const categoryId = c.req.param('id');
    const drillDownData = categoryService.getCategoryDrillDown(categoryId);
    
    if (!drillDownData) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'CATEGORY_NOT_FOUND',
          message: 'Category not found'
        }
      }, 404);
    }
    
    return c.json<ApiResponse>({
      success: true,
      data: drillDownData
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'DRILL_DOWN_ERROR',
        message: error.message || 'Failed to get category drill-down'
      }
    }, 500);
  }
});

// POST /api/v1/categories/generate-mis - AI-powered custom MIS generation
categoryRoutes.post('/generate-mis', async (c) => {
  const body = await c.req.json<{ industry: string; businessType: string }>();
  
  try {
    const user = c.get('user');
    
    // Use enhanced Gemini service for custom MIS generation
    const misResult = await geminiService.interpretMISStructure({
      structure_data: `Generate MIS structure for ${body.businessType} in ${body.industry}`,
      format: 'text',
      industry_hint: body.industry
    });
    
    if (misResult.categories.length === 0) {
      // Fallback to default comprehensive structure
      const defaultCategories = categoryService.getCategories(user.id);
      
      return c.json<ApiResponse>({
        success: true,
        data: {
          categories: defaultCategories,
          message: 'Using comprehensive default MIS structure',
          isDefault: true
        }
      });
    }
    
    return c.json<ApiResponse>({
      success: true,
      data: {
        categories: misResult.categories,
        confidence: misResult.confidence,
        industryDetected: misResult.industry_detected,
        glCodePattern: misResult.gl_code_pattern,
        validationIssues: misResult.validation_issues,
        message: 'AI-generated custom MIS structure'
      }
    });
  } catch (error: any) {
    console.error('MIS generation error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'MIS_GENERATION_ERROR',
        message: 'Failed to generate custom MIS structure',
        details: error.message
      }
    }, 500);
  }
});

// POST /api/v1/categories - Create new category
categoryRoutes.post('/', async (c) => {
  try {
    const user = c.get('user');
    const body = await c.req.json();
    
    // Validate required fields
    if (!body.name) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Category name is required'
        }
      }, 400);
    }
    
    const newCategory = categoryService.createCategory(body, user.id);
    
    return c.json<ApiResponse<Category>>({
      success: true,
      data: newCategory,
      meta: {
        message: 'Category created successfully'
      }
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'CREATE_CATEGORY_ERROR',
        message: error.message || 'Failed to create category'
      }
    }, 500);
  }
});

// PUT /api/v1/categories/:id - Update category
categoryRoutes.put('/:id', async (c) => {
  try {
    const categoryId = c.req.param('id');
    const updates = await c.req.json();
    
    const updatedCategory = categoryService.updateCategory(categoryId, updates);
    
    if (!updatedCategory) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'CATEGORY_NOT_FOUND',
          message: 'Category not found'
        }
      }, 404);
    }
    
    return c.json<ApiResponse<Category>>({
      success: true,
      data: updatedCategory,
      meta: {
        message: 'Category updated successfully'
      }
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'UPDATE_CATEGORY_ERROR',
        message: error.message || 'Failed to update category'
      }
    }, 500);
  }
});

// DELETE /api/v1/categories/:id - Delete category
categoryRoutes.delete('/:id', async (c) => {
  try {
    const categoryId = c.req.param('id');
    
    const success = categoryService.deleteCategory(categoryId);
    
    if (!success) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'DELETE_FAILED',
          message: 'Category not found or cannot be deleted (system categories are protected)'
        }
      }, 400);
    }
    
    return c.json<ApiResponse>({
      success: true,
      data: { message: 'Category deleted successfully' }
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'DELETE_CATEGORY_ERROR',
        message: error.message || 'Failed to delete category'
      }
    }, 500);
  }
});

export { categoryRoutes };