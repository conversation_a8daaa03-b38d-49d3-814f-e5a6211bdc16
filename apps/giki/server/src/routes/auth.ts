import { <PERSON>o } from 'hono';
import { z<PERSON>alidator } from '@hono/zod-validator';
import { z } from 'zod';
import { getAuthService } from '../services/authService.js';
import { optionalAuthMiddleware } from '../middleware/authMiddleware.js';
import type { LoginRequest, RegisterRequest, ApiResponse, User, AuthTokens } from '@shared/types/index';

const authRoutes = new Hono();

// Apply optional auth middleware to auth routes (for /me endpoint)
authRoutes.use('/me', optionalAuthMiddleware);

// Validation schemas
const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6)
});

const registerSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  name: z.string().optional(),
  company: z.string().optional()
});

// POST /api/v1/auth/login
authRoutes.post('/login', zValidator('json', loginSchema), async (c) => {
  try {
    const body = await c.req.json<LoginRequest>();
    const authService = getAuthService();
    
    const result = await authService.login(body.email, body.password);
    
    if (!result.success) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'INVALID_CREDENTIALS',
          message: 'Invalid email or password'
        }
      }, 401);
    }
    
    // Set cookies for tokens
    c.header('Set-Cookie', `accessToken=${result.tokens.accessToken}; HttpOnly; Secure; SameSite=Strict; Path=/`);
    c.header('Set-Cookie', `refreshToken=${result.tokens.refreshToken}; HttpOnly; Secure; SameSite=Strict; Path=/`);
    
    return c.json<ApiResponse<{ user: User; tokens: AuthTokens }>>({
      success: true,
      data: {
        user: result.user,
        tokens: result.tokens
      }
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'AUTH_ERROR',
        message: error.message || 'Authentication failed'
      }
    }, 500);
  }
});

// POST /api/v1/auth/register
authRoutes.post('/register', zValidator('json', registerSchema), async (c) => {
  try {
    const body = await c.req.json<RegisterRequest>();
    const authService = getAuthService();
    
    const result = await authService.register(body);
    
    if (!result.success) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'REGISTRATION_FAILED',
          message: result.error || 'Registration failed'
        }
      }, 400);
    }
    
    return c.json<ApiResponse<{ user: User; tokens: AuthTokens }>>({
      success: true,
      data: {
        user: result.user,
        tokens: result.tokens
      }
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'REGISTRATION_ERROR',
        message: error.message || 'Registration failed'
      }
    }, 500);
  }
});

// POST /api/v1/auth/refresh
authRoutes.post('/refresh', async (c) => {
  try {
    const refreshToken = c.req.header('Authorization')?.replace('Bearer ', '') || 
                        (c.req.cookie && c.req.cookie('refreshToken'));
    
    if (!refreshToken) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'NO_REFRESH_TOKEN',
          message: 'Refresh token not provided'
        }
      }, 401);
    }
    
    const authService = getAuthService();
    const result = await authService.refreshTokens(refreshToken);
    
    if (!result.success) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'INVALID_REFRESH_TOKEN',
          message: 'Invalid or expired refresh token'
        }
      }, 401);
    }
    
    // Set new cookies
    c.header('Set-Cookie', `accessToken=${result.tokens.accessToken}; HttpOnly; Secure; SameSite=Strict; Path=/`);
    c.header('Set-Cookie', `refreshToken=${result.tokens.refreshToken}; HttpOnly; Secure; SameSite=Strict; Path=/`);
    
    return c.json<ApiResponse<AuthTokens>>({
      success: true,
      data: result.tokens
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'TOKEN_REFRESH_ERROR',
        message: error.message || 'Token refresh failed'
      }
    }, 500);
  }
});

// POST /api/v1/auth/logout
authRoutes.post('/logout', async (c) => {
  try {
    // Clear cookies
    c.header('Set-Cookie', 'accessToken=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0');
    c.header('Set-Cookie', 'refreshToken=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0');
    
    return c.json<ApiResponse>({
      success: true,
      data: { message: 'Logged out successfully' }
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'LOGOUT_ERROR',
        message: error.message || 'Logout failed'
      }
    }, 500);
  }
});

// GET /api/v1/auth/me
authRoutes.get('/me', async (c) => {
  try {
    const accessToken = c.req.header('Authorization')?.replace('Bearer ', '') ||
                       (c.req.cookie && c.req.cookie('accessToken'));
    
    if (!accessToken) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'NO_ACCESS_TOKEN',
          message: 'Access token not provided'
        }
      }, 401);
    }
    
    const authService = getAuthService();
    const user = await authService.getCurrentUser(accessToken);
    
    if (!user) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid or expired access token'
        }
      }, 401);
    }
    
    return c.json<ApiResponse<User>>({
      success: true,
      data: user
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'AUTH_ERROR',
        message: error.message || 'Failed to get user info'
      }
    }, 500);
  }
});

export { authRoutes };