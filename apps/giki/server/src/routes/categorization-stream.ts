/**
 * Streaming categorization endpoints for real-time progress updates
 */

import { Hono } from 'hono';
import { streamSSE } from 'hono/streaming';
import { getEnhancedGenAIService } from '../services/enhancedGenAIService.js';
// import { transactionService } from '../services/transactionService.ts'; // Service not yet implemented
import { categoryService } from '../services/categoryService.js';

const app = new Hono();

interface StreamingSession {
  id: string;
  userId: string;
  transactionIds: string[];
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  results: any[];
  createdAt: Date;
}

// In-memory session storage (in production, use Redis or database)
const streamingSessions = new Map<string, StreamingSession>();

/**
 * Start batch categorization with streaming progress
 */
app.post('/start', async (c) => {
  const { transactionIds, userId } = await c.req.json();
  
  if (!transactionIds || !Array.isArray(transactionIds) || transactionIds.length === 0) {
    return c.json({ success: false, error: 'Invalid transaction IDs' }, 400);
  }

  const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  // Create streaming session
  const session: StreamingSession = {
    id: sessionId,
    userId,
    transactionIds,
    status: 'pending',
    progress: 0,
    results: [],
    createdAt: new Date()
  };
  
  streamingSessions.set(sessionId, session);
  
  // Start background processing
  processCategorizationStream(sessionId).catch(error => {
    console.error(`Stream processing error for session ${sessionId}:`, error);
    const session = streamingSessions.get(sessionId);
    if (session) {
      session.status = 'error';
      streamingSessions.set(sessionId, session);
    }
  });
  
  return c.json({
    success: true,
    sessionId,
    message: `Started categorization for ${transactionIds.length} transactions`
  });
});

/**
 * SSE stream endpoint for real-time progress updates
 */
app.get('/stream/:sessionId', (c) => {
  const sessionId = c.req.param('sessionId');
  const session = streamingSessions.get(sessionId);
  
  if (!session) {
    return c.json({ success: false, error: 'Session not found' }, 404);
  }

  return streamSSE(c, async (stream) => {
    console.log(`📡 Starting SSE stream for session: ${sessionId}`);
    
    // Send initial status
    await stream.writeSSE({
      data: JSON.stringify({
        type: 'status',
        sessionId,
        status: session.status,
        progress: session.progress,
        total: session.transactionIds.length
      }),
      event: 'status-update'
    });

    // Stream updates until completion
    const checkInterval = setInterval(async () => {
      const currentSession = streamingSessions.get(sessionId);
      if (!currentSession) {
        clearInterval(checkInterval);
        return;
      }

      // Send progress update
      await stream.writeSSE({
        data: JSON.stringify({
          type: 'progress',
          sessionId,
          status: currentSession.status,
          progress: currentSession.progress,
          processed: currentSession.results.length,
          total: currentSession.transactionIds.length,
          lastResult: currentSession.results[currentSession.results.length - 1]
        }),
        event: 'progress-update'
      });

      // Check if completed or errored
      if (currentSession.status === 'completed' || currentSession.status === 'error') {
        await stream.writeSSE({
          data: JSON.stringify({
            type: 'complete',
            sessionId,
            status: currentSession.status,
            results: currentSession.results,
            summary: {
              totalProcessed: currentSession.results.length,
              averageConfidence: currentSession.results.reduce((sum, r) => sum + (r.confidence || 0), 0) / currentSession.results.length,
              highConfidenceCount: currentSession.results.filter(r => (r.confidence || 0) > 0.8).length
            }
          }),
          event: 'completion'
        });
        
        clearInterval(checkInterval);
        console.log(`✅ SSE stream completed for session: ${sessionId}`);
        
        // Clean up session after 1 hour
        setTimeout(() => {
          streamingSessions.delete(sessionId);
          console.log(`🗑️ Cleaned up session: ${sessionId}`);
        }, 60 * 60 * 1000);
      }
    }, 1000); // Check every second

    // Handle client disconnect
    stream.onAbort(() => {
      console.log(`🔌 Client disconnected from session: ${sessionId}`);
      clearInterval(checkInterval);
    });
  });
});

/**
 * Get session status
 */
app.get('/status/:sessionId', (c) => {
  const sessionId = c.req.param('sessionId');
  const session = streamingSessions.get(sessionId);
  
  if (!session) {
    return c.json({ success: false, error: 'Session not found' }, 404);
  }
  
  return c.json({
    success: true,
    session: {
      id: session.id,
      status: session.status,
      progress: session.progress,
      processed: session.results.length,
      total: session.transactionIds.length,
      results: session.results
    }
  });
});

/**
 * Background processing function
 */
async function processCategorizationStream(sessionId: string): Promise<void> {
  const session = streamingSessions.get(sessionId);
  if (!session) return;

  try {
    session.status = 'processing';
    streamingSessions.set(sessionId, session);
    
    console.log(`🚀 Starting background categorization for session: ${sessionId}`);
    
    // Get transactions and categories
    const transactions = await Promise.all(
      session.transactionIds.map(id => transactionService.getTransactionById(id))
    );
    const categories = await categoryService.getCategories();
    
    const aiService = getEnhancedGenAIService();
    
    // Process transactions with streaming
    let processed = 0;
    for await (const update of aiService.streamBatchCategorization(transactions, categories)) {
      processed = update.processed;
      
      // Update session
      const currentSession = streamingSessions.get(sessionId);
      if (currentSession) {
        currentSession.progress = update.progress;
        currentSession.results.push({
          transactionId: transactions[update.processed - 1].id,
          ...update.result,
          processingTime: Date.now() - session.createdAt.getTime()
        });
        streamingSessions.set(sessionId, currentSession);
      }
      
      console.log(`📈 Session ${sessionId}: ${update.processed}/${update.total} (${update.progress.toFixed(1)}%)`);
    }
    
    // Mark as completed
    const finalSession = streamingSessions.get(sessionId);
    if (finalSession) {
      finalSession.status = 'completed';
      finalSession.progress = 100;
      streamingSessions.set(sessionId, finalSession);
      
      console.log(`✅ Session ${sessionId} completed: ${processed} transactions processed`);
    }
    
  } catch (error) {
    console.error(`❌ Error in session ${sessionId}:`, error);
    const errorSession = streamingSessions.get(sessionId);
    if (errorSession) {
      errorSession.status = 'error';
      streamingSessions.set(sessionId, errorSession);
    }
  }
}

export default app;