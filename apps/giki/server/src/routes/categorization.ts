import { Hono } from 'hono';
import type { ApiResponse } from '@shared/types/index';
import { getAdvancedGenAIService } from '../services/advancedGenAIService.js';
import { z } from 'zod';

const categorizationRoutes = new Hono();
const advancedGenAIService = getAdvancedGenAIService();

// Request/Response schemas
const TransactionSchema = z.object({
  description: z.string().min(1, 'Description is required'),
  amount: z.number().positive('Amount must be positive'),
  date: z.string().datetime('Invalid date format'),
  vendor: z.string().optional(),
  categories: z.array(z.object({
    id: z.string(),
    name: z.string(),
    path: z.string(),
    examples: z.array(z.string()).optional()
  }))
});

const BulkCategorizationSchema = z.object({
  transactions: z.array(TransactionSchema).min(1, 'At least one transaction is required'),
  useAdvanced: z.boolean().default(true),
  useParallel: z.boolean().default(false)
});

// POST /api/v1/categorization/single
categorizationRoutes.post('/single', async (c) => {
  try {
    const body = await c.req.json();
    const validatedData = TransactionSchema.parse(body);
    
    const transaction = {
      ...validatedData,
      date: new Date(validatedData.date)
    };

    console.log(`🤖 Categorizing single transaction: ${transaction.description}`);
    
    const result = await advancedGenAIService.categorizeTransactionAdvanced(transaction);
    
    return c.json<ApiResponse<typeof result>>({
      success: true,
      data: result,
      meta: {
        processingTime: result.processingTime,
        confidence: result.confidence,
        agentAgreement: result.agentAgreement
      }
    });

  } catch (error: any) {
    console.error('❌ Single categorization error:', error);
    
    if (error.name === 'ZodError') {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          details: error.errors
        }
      }, 400);
    }

    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'CATEGORIZATION_ERROR',
        message: 'Failed to categorize transaction',
        details: error.message
      }
    }, 500);
  }
});

// POST /api/v1/categorization/parallel
categorizationRoutes.post('/parallel', async (c) => {
  try {
    const body = await c.req.json();
    const transaction = TransactionSchema.parse(body);
    
    const transactionData = {
      ...transaction,
      date: new Date(transaction.date)
    };

    console.log(`🔄 Parallel categorization for: ${transactionData.description}`);
    
    const result = await advancedGenAIService.categorizeWithParallelAgents(transactionData);
    
    return c.json<ApiResponse<typeof result>>({
      success: true,
      data: result,
      meta: {
        agentsUsed: result.allResults.length,
        consensusMetrics: result.consensusMetrics,
        averageProcessingTime: result.allResults.reduce((sum, r) => sum + r.processingTime, 0) / result.allResults.length
      }
    });

  } catch (error: any) {
    console.error('❌ Parallel categorization error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'PARALLEL_CATEGORIZATION_ERROR',
        message: 'Failed to categorize with parallel agents',
        details: error.message
      }
    }, 500);
  }
});

// POST /api/v1/categorization/bulk
categorizationRoutes.post('/bulk', async (c) => {
  try {
    const body = await c.req.json();
    const validatedData = BulkCategorizationSchema.parse(body);
    
    console.log(`📊 Bulk categorization for ${validatedData.transactions.length} transactions`);
    console.log(`🔧 Advanced: ${validatedData.useAdvanced}, Parallel: ${validatedData.useParallel}`);

    const results = [];
    const startTime = Date.now();

    for (const [index, transactionData] of validatedData.transactions.entries()) {
      const transaction = {
        ...transactionData,
        date: new Date(transactionData.date)
      };

      console.log(`📝 Processing transaction ${index + 1}/${validatedData.transactions.length}: ${transaction.description}`);

      try {
        let result;
        
        if (validatedData.useParallel) {
          const parallelResult = await advancedGenAIService.categorizeWithParallelAgents(transaction);
          result = {
            categoryId: parallelResult.finalResult.finalCategoryId,
            categoryName: parallelResult.finalResult.finalCategoryName,
            confidence: parallelResult.finalResult.confidence,
            reasoning: parallelResult.finalResult.reasoning,
            agentAgreement: parallelResult.consensusMetrics.agreement,
            evidenceChain: parallelResult.finalResult.evidenceChain,
            processingTime: parallelResult.allResults.reduce((sum, r) => sum + r.processingTime, 0) / parallelResult.allResults.length,
            method: 'parallel',
            agentsUsed: parallelResult.allResults.length
          };
        } else if (validatedData.useAdvanced) {
          const advancedResult = await advancedGenAIService.categorizeTransactionAdvanced(transaction);
          result = {
            ...advancedResult,
            method: 'advanced',
            agentsUsed: 3 // Generator + Critic + Consensus
          };
        } else {
          // Fallback to basic categorization (not implemented in this service)
          throw new Error('Basic categorization not available in advanced service');
        }

        results.push({
          transaction: transactionData,
          result,
          success: true
        });

      } catch (error: any) {
        console.error(`❌ Failed to categorize transaction ${index + 1}:`, error);
        results.push({
          transaction: transactionData,
          error: {
            code: 'CATEGORIZATION_FAILED',
            message: error.message
          },
          success: false
        });
      }
    }

    const totalProcessingTime = Date.now() - startTime;
    const successfulResults = results.filter(r => r.success);
    const failedResults = results.filter(r => !r.success);

    console.log(`✅ Bulk categorization complete: ${successfulResults.length} success, ${failedResults.length} failed`);

    return c.json<ApiResponse<typeof results>>({
      success: true,
      data: results,
      meta: {
        totalTransactions: validatedData.transactions.length,
        successful: successfulResults.length,
        failed: failedResults.length,
        totalProcessingTime,
        averageProcessingTime: totalProcessingTime / validatedData.transactions.length,
        averageConfidence: successfulResults.length > 0 
          ? successfulResults.reduce((sum, r) => sum + (r.result?.confidence || 0), 0) / successfulResults.length 
          : 0,
        method: validatedData.useParallel ? 'parallel' : 'advanced'
      }
    });

  } catch (error: any) {
    console.error('❌ Bulk categorization error:', error);
    
    if (error.name === 'ZodError') {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid bulk categorization data',
          details: error.errors
        }
      }, 400);
    }

    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'BULK_CATEGORIZATION_ERROR',
        message: 'Failed to process bulk categorization',
        details: error.message
      }
    }, 500);
  }
});

// GET /api/v1/categorization/health
categorizationRoutes.get('/health', async (c) => {
  try {
    // Test if services are working
    const testTransaction = {
      description: 'Test transaction for health check',
      amount: 100,
      date: new Date(),
      categories: [
        { id: 'test-1', name: 'Test Category', path: '/Test' }
      ]
    };

    const startTime = Date.now();
    const result = await advancedGenAIService.categorizeTransactionAdvanced(testTransaction);
    const responseTime = Date.now() - startTime;

    return c.json<ApiResponse>({
      success: true,
      data: {
        status: 'healthy',
        services: {
          advancedGenAI: 'operational',
          authentication: process.env.GEMINI_API_KEY ? 'configured' : 'missing_api_key'
        },
        performance: {
          responseTime,
          testConfidence: result.confidence,
          agentAgreement: result.agentAgreement
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'HEALTH_CHECK_FAILED',
        message: 'Categorization service is unhealthy',
        details: error.message
      }
    }, 503);
  }
});

export { categorizationRoutes };