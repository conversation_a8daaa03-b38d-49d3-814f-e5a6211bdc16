import { Hono } from 'hono';
import { getFileUploadService } from '../services/fileUploadService.js';
import { authMiddleware } from '../middleware/authMiddleware.js';
import type { ApiResponse, FileUpload } from '@shared/types/index';

const fileRoutes = new Hono();
const fileService = getFileUploadService();

// Apply auth middleware to all file routes
fileRoutes.use('*', authMiddleware);

// GET /api/v1/files/uploaded-files
fileRoutes.get('/uploaded-files', async (c) => {
  try {
    const user = c.get('user');
    const files = fileService.getUserFiles(user.id);
    
    return c.json<ApiResponse<FileUpload[]>>({
      success: true,
      data: files,
      meta: {
        total: files.length
      }
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'GET_FILES_ERROR',
        message: error.message || 'Failed to get uploaded files'
      }
    }, 500);
  }
});

// POST /api/v1/files/upload
fileRoutes.post('/upload', async (c) => {
  try {
    const user = c.get('user');
    const body = await c.req.parseBody();
    const file = body['file'] as File;
    
    if (!file) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'NO_FILE',
          message: 'No file provided'
        }
      }, 400);
    }
    
    // Upload file using service
    const fileRecord = await fileService.uploadFile(file, user.id);
    
    // NEW SMART WORKFLOW: Don't auto-process, require preview+confirmation
    // Files now stay in 'pending' status until user confirms via preview
    
    return c.json<ApiResponse<FileUpload & { nextStep?: string }>>({
      success: true,
      data: {
        ...fileRecord,
        nextStep: 'preview_required' // Indicate next step to frontend
      },
      meta: {
        message: 'File uploaded successfully. Use /preview endpoint to review before processing.',
        previewUrl: `/api/v1/files/${fileRecord.id}/preview`
      }
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'UPLOAD_ERROR',
        message: error.message || 'File upload failed'
      }
    }, 500);
  }
});

// GET /api/v1/files/:id
fileRoutes.get('/:id', async (c) => {
  try {
    const user = c.get('user');
    const fileId = c.req.param('id');
    const file = fileService.getFile(fileId);
    
    if (!file || file.userId !== user.id) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'FILE_NOT_FOUND',
          message: 'File not found'
        }
      }, 404);
    }
    
    return c.json<ApiResponse<FileUpload>>({
      success: true,
      data: file
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'GET_FILE_ERROR',
        message: error.message || 'Failed to get file'
      }
    }, 500);
  }
});

// GET /api/v1/files/:id/progress
fileRoutes.get('/:id/progress', async (c) => {
  try {
    const user = c.get('user');
    const fileId = c.req.param('id');
    const file = fileService.getFile(fileId);
    
    if (!file || file.userId !== user.id) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'FILE_NOT_FOUND',
          message: 'File not found'
        }
      }, 404);
    }
    
    const progress = fileService.getUploadProgress(fileId);
    
    return c.json<ApiResponse>({
      success: true,
      data: {
        fileId,
        progress: progress || 0,
        status: file.status,
        transactionCount: file.transactionCount
      }
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'PROGRESS_ERROR',
        message: error.message || 'Failed to get progress'
      }
    }, 500);
  }
});

// DELETE /api/v1/files/:id
fileRoutes.delete('/:id', async (c) => {
  try {
    const user = c.get('user');
    const fileId = c.req.param('id');
    
    const success = await fileService.deleteFile(fileId, user.id);
    
    if (!success) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'DELETE_FAILED',
          message: 'Failed to delete file or file not found'
        }
      }, 404);
    }
    
    return c.json<ApiResponse>({
      success: true,
      data: { message: 'File deleted successfully' }
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'DELETE_ERROR',
        message: error.message || 'Failed to delete file'
      }
    }, 500);
  }
});

// GET /api/v1/files/:id/preview - Smart file processing: AI preview before processing
fileRoutes.get('/:id/preview', async (c) => {
  try {
    const user = c.get('user');
    const fileId = c.req.param('id');
    const file = fileService.getFile(fileId);
    
    if (!file || file.userId !== user.id) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'FILE_NOT_FOUND',
          message: 'File not found'
        }
      }, 404);
    }
    
    // Get AI preview with schema interpretation and validation
    const previewResult = await fileService.previewFile(fileId);
    
    return c.json<ApiResponse>({
      success: previewResult.success,
      data: previewResult.success ? {
        fileId,
        fileName: file.fileName,
        schemaInterpretation: previewResult.schemaInterpretation,
        validationResult: previewResult.validationResult,
        previewData: previewResult.previewData,
        needsHumanReview: previewResult.needsHumanReview,
        recommendedAction: previewResult.needsHumanReview ? 'review_required' : 'ready_to_process'
      } : null,
      error: previewResult.success ? undefined : {
        code: 'PREVIEW_ERROR',
        message: previewResult.error || 'Failed to generate preview'
      }
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'PREVIEW_ERROR',
        message: error.message || 'Failed to generate file preview'
      }
    }, 500);
  }
});

// POST /api/v1/files/:id/confirm-processing - Confirm processing after preview review
fileRoutes.post('/:id/confirm-processing', async (c) => {
  try {
    const user = c.get('user');
    const fileId = c.req.param('id');
    const body = await c.req.json();
    const file = fileService.getFile(fileId);
    
    if (!file || file.userId !== user.id) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'FILE_NOT_FOUND',
          message: 'File not found'
        }
      }, 404);
    }
    
    // Validate user confirmation
    if (!body.confirmed) {
      return c.json<ApiResponse>({
        success: false,
        error: {
          code: 'NOT_CONFIRMED',
          message: 'User confirmation required for processing'
        }
      }, 400);
    }
    
    // Process file with validated schema interpretation
    const processResult = await fileService.processFile(fileId, {
      userConfirmed: true,
      schemaOverrides: body.schemaOverrides // Allow user to override AI interpretation
    });
    
    return c.json<ApiResponse>({
      success: processResult.success,
      data: processResult.success ? {
        fileId,
        transactionCount: processResult.transactionCount,
        message: 'File processed successfully with AI validation'
      } : null,
      error: processResult.success ? undefined : {
        code: 'PROCESSING_ERROR',
        message: processResult.error || 'File processing failed'
      }
    });
  } catch (error: any) {
    return c.json<ApiResponse>({
      success: false,
      error: {
        code: 'CONFIRM_ERROR',
        message: error.message || 'Failed to confirm file processing'
      }
    }, 500);
  }
});

// POST /api/v1/files/mapping/assess-quality
fileRoutes.post('/mapping/assess-quality', async (c) => {
  const body = await c.req.json();
  
  // Mock quality assessment
  return c.json<ApiResponse>({
    success: true,
    data: {
      quality: 'high',
      score: 95,
      issues: [],
      recommendations: []
    }
  });
});

export { fileRoutes };