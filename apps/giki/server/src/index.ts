// Load environment variables first
import './utils/env-loader';

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { secureHeaders } from 'hono/secure-headers';

// Import routes
import { authRoutes } from './routes/auth.js';
import { fileRoutes } from './routes/files.js';
import { transactionRoutes } from './routes/transactions.js';
import { categoryRoutes } from './routes/categories.js';
import { categorizationRoutes } from './routes/categorization.js';
import intelligenceRoutes from './routes/categorizationIntelligence.js';
import { exportRoutes } from './routes/exports.js';
import { healthRoutes } from './routes/health.js';
import aiAnalysisRoutes from './api/routes/ai-analysis.js';

// Create Hono app
const app = new Hono();

// Global middleware
app.use('*', logger());
app.use('*', cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:3001',
    'https://giki.ai',
    'https://app.giki.ai'
  ],
  credentials: true,
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization']
}));
app.use('*', secureHeaders());
app.use('*', prettyJSON());

// API Routes
const apiV1 = new Hono();

// Mount route groups
apiV1.route('/auth', authRoutes);
apiV1.route('/files', fileRoutes);
apiV1.route('/transactions', transactionRoutes);
apiV1.route('/categories', categoryRoutes);
apiV1.route('/categorization', categorizationRoutes);
apiV1.route('/categorization/intelligence', intelligenceRoutes);
apiV1.route('/exports', exportRoutes);
apiV1.route('/health', healthRoutes);
apiV1.route('/ai', aiAnalysisRoutes);

// Mount API v1 under /api/v1
app.route('/api/v1', apiV1);

// Root endpoint
app.get('/', (c) => {
  return c.json({
    success: true,
    message: 'Giki AI API Server',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.notFound((c) => {
  return c.json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Route ${c.req.path} not found`,
      method: c.req.method
    }
  }, 404);
});

// Error handler
app.onError((err, c) => {
  console.error(`[ERROR] ${err.message}`, err);
  
  const status = err.status || 500;
  const code = err.code || 'INTERNAL_SERVER_ERROR';
  
  return c.json({
    success: false,
    error: {
      code,
      message: err.message || 'An unexpected error occurred',
      ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    }
  }, status);
});

// Server configuration
const PORT = parseInt(process.env.PORT || '8000');
const HOST = process.env.HOST || '0.0.0.0';

// Start server
console.log(`🚀 Starting Giki AI BHVR Server...`);
console.log(`📦 Environment: ${process.env.NODE_ENV || 'development'}`);
console.log(`🌐 Server URL: http://${HOST}:${PORT}`);
console.log(`📍 API Base: http://${HOST}:${PORT}/api/v1`);

// Export for Bun
export default {
  port: PORT,
  hostname: HOST,
  fetch: app.fetch
};