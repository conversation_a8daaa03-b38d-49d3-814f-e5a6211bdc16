/**
 * Environment Loader for Giki Server
 * 
 * Automatically loads the correct environment file based on NODE_ENV.
 * Import this at the top of any server file to ensure environment variables are loaded.
 */

import { config } from 'dotenv';
import path from 'path';
import { existsSync } from 'fs';

// Determine which env file to load
const envFile = process.env.NODE_ENV === 'production' 
  ? '.env.production' 
  : '.env.development';

// Look at workspace root for .env files
const workspaceRoot = path.resolve(import.meta.dir, '../../../../../');
const envPath = path.resolve(workspaceRoot, envFile);

// Only load if file exists
if (existsSync(envPath)) {
  const result = config({ path: envPath });
  
  if (result.error) {
    console.error(`[env-loader] Error loading ${envFile}:`, result.error);
  } else {
    // Only log in development
    if (process.env.NODE_ENV !== 'production') {
      console.log(`[env-loader] ✅ Loaded ${envFile} (${Object.keys(result.parsed || {}).length} variables)`);
    }
  }
} else {
  console.warn(`[env-loader] ⚠️  ${envFile} not found at ${envPath}`);
}

// Ensure API key compatibility - map different env var names
if (process.env.GOOGLE_GENERATIVE_AI_API_KEY && !process.env.GEMINI_API_KEY) {
  process.env.GEMINI_API_KEY = process.env.GOOGLE_GENERATIVE_AI_API_KEY;
}
if (process.env.GEMINI_API_KEY && !process.env.GOOGLE_API_KEY) {
  process.env.GOOGLE_API_KEY = process.env.GEMINI_API_KEY;
}
if (process.env.GOOGLE_API_KEY && !process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  process.env.GOOGLE_GENERATIVE_AI_API_KEY = process.env.GOOGLE_API_KEY;
}

// Export empty object to make this a module
export {};