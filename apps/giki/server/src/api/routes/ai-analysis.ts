/**
 * AI Analysis API Routes using Vercel AI SDK
 * 
 * Provides endpoints for transaction analysis, categorization,
 * and conversational AI with streaming support.
 */

import { Hono } from 'hono';
import { streamSSE } from 'hono/streaming';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { vendorExtractionService } from '../../services/ai-sdk/vendorExtractionService';
import { categoryMappingService } from '../../services/ai-sdk/categoryMappingService';
import { categoryGenerationService } from '../../services/ai-sdk/categoryGenerationService';
import { transactionAnalysisService } from '../../services/ai-sdk/transactionAnalysisService';
import { conversationalAgentService } from '../../services/ai-sdk/conversationalAgentService';
import { schemaInterpretationService } from '../../services/ai-sdk/schemaInterpretationService';
import { categoryManagementService } from '../../services/ai-sdk/categoryManagementService';

const app = new Hono();

// Validation schemas
const transactionSchema = z.object({
  id: z.string(),
  description: z.string(),
  amount: z.number(),
  type: z.enum(['debit', 'credit']),
  date: z.string(),
  vendor: z.string().optional()
});

const batchAnalysisSchema = z.object({
  transactions: z.array(transactionSchema),
  businessProfile: z.object({
    industry: z.string(),
    businessType: z.enum(['retail', 'service', 'manufacturing', 'saas', 'consulting', 'other']),
    size: z.enum(['small', 'medium', 'large']),
    location: z.string().optional()
  })
});

const conversationSchema = z.object({
  message: z.string(),
  sessionId: z.string().optional(),
  context: z.object({
    currentPage: z.string().optional(),
    userRole: z.string().optional(),
    uploadedFiles: z.array(z.string()).optional()
  }).optional()
});

const schemaInterpretSchema = z.object({
  fileName: z.string(),
  headers: z.array(z.string()),
  rows: z.array(z.array(z.any())),
  totalRows: z.number(),
  fileSize: z.number()
});

/**
 * Extract vendor from transaction description
 */
app.post('/extract-vendor', 
  zValidator('json', z.object({ description: z.string() })),
  async (c) => {
    const { description } = c.req.valid('json');
    
    try {
      const result = await vendorExtractionService.extractVendor(description);
      return c.json({
        success: true,
        data: result
      });
    } catch (error: any) {
      return c.json({
        success: false,
        error: error.message
      }, 500);
    }
  }
);

/**
 * Batch vendor extraction
 */
app.post('/extract-vendors-batch',
  zValidator('json', z.object({ descriptions: z.array(z.string()) })),
  async (c) => {
    const { descriptions } = c.req.valid('json');
    
    try {
      const results = await vendorExtractionService.extractVendorsBatch(descriptions);
      return c.json({
        success: true,
        data: results
      });
    } catch (error: any) {
      return c.json({
        success: false,
        error: error.message
      }, 500);
    }
  }
);

/**
 * Categorize single transaction
 */
app.post('/categorize-transaction',
  zValidator('json', z.object({
    transaction: transactionSchema,
    categories: z.array(z.object({
      id: z.string(),
      name: z.string(),
      glCode: z.string().optional(),
      description: z.string().optional(),
      parentId: z.string().optional()
    }))
  })),
  async (c) => {
    const { transaction, categories } = c.req.valid('json');
    
    try {
      const result = await categoryMappingService.categorizeTransaction(
        transaction,
        categories
      );
      return c.json({
        success: true,
        data: result
      });
    } catch (error: any) {
      return c.json({
        success: false,
        error: error.message
      }, 500);
    }
  }
);

/**
 * Stream batch analysis with SSE
 */
app.post('/analyze-batch-stream',
  zValidator('json', batchAnalysisSchema),
  async (c) => {
    const { transactions, businessProfile } = c.req.valid('json');
    
    return streamSSE(c, async (stream) => {
      try {
        const analysisStream = transactionAnalysisService.streamBatchAnalysis(
          transactions,
          businessProfile
        );
        
        for await (const progress of analysisStream) {
          await stream.writeSSE({
            data: JSON.stringify(progress),
            event: 'progress',
            id: String(Date.now())
          });
          
          if (progress.phase === 'complete') {
            break;
          }
        }
        
        // Get final result
        const finalResult = await analysisStream.next();
        await stream.writeSSE({
          data: JSON.stringify(finalResult.value),
          event: 'complete',
          id: String(Date.now())
        });
        
      } catch (error: any) {
        await stream.writeSSE({
          data: JSON.stringify({ error: error.message }),
          event: 'error',
          id: String(Date.now())
        });
      }
    });
  }
);

/**
 * Non-streaming batch analysis
 */
app.post('/analyze-batch',
  zValidator('json', batchAnalysisSchema),
  async (c) => {
    const { transactions, businessProfile } = c.req.valid('json');
    
    try {
      const result = await transactionAnalysisService.analyzeBatch(
        transactions,
        businessProfile
      );
      return c.json({
        success: true,
        data: result
      });
    } catch (error: any) {
      return c.json({
        success: false,
        error: error.message
      }, 500);
    }
  }
);

/**
 * Conversational AI endpoint
 */
app.post('/conversation',
  zValidator('json', conversationSchema),
  async (c) => {
    const { message, sessionId, context } = c.req.valid('json');
    const userId = c.req.header('x-user-id') || 'anonymous';
    const session = sessionId || `session-${Date.now()}`;
    
    try {
      const response = await conversationalAgentService.processMessage(
        message,
        session,
        userId,
        context
      );
      return c.json({
        success: true,
        data: response,
        sessionId: session
      });
    } catch (error: any) {
      return c.json({
        success: false,
        error: error.message
      }, 500);
    }
  }
);

/**
 * Stream conversational response
 */
app.post('/conversation-stream',
  zValidator('json', conversationSchema),
  async (c) => {
    const { message, sessionId, context } = c.req.valid('json');
    const userId = c.req.header('x-user-id') || 'anonymous';
    const session = sessionId || `session-${Date.now()}`;
    
    return streamSSE(c, async (stream) => {
      try {
        const responseStream = conversationalAgentService.streamResponse(
          message,
          session,
          userId,
          context
        );
        
        for await (const chunk of responseStream) {
          await stream.writeSSE({
            data: chunk,
            event: 'chunk',
            id: String(Date.now())
          });
        }
        
        const finalResponse = await responseStream.next();
        await stream.writeSSE({
          data: JSON.stringify(finalResponse.value),
          event: 'complete',
          id: String(Date.now())
        });
        
      } catch (error: any) {
        await stream.writeSSE({
          data: JSON.stringify({ error: error.message }),
          event: 'error',
          id: String(Date.now())
        });
      }
    });
  }
);

/**
 * Interpret file schema
 */
app.post('/interpret-schema',
  zValidator('json', schemaInterpretSchema),
  async (c) => {
    const filePreview = c.req.valid('json');
    
    try {
      const interpretation = await schemaInterpretationService.interpretSchema(filePreview);
      return c.json({
        success: true,
        data: interpretation
      });
    } catch (error: any) {
      return c.json({
        success: false,
        error: error.message
      }, 500);
    }
  }
);

/**
 * Stream schema interpretation
 */
app.post('/interpret-schema-stream',
  zValidator('json', schemaInterpretSchema),
  async (c) => {
    const filePreview = c.req.valid('json');
    
    return streamSSE(c, async (stream) => {
      try {
        const interpretationStream = schemaInterpretationService.streamInterpretSchema(filePreview);
        
        for await (const partial of interpretationStream) {
          await stream.writeSSE({
            data: JSON.stringify(partial),
            event: 'progress',
            id: String(Date.now())
          });
        }
        
        const final = await interpretationStream.next();
        await stream.writeSSE({
          data: JSON.stringify(final.value),
          event: 'complete',
          id: String(Date.now())
        });
        
      } catch (error: any) {
        await stream.writeSSE({
          data: JSON.stringify({ error: error.message }),
          event: 'error',
          id: String(Date.now())
        });
      }
    });
  }
);

/**
 * Generate category hierarchy for business
 */
app.post('/generate-categories',
  zValidator('json', z.object({
    businessProfile: z.object({
      industry: z.string(),
      businessType: z.enum(['retail', 'service', 'manufacturing', 'saas', 'consulting', 'other']),
      size: z.enum(['small', 'medium', 'large']),
      location: z.string().optional(),
      specialRequirements: z.array(z.string()).optional()
    }),
    sampleTransactions: z.array(transactionSchema).optional()
  })),
  async (c) => {
    const { businessProfile, sampleTransactions } = c.req.valid('json');
    
    try {
      const hierarchy = await categoryGenerationService.generateCategoryHierarchy(
        businessProfile,
        sampleTransactions
      );
      return c.json({
        success: true,
        data: hierarchy
      });
    } catch (error: any) {
      return c.json({
        success: false,
        error: error.message
      }, 500);
    }
  }
);

/**
 * Stream category hierarchy generation
 */
app.post('/generate-categories-stream',
  zValidator('json', z.object({
    businessProfile: z.object({
      industry: z.string(),
      businessType: z.enum(['retail', 'service', 'manufacturing', 'saas', 'consulting', 'other']),
      size: z.enum(['small', 'medium', 'large'])
    })
  })),
  async (c) => {
    const { businessProfile } = c.req.valid('json');
    
    return streamSSE(c, async (stream) => {
      try {
        const generationStream = categoryGenerationService.streamGenerateCategoryHierarchy(businessProfile);
        
        for await (const partial of generationStream) {
          await stream.writeSSE({
            data: JSON.stringify(partial),
            event: 'progress',
            id: String(Date.now())
          });
        }
        
        const final = await generationStream.next();
        await stream.writeSSE({
          data: JSON.stringify(final.value),
          event: 'complete',
          id: String(Date.now())
        });
        
      } catch (error: any) {
        await stream.writeSSE({
          data: JSON.stringify({ error: error.message }),
          event: 'error',
          id: String(Date.now())
        });
      }
    });
  }
);

/**
 * Build category tree from flat list
 */
app.post('/build-category-tree',
  zValidator('json', z.object({
    categories: z.array(z.object({
      id: z.string(),
      name: z.string(),
      glCode: z.string().optional(),
      description: z.string().optional(),
      level: z.number().optional(),
      parentId: z.string().nullable().optional(),
      path: z.string().optional()
    }))
  })),
  async (c) => {
    const { categories } = c.req.valid('json');
    
    try {
      const tree = categoryManagementService.buildCategoryTree(categories);
      const visualization = categoryManagementService.generateTreeVisualization(tree);
      
      return c.json({
        success: true,
        data: {
          tree,
          visualization
        }
      });
    } catch (error: any) {
      return c.json({
        success: false,
        error: error.message
      }, 500);
    }
  }
);

/**
 * Suggest new categories based on uncategorized transactions
 */
app.post('/suggest-categories',
  zValidator('json', z.object({
    uncategorizedTransactions: z.array(z.object({
      description: z.string(),
      amount: z.number()
    })),
    existingCategories: z.array(z.any()).optional()
  })),
  async (c) => {
    const { uncategorizedTransactions, existingCategories } = c.req.valid('json');
    
    try {
      // Build tree if categories provided
      let tree;
      if (existingCategories && existingCategories.length > 0) {
        tree = categoryManagementService.buildCategoryTree(existingCategories);
      }
      
      const suggestions = await categoryManagementService.suggestNewCategories(
        uncategorizedTransactions,
        tree
      );
      
      return c.json({
        success: true,
        data: suggestions
      });
    } catch (error: any) {
      return c.json({
        success: false,
        error: error.message
      }, 500);
    }
  }
);

/**
 * Analyze category health
 */
app.post('/analyze-category-health',
  zValidator('json', z.object({
    categories: z.array(z.any())
  })),
  async (c) => {
    const { categories } = c.req.valid('json');
    
    try {
      const tree = categoryManagementService.buildCategoryTree(categories);
      const analytics = await categoryManagementService.analyzeCategoryHealth(tree);
      
      return c.json({
        success: true,
        data: analytics
      });
    } catch (error: any) {
      return c.json({
        success: false,
        error: error.message
      }, 500);
    }
  }
);

/**
 * Get contextual help
 */
app.get('/help/:page',
  async (c) => {
    const page = c.req.param('page');
    const topic = c.req.query('topic');
    
    try {
      const help = await conversationalAgentService.getContextualHelp(page, topic);
      return c.json({
        success: true,
        data: help
      });
    } catch (error: any) {
      return c.json({
        success: false,
        error: error.message
      }, 500);
    }
  }
);

/**
 * Get smart suggestions
 */
app.post('/smart-suggestions',
  zValidator('json', z.object({
    context: z.object({
      currentPage: z.string().optional(),
      uploadedFiles: z.array(z.string()).optional()
    }),
    recentActivity: z.array(z.string()).optional()
  })),
  async (c) => {
    const { context, recentActivity } = c.req.valid('json');
    
    try {
      const suggestions = await conversationalAgentService.getSmartSuggestions(
        context,
        recentActivity
      );
      return c.json({
        success: true,
        data: suggestions
      });
    } catch (error: any) {
      return c.json({
        success: false,
        error: error.message
      }, 500);
    }
  }
);

export default app;