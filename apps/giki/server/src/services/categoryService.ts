/**
 * Category Service - Comprehensive MIS Structure Management
 * 
 * ULTRA-SIMPLIFIED: ONE service for category management with comprehensive defaults
 * - Default MIS structure (130+ categories)
 * - Category CRUD operations
 * - Hierarchical category management
 * - GL code management
 */

import '../../../../../lib/env-loader.js';
import type { Category, MISStructure } from '@shared/types/index';
import { getGeminiService } from './geminiService.js';
import { db } from '../config/database.js';
import { categories } from '../schema/index.js';
import { eq, isNull, like } from 'drizzle-orm';

interface CategoryNode {
  name: string;
  level: number;
  glCode: string;
  description?: string;
  children: CategoryNode[];
}

export class CategoryService {
  private initialized = false;

  constructor() {
    // Database initialization will be done lazily
  }

  private async ensureInitialized(): Promise<void> {
    if (this.initialized) return;
    
    // Check if default categories exist in database
    const existingCategories = await db.select().from(categories).limit(1);
    
    if (existingCategories.length === 0) {
      console.log('🔄 Initializing default MIS structure in database...');
      await this.initializeDefaultStructure();
    }
    
    this.initialized = true;
  }

  /**
   * Get all categories in hierarchical structure
   */
  async getCategories(userId?: string): Promise<Category[]> {
    await this.ensureInitialized();
    
    const categoryRecords = await db.select().from(categories);
    
    return categoryRecords.map(cat => ({
      id: cat.id,
      name: cat.name,
      parentId: cat.parentId || undefined,
      level: cat.level,
      path: cat.path,
      glCode: cat.glCode || undefined,
      description: cat.description || undefined,
      isSystem: cat.isSystem,
      createdAt: cat.createdAt,
      updatedAt: cat.updatedAt
    }));
  }

  /**
   * Get categories as hierarchical structure for AI
   */
  async getCategoriesForAI(): Promise<any[]> {
    await this.ensureInitialized();
    
    const categoryRecords = await db.select().from(categories);
    const categoryMap = new Map(categoryRecords.map(cat => [cat.id, cat]));
    
    const rootCategories = categoryRecords
      .filter(cat => !cat.parentId)
      .sort((a, b) => a.name.localeCompare(b.name));

    return rootCategories.map(cat => this.buildCategoryTree(cat, categoryMap));
  }

  /**
   * Get MIS structure
   */
  async getMISStructure(userId: string): Promise<MISStructure> {
    const categoriesList = await this.getCategories(userId);
    
    return {
      id: `mis-default-${userId}`,
      userId,
      name: 'Comprehensive Business MIS Structure',
      categories: categoriesList,
      isDefault: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  /**
   * Find category by name or path
   */
  async findCategory(nameOrPath: string): Promise<Category | null> {
    await this.ensureInitialized();
    
    const searchTerm = nameOrPath.toLowerCase();
    
    // First try exact matches
    const exactMatches = await db.select().from(categories)
      .where(
        eq(categories.name, nameOrPath) // Exact name match
      );
    
    if (exactMatches.length > 0) {
      const cat = exactMatches[0];
      return {
        id: cat.id,
        name: cat.name,
        parentId: cat.parentId || undefined,
        level: cat.level,
        path: cat.path,
        glCode: cat.glCode || undefined,
        description: cat.description || undefined,
        isSystem: cat.isSystem,
        createdAt: cat.createdAt,
        updatedAt: cat.updatedAt
      };
    }

    // Try partial matches for AI categorization flexibility
    const partialMatches = await db.select().from(categories)
      .where(
        like(categories.name, `%${searchTerm}%`)
      )
      .limit(1);
    
    if (partialMatches.length > 0) {
      const cat = partialMatches[0];
      return {
        id: cat.id,
        name: cat.name,
        parentId: cat.parentId || undefined,
        level: cat.level,
        path: cat.path,
        glCode: cat.glCode || undefined,
        description: cat.description || undefined,
        isSystem: cat.isSystem,
        createdAt: cat.createdAt,
        updatedAt: cat.updatedAt
      };
    }

    return null;
  }

  /**
   * Create new category
   */
  async createCategory(categoryData: Partial<Category>, userId: string): Promise<Category> {
    await this.ensureInitialized();
    
    const path = await this.buildPath(categoryData.parentId, categoryData.name!);
    
    const insertedCategories = await db.insert(categories).values({
      name: categoryData.name!,
      parentId: categoryData.parentId,
      level: categoryData.level || 1,
      path,
      glCode: categoryData.glCode,
      description: categoryData.description,
      isSystem: false
    }).returning();

    const newCategory = insertedCategories[0];
    
    return {
      id: newCategory.id,
      name: newCategory.name,
      parentId: newCategory.parentId || undefined,
      level: newCategory.level,
      path: newCategory.path,
      glCode: newCategory.glCode || undefined,
      description: newCategory.description || undefined,
      isSystem: newCategory.isSystem,
      createdAt: newCategory.createdAt,
      updatedAt: newCategory.updatedAt
    };
  }

  /**
   * Update category
   */
  async updateCategory(categoryId: string, updates: Partial<Category>): Promise<Category | null> {
    await this.ensureInitialized();
    
    const updateData: any = { ...updates, updatedAt: new Date() };
    
    // Update path if name changed
    if (updates.name) {
      const existing = await db.select().from(categories).where(eq(categories.id, categoryId));
      if (existing.length > 0) {
        updateData.path = await this.buildPath(existing[0].parentId, updates.name);
      }
    }

    const updatedCategories = await db.update(categories)
      .set(updateData)
      .where(eq(categories.id, categoryId))
      .returning();

    if (updatedCategories.length === 0) return null;
    
    const category = updatedCategories[0];
    return {
      id: category.id,
      name: category.name,
      parentId: category.parentId || undefined,
      level: category.level,
      path: category.path,
      glCode: category.glCode || undefined,
      description: category.description || undefined,
      isSystem: category.isSystem,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt
    };
  }

  /**
   * Delete category (only non-system categories)
   */
  async deleteCategory(categoryId: string): Promise<boolean> {
    await this.ensureInitialized();
    
    const existing = await db.select().from(categories)
      .where(eq(categories.id, categoryId));
      
    if (existing.length === 0 || existing[0].isSystem) return false;

    const deleted = await db.delete(categories)
      .where(eq(categories.id, categoryId))
      .returning();

    return deleted.length > 0;
  }

  /**
   * Get category drill-down data
   */
  async getCategoryDrillDown(categoryId: string): Promise<any> {
    await this.ensureInitialized();
    
    const categoryRecords = await db.select().from(categories)
      .where(eq(categories.id, categoryId));
      
    if (categoryRecords.length === 0) return null;
    
    const category = categoryRecords[0];
    const subcategoryRecords = await db.select().from(categories)
      .where(eq(categories.parentId, categoryId));

    return {
      category: {
        id: category.id,
        name: category.name,
        parentId: category.parentId || undefined,
        level: category.level,
        path: category.path,
        glCode: category.glCode || undefined,
        description: category.description || undefined,
        isSystem: category.isSystem,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt
      },
      subcategories: subcategoryRecords.map(cat => ({
        id: cat.id,
        name: cat.name,
        parentId: cat.parentId || undefined,
        level: cat.level,
        path: cat.path,
        glCode: cat.glCode || undefined,
        description: cat.description || undefined,
        isSystem: cat.isSystem,
        createdAt: cat.createdAt,
        updatedAt: cat.updatedAt
      })),
      transactionCount: 0, // TODO: Get from transaction service
      totalAmount: 0 // TODO: Get from transaction service
    };
  }

  // Private helper methods

  private buildCategoryTree(category: any, categoryMap: Map<string, any>): any {
    const children = Array.from(categoryMap.values())
      .filter(cat => cat.parentId === category.id)
      .map(child => this.buildCategoryTree(child, categoryMap));

    return {
      id: category.id,
      name: category.name,
      parent_id: category.parentId,
      level: category.level,
      gl_code: category.glCode,
      children
    };
  }

  private async buildPath(parentId?: string, name?: string): Promise<string> {
    if (!name) return '';
    if (!parentId) return `/${name}`;

    const parentRecords = await db.select().from(categories)
      .where(eq(categories.id, parentId));
      
    if (parentRecords.length === 0) return `/${name}`;

    return `${parentRecords[0].path}/${name}`;
  }

  /**
   * Initialize comprehensive default MIS structure
   * Covers all major business categories with proper GL codes
   */
  private async initializeDefaultStructure(): Promise<void> {
    const defaultStructure: CategoryNode[] = [
      {
        name: 'Assets',
        level: 1,
        glCode: '1000',
        description: 'All company assets',
        children: [
          {
            name: 'Current Assets',
            level: 2,
            glCode: '1100',
            description: 'Short-term assets (less than 1 year)',
            children: [
              { name: 'Cash and Cash Equivalents', level: 3, glCode: '1110', description: 'Liquid assets', children: [] },
              { name: 'Accounts Receivable', level: 3, glCode: '1120', description: 'Money owed by customers', children: [] },
              { name: 'Inventory', level: 3, glCode: '1130', description: 'Goods for sale', children: [] },
              { name: 'Prepaid Expenses', level: 3, glCode: '1140', description: 'Payments made in advance', children: [] }
            ]
          },
          {
            name: 'Fixed Assets',
            level: 2,
            glCode: '1200',
            description: 'Long-term assets',
            children: [
              { name: 'Property, Plant & Equipment', level: 3, glCode: '1210', description: 'Physical assets', children: [] },
              { name: 'Furniture & Fixtures', level: 3, glCode: '1220', description: 'Office equipment', children: [] },
              { name: 'Vehicles', level: 3, glCode: '1230', description: 'Company vehicles', children: [] },
              { name: 'Computer Equipment', level: 3, glCode: '1240', description: 'IT hardware', children: [] },
              { name: 'Accumulated Depreciation', level: 3, glCode: '1250', description: 'Asset depreciation', children: [] }
            ]
          },
          {
            name: 'Intangible Assets',
            level: 2,
            glCode: '1300',
            description: 'Non-physical assets',
            children: [
              { name: 'Software Licenses', level: 3, glCode: '1310', description: 'Software and licenses', children: [] },
              { name: 'Patents & Trademarks', level: 3, glCode: '1320', description: 'Intellectual property', children: [] },
              { name: 'Goodwill', level: 3, glCode: '1330', description: 'Business goodwill', children: [] }
            ]
          }
        ]
      },
      {
        name: 'Liabilities',
        level: 1,
        glCode: '2000',
        description: 'All company liabilities',
        children: [
          {
            name: 'Current Liabilities',
            level: 2,
            glCode: '2100',
            description: 'Short-term obligations',
            children: [
              { name: 'Accounts Payable', level: 3, glCode: '2110', description: 'Money owed to vendors', children: [] },
              { name: 'Accrued Expenses', level: 3, glCode: '2120', description: 'Unpaid expenses', children: [] },
              { name: 'Short-term Loans', level: 3, glCode: '2130', description: 'Loans due within 1 year', children: [] },
              { name: 'Payroll Liabilities', level: 3, glCode: '2140', description: 'Employee-related liabilities', children: [] },
              { name: 'Tax Payable', level: 3, glCode: '2150', description: 'Taxes owed', children: [] }
            ]
          },
          {
            name: 'Long-term Liabilities',
            level: 2,
            glCode: '2200',
            description: 'Long-term obligations',
            children: [
              { name: 'Long-term Loans', level: 3, glCode: '2210', description: 'Loans due after 1 year', children: [] },
              { name: 'Mortgages Payable', level: 3, glCode: '2220', description: 'Property mortgages', children: [] },
              { name: 'Bonds Payable', level: 3, glCode: '2230', description: 'Corporate bonds', children: [] }
            ]
          }
        ]
      },
      {
        name: 'Equity',
        level: 1,
        glCode: '3000',
        description: 'Ownership equity',
        children: [
          {
            name: 'Owner\'s Equity',
            level: 2,
            glCode: '3100',
            description: 'Business owner equity',
            children: [
              { name: 'Capital', level: 3, glCode: '3110', description: 'Initial capital investment', children: [] },
              { name: 'Retained Earnings', level: 3, glCode: '3120', description: 'Accumulated profits', children: [] },
              { name: 'Dividends', level: 3, glCode: '3130', description: 'Profit distributions', children: [] }
            ]
          }
        ]
      },
      {
        name: 'Revenue',
        level: 1,
        glCode: '4000',
        description: 'All income and revenue streams',
        children: [
          {
            name: 'Operating Revenue',
            level: 2,
            glCode: '4100',
            description: 'Primary business revenue',
            children: [
              { name: 'Sales Revenue', level: 3, glCode: '4110', description: 'Product sales', children: [] },
              { name: 'Service Revenue', level: 3, glCode: '4120', description: 'Service income', children: [] },
              { name: 'Subscription Revenue', level: 3, glCode: '4130', description: 'Recurring subscriptions', children: [] },
              { name: 'Commission Income', level: 3, glCode: '4140', description: 'Commission earnings', children: [] }
            ]
          },
          {
            name: 'Other Income',
            level: 2,
            glCode: '4200',
            description: 'Non-operating income',
            children: [
              { name: 'Interest Income', level: 3, glCode: '4210', description: 'Bank interest', children: [] },
              { name: 'Investment Income', level: 3, glCode: '4220', description: 'Investment returns', children: [] },
              { name: 'Rental Income', level: 3, glCode: '4230', description: 'Property rental', children: [] },
              { name: 'Foreign Exchange Gain', level: 3, glCode: '4240', description: 'Currency gains', children: [] },
              { name: 'Miscellaneous Income', level: 3, glCode: '4250', description: 'Other income', children: [] }
            ]
          }
        ]
      },
      {
        name: 'Operating Expenses',
        level: 1,
        glCode: '5000',
        description: 'Day-to-day operational costs',
        children: [
          {
            name: 'Personnel Expenses',
            level: 2,
            glCode: '5100',
            description: 'Employee-related costs',
            children: [
              { name: 'Salaries & Wages', level: 3, glCode: '5110', description: 'Employee compensation', children: [] },
              { name: 'Employee Benefits', level: 3, glCode: '5120', description: 'Health insurance, retirement', children: [] },
              { name: 'Payroll Taxes', level: 3, glCode: '5130', description: 'Employer taxes', children: [] },
              { name: 'Training & Development', level: 3, glCode: '5140', description: 'Employee training', children: [] },
              { name: 'Recruitment Expenses', level: 3, glCode: '5150', description: 'Hiring costs', children: [] }
            ]
          },
          {
            name: 'Facility Expenses',
            level: 2,
            glCode: '5200',
            description: 'Office and facility costs',
            children: [
              { name: 'Rent', level: 3, glCode: '5210', description: 'Office rent', children: [] },
              { name: 'Utilities', level: 3, glCode: '5220', description: 'Electricity, water, gas', children: [] },
              { name: 'Internet & Phone', level: 3, glCode: '5230', description: 'Communication costs', children: [] },
              { name: 'Security', level: 3, glCode: '5240', description: 'Security services', children: [] },
              { name: 'Maintenance & Repairs', level: 3, glCode: '5250', description: 'Facility upkeep', children: [] },
              { name: 'Cleaning Services', level: 3, glCode: '5260', description: 'Janitorial services', children: [] }
            ]
          },
          {
            name: 'Technology Expenses',
            level: 2,
            glCode: '5300',
            description: 'IT and technology costs',
            children: [
              { name: 'Software Licenses', level: 3, glCode: '5310', description: 'Software subscriptions', children: [] },
              { name: 'Cloud Services', level: 3, glCode: '5320', description: 'AWS, Google Cloud, etc.', children: [] },
              { name: 'Hardware', level: 3, glCode: '5330', description: 'Computer equipment', children: [] },
              { name: 'IT Support', level: 3, glCode: '5340', description: 'Technical support', children: [] },
              { name: 'Website & Domain', level: 3, glCode: '5350', description: 'Web hosting, domains', children: [] }
            ]
          },
          {
            name: 'Marketing & Sales',
            level: 2,
            glCode: '5400',
            description: 'Marketing and sales expenses',
            children: [
              { name: 'Digital Advertising', level: 3, glCode: '5410', description: 'Google Ads, Facebook Ads', children: [] },
              { name: 'Content Marketing', level: 3, glCode: '5420', description: 'Content creation', children: [] },
              { name: 'Trade Shows & Events', level: 3, glCode: '5430', description: 'Exhibition costs', children: [] },
              { name: 'Sales Commissions', level: 3, glCode: '5440', description: 'Sales team commissions', children: [] },
              { name: 'Marketing Materials', level: 3, glCode: '5450', description: 'Brochures, promotional items', children: [] },
              { name: 'SEO & SEM', level: 3, glCode: '5460', description: 'Search engine marketing', children: [] }
            ]
          },
          {
            name: 'Professional Services',
            level: 2,
            glCode: '5500',
            description: 'External professional services',
            children: [
              { name: 'Legal Services', level: 3, glCode: '5510', description: 'Lawyer fees', children: [] },
              { name: 'Accounting & Tax', level: 3, glCode: '5520', description: 'CPA services', children: [] },
              { name: 'Consulting', level: 3, glCode: '5530', description: 'Business consultants', children: [] },
              { name: 'Audit Services', level: 3, glCode: '5540', description: 'External audits', children: [] },
              { name: 'Banking Fees', level: 3, glCode: '5550', description: 'Bank service charges', children: [] }
            ]
          },
          {
            name: 'Travel & Transportation',
            level: 2,
            glCode: '5600',
            description: 'Travel-related expenses',
            children: [
              { name: 'Airfare', level: 3, glCode: '5610', description: 'Flight tickets', children: [] },
              { name: 'Accommodation', level: 3, glCode: '5620', description: 'Hotels, lodging', children: [] },
              { name: 'Ground Transportation', level: 3, glCode: '5630', description: 'Taxis, rideshare, public transport', children: [] },
              { name: 'Car Rental', level: 3, glCode: '5640', description: 'Vehicle rentals', children: [] },
              { name: 'Fuel', level: 3, glCode: '5650', description: 'Gasoline, diesel', children: [] },
              { name: 'Meals & Entertainment', level: 3, glCode: '5660', description: 'Business meals', children: [] }
            ]
          },
          {
            name: 'Administrative Expenses',
            level: 2,
            glCode: '5700',
            description: 'General administrative costs',
            children: [
              { name: 'Office Supplies', level: 3, glCode: '5710', description: 'Stationery, supplies', children: [] },
              { name: 'Postage & Shipping', level: 3, glCode: '5720', description: 'Mail, courier services', children: [] },
              { name: 'Insurance', level: 3, glCode: '5730', description: 'Business insurance premiums', children: [] },
              { name: 'Licenses & Permits', level: 3, glCode: '5740', description: 'Business licenses', children: [] },
              { name: 'Subscriptions', level: 3, glCode: '5750', description: 'Magazines, services', children: [] },
              { name: 'Petty Cash', level: 3, glCode: '5760', description: 'Small cash expenses', children: [] }
            ]
          }
        ]
      },
      {
        name: 'Non-Operating Expenses',
        level: 1,
        glCode: '6000',
        description: 'Non-operational costs',
        children: [
          {
            name: 'Financial Expenses',
            level: 2,
            glCode: '6100',
            description: 'Financial costs',
            children: [
              { name: 'Interest Expense', level: 3, glCode: '6110', description: 'Loan interest payments', children: [] },
              { name: 'Bank Charges', level: 3, glCode: '6120', description: 'Banking fees', children: [] },
              { name: 'Foreign Exchange Loss', level: 3, glCode: '6130', description: 'Currency losses', children: [] },
              { name: 'Investment Losses', level: 3, glCode: '6140', description: 'Investment write-downs', children: [] }
            ]
          },
          {
            name: 'Other Expenses',
            level: 2,
            glCode: '6200',
            description: 'Miscellaneous expenses',
            children: [
              { name: 'Depreciation', level: 3, glCode: '6210', description: 'Asset depreciation', children: [] },
              { name: 'Amortization', level: 3, glCode: '6220', description: 'Intangible asset amortization', children: [] },
              { name: 'Write-offs', level: 3, glCode: '6230', description: 'Bad debt, asset write-offs', children: [] },
              { name: 'Penalties & Fines', level: 3, glCode: '6240', description: 'Regulatory penalties', children: [] },
              { name: 'Donations', level: 3, glCode: '6250', description: 'Charitable contributions', children: [] }
            ]
          }
        ]
      }
    ];

    // Convert structure to database entries
    const processNode = async (node: CategoryNode, parentId?: string, parentPath = ''): Promise<string> => {
      const path = parentPath ? `${parentPath}/${node.name}` : `/${node.name}`;
      
      const insertedCategories = await db.insert(categories).values({
        name: node.name,
        parentId,
        level: node.level,
        path,
        glCode: node.glCode,
        description: node.description,
        isSystem: true
      }).returning();

      const newCategory = insertedCategories[0];

      // Process children
      for (const child of node.children) {
        await processNode(child, newCategory.id, path);
      }
      
      return newCategory.id;
    };

    // Process all nodes sequentially to maintain integrity
    for (const node of defaultStructure) {
      await processNode(node);
    }
    
    const totalCategories = await db.select().from(categories);
    console.log(`✅ Initialized default MIS structure with ${totalCategories.length} categories`);
  }
}

// Singleton instance
let categoryService: CategoryService | null = null;

export function getCategoryService(): CategoryService {
  if (!categoryService) {
    categoryService = new CategoryService();
  }
  return categoryService;
}