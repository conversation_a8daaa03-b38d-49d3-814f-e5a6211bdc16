/**
 * Transaction Analysis Service - Main Orchestrator
 * 
 * Following Pitch Prep's contextAwareAnalysisService pattern, this service orchestrates
 * the complete transaction categorization pipeline using specialized AI services.
 * 
 * Uses @google/genai SDK exclusively with gemini-2.0-flash-001 model
 * One isolated prompt per service for easy optimization and testing
 */

import { GoogleGenAI, Type } from '@google/genai';
import { sql } from '../../database/connection';

// Import specialized AI services (to be created)
import { VendorExtractionService } from './vendorExtractionService';
import { CategoryMappingService } from './categoryMappingService';
import { MISStructureGenerationService } from './misStructureGenerationService';
import { ConfidenceCalibrationService } from './confidenceCalibrationService';

export interface TransactionAnalysisRequest {
  transactions: Array<{
    id: string;
    description: string;
    amount: number;
    type: 'debit' | 'credit';
    date?: string;
    vendor?: string;
  }>;
  businessProfile: {
    industry: string;
    businessType: string;
    size: 'small' | 'medium' | 'large';
    existingCategories?: Array<{ id: string; name: string; glCode?: string }>;
  };
  analysisOptions?: {
    includeVendorExtraction?: boolean;
    includeMISGeneration?: boolean;
    includeConfidenceCalibration?: boolean;
    accuracyTarget?: number; // Default: 0.87 (87%)
  };
}

export interface TransactionCategorizationResult {
  transactionId: string;
  originalDescription: string;
  extractedVendor?: string;
  category: {
    id: string;
    name: string;
    glCode?: string;
    level: number;
  };
  confidence: number;
  reasoning: string;
  processingTime: number;
  aiModelUsed: string;
}

export interface MISStructure {
  name: string;
  level: number;
  glCode: string;
  description: string;
  children: MISStructure[];
}

export interface TransactionAnalysisResult {
  analysisId: string;
  businessProfile: TransactionAnalysisRequest['businessProfile'];
  categorizations: TransactionCategorizationResult[];
  generatedMISStructure?: MISStructure[];
  accuracyMetrics: {
    averageConfidence: number;
    highConfidenceCount: number; // >= 80%
    mediumConfidenceCount: number; // 60-79%
    lowConfidenceCount: number; // < 60%
    targetAccuracyMet: boolean; // Based on 87% target
  };
  processingMetrics: {
    totalProcessingTime: number;
    averageTimePerTransaction: number;
    vendorExtractionTime?: number;
    categorizationTime: number;
    misGenerationTime?: number;
    confidenceCalibrationTime?: number;
  };
  recommendations: {
    categoryOptimizations: string[];
    misImprovements: string[];
    accuracyImprovements: string[];
  };
}

/**
 * Main Transaction Analysis Service
 * Orchestrates AI-powered transaction categorization using specialized services
 */
export class TransactionAnalysisService {
  private geminiClient: GoogleGenAI;
  private vendorExtractionService: VendorExtractionService;
  private categoryMappingService: CategoryMappingService;
  private misGenerationService: MISStructureGenerationService;
  private confidenceCalibrationService: ConfidenceCalibrationService;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      console.warn('[TransactionAnalysisService] WARNING: GEMINI_API_KEY not configured - service will not function');
      // Create dummy clients to avoid crashes (following Pitch Prep pattern)
      this.geminiClient = null as any;
      this.vendorExtractionService = null as any;
      this.categoryMappingService = null as any;
      this.misGenerationService = null as any;
      this.confidenceCalibrationService = null as any;
      return;
    }

    // Initialize @google/genai SDK
    this.geminiClient = new GoogleGenAI({ apiKey });
    
    // Initialize specialized services
    this.vendorExtractionService = new VendorExtractionService(apiKey);
    this.categoryMappingService = new CategoryMappingService(apiKey);
    this.misGenerationService = new MISStructureGenerationService(apiKey);
    this.confidenceCalibrationService = new ConfidenceCalibrationService(apiKey);
    
    console.log('✅ TransactionAnalysisService initialized with @google/genai SDK');
  }

  /**
   * Main entry point for complete transaction analysis
   * Following Pitch Prep's analyzeUserJourney pattern
   */
  async analyzeTransactions(request: TransactionAnalysisRequest): Promise<TransactionAnalysisResult> {
    const analysisId = `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    console.log(`[TransactionAnalysisService] Starting analysis ${analysisId} for ${request.transactions.length} transactions`);
    console.log(`[TransactionAnalysisService] Business: ${request.businessProfile.businessType} in ${request.businessProfile.industry}`);

    // Ensure we have valid configuration
    if (!this.geminiClient) {
      throw new Error('TransactionAnalysisService not properly configured - missing API key');
    }

    const results: TransactionCategorizationResult[] = [];
    const processingMetrics = {
      vendorExtractionTime: 0,
      categorizationTime: 0,
      misGenerationTime: 0,
      confidenceCalibrationTime: 0
    };

    try {
      // Step 1: Extract vendors from transaction descriptions (if enabled)
      const vendorExtractionStart = Date.now();
      const vendorExtractionMap = new Map<string, string>();
      
      if (request.analysisOptions?.includeVendorExtraction !== false) {
        console.log(`[TransactionAnalysisService] Step 1: Extracting vendors from ${request.transactions.length} transactions`);
        
        for (const transaction of request.transactions) {
          if (!transaction.vendor) {
            const extractedVendor = await this.vendorExtractionService.extractVendor(transaction.description);
            if (extractedVendor) {
              vendorExtractionMap.set(transaction.id, extractedVendor);
              console.log(`   ✓ ${transaction.id}: "${transaction.description.substr(0, 40)}..." → "${extractedVendor}"`);
            }
          }
        }
        
        processingMetrics.vendorExtractionTime = Date.now() - vendorExtractionStart;
        console.log(`   ✅ Vendor extraction completed in ${processingMetrics.vendorExtractionTime}ms`);
      }

      // Step 2: Generate or validate MIS structure (if enabled)
      const misGenerationStart = Date.now();
      let misStructure: MISStructure[] | undefined;
      
      if (request.analysisOptions?.includeMISGeneration !== false) {
        console.log(`[TransactionAnalysisService] Step 2: Generating MIS structure for ${request.businessProfile.industry} industry`);
        
        misStructure = await this.misGenerationService.generateMISStructure(
          request.businessProfile.industry,
          request.businessProfile.businessType,
          request.businessProfile.existingCategories
        );
        
        processingMetrics.misGenerationTime = Date.now() - misGenerationStart;
        console.log(`   ✅ MIS structure generated with ${misStructure.length} main categories in ${processingMetrics.misGenerationTime}ms`);
      }

      // Step 3: Categorize each transaction using context accumulation
      const categorizationStart = Date.now();
      console.log(`[TransactionAnalysisService] Step 3: Categorizing ${request.transactions.length} transactions`);

      const categories = request.businessProfile.existingCategories || this.extractCategoriesFromMIS(misStructure);
      
      for (const transaction of request.transactions) {
        const vendor = transaction.vendor || vendorExtractionMap.get(transaction.id);
        
        const categorizationResult = await this.categoryMappingService.categorizeTransaction({
          description: transaction.description,
          amount: transaction.amount,
          type: transaction.type,
          vendor,
          categories,
          businessContext: request.businessProfile
        });

        results.push({
          transactionId: transaction.id,
          originalDescription: transaction.description,
          extractedVendor: vendor,
          category: categorizationResult.category,
          confidence: categorizationResult.confidence,
          reasoning: categorizationResult.reasoning,
          processingTime: categorizationResult.processingTime,
          aiModelUsed: 'gemini-2.0-flash-001'
        });

        console.log(`   ✓ ${transaction.id}: "${transaction.description.substr(0, 40)}..." → ${categorizationResult.category.name} (${(categorizationResult.confidence * 100).toFixed(1)}%)`);
      }

      processingMetrics.categorizationTime = Date.now() - categorizationStart;
      console.log(`   ✅ Transaction categorization completed in ${processingMetrics.categorizationTime}ms`);

      // Step 4: Confidence calibration and accuracy analysis (if enabled)
      const calibrationStart = Date.now();
      
      if (request.analysisOptions?.includeConfidenceCalibration !== false) {
        console.log(`[TransactionAnalysisService] Step 4: Performing confidence calibration analysis`);
        
        await this.confidenceCalibrationService.calibrateConfidence(results, request.businessProfile);
        
        processingMetrics.confidenceCalibrationTime = Date.now() - calibrationStart;
        console.log(`   ✅ Confidence calibration completed in ${processingMetrics.confidenceCalibrationTime}ms`);
      }

      // Calculate accuracy metrics
      const accuracyMetrics = this.calculateAccuracyMetrics(results, request.analysisOptions?.accuracyTarget || 0.87);
      
      // Generate recommendations using AI analysis
      const recommendations = await this.generateRecommendations(request, results, accuracyMetrics);

      const totalProcessingTime = Date.now() - startTime;
      
      console.log(`[TransactionAnalysisService] ✅ Analysis ${analysisId} completed in ${totalProcessingTime}ms`);
      console.log(`   Average Confidence: ${(accuracyMetrics.averageConfidence * 100).toFixed(1)}%`);
      console.log(`   High Confidence Results: ${accuracyMetrics.highConfidenceCount}/${results.length}`);
      console.log(`   Target Accuracy Met: ${accuracyMetrics.targetAccuracyMet ? '✅ Yes' : '⚠️ No'}`);

      return {
        analysisId,
        businessProfile: request.businessProfile,
        categorizations: results,
        generatedMISStructure: misStructure,
        accuracyMetrics,
        processingMetrics: {
          totalProcessingTime,
          averageTimePerTransaction: totalProcessingTime / request.transactions.length,
          ...processingMetrics
        },
        recommendations
      };

    } catch (error: any) {
      console.error(`[TransactionAnalysisService] Analysis ${analysisId} failed:`, error);
      throw new Error(`Transaction analysis failed: ${error.message}`);
    }
  }

  /**
   * Calculate accuracy metrics for the analysis results
   */
  private calculateAccuracyMetrics(results: TransactionCategorizationResult[], targetAccuracy: number) {
    const confidences = results.map(r => r.confidence);
    const averageConfidence = confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;
    
    const highConfidenceCount = confidences.filter(c => c >= 0.8).length;
    const mediumConfidenceCount = confidences.filter(c => c >= 0.6 && c < 0.8).length;
    const lowConfidenceCount = confidences.filter(c => c < 0.6).length;
    
    const targetAccuracyMet = averageConfidence >= targetAccuracy;

    return {
      averageConfidence,
      highConfidenceCount,
      mediumConfidenceCount,
      lowConfidenceCount,
      targetAccuracyMet
    };
  }

  /**
   * Generate AI-powered recommendations for improvement
   */
  private async generateRecommendations(
    request: TransactionAnalysisRequest,
    results: TransactionCategorizationResult[],
    accuracyMetrics: any
  ) {
    const lowConfidenceResults = results.filter(r => r.confidence < 0.6);
    const highConfidenceResults = results.filter(r => r.confidence >= 0.8);

    const recommendationPrompt = `
    Analyze transaction categorization results and provide improvement recommendations:

    Business Context:
    - Industry: ${request.businessProfile.industry}
    - Business Type: ${request.businessProfile.businessType}
    - Total Transactions: ${results.length}

    Performance Metrics:
    - Average Confidence: ${(accuracyMetrics.averageConfidence * 100).toFixed(1)}%
    - High Confidence: ${accuracyMetrics.highConfidenceCount}/${results.length}
    - Low Confidence: ${accuracyMetrics.lowConfidenceCount}/${results.length}

    Low Confidence Examples:
    ${lowConfidenceResults.slice(0, 3).map(r => `- "${r.originalDescription}" → ${r.category.name} (${(r.confidence * 100).toFixed(1)}%): ${r.reasoning}`).join('\n')}

    High Confidence Examples:
    ${highConfidenceResults.slice(0, 3).map(r => `- "${r.originalDescription}" → ${r.category.name} (${(r.confidence * 100).toFixed(1)}%): ${r.reasoning}`).join('\n')}

    Generate specific improvement recommendations in JSON format:
    {
      "categoryOptimizations": ["Specific category improvements"],
      "misImprovements": ["MIS structure enhancements"],
      "accuracyImprovements": ["Actions to improve accuracy"]
    }
    `;

    try {
      const result = await this.geminiClient.models.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: [{
          role: 'user',
          parts: [{ text: recommendationPrompt }]
        }],
        config: {
          temperature: 0.7,
          maxOutputTokens: 2000,
          responseMimeType: 'application/json',
          responseSchema: {
            type: Type.OBJECT,
            properties: {
              categoryOptimizations: { type: Type.ARRAY, items: { type: Type.STRING } },
              misImprovements: { type: Type.ARRAY, items: { type: Type.STRING } },
              accuracyImprovements: { type: Type.ARRAY, items: { type: Type.STRING } }
            },
            required: ['categoryOptimizations', 'misImprovements', 'accuracyImprovements']
          }
        }
      });

      return JSON.parse(result.text);
    } catch (error) {
      console.error('Error generating recommendations:', error);
      return {
        categoryOptimizations: ['Review low-confidence categorizations for pattern improvements'],
        misImprovements: ['Consider adding industry-specific subcategories'],
        accuracyImprovements: ['Enhance vendor extraction for better context']
      };
    }
  }

  /**
   * Extract categories from generated MIS structure
   */
  private extractCategoriesFromMIS(misStructure?: MISStructure[]): Array<{ id: string; name: string; glCode?: string }> {
    if (!misStructure) return [];

    const categories: Array<{ id: string; name: string; glCode?: string }> = [];
    
    const extractRecursive = (items: MISStructure[]) => {
      for (const item of items) {
        categories.push({
          id: item.glCode || item.name.toLowerCase().replace(/\s+/g, '_'),
          name: item.name,
          glCode: item.glCode
        });
        
        if (item.children && item.children.length > 0) {
          extractRecursive(item.children);
        }
      }
    };

    extractRecursive(misStructure);
    return categories;
  }
}

// Export singleton instance following Pitch Prep pattern
export const transactionAnalysisService = new TransactionAnalysisService();