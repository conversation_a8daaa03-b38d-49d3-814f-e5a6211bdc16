/**
 * Standard Industry Templates
 * 
 * Provides GAAP/IFRS compliant chart of accounts templates
 * for different industries and business types.
 */

import { HierarchicalCategory } from './hierarchyService';

// SaaS Business Template
export const SAAS_TEMPLATE: HierarchicalCategory[] = [
  {
    id: 'assets',
    name: 'Assets',
    level: 0,
    parentId: null,
    glCode: '1000',
    description: 'Resources controlled by the company',
    children: [
      {
        id: 'current-assets',
        name: 'Current Assets',
        level: 1,
        parentId: 'assets',
        glCode: '1100',
        children: [
          { id: 'cash', name: 'Cash & Cash Equivalents', level: 2, parentId: 'current-assets', glCode: '1110' },
          { id: 'accounts-receivable', name: 'Accounts Receivable', level: 2, parentId: 'current-assets', glCode: '1120' },
          { id: 'prepaid-expenses', name: 'Prepaid Expenses', level: 2, parentId: 'current-assets', glCode: '1130' }
        ]
      },
      {
        id: 'non-current-assets',
        name: 'Non-Current Assets',
        level: 1,
        parentId: 'assets',
        glCode: '1200',
        children: [
          { id: 'equipment', name: 'Equipment & Software', level: 2, parentId: 'non-current-assets', glCode: '1210' },
          { id: 'intangible', name: 'Intangible Assets', level: 2, parentId: 'non-current-assets', glCode: '1220' }
        ]
      }
    ]
  },
  {
    id: 'liabilities',
    name: 'Liabilities',
    level: 0,
    parentId: null,
    glCode: '2000',
    description: 'Obligations of the company',
    children: [
      {
        id: 'current-liabilities',
        name: 'Current Liabilities',
        level: 1,
        parentId: 'liabilities',
        glCode: '2100',
        children: [
          { id: 'accounts-payable', name: 'Accounts Payable', level: 2, parentId: 'current-liabilities', glCode: '2110' },
          { id: 'accrued-expenses', name: 'Accrued Expenses', level: 2, parentId: 'current-liabilities', glCode: '2120' },
          { id: 'deferred-revenue', name: 'Deferred Revenue', level: 2, parentId: 'current-liabilities', glCode: '2130' }
        ]
      },
      {
        id: 'long-term-liabilities',
        name: 'Long-term Liabilities', 
        level: 1,
        parentId: 'liabilities',
        glCode: '2200',
        children: [
          { id: 'long-term-debt', name: 'Long-term Debt', level: 2, parentId: 'long-term-liabilities', glCode: '2210' }
        ]
      }
    ]
  },
  {
    id: 'equity',
    name: 'Equity',
    level: 0,
    parentId: null,
    glCode: '3000',
    description: 'Owner equity in the company',
    children: [
      { id: 'common-stock', name: 'Common Stock', level: 1, parentId: 'equity', glCode: '3100' },
      { id: 'retained-earnings', name: 'Retained Earnings', level: 1, parentId: 'equity', glCode: '3200' }
    ]
  },
  {
    id: 'revenue',
    name: 'Revenue',
    level: 0,
    parentId: null,
    glCode: '4000',
    description: 'Income generated from business operations',
    children: [
      { id: 'subscription-revenue', name: 'Subscription Revenue', level: 1, parentId: 'revenue', glCode: '4100' },
      { id: 'professional-services', name: 'Professional Services', level: 1, parentId: 'revenue', glCode: '4200' },
      { id: 'setup-fees', name: 'Setup & Implementation Fees', level: 1, parentId: 'revenue', glCode: '4300' }
    ]
  },
  {
    id: 'expenses',
    name: 'Expenses',
    level: 0,
    parentId: null,
    glCode: '5000',
    description: 'Costs incurred in business operations',
    children: [
      {
        id: 'cost-of-revenue',
        name: 'Cost of Revenue',
        level: 1,
        parentId: 'expenses',
        glCode: '5100',
        children: [
          { id: 'hosting-costs', name: 'Hosting & Infrastructure', level: 2, parentId: 'cost-of-revenue', glCode: '5110' },
          { id: 'customer-support', name: 'Customer Support', level: 2, parentId: 'cost-of-revenue', glCode: '5120' },
          { id: 'payment-processing', name: 'Payment Processing Fees', level: 2, parentId: 'cost-of-revenue', glCode: '5130' }
        ]
      },
      {
        id: 'sales-marketing',
        name: 'Sales & Marketing',
        level: 1,
        parentId: 'expenses',
        glCode: '5200',
        children: [
          { id: 'advertising', name: 'Advertising & Promotion', level: 2, parentId: 'sales-marketing', glCode: '5210' },
          { id: 'sales-commissions', name: 'Sales Commissions', level: 2, parentId: 'sales-marketing', glCode: '5220' },
          { id: 'marketing-tools', name: 'Marketing Tools & Software', level: 2, parentId: 'sales-marketing', glCode: '5230' }
        ]
      },
      {
        id: 'research-development',
        name: 'Research & Development',
        level: 1,
        parentId: 'expenses',
        glCode: '5300',
        children: [
          { id: 'engineering-salaries', name: 'Engineering Salaries', level: 2, parentId: 'research-development', glCode: '5310' },
          { id: 'development-tools', name: 'Development Tools & Software', level: 2, parentId: 'research-development', glCode: '5320' }
        ]
      },
      {
        id: 'general-admin',
        name: 'General & Administrative',
        level: 1,
        parentId: 'expenses',
        glCode: '5400',
        children: [
          { id: 'office-expenses', name: 'Office Expenses', level: 2, parentId: 'general-admin', glCode: '5410' },
          { id: 'professional-fees', name: 'Professional Fees', level: 2, parentId: 'general-admin', glCode: '5420' },
          { id: 'insurance', name: 'Insurance', level: 2, parentId: 'general-admin', glCode: '5430' },
          { id: 'travel', name: 'Travel & Entertainment', level: 2, parentId: 'general-admin', glCode: '5440' }
        ]
      }
    ]
  }
];

// E-commerce/Retail Template
export const RETAIL_TEMPLATE: HierarchicalCategory[] = [
  {
    id: 'assets',
    name: 'Assets',
    level: 0,
    parentId: null,
    glCode: '1000',
    children: [
      {
        id: 'current-assets',
        name: 'Current Assets',
        level: 1,
        parentId: 'assets',
        glCode: '1100',
        children: [
          { id: 'cash', name: 'Cash & Cash Equivalents', level: 2, parentId: 'current-assets', glCode: '1110' },
          { id: 'accounts-receivable', name: 'Accounts Receivable', level: 2, parentId: 'current-assets', glCode: '1120' },
          { id: 'inventory', name: 'Inventory', level: 2, parentId: 'current-assets', glCode: '1130' },
          { id: 'prepaid-expenses', name: 'Prepaid Expenses', level: 2, parentId: 'current-assets', glCode: '1140' }
        ]
      },
      {
        id: 'fixed-assets',
        name: 'Fixed Assets',
        level: 1,
        parentId: 'assets',
        glCode: '1200',
        children: [
          { id: 'equipment', name: 'Equipment & Fixtures', level: 2, parentId: 'fixed-assets', glCode: '1210' },
          { id: 'vehicles', name: 'Vehicles', level: 2, parentId: 'fixed-assets', glCode: '1220' }
        ]
      }
    ]
  },
  {
    id: 'liabilities',
    name: 'Liabilities',
    level: 0,
    parentId: null,
    glCode: '2000',
    children: [
      {
        id: 'current-liabilities',
        name: 'Current Liabilities',
        level: 1,
        parentId: 'liabilities',
        glCode: '2100',
        children: [
          { id: 'accounts-payable', name: 'Accounts Payable', level: 2, parentId: 'current-liabilities', glCode: '2110' },
          { id: 'sales-tax-payable', name: 'Sales Tax Payable', level: 2, parentId: 'current-liabilities', glCode: '2120' },
          { id: 'accrued-expenses', name: 'Accrued Expenses', level: 2, parentId: 'current-liabilities', glCode: '2130' }
        ]
      }
    ]
  },
  {
    id: 'equity',
    name: 'Equity',
    level: 0,
    parentId: null,
    glCode: '3000',
    children: [
      { id: 'owners-equity', name: 'Owner\'s Equity', level: 1, parentId: 'equity', glCode: '3100' },
      { id: 'retained-earnings', name: 'Retained Earnings', level: 1, parentId: 'equity', glCode: '3200' }
    ]
  },
  {
    id: 'revenue',
    name: 'Revenue',
    level: 0,
    parentId: null,
    glCode: '4000',
    children: [
      { id: 'product-sales', name: 'Product Sales', level: 1, parentId: 'revenue', glCode: '4100' },
      { id: 'shipping-revenue', name: 'Shipping & Handling', level: 1, parentId: 'revenue', glCode: '4200' },
      { id: 'other-revenue', name: 'Other Revenue', level: 1, parentId: 'revenue', glCode: '4900' }
    ]
  },
  {
    id: 'expenses',
    name: 'Expenses',
    level: 0,
    parentId: null,
    glCode: '5000',
    children: [
      {
        id: 'cost-of-goods-sold',
        name: 'Cost of Goods Sold',
        level: 1,
        parentId: 'expenses',
        glCode: '5100',
        children: [
          { id: 'product-costs', name: 'Product Costs', level: 2, parentId: 'cost-of-goods-sold', glCode: '5110' },
          { id: 'shipping-costs', name: 'Shipping Costs', level: 2, parentId: 'cost-of-goods-sold', glCode: '5120' },
          { id: 'payment-processing', name: 'Payment Processing', level: 2, parentId: 'cost-of-goods-sold', glCode: '5130' }
        ]
      },
      {
        id: 'operating-expenses',
        name: 'Operating Expenses',
        level: 1,
        parentId: 'expenses',
        glCode: '5200',
        children: [
          { id: 'advertising', name: 'Advertising & Marketing', level: 2, parentId: 'operating-expenses', glCode: '5210' },
          { id: 'rent', name: 'Rent & Utilities', level: 2, parentId: 'operating-expenses', glCode: '5220' },
          { id: 'insurance', name: 'Insurance', level: 2, parentId: 'operating-expenses', glCode: '5230' },
          { id: 'professional-services', name: 'Professional Services', level: 2, parentId: 'operating-expenses', glCode: '5240' },
          { id: 'supplies', name: 'Office Supplies', level: 2, parentId: 'operating-expenses', glCode: '5250' },
          { id: 'travel', name: 'Travel & Entertainment', level: 2, parentId: 'operating-expenses', glCode: '5260' }
        ]
      }
    ]
  }
];

// Professional Services Template
export const CONSULTING_TEMPLATE: HierarchicalCategory[] = [
  {
    id: 'assets',
    name: 'Assets',
    level: 0,
    parentId: null,
    glCode: '1000',
    children: [
      {
        id: 'current-assets',
        name: 'Current Assets',
        level: 1,
        parentId: 'assets',
        glCode: '1100',
        children: [
          { id: 'cash', name: 'Cash & Cash Equivalents', level: 2, parentId: 'current-assets', glCode: '1110' },
          { id: 'accounts-receivable', name: 'Accounts Receivable', level: 2, parentId: 'current-assets', glCode: '1120' },
          { id: 'unbilled-revenue', name: 'Unbilled Revenue', level: 2, parentId: 'current-assets', glCode: '1130' },
          { id: 'prepaid-expenses', name: 'Prepaid Expenses', level: 2, parentId: 'current-assets', glCode: '1140' }
        ]
      },
      {
        id: 'fixed-assets',
        name: 'Fixed Assets',
        level: 1,
        parentId: 'assets',
        glCode: '1200',
        children: [
          { id: 'equipment', name: 'Equipment & Software', level: 2, parentId: 'fixed-assets', glCode: '1210' },
          { id: 'furniture', name: 'Furniture & Fixtures', level: 2, parentId: 'fixed-assets', glCode: '1220' }
        ]
      }
    ]
  },
  {
    id: 'liabilities',
    name: 'Liabilities',
    level: 0,
    parentId: null,
    glCode: '2000',
    children: [
      {
        id: 'current-liabilities',
        name: 'Current Liabilities',
        level: 1,
        parentId: 'liabilities',
        glCode: '2100',
        children: [
          { id: 'accounts-payable', name: 'Accounts Payable', level: 2, parentId: 'current-liabilities', glCode: '2110' },
          { id: 'accrued-salaries', name: 'Accrued Salaries', level: 2, parentId: 'current-liabilities', glCode: '2120' },
          { id: 'payroll-taxes', name: 'Payroll Taxes Payable', level: 2, parentId: 'current-liabilities', glCode: '2130' }
        ]
      }
    ]
  },
  {
    id: 'equity',
    name: 'Equity',
    level: 0,
    parentId: null,
    glCode: '3000',
    children: [
      { id: 'owners-equity', name: 'Owner\'s Equity', level: 1, parentId: 'equity', glCode: '3100' },
      { id: 'retained-earnings', name: 'Retained Earnings', level: 1, parentId: 'equity', glCode: '3200' }
    ]
  },
  {
    id: 'revenue',
    name: 'Revenue',
    level: 0,
    parentId: null,
    glCode: '4000',
    children: [
      { id: 'consulting-fees', name: 'Consulting Fees', level: 1, parentId: 'revenue', glCode: '4100' },
      { id: 'project-revenue', name: 'Project Revenue', level: 1, parentId: 'revenue', glCode: '4200' },
      { id: 'training-revenue', name: 'Training Revenue', level: 1, parentId: 'revenue', glCode: '4300' },
      { id: 'other-revenue', name: 'Other Revenue', level: 1, parentId: 'revenue', glCode: '4900' }
    ]
  },
  {
    id: 'expenses',
    name: 'Expenses',
    level: 0,
    parentId: null,
    glCode: '5000',
    children: [
      {
        id: 'personnel-costs',
        name: 'Personnel Costs',
        level: 1,
        parentId: 'expenses',
        glCode: '5100',
        children: [
          { id: 'salaries', name: 'Salaries & Wages', level: 2, parentId: 'personnel-costs', glCode: '5110' },
          { id: 'benefits', name: 'Employee Benefits', level: 2, parentId: 'personnel-costs', glCode: '5120' },
          { id: 'contractor-fees', name: 'Contractor Fees', level: 2, parentId: 'personnel-costs', glCode: '5130' }
        ]
      },
      {
        id: 'operating-expenses',
        name: 'Operating Expenses',
        level: 1,
        parentId: 'expenses',
        glCode: '5200',
        children: [
          { id: 'office-rent', name: 'Office Rent', level: 2, parentId: 'operating-expenses', glCode: '5210' },
          { id: 'utilities', name: 'Utilities', level: 2, parentId: 'operating-expenses', glCode: '5220' },
          { id: 'software', name: 'Software & Licenses', level: 2, parentId: 'operating-expenses', glCode: '5230' },
          { id: 'professional-services', name: 'Professional Services', level: 2, parentId: 'operating-expenses', glCode: '5240' },
          { id: 'travel', name: 'Travel & Entertainment', level: 2, parentId: 'operating-expenses', glCode: '5250' },
          { id: 'marketing', name: 'Marketing & Business Development', level: 2, parentId: 'operating-expenses', glCode: '5260' }
        ]
      }
    ]
  }
];

// Template mapping
export const STANDARD_TEMPLATES: Record<string, HierarchicalCategory[]> = {
  'saas': SAAS_TEMPLATE,
  'software': SAAS_TEMPLATE,
  'technology': SAAS_TEMPLATE,
  'retail': RETAIL_TEMPLATE,
  'e-commerce': RETAIL_TEMPLATE,
  'ecommerce': RETAIL_TEMPLATE,
  'consulting': CONSULTING_TEMPLATE,
  'service': CONSULTING_TEMPLATE,
  'professional-services': CONSULTING_TEMPLATE
};

/**
 * Get standard template for industry/business type
 */
export function getStandardTemplate(industry: string, businessType?: string): HierarchicalCategory[] {
  const key = businessType || industry.toLowerCase();
  return STANDARD_TEMPLATES[key] || STANDARD_TEMPLATES['service']; // Default to service template
}

/**
 * Get all available template names
 */
export function getAvailableTemplates(): string[] {
  return Object.keys(STANDARD_TEMPLATES);
}