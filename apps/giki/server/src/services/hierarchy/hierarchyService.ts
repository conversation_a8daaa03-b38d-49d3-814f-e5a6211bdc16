/**
 * Hierarchy Management Service
 * 
 * Manages category hierarchies from multiple sources:
 * 1. Historical data (most preferred) - learn from customer's past transactions
 * 2. Uploaded files - Excel/CSV with hierarchy structure
 * 3. UI created - user builds hierarchy in interface
 * 4. Standard templates - industry-specific GL structures
 */

import { z } from 'zod';

// Load environment variables
import '../../utils/env-loader';

// Hierarchy source types (in priority order)
export type HierarchySource = 'historical' | 'uploaded' | 'ui_created' | 'standard';

// Hierarchical category schema
const hierarchicalCategorySchema = z.object({
  id: z.string().describe('Unique category identifier'),
  name: z.string().describe('Category display name'),
  level: z.number().describe('Hierarchy level (0=root, 1=sub, 2=leaf)'),
  parentId: z.string().nullable().describe('Parent category ID'),
  glCode: z.string().optional().describe('General Ledger code'),
  description: z.string().optional().describe('Category description'),
  // Historical learning data
  transactionCount: z.number().optional().describe('Number of transactions categorized here'),
  accuracyScore: z.number().optional().describe('Accuracy score from historical data'),
  commonDescriptions: z.array(z.string()).optional().describe('Common transaction descriptions'),
  // Hierarchy structure
  children: z.array(z.lazy(() => hierarchicalCategorySchema)).optional().describe('Child categories'),
  // Business context
  industryRelevant: z.array(z.string()).optional().describe('Industries where this category is relevant')
});

// Category hierarchy schema
const categoryHierarchySchema = z.object({
  id: z.string().describe('Unique hierarchy identifier'),
  name: z.string().describe('Hierarchy name (e.g., "Acme Corp Chart of Accounts")'),
  source: z.enum(['historical', 'uploaded', 'ui_created', 'standard']).describe('How this hierarchy was created'),
  industry: z.string().describe('Primary industry this hierarchy serves'),
  businessType: z.enum(['retail', 'service', 'manufacturing', 'saas', 'consulting', 'other']).describe('Business type'),
  version: z.string().describe('Hierarchy version for tracking changes'),
  categories: z.array(hierarchicalCategorySchema).describe('Root-level categories'),
  metadata: z.object({
    totalCategories: z.number().describe('Total categories across all levels'),
    maxDepth: z.number().describe('Maximum hierarchy depth'),
    createdAt: z.string().describe('ISO timestamp of creation'),
    lastUsed: z.string().optional().describe('ISO timestamp of last usage'),
    // Historical learning metadata
    totalTransactions: z.number().optional().describe('Transactions used for learning'),
    overallAccuracy: z.number().optional().describe('Overall categorization accuracy'),
    // File upload metadata  
    uploadedFile: z.string().optional().describe('Original filename if uploaded'),
    // Standard template metadata
    templateId: z.string().optional().describe('Standard template ID if used'),
    compliance: z.array(z.string()).optional().describe('Compliance standards (GAAP, IFRS)')
  })
});

export type HierarchicalCategory = z.infer<typeof hierarchicalCategorySchema>;
export type CategoryHierarchy = z.infer<typeof categoryHierarchySchema>;

// Transaction pattern for historical learning
export interface TransactionPattern {
  description: string;
  categoryId: string;
  categoryPath: string[];
  frequency: number;
  confidence: number;
}

// Usage statistics for optimization
export interface HierarchyUsageStats {
  categoryId: string;
  categoryName: string;
  transactionCount: number;
  averageAccuracy: number;
  commonMisclassifications: string[];
  lastUsed: string;
}

export class HierarchyService {
  private hierarchies: Map<string, CategoryHierarchy> = new Map();

  /**
   * Get hierarchy by priority: historical > uploaded > ui_created > standard
   */
  async getHierarchyForBusiness(
    businessContext: {
      industry: string;
      businessType: string;
      userId: string;
    },
    preferredSource?: HierarchySource
  ): Promise<CategoryHierarchy> {
    // Try to get user's preferred hierarchy
    const userHierarchies = Array.from(this.hierarchies.values())
      .filter(h => h.industry === businessContext.industry)
      .sort((a, b) => this.getSourcePriority(a.source) - this.getSourcePriority(b.source));

    if (userHierarchies.length > 0) {
      return userHierarchies[0];
    }

    // Fallback to standard template
    return this.getStandardTemplate(businessContext.industry, businessContext.businessType);
  }

  /**
   * Learn hierarchy from customer's historical transaction data
   */
  async learnFromHistoricalData(
    transactions: Array<{
      description: string;
      amount: number;
      type: 'debit' | 'credit';
      category?: string; // If user has already categorized
      date: string;
    }>,
    businessContext: {
      industry: string;
      businessType: string;
      userId: string;
    }
  ): Promise<CategoryHierarchy> {
    console.log(`[HierarchyService] Learning from ${transactions.length} historical transactions`);

    // Extract patterns from historical data
    const patterns = this.extractTransactionPatterns(transactions);
    
    // Build hierarchy structure from patterns
    const learnedHierarchy = this.buildHierarchyFromPatterns(patterns, businessContext);
    
    // Calculate accuracy scores
    learnedHierarchy.metadata.overallAccuracy = this.calculateAccuracy(patterns);
    learnedHierarchy.metadata.totalTransactions = transactions.length;

    // Store the learned hierarchy
    this.hierarchies.set(learnedHierarchy.id, learnedHierarchy);

    console.log(`[HierarchyService] Learned hierarchy with ${learnedHierarchy.metadata.totalCategories} categories, ${(learnedHierarchy.metadata.overallAccuracy! * 100).toFixed(1)}% accuracy`);
    
    return learnedHierarchy;
  }

  /**
   * Import hierarchy from uploaded file (Excel/CSV)
   */
  async importFromFile(
    fileContent: string,
    fileName: string,
    businessContext: {
      industry: string;
      businessType: string;
      userId: string;
    }
  ): Promise<CategoryHierarchy> {
    console.log(`[HierarchyService] Importing hierarchy from ${fileName}`);

    // Parse file content (implementation depends on format)
    const parsedHierarchy = await this.parseHierarchyFile(fileContent, fileName);
    
    // Create hierarchy object
    const uploadedHierarchy: CategoryHierarchy = {
      id: `uploaded-${Date.now()}`,
      name: `${businessContext.industry} Hierarchy (${fileName})`,
      source: 'uploaded',
      industry: businessContext.industry,
      businessType: businessContext.businessType,
      version: '1.0.0',
      categories: parsedHierarchy.categories,
      metadata: {
        totalCategories: this.countTotalCategories(parsedHierarchy.categories),
        maxDepth: this.calculateMaxDepth(parsedHierarchy.categories),
        createdAt: new Date().toISOString(),
        uploadedFile: fileName
      }
    };

    // Store the uploaded hierarchy
    this.hierarchies.set(uploadedHierarchy.id, uploadedHierarchy);

    return uploadedHierarchy;
  }

  /**
   * Get standard industry template
   */
  async getStandardTemplate(
    industry: string,
    businessType: string
  ): Promise<CategoryHierarchy> {
    console.log(`[HierarchyService] Getting standard template for ${industry} ${businessType}`);

    // Return appropriate standard template based on industry
    const templateId = `${industry.toLowerCase()}-${businessType}`;
    
    const standardHierarchy: CategoryHierarchy = {
      id: `standard-${templateId}`,
      name: `${industry} Standard Chart of Accounts`,
      source: 'standard',
      industry,
      businessType: businessType as any,
      version: '1.0.0',
      categories: this.getStandardCategories(industry, businessType),
      metadata: {
        totalCategories: 0, // Will be calculated
        maxDepth: 0, // Will be calculated
        createdAt: new Date().toISOString(),
        templateId,
        compliance: ['GAAP', 'IFRS']
      }
    };

    // Calculate metadata
    standardHierarchy.metadata.totalCategories = this.countTotalCategories(standardHierarchy.categories);
    standardHierarchy.metadata.maxDepth = this.calculateMaxDepth(standardHierarchy.categories);

    return standardHierarchy;
  }

  /**
   * Update hierarchy based on usage patterns and corrections
   */
  async optimizeHierarchy(
    hierarchyId: string,
    usageStats: HierarchyUsageStats[],
    corrections: Array<{
      transactionId: string;
      originalCategory: string;
      correctedCategory: string;
      reason?: string;
    }>
  ): Promise<CategoryHierarchy> {
    const hierarchy = this.hierarchies.get(hierarchyId);
    if (!hierarchy) {
      throw new Error(`Hierarchy ${hierarchyId} not found`);
    }

    console.log(`[HierarchyService] Optimizing hierarchy with ${corrections.length} corrections`);

    // Apply optimizations based on usage patterns
    const optimizedHierarchy = this.applyOptimizations(hierarchy, usageStats, corrections);
    
    // Update version
    optimizedHierarchy.version = this.incrementVersion(hierarchy.version);
    optimizedHierarchy.metadata.lastUsed = new Date().toISOString();

    // Store updated hierarchy
    this.hierarchies.set(hierarchyId, optimizedHierarchy);

    return optimizedHierarchy;
  }

  // Private helper methods
  private getSourcePriority(source: HierarchySource): number {
    const priorities = { historical: 1, uploaded: 2, ui_created: 3, standard: 4 };
    return priorities[source];
  }

  private extractTransactionPatterns(transactions: any[]): TransactionPattern[] {
    // Implementation: analyze transaction descriptions and existing categories
    // Group similar transactions, calculate frequencies, build patterns
    return [];
  }

  private buildHierarchyFromPatterns(patterns: TransactionPattern[], context: any): CategoryHierarchy {
    // Implementation: convert patterns into hierarchical structure
    return {} as CategoryHierarchy;
  }

  private calculateAccuracy(patterns: TransactionPattern[]): number {
    // Implementation: calculate overall accuracy from patterns
    return 0.85;
  }

  private async parseHierarchyFile(content: string, fileName: string): Promise<{ categories: HierarchicalCategory[] }> {
    // Implementation: parse Excel/CSV file content
    return { categories: [] };
  }

  private getStandardCategories(industry: string, businessType: string): HierarchicalCategory[] {
    // Import and use standard templates
    const { getStandardTemplate } = require('./standardTemplates');
    return getStandardTemplate(industry, businessType);
  }

  private countTotalCategories(categories: HierarchicalCategory[]): number {
    let total = categories.length;
    for (const category of categories) {
      if (category.children?.length) {
        total += this.countTotalCategories(category.children);
      }
    }
    return total;
  }

  private calculateMaxDepth(categories: HierarchicalCategory[]): number {
    let maxDepth = 0;
    for (const category of categories) {
      const depth = 1 + (category.children?.length ? this.calculateMaxDepth(category.children) : 0);
      maxDepth = Math.max(maxDepth, depth);
    }
    return maxDepth;
  }

  private applyOptimizations(
    hierarchy: CategoryHierarchy, 
    usageStats: HierarchyUsageStats[], 
    corrections: any[]
  ): CategoryHierarchy {
    // Implementation: apply optimizations based on usage and corrections
    return { ...hierarchy };
  }

  private incrementVersion(version: string): string {
    const parts = version.split('.');
    parts[2] = String(parseInt(parts[2]) + 1);
    return parts.join('.');
  }
}

// Export singleton instance
export const hierarchyService = new HierarchyService();