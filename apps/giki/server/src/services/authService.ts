import bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import type { User, AuthTokens, RegisterRequest, UserRole } from '@shared/types/index';

// Mock database (replace with actual database later)
const users = new Map<string, User & { password: string }>();
const refreshTokens = new Map<string, string>(); // token -> userId

class AuthService {
  private readonly SALT_ROUNDS = 10;
  private readonly ACCESS_TOKEN_EXPIRY = '15m';
  private readonly REFRESH_TOKEN_EXPIRY = '7d';

  async login(email: string, password: string): Promise<{
    success: boolean;
    user?: User;
    tokens?: AuthTokens;
    error?: string;
  }> {
    try {
      // Find user by email
      const userEntry = Array.from(users.values()).find(u => u.email === email);
      
      if (!userEntry) {
        return { success: false, error: 'User not found' };
      }

      // Verify password
      const isValid = await bcrypt.compare(password, userEntry.password);
      
      if (!isValid) {
        return { success: false, error: 'Invalid password' };
      }

      // Generate tokens
      const tokens = this.generateTokens(userEntry.id);
      
      // Store refresh token
      refreshTokens.set(tokens.refreshToken, userEntry.id);

      // Return user without password
      const { password: _, ...user } = userEntry;
      
      return {
        success: true,
        user,
        tokens
      };
    } catch (error: any) {
      console.error('Login error:', error);
      return { success: false, error: error.message };
    }
  }

  async register(data: RegisterRequest): Promise<{
    success: boolean;
    user?: User;
    tokens?: AuthTokens;
    error?: string;
  }> {
    try {
      // Check if user exists
      const exists = Array.from(users.values()).some(u => u.email === data.email);
      
      if (exists) {
        return { success: false, error: 'User already exists' };
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(data.password, this.SALT_ROUNDS);

      // Create user
      const userId = uuidv4();
      const now = new Date();
      
      const newUser: User & { password: string } = {
        id: userId,
        email: data.email,
        name: data.name,
        company: data.company,
        role: 'user' as UserRole,
        password: hashedPassword,
        createdAt: now,
        updatedAt: now
      };

      // Save user
      users.set(userId, newUser);

      // Generate tokens
      const tokens = this.generateTokens(userId);
      
      // Store refresh token
      refreshTokens.set(tokens.refreshToken, userId);

      // Return user without password
      const { password: _, ...user } = newUser;
      
      return {
        success: true,
        user,
        tokens
      };
    } catch (error: any) {
      console.error('Register error:', error);
      return { success: false, error: error.message };
    }
  }

  async refreshTokens(refreshToken: string): Promise<{
    success: boolean;
    tokens?: AuthTokens;
    error?: string;
  }> {
    try {
      // Verify refresh token
      const userId = refreshTokens.get(refreshToken);
      
      if (!userId) {
        return { success: false, error: 'Invalid refresh token' };
      }

      // Check if user still exists
      const user = users.get(userId);
      
      if (!user) {
        return { success: false, error: 'User not found' };
      }

      // Delete old refresh token
      refreshTokens.delete(refreshToken);

      // Generate new tokens
      const tokens = this.generateTokens(userId);
      
      // Store new refresh token
      refreshTokens.set(tokens.refreshToken, userId);

      return {
        success: true,
        tokens
      };
    } catch (error: any) {
      console.error('Refresh token error:', error);
      return { success: false, error: error.message };
    }
  }

  async getCurrentUser(accessToken: string): Promise<User | null> {
    try {
      if (!accessToken || accessToken === 'stored-in-cookie') {
        return null;
      }

      // Simple token verification (replace with JWT in production)
      // For now, just check if it's a valid UUID (user ID)
      const userId = accessToken; // In production, decode JWT to get userId
      
      const userEntry = users.get(userId);
      
      if (!userEntry) {
        return null;
      }

      // Return user without password
      const { password: _, ...user } = userEntry;
      
      return user;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  private generateTokens(userId: string): AuthTokens {
    // Simple token generation (replace with JWT in production)
    const accessToken = userId; // In production, use JWT
    const refreshToken = uuidv4();
    
    return {
      accessToken,
      refreshToken
    };
  }

  // Seed some test data
  async seedTestData() {
    // Check if test user already exists
    const exists = Array.from(users.values()).some(u => u.email === '<EMAIL>');
    
    if (!exists) {
      const testUser: RegisterRequest = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
        company: 'Test Company'
      };
      
      await this.register(testUser);
      console.log('Test user created: <EMAIL> / password123');
    }
  }
}

// Singleton instance
let authService: AuthService;

export function getAuthService(): AuthService {
  if (!authService) {
    authService = new AuthService();
    // Seed test data in development
    if (process.env.NODE_ENV !== 'production') {
      authService.seedTestData();
    }
  }
  return authService;
}