import { v4 as uuidv4 } from 'uuid';
import { promises as fs } from 'fs';
import path from 'path';
import type { FileUpload, FileStatus, Transaction } from '@shared/types/index';
import { getExcelCsvParserService } from './excelCsvParserService.js';
import { getGeminiService } from './geminiService.js';
import { db } from '../config/database.js';
import { files, transactions } from '../schema/index.js';
import { eq, and } from 'drizzle-orm';

// Keep upload progress in memory for performance (temporary state)
const uploadProgress = new Map<string, number>();

interface FileProcessingResult {
  success: boolean;
  transactionCount?: number;
  transactions?: Transaction[];
  error?: string;
  schemaInterpretation?: any;
  validationResult?: any;
  needsReview?: boolean;
}

interface ProcessingOptions {
  userConfirmed?: boolean;
  schemaOverrides?: {
    dateColumn?: string;
    descriptionColumn?: string;
    amountColumn?: string;
    vendorColumn?: string;
  };
}

interface PreviewResult {
  success: boolean;
  schemaInterpretation: any;
  validationResult: any;
  previewData: any[];
  needsHumanReview: boolean;
  error?: string;
}

export class FileUploadService {
  private readonly UPLOAD_DIR = path.join(process.cwd(), 'uploads');
  private readonly MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
  private readonly ALLOWED_TYPES = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel', // .xls
    'text/csv', // .csv
    'application/pdf' // .pdf
  ];

  constructor() {
    this.ensureUploadDirectoryExists();
  }

  /**
   * Preview file processing - AI analysis without full processing
   * Shows user what will be processed and allows confirmation
   */
  async previewFile(fileId: string): Promise<PreviewResult> {
    const fileRecords = await db.select().from(files).where(eq(files.id, fileId));
    const fileRecord = fileRecords[0];
    
    if (!fileRecord || !fileRecord.filePath) {
      return {
        success: false,
        schemaInterpretation: null,
        validationResult: null,
        previewData: [],
        needsHumanReview: true,
        error: 'File not found or no file path'
      };
    }

    try {
      // Check if file is Excel/CSV
      const isExcelCsv = fileRecord.fileName.toLowerCase().match(/\.(xlsx|xls|csv)$/);
      
      if (!isExcelCsv) {
        return {
          success: false,
          schemaInterpretation: null,
          validationResult: null,
          previewData: [],
          needsHumanReview: true,
          error: 'Preview only available for Excel/CSV files'
        };
      }

      // Parse file for schema analysis
      const parserService = getExcelCsvParserService();
      const parseResult = await parserService.parseFile(fileRecord.filePath, fileRecord.fileName);
      
      if (!parseResult.success || !parseResult.transactions) {
        return {
          success: false,
          schemaInterpretation: null,
          validationResult: null,
          previewData: [],
          needsHumanReview: true,
          error: parseResult.error || 'Failed to parse file for preview'
        };
      }

      // Get headers and sample data for AI analysis
      const headers = parseResult.metadata?.columnsDetected || [];
      const sampleRows = parseResult.transactions.slice(0, 5).map(t => [
        t.date?.toISOString().split('T')[0] || '',
        t.description || '',
        t.amount || 0,
        t.rawData?.vendor || ''
      ]);

      // Use Gemini for schema interpretation
      const geminiService = getGeminiService();
      const schemaInterpretation = await geminiService.interpretSchema({
        headers,
        sampleRows,
        fileName: fileRecord.fileName
      });

      // Validate the interpretation
      const validationResult = await geminiService.validateSchemaInterpretation({
        interpretation: schemaInterpretation,
        actualData: sampleRows
      });

      // Create preview data
      const previewData = parseResult.transactions.slice(0, 10).map(t => ({
        date: t.date?.toISOString().split('T')[0] || '',
        description: t.description || '',
        amount: t.amount || 0,
        type: t.type || 'unknown',
        confidence: 'pending' // Will be filled during actual processing
      }));

      return {
        success: true,
        schemaInterpretation,
        validationResult,
        previewData,
        needsHumanReview: schemaInterpretation.needs_human_review || !validationResult.is_valid
      };

    } catch (error: any) {
      console.error('Error previewing file:', error);
      return {
        success: false,
        schemaInterpretation: null,
        validationResult: null,
        previewData: [],
        needsHumanReview: true,
        error: error.message || 'Preview failed'
      };
    }
  }

  private async ensureUploadDirectoryExists() {
    try {
      await fs.access(this.UPLOAD_DIR);
    } catch {
      await fs.mkdir(this.UPLOAD_DIR, { recursive: true });
    }
  }

  /**
   * Upload and store a file
   */
  async uploadFile(file: File, userId: string): Promise<FileUpload> {
    // Validate file
    this.validateFile(file);

    // Save file to disk first
    const buffer = Buffer.from(await file.arrayBuffer());
    const fileId = uuidv4();
    const filePath = path.join(this.UPLOAD_DIR, `${fileId}_${file.name}`);
    
    await fs.writeFile(filePath, buffer);

    // Insert file record into database
    const insertedFiles = await db.insert(files).values({
      id: fileId,
      userId,
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
      filePath,
      status: 'pending'
    }).returning();

    const fileRecord = insertedFiles[0];
    
    // Convert database record to API format
    return {
      id: fileRecord.id,
      userId: fileRecord.userId,
      fileName: fileRecord.fileName,
      fileType: fileRecord.fileType,
      fileSize: fileRecord.fileSize,
      status: fileRecord.status as FileStatus,
      transactionCount: fileRecord.transactionCount || undefined,
      processedAt: fileRecord.processedAt || undefined,
      metadata: fileRecord.metadata,
      createdAt: fileRecord.createdAt,
      updatedAt: fileRecord.updatedAt
    };
  }

  /**
   * Process uploaded file (parse Excel/CSV, extract transactions)
   * Enhanced: Uses AI-validated schema interpretation and user confirmation
   */
  async processFile(fileId: string, options: ProcessingOptions = {}): Promise<FileProcessingResult> {
    const fileRecords = await db.select().from(files).where(eq(files.id, fileId));
    const fileRecord = fileRecords[0];
    
    if (!fileRecord) {
      return {
        success: false,
        error: 'File not found'
      };
    }

    if (!fileRecord.filePath) {
      return {
        success: false,
        error: 'File path not found'
      };
    }

    try {
      // Update status to processing in database
      await db.update(files)
        .set({ 
          status: 'processing',
          updatedAt: new Date()
        })
        .where(eq(files.id, fileId));
      
      uploadProgress.set(fileId, 0);

      // Check if file is Excel/CSV (skip PDF processing for now)
      const isExcelCsv = fileRecord.fileName.toLowerCase().match(/\.(xlsx|xls|csv)$/);
      
      if (!isExcelCsv) {
        // For PDF files, simulate processing for now
        uploadProgress.set(fileId, 50);
        await new Promise(resolve => setTimeout(resolve, 1000));
        uploadProgress.set(fileId, 100);
        
        // Update database record
        await db.update(files)
          .set({
            status: 'completed',
            transactionCount: 0,
            processedAt: new Date(),
            updatedAt: new Date()
          })
          .where(eq(files.id, fileId));
        
        uploadProgress.delete(fileId);

        return {
          success: true,
          transactionCount: 0
        };
      }

      // Progress: Start parsing
      uploadProgress.set(fileId, 10);

      // Step 1: Parse file using validated schema interpretation
      const parserService = getExcelCsvParserService();
      let parseResult;
      
      if (options.userConfirmed && options.schemaOverrides) {
        // Use user-confirmed schema overrides for parsing
        parseResult = await parserService.parseFile(
          fileRecord.filePath, 
          fileRecord.fileName, 
          options.schemaOverrides
        );
      } else {
        // Use default parsing (backwards compatibility)
        parseResult = await parserService.parseFile(fileRecord.filePath, fileRecord.fileName);
      }
      
      uploadProgress.set(fileId, 40);

      if (!parseResult.success) {
        throw new Error(parseResult.error || 'Failed to parse file');
      }

      // Step 2: AI-powered transaction categorization
      const geminiService = getGeminiService();
      const rawTransactions = parseResult.transactions || [];
      const categorizedTransactions = [];
      
      uploadProgress.set(fileId, 60);
      
      // Process transactions in batches for AI categorization
      const batchSize = 10;
      for (let i = 0; i < rawTransactions.length; i += batchSize) {
        const batch = rawTransactions.slice(i, i + batchSize);
        
        for (const txn of batch) {
          try {
            // Categorize each transaction using AI
            const categorizationResult = await geminiService.categorizeTransaction({
              description: txn.description || '',
              amount: txn.amount || 0,
              date: txn.date,
              vendor: txn.rawData?.vendor || ''
            });
            
            // Add AI categorization to transaction
            categorizedTransactions.push({
              ...txn,
              aiCategory: categorizationResult.category_name,
              aiCategoryPath: categorizationResult.category_path,
              aiConfidence: categorizationResult.confidence,
              aiReasoning: categorizationResult.reasoning,
              needsReview: categorizationResult.needs_review || categorizationResult.confidence < 80
            });
          } catch (error: any) {
            console.error(`AI categorization failed for transaction:`, error);
            // Add transaction without AI categorization
            categorizedTransactions.push({
              ...txn,
              aiCategory: 'Uncategorized',
              aiCategoryPath: 'Uncategorized',
              aiConfidence: 0,
              aiReasoning: 'AI categorization failed',
              needsReview: true
            });
          }
        }
        
        // Update progress after each batch
        const progressPercent = Math.min(90, 60 + (i / rawTransactions.length) * 30);
        uploadProgress.set(fileId, progressPercent);
      }

      // Step 3: Convert to final Transaction entities
      const transactions = parserService.convertToTransactions(
        categorizedTransactions,
        fileRecord.userId,
        fileRecord.id
      );

      uploadProgress.set(fileId, 95);

      // Save transactions to database
      const transactionCount = transactions.length;
      
      if (transactions.length > 0) {
        // Prepare transaction data for database insertion
        const transactionData = transactions.map(txn => ({
          userId: txn.userId,
          fileId: txn.fileId,
          date: txn.date,
          description: txn.description,
          amount: txn.amount.toString(),
          type: txn.type,
          categoryId: txn.categoryId,
          confidence: txn.aiConfidence ? (txn.aiConfidence / 100).toString() : null,
          vendor: txn.vendor,
          metadata: {
            aiCategory: txn.aiCategory,
            aiCategoryPath: txn.aiCategoryPath,
            aiReasoning: txn.aiReasoning,
            needsReview: txn.needsReview,
            rawData: txn.rawData
          }
        }));
        
        // Insert transactions in batches for performance
        const batchSize = 100;
        for (let i = 0; i < transactionData.length; i += batchSize) {
          const batch = transactionData.slice(i, i + batchSize);
          await db.insert(transactions).values(batch);
        }
      }
      
      // Update file record in database
      await db.update(files)
        .set({
          status: 'completed',
          transactionCount,
          processedAt: new Date(),
          updatedAt: new Date(),
          metadata: parseResult.metadata
        })
        .where(eq(files.id, fileId));

      uploadProgress.set(fileId, 100);
      
      // Complete processing
      uploadProgress.delete(fileId);

      console.log(`Successfully processed file ${fileRecord.fileName}: ${transactionCount} transactions extracted`);

      return {
        success: true,
        transactionCount,
        transactions
      };

    } catch (error: any) {
      console.error(`Error processing file ${fileRecord?.fileName}:`, error);
      
      // Update database record to failed status
      if (fileRecord) {
        await db.update(files)
          .set({
            status: 'failed',
            errorMessage: error.message,
            updatedAt: new Date()
          })
          .where(eq(files.id, fileId));
      }
      
      uploadProgress.delete(fileId);

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get file by ID
   */
  async getFile(fileId: string): Promise<FileUpload | null> {
    const fileRecords = await db.select().from(files).where(eq(files.id, fileId));
    const fileRecord = fileRecords[0];
    
    if (!fileRecord) return null;

    // Convert database record to API format
    return {
      id: fileRecord.id,
      userId: fileRecord.userId,
      fileName: fileRecord.fileName,
      fileType: fileRecord.fileType,
      fileSize: fileRecord.fileSize,
      status: fileRecord.status as FileStatus,
      transactionCount: fileRecord.transactionCount || undefined,
      processedAt: fileRecord.processedAt || undefined,
      metadata: fileRecord.metadata,
      createdAt: fileRecord.createdAt,
      updatedAt: fileRecord.updatedAt
    };
  }

  /**
   * Get all files for a user
   */
  async getUserFiles(userId: string): Promise<FileUpload[]> {
    const fileRecords = await db.select()
      .from(files)
      .where(eq(files.userId, userId))
      .orderBy(files.createdAt);
    
    // Convert database records to API format
    return fileRecords.map(fileRecord => ({
      id: fileRecord.id,
      userId: fileRecord.userId,
      fileName: fileRecord.fileName,
      fileType: fileRecord.fileType,
      fileSize: fileRecord.fileSize,
      status: fileRecord.status as FileStatus,
      transactionCount: fileRecord.transactionCount || undefined,
      processedAt: fileRecord.processedAt || undefined,
      metadata: fileRecord.metadata,
      createdAt: fileRecord.createdAt,
      updatedAt: fileRecord.updatedAt
    })).reverse(); // Most recent first
  }

  /**
   * Get upload progress for a file
   */
  getUploadProgress(fileId: string): number | null {
    return uploadProgress.get(fileId) || null;
  }

  /**
   * Delete a file
   */
  async deleteFile(fileId: string, userId: string): Promise<boolean> {
    const fileRecords = await db.select().from(files)
      .where(and(eq(files.id, fileId), eq(files.userId, userId)));
    const fileRecord = fileRecords[0];
    
    if (!fileRecord) {
      return false;
    }

    try {
      // Delete from disk
      if (fileRecord.filePath) {
        await fs.unlink(fileRecord.filePath);
      }
      
      // Delete related transactions first (foreign key constraint)
      await db.delete(transactions).where(eq(transactions.fileId, fileId));
      
      // Delete file record from database
      await db.delete(files).where(eq(files.id, fileId));
      
      // Remove from memory progress tracking
      uploadProgress.delete(fileId);
      
      return true;
    } catch (error) {
      console.error('Error deleting file:', error);
      return false;
    }
  }

  /**
   * Validate uploaded file
   */
  private validateFile(file: File): void {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error(`File size exceeds maximum allowed size of ${this.MAX_FILE_SIZE / 1024 / 1024}MB`);
    }

    // Check file type
    if (!this.ALLOWED_TYPES.includes(file.type)) {
      const allowedExtensions = ['.xlsx', '.xls', '.csv', '.pdf'];
      throw new Error(`File type not supported. Allowed types: ${allowedExtensions.join(', ')}`);
    }

    // Check file name
    if (!file.name || file.name.trim().length === 0) {
      throw new Error('File name is required');
    }
  }

  /**
   * Get storage stats
   */
  async getStorageStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    statusCounts: Record<string, number>;
    uploadsInProgress: number;
  }> {
    const fileRecords = await db.select().from(files);
    const totalFiles = fileRecords.length;
    const totalSize = fileRecords.reduce((sum, file) => sum + file.fileSize, 0);
    
    const statusCounts = fileRecords.reduce((counts, file) => {
      counts[file.status] = (counts[file.status] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    return {
      totalFiles,
      totalSize,
      statusCounts,
      uploadsInProgress: uploadProgress.size
    };
  }
}

// Singleton instance
let fileUploadService: FileUploadService | null = null;

export function getFileUploadService(): FileUploadService {
  if (!fileUploadService) {
    fileUploadService = new FileUploadService();
  }
  return fileUploadService;
}