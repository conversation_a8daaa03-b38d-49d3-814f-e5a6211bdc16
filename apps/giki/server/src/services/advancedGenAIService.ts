/**
 * Advanced GenAI Service using Google ADK patterns
 * Implements Generator-Critic pattern for improved categorization accuracy
 */

import { GoogleGenAI } from '@google/genai';
import { z } from 'zod';

// Enhanced response schemas with confidence and reasoning
const CategorizationResponseSchema = z.object({
  categoryId: z.string(),
  categoryName: z.string(),
  confidence: z.number().min(0).max(1),
  reasoning: z.string(),
  alternativeCategories: z.array(z.object({
    categoryId: z.string(),
    categoryName: z.string(),
    confidence: z.number().min(0).max(1)
  })).optional()
});

const CriticResponseSchema = z.object({
  isValid: z.boolean(),
  confidence: z.number().min(0).max(1),
  concerns: z.array(z.string()),
  improvements: z.array(z.string()),
  finalCategoryId: z.string().optional(),
  finalCategoryName: z.string().optional(),
  criticalReasoning: z.string()
});

const ConsensusResponseSchema = z.object({
  finalCategoryId: z.string(),
  finalCategoryName: z.string(),
  confidence: z.number().min(0).max(1),
  reasoning: z.string(),
  agentAgreement: z.number().min(0).max(1),
  evidenceChain: z.array(z.object({
    agent: z.string(),
    decision: z.string(),
    confidence: z.number()
  }))
});

// JSON Schema versions for GenAI SDK
const CategorizationJsonSchema = {
  type: "object",
  properties: {
    categoryId: { type: "string" },
    categoryName: { type: "string" },
    confidence: { type: "number", minimum: 0, maximum: 1 },
    reasoning: { type: "string" },
    alternativeCategories: {
      type: "array",
      items: {
        type: "object",
        properties: {
          categoryId: { type: "string" },
          categoryName: { type: "string" },
          confidence: { type: "number", minimum: 0, maximum: 1 }
        },
        required: ["categoryId", "categoryName", "confidence"]
      }
    }
  },
  required: ["categoryId", "categoryName", "confidence", "reasoning"]
};

const CriticJsonSchema = {
  type: "object",
  properties: {
    isValid: { type: "boolean" },
    confidence: { type: "number", minimum: 0, maximum: 1 },
    concerns: { type: "array", items: { type: "string" } },
    improvements: { type: "array", items: { type: "string" } },
    finalCategoryId: { type: "string" },
    finalCategoryName: { type: "string" },
    criticalReasoning: { type: "string" }
  },
  required: ["isValid", "confidence", "concerns", "improvements", "criticalReasoning"]
};

const ConsensusJsonSchema = {
  type: "object",
  properties: {
    finalCategoryId: { type: "string" },
    finalCategoryName: { type: "string" },
    confidence: { type: "number", minimum: 0, maximum: 1 },
    reasoning: { type: "string" },
    agentAgreement: { type: "number", minimum: 0, maximum: 1 },
    evidenceChain: {
      type: "array",
      items: {
        type: "object",
        properties: {
          agent: { type: "string" },
          decision: { type: "string" },
          confidence: { type: "number" }
        },
        required: ["agent", "decision", "confidence"]
      }
    }
  },
  required: ["finalCategoryId", "finalCategoryName", "confidence", "reasoning", "agentAgreement", "evidenceChain"]
};

type CategorizationResponse = z.infer<typeof CategorizationResponseSchema>;
type CriticResponse = z.infer<typeof CriticResponseSchema>;
type ConsensusResponse = z.infer<typeof ConsensusResponseSchema>;

interface Transaction {
  description: string;
  amount: number;
  date: Date;
  vendor?: string;
  categories: Array<{
    id: string;
    name: string;
    path: string;
    examples?: string[];
  }>;
}

export class AdvancedGenAIService {
  private genAI: GoogleGenAI;
  private static instance: AdvancedGenAIService;

  private constructor() {
    // Use API key authentication (much simpler and available)
    const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY or GOOGLE_API_KEY environment variable is required');
    }
    
    this.genAI = new GoogleGenAI({
      apiKey: apiKey
    });
  }

  static getInstance(): AdvancedGenAIService {
    if (!this.instance) {
      this.instance = new AdvancedGenAIService();
    }
    return this.instance;
  }

  /**
   * Generator Agent - Primary categorization using Gemini 2.0 Flash
   */
  private async generateCategorization(transaction: Transaction): Promise<CategorizationResponse> {
    const categoryList = transaction.categories.map(cat => 
      `- ${cat.name} (${cat.id}): ${cat.path}${cat.examples ? ' Examples: ' + cat.examples.join(', ') : ''}`
    ).join('\n');

    const prompt = `You are a financial transaction categorization expert. Analyze the transaction and provide the most appropriate category.

TRANSACTION:
- Description: "${transaction.description}"
- Amount: $${transaction.amount}
- Date: ${transaction.date.toISOString().split('T')[0]}
${transaction.vendor ? `- Vendor: ${transaction.vendor}` : ''}

AVAILABLE CATEGORIES:
${categoryList}

INSTRUCTIONS:
1. Analyze the transaction description, amount, and context
2. Consider industry standards for financial categorization
3. Match to the MOST SPECIFIC appropriate category
4. Provide high confidence only when certain
5. Include up to 3 alternative categories with their confidence scores
6. Explain your reasoning clearly

REQUIRED JSON FORMAT - You MUST respond with exactly this structure:
{
  "categoryId": "the exact category ID from the list (e.g., cat-software)",
  "categoryName": "the exact category name from the list",  
  "confidence": 0.95,
  "reasoning": "detailed explanation of your categorization decision",
  "alternativeCategories": [
    {
      "categoryId": "alternative-id",
      "categoryName": "Alternative Category Name",
      "confidence": 0.8
    }
  ]
}

Respond ONLY with valid JSON in the exact format above.`;

    const result = await this.genAI.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      config: {
        responseJsonSchema: CategorizationJsonSchema,
        responseMimeType: 'application/json',
        temperature: 0.1, // Low temperature for consistent categorization
        candidateCount: 1,
        maxOutputTokens: 1000
      }
    });

    const parsed = JSON.parse(result.text);
    return CategorizationResponseSchema.parse(parsed);
  }

  /**
   * Critic Agent - Independent validation using Gemini 2.5 Pro
   */
  private async criticizeCategorization(
    transaction: Transaction, 
    primaryResult: CategorizationResponse
  ): Promise<CriticResponse> {
    const categoryContext = transaction.categories.find(cat => cat.id === primaryResult.categoryId);

    const prompt = `You are an expert financial auditor reviewing a transaction categorization. Critically evaluate the proposed categorization.

ORIGINAL TRANSACTION:
- Description: "${transaction.description}"
- Amount: $${transaction.amount}
- Date: ${transaction.date.toISOString().split('T')[0]}

PROPOSED CATEGORIZATION:
- Category: ${primaryResult.categoryName} (${primaryResult.categoryId})
- Confidence: ${(primaryResult.confidence * 100).toFixed(1)}%
- Original Reasoning: ${primaryResult.reasoning}
- Category Path: ${categoryContext?.path || 'Unknown'}

EVALUATION CRITERIA:
1. Is the categorization accurate according to standard accounting practices?
2. Does the transaction description match the category characteristics?
3. Is there a more specific or appropriate category available?
4. Are there any red flags or ambiguities that reduce confidence?
5. Would a financial professional agree with this categorization?

AVAILABLE ALTERNATIVES:
${transaction.categories.map(cat => `- ${cat.name} (${cat.id}): ${cat.path}`).join('\n')}

REQUIRED JSON FORMAT - You MUST respond with exactly this structure:
{
  "isValid": true,
  "confidence": 0.9,
  "concerns": ["list", "of", "concerns"],
  "improvements": ["suggested", "improvements"],
  "finalCategoryId": "cat-software", 
  "finalCategoryName": "Software Subscriptions",
  "criticalReasoning": "detailed critical assessment and reasoning"
}

Respond ONLY with valid JSON in the exact format above.`;

    const result = await this.genAI.models.generateContent({
      model: 'gemini-2.5-pro-001',
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      config: {
        responseJsonSchema: CriticJsonSchema,
        responseMimeType: 'application/json',
        temperature: 0.3, // Slightly higher for critical analysis
        candidateCount: 1,
        maxOutputTokens: 1200
      }
    });

    const parsed = JSON.parse(result.text);
    return CriticResponseSchema.parse(parsed);
  }

  /**
   * Consensus Agent - Final decision making using accumulated context
   */
  private async buildConsensus(
    transaction: Transaction,
    generatorResult: CategorizationResponse,
    criticResult: CriticResponse
  ): Promise<ConsensusResponse> {
    const prompt = `You are the final decision maker in a multi-agent financial categorization system. Review all evidence and make the definitive categorization decision.

TRANSACTION DETAILS:
- Description: "${transaction.description}"
- Amount: $${transaction.amount}
- Date: ${transaction.date.toISOString().split('T')[0]}

GENERATOR AGENT ANALYSIS:
- Category: ${generatorResult.categoryName} (${generatorResult.categoryId})
- Confidence: ${(generatorResult.confidence * 100).toFixed(1)}%
- Reasoning: ${generatorResult.reasoning}

CRITIC AGENT REVIEW:
- Validation: ${criticResult.isValid ? 'APPROVED' : 'REJECTED'}
- Critic Confidence: ${(criticResult.confidence * 100).toFixed(1)}%
- Concerns: ${criticResult.concerns.join(', ') || 'None'}
- Improvements: ${criticResult.improvements.join(', ') || 'None'}
- Alternative Suggestion: ${criticResult.finalCategoryName || 'None'}
- Critical Reasoning: ${criticResult.criticalReasoning}

DECISION FRAMEWORK:
1. If critic approves with high confidence, accept generator result
2. If critic rejects with valid concerns, consider alternative
3. Calculate agent agreement percentage based on confidence alignment
4. Provide detailed evidence chain showing decision process
5. Final confidence should reflect consensus strength

REQUIRED JSON FORMAT - You MUST respond with exactly this structure:
{
  "finalCategoryId": "cat-software",
  "finalCategoryName": "Software Subscriptions", 
  "confidence": 0.92,
  "reasoning": "detailed decision reasoning based on all evidence",
  "agentAgreement": 0.85,
  "evidenceChain": [
    {
      "agent": "generator",
      "decision": "Software Subscriptions",
      "confidence": 0.95
    },
    {
      "agent": "critic", 
      "decision": "APPROVED",
      "confidence": 0.88
    },
    {
      "agent": "consensus",
      "decision": "Software Subscriptions",
      "confidence": 0.92
    }
  ]
}

Respond ONLY with valid JSON in the exact format above.`;

    const result = await this.genAI.models.generateContent({
      model: 'gemini-2.5-flash-001',
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      config: {
        responseJsonSchema: ConsensusJsonSchema,
        responseMimeType: 'application/json',
        temperature: 0.2, // Balanced for decision making
        candidateCount: 1,
        maxOutputTokens: 1000
      }
    });

    const parsed = JSON.parse(result.text);
    return ConsensusResponseSchema.parse(parsed);
  }

  /**
   * Main categorization method using Generator-Critic pattern
   */
  async categorizeTransactionAdvanced(transaction: Transaction): Promise<{
    categoryId: string;
    categoryName: string;
    confidence: number;
    reasoning: string;
    agentAgreement: number;
    evidenceChain: Array<{
      agent: string;
      decision: string;
      confidence: number;
    }>;
    processingTime: number;
  }> {
    const startTime = Date.now();

    try {
      console.log(`🤖 Starting advanced categorization for: ${transaction.description}`);

      // Step 1: Generator Agent
      console.log('📝 Running Generator Agent...');
      const generatorResult = await this.generateCategorization(transaction);
      console.log(`✅ Generator: ${generatorResult.categoryName} (${(generatorResult.confidence * 100).toFixed(1)}%)`);

      // Step 2: Critic Agent  
      console.log('🔍 Running Critic Agent...');
      const criticResult = await this.criticizeCategorization(transaction, generatorResult);
      console.log(`✅ Critic: ${criticResult.isValid ? 'APPROVED' : 'REJECTED'} (${(criticResult.confidence * 100).toFixed(1)}%)`);

      // Step 3: Consensus Agent
      console.log('⚖️ Building Consensus...');
      const consensusResult = await this.buildConsensus(transaction, generatorResult, criticResult);
      console.log(`✅ Final Decision: ${consensusResult.finalCategoryName} (${(consensusResult.confidence * 100).toFixed(1)}%)`);

      const processingTime = Date.now() - startTime;
      console.log(`⏱️ Processing completed in ${processingTime}ms`);

      return {
        categoryId: consensusResult.finalCategoryId,
        categoryName: consensusResult.finalCategoryName,
        confidence: consensusResult.confidence,
        reasoning: consensusResult.reasoning,
        agentAgreement: consensusResult.agentAgreement,
        evidenceChain: consensusResult.evidenceChain,
        processingTime
      };

    } catch (error: any) {
      console.error('❌ Advanced categorization failed:', error);
      
      // Fallback to simple categorization if advanced fails
      console.log('🔄 Falling back to simple categorization...');
      const fallbackResult = await this.generateCategorization(transaction);
      
      return {
        categoryId: fallbackResult.categoryId,
        categoryName: fallbackResult.categoryName,
        confidence: fallbackResult.confidence * 0.7, // Reduced confidence for fallback
        reasoning: `Fallback result: ${fallbackResult.reasoning}`,
        agentAgreement: 0.5, // Low agreement due to fallback
        evidenceChain: [{
          agent: 'generator-fallback',
          decision: fallbackResult.categoryName,
          confidence: fallbackResult.confidence
        }],
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Parallel categorization using multiple approaches simultaneously
   */
  async categorizeWithParallelAgents(transaction: Transaction): Promise<{
    finalResult: ConsensusResponse;
    allResults: Array<{
      agent: string;
      result: CategorizationResponse;
      processingTime: number;
    }>;
    consensusMetrics: {
      agreement: number;
      confidence: number;
      variance: number;
    };
  }> {
    const startTime = Date.now();

    // Run multiple categorization agents in parallel
    const parallelAgents = [
      { name: 'primary-gemini-2.0', model: 'gemini-2.0-flash-001', temperature: 0.1 },
      { name: 'secondary-gemini-2.5', model: 'gemini-2.5-flash-001', temperature: 0.2 },
      { name: 'conservative-agent', model: 'gemini-2.0-flash-001', temperature: 0.05 }
    ];

    console.log('🔄 Running parallel categorization agents...');
    
    const agentPromises = parallelAgents.map(async (agent) => {
      const agentStartTime = Date.now();
      try {
        const result = await this.generateCategorization(transaction);
        return {
          agent: agent.name,
          result,
          processingTime: Date.now() - agentStartTime
        };
      } catch (error) {
        console.error(`❌ Agent ${agent.name} failed:`, error);
        return null;
      }
    });

    const agentResults = (await Promise.all(agentPromises)).filter(result => result !== null) as Array<{
      agent: string;
      result: CategorizationResponse;
      processingTime: number;
    }>;

    // Calculate consensus metrics
    const categories = agentResults.map(r => r.result.categoryId);
    const confidences = agentResults.map(r => r.result.confidence);
    
    const mostCommonCategory = categories.reduce((a, b) => 
      categories.filter(v => v === a).length >= categories.filter(v => v === b).length ? a : b
    );

    const agreement = categories.filter(cat => cat === mostCommonCategory).length / categories.length;
    const avgConfidence = confidences.reduce((a, b) => a + b, 0) / confidences.length;
    const variance = confidences.reduce((sum, conf) => sum + Math.pow(conf - avgConfidence, 2), 0) / confidences.length;

    // Build final consensus
    const primaryResult = agentResults.find(r => r.result.categoryId === mostCommonCategory)?.result || agentResults[0].result;
    
    const consensusResult: ConsensusResponse = {
      finalCategoryId: primaryResult.categoryId,
      finalCategoryName: primaryResult.categoryName,
      confidence: avgConfidence * agreement, // Confidence weighted by agreement
      reasoning: `Consensus from ${agentResults.length} agents: ${primaryResult.reasoning}`,
      agentAgreement: agreement,
      evidenceChain: agentResults.map(r => ({
        agent: r.agent,
        decision: r.result.categoryName,
        confidence: r.result.confidence
      }))
    };

    console.log(`✅ Parallel consensus: ${consensusResult.finalCategoryName} (${(consensusResult.confidence * 100).toFixed(1)}% confidence, ${(agreement * 100).toFixed(1)}% agreement)`);

    return {
      finalResult: consensusResult,
      allResults: agentResults,
      consensusMetrics: {
        agreement,
        confidence: avgConfidence,
        variance
      }
    };
  }
}

export function getAdvancedGenAIService(): AdvancedGenAIService {
  return AdvancedGenAIService.getInstance();
}