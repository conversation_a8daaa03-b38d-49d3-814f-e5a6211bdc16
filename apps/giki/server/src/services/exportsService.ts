import { db, exportHistory, transactions, categories } from '../database/index.js';
import { eq, and, desc } from 'drizzle-orm';
import type { ExportRequest, ExportHistory, ExportFormat } from '@shared/types/index';
import * as fs from 'fs/promises';
import * as path from 'path';

class ExportsService {
  private static instance: ExportsService;
  private exportsDir = path.join(process.cwd(), 'exports');

  static getInstance(): ExportsService {
    if (!this.instance) {
      this.instance = new ExportsService();
    }
    return this.instance;
  }

  constructor() {
    // Ensure exports directory exists
    this.ensureExportsDirectory();
  }

  private async ensureExportsDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.exportsDir, { recursive: true });
    } catch (error) {
      console.error('Failed to create exports directory:', error);
    }
  }

  /**
   * Get export history from database (not mock data)
   */
  async getExportHistory(userId: string): Promise<ExportHistory[]> {
    const exports = await db.select().from(exportHistory)
      .where(eq(exportHistory.userId, userId))
      .orderBy(desc(exportHistory.createdAt));

    return exports.map(exp => ({
      id: exp.id,
      userId: exp.userId,
      format: exp.format,
      fileName: exp.fileName,
      fileUrl: exp.fileUrl,
      recordCount: exp.recordCount,
      createdAt: exp.createdAt,
      expiresAt: exp.expiresAt
    }));
  }

  /**
   * Generate export file with real data
   */
  async generateExport(userId: string, request: ExportRequest): Promise<ExportHistory> {
    // Get real transaction data from database
    const transactionData = await db.select({
      date: transactions.date,
      description: transactions.description,
      amount: transactions.amount,
      type: transactions.type,
      categoryName: categories.name,
      glCode: categories.glCode
    })
    .from(transactions)
    .leftJoin(categories, eq(transactions.categoryId, categories.id))
    .where(eq(transactions.userId, userId))
    .orderBy(desc(transactions.date));

    if (transactionData.length === 0) {
      throw new Error('No transaction data found for export');
    }

    // Generate the actual file
    const fileName = `export-${Date.now()}.${this.getFileExtension(request.format)}`;
    const filePath = path.join(this.exportsDir, fileName);
    const fileUrl = `/api/v1/exports/download/${fileName}`;

    await this.generateFileContent(filePath, request.format, transactionData);

    // Get file size
    const fileStats = await fs.stat(filePath);
    const fileSize = fileStats.size;

    // Save export record to database
    const [exportRecord] = await db.insert(exportHistory).values({
      userId,
      format: request.format,
      fileName,
      fileUrl,
      fileSize,
      recordCount: transactionData.length,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    }).returning();

    return {
      id: exportRecord.id,
      userId: exportRecord.userId,
      format: exportRecord.format,
      fileName: exportRecord.fileName,
      fileUrl: exportRecord.fileUrl,
      recordCount: exportRecord.recordCount,
      createdAt: exportRecord.createdAt,
      expiresAt: exportRecord.expiresAt
    };
  }

  /**
   * Get export file for download
   */
  async getExportFile(exportId: string, userId: string): Promise<{ 
    filePath: string; 
    fileName: string;
    isExpired: boolean;
  } | null> {
    const [exportRecord] = await db.select().from(exportHistory)
      .where(and(
        eq(exportHistory.id, exportId),
        eq(exportHistory.userId, userId)
      ))
      .limit(1);

    if (!exportRecord) {
      return null;
    }

    const isExpired = new Date() > exportRecord.expiresAt;
    const filePath = path.join(this.exportsDir, exportRecord.fileName);

    return {
      filePath,
      fileName: exportRecord.fileName,
      isExpired
    };
  }

  /**
   * Delete export from database and file system
   */
  async deleteExport(exportId: string, userId: string): Promise<boolean> {
    const [exportRecord] = await db.select().from(exportHistory)
      .where(and(
        eq(exportHistory.id, exportId),
        eq(exportHistory.userId, userId)
      ))
      .limit(1);

    if (!exportRecord) {
      return false;
    }

    // Delete file from file system
    const filePath = path.join(this.exportsDir, exportRecord.fileName);
    try {
      await fs.unlink(filePath);
    } catch (error) {
      console.warn('Failed to delete export file:', error);
      // Continue with database deletion even if file deletion fails
    }

    // Delete record from database
    await db.delete(exportHistory)
      .where(eq(exportHistory.id, exportId));

    return true;
  }

  /**
   * Get supported export formats
   */
  getSupportedFormats(): Array<{
    id: ExportFormat;
    name: string;
    description: string;
    fileExtension: string;
  }> {
    return [
      {
        id: 'csv' as ExportFormat,
        name: 'CSV',
        description: 'Comma-separated values',
        fileExtension: '.csv'
      },
      {
        id: 'excel' as ExportFormat,
        name: 'Excel',
        description: 'Microsoft Excel format',
        fileExtension: '.xlsx'
      },
      {
        id: 'quickbooks' as ExportFormat,
        name: 'QuickBooks',
        description: 'QuickBooks IIF format',
        fileExtension: '.iif'
      },
      {
        id: 'tally' as ExportFormat,
        name: 'Tally',
        description: 'Tally XML format',
        fileExtension: '.xml'
      },
      {
        id: 'zoho' as ExportFormat,
        name: 'Zoho Books',
        description: 'Zoho Books CSV format',
        fileExtension: '.csv'
      },
      {
        id: 'pdf' as ExportFormat,
        name: 'PDF',
        description: 'Portable Document Format',
        fileExtension: '.pdf'
      }
    ];
  }

  /**
   * Generate actual file content based on format
   */
  private async generateFileContent(
    filePath: string,
    format: ExportFormat,
    data: any[]
  ): Promise<void> {
    switch (format) {
      case 'csv':
      case 'zoho':
        await this.generateCSV(filePath, data);
        break;
      case 'excel':
        await this.generateExcel(filePath, data);
        break;
      case 'quickbooks':
        await this.generateQuickBooks(filePath, data);
        break;
      case 'tally':
        await this.generateTally(filePath, data);
        break;
      case 'pdf':
        await this.generatePDF(filePath, data);
        break;
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  private async generateCSV(filePath: string, data: any[]): Promise<void> {
    const headers = ['Date', 'Description', 'Amount', 'Type', 'Category', 'GL Code'];
    const csvContent = [
      headers.join(','),
      ...data.map(row => [
        row.date?.toISOString().split('T')[0] || '',
        `"${row.description || ''}"`,
        row.amount || '0',
        row.type || '',
        `"${row.categoryName || ''}"`,
        row.glCode || ''
      ].join(','))
    ].join('\\n');

    await fs.writeFile(filePath, csvContent, 'utf-8');
  }

  private async generateExcel(filePath: string, data: any[]): Promise<void> {
    // For now, generate CSV format
    // TODO: Implement actual Excel generation using a library like xlsx
    await this.generateCSV(filePath, data);
  }

  private async generateQuickBooks(filePath: string, data: any[]): Promise<void> {
    // Generate QuickBooks IIF format
    const iifContent = [
      '!HDR\\tVER\\tREL\\tCOMPNY\\tDATE\\tTIME\\tPROP\\tFLAGS\\tHIDDEN',
      '!VER\\t14.0',
      '!TRNS\\tTRNSTYPE\\tDATE\\tACCNT\\tNAME\\tCLASS\\tAMOUNT\\tDOCNUM\\tMEMO',
      '!SPL\\tSPLID\\tTRNSTYPE\\tDATE\\tACCNT\\tNAME\\tCLASS\\tAMOUNT\\tDOCNUM\\tMEMO',
      '!ENDTRNS',
      ...data.map(row => [
        'TRNS',
        'DEPOSIT', // or 'CHECK' based on type
        row.date?.toISOString().split('T')[0] || '',
        row.categoryName || 'Uncategorized',
        row.description || '',
        '',
        row.amount || '0',
        '',
        row.description || ''
      ].join('\\t')),
      'ENDTRNS'
    ].join('\\n');

    await fs.writeFile(filePath, iifContent, 'utf-8');
  }

  private async generateTally(filePath: string, data: any[]): Promise<void> {
    // Generate basic Tally XML format
    const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<ENVELOPE>
  <HEADER>
    <TALLYREQUEST>Import Data</TALLYREQUEST>
  </HEADER>
  <BODY>
    <IMPORTDATA>
      <REQUESTDESC>
        <REPORTNAME>Vouchers</REPORTNAME>
      </REQUESTDESC>
      <REQUESTDATA>
        ${data.map(row => `
        <TALLYMESSAGE xmlns:UDF="TallyUDF">
          <VOUCHER REMOTEID="generated" VCHTYPE="Journal" ACTION="Create">
            <DATE>${row.date?.toISOString().split('T')[0] || ''}</DATE>
            <NARRATION>${row.description || ''}</NARRATION>
            <ALLLEDGERENTRIES.LIST>
              <LEDGERNAME>${row.categoryName || 'Uncategorized'}</LEDGERNAME>
              <AMOUNT>${row.amount || '0'}</AMOUNT>
            </ALLLEDGERENTRIES.LIST>
          </VOUCHER>
        </TALLYMESSAGE>
        `).join('')}
      </REQUESTDATA>
    </IMPORTDATA>
  </BODY>
</ENVELOPE>`;

    await fs.writeFile(filePath, xmlContent, 'utf-8');
  }

  private async generatePDF(filePath: string, data: any[]): Promise<void> {
    // For now, generate a simple text-based PDF representation
    // TODO: Implement actual PDF generation using a library like pdfkit or puppeteer
    const content = [
      'Transaction Export Report',
      `Generated on: ${new Date().toISOString()}`,
      `Total Records: ${data.length}`,
      '',
      'Date\\tDescription\\tAmount\\tType\\tCategory\\tGL Code',
      ...data.map(row => [
        row.date?.toISOString().split('T')[0] || '',
        row.description || '',
        row.amount || '0',
        row.type || '',
        row.categoryName || '',
        row.glCode || ''
      ].join('\\t'))
    ].join('\\n');

    await fs.writeFile(filePath, content, 'utf-8');
  }

  private getFileExtension(format: ExportFormat): string {
    switch (format) {
      case 'csv':
      case 'zoho':
        return 'csv';
      case 'excel':
        return 'xlsx';
      case 'quickbooks':
        return 'iif';
      case 'tally':
        return 'xml';
      case 'pdf':
        return 'pdf';
      default:
        return 'txt';
    }
  }
}

export function getExportsService(): ExportsService {
  return ExportsService.getInstance();
}