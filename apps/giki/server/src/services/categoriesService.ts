import { db, categories } from '../database/index.js';
import { getGenAIService } from './genAIService.js';
import { eq, and, desc } from 'drizzle-orm';
import type { Category, MISStructure } from '@shared/types/index';

class CategoriesService {
  private static instance: CategoriesService;
  private genAIService = getGenAIService();

  static getInstance(): CategoriesService {
    if (!this.instance) {
      this.instance = new CategoriesService();
    }
    return this.instance;
  }

  /**
   * Get all categories from database (not mock data)
   */
  async getCategories(): Promise<Category[]> {
    const dbCategories = await db.select().from(categories)
      .where(eq(categories.isActive, true))
      .orderBy(categories.sortOrder, categories.name);

    return dbCategories.map(cat => ({
      id: cat.id,
      name: cat.name,
      parentId: cat.parentId || undefined,
      level: cat.level,
      path: cat.path,
      glCode: cat.glCode || undefined,
      description: cat.description || undefined,
      isSystem: cat.isSystem,
      createdAt: cat.createdAt,
      updatedAt: cat.updatedAt
    }));
  }

  /**
   * Get MIS structure from database (not mock data)
   */
  async getMISStructure(userId: string): Promise<MISStructure | null> {
    // First try to get user-specific MIS structure
    const userMIS = await db.select().from(misStructures)
      .where(and(
        eq(misStructures.userId, userId),
        eq(misStructures.isDefault, true)
      ))
      .limit(1);

    if (userMIS.length > 0) {
      const structure = userMIS[0];
      const structureCategories = await this.getCategories();
      
      return {
        id: structure.id,
        userId: structure.userId,
        name: structure.name,
        categories: structureCategories,
        isDefault: structure.isDefault,
        createdAt: structure.createdAt,
        updatedAt: structure.updatedAt
      };
    }

    // If no user MIS, try to create one using AI
    return await this.generateAndSaveMISStructure(userId, 'Technology', 'SaaS');
  }

  /**
   * Generate new MIS structure using AI and save to database
   */
  async generateAndSaveMISStructure(
    userId: string,
    industry: string,
    businessType: string
  ): Promise<MISStructure> {
    // Use AI service to generate structure (no fallbacks)
    const aiStructure = await this.genAIService.generateMISStructure(industry, businessType);
    
    // Save the MIS structure metadata to database
    const [newMIS] = await db.insert(misStructures).values({
      userId,
      name: `${industry} - ${businessType} MIS`,
      description: `AI-generated MIS structure for ${businessType} business in ${industry}`,
      isDefault: true,
      templateId: `${industry.toLowerCase()}-${businessType.toLowerCase()}`,
      configuration: { industry, businessType, aiGenerated: true }
    }).returning();

    // Convert AI structure to categories and save them
    const newCategories = await this.saveAIStructureAsCategories(aiStructure, userId);

    return {
      id: newMIS.id,
      userId: newMIS.userId,
      name: newMIS.name,
      categories: newCategories,
      isDefault: newMIS.isDefault,
      createdAt: newMIS.createdAt,
      updatedAt: newMIS.updatedAt
    };
  }

  /**
   * Convert AI-generated structure to database categories
   */
  private async saveAIStructureAsCategories(
    aiStructure: any[],
    userId: string,
    parentId?: string,
    parentPath: string = ''
  ): Promise<Category[]> {
    const savedCategories: Category[] = [];

    for (const node of aiStructure) {
      const path = parentPath ? `${parentPath}/${node.name}` : `/${node.name}`;
      
      // Save category to database
      const [newCategory] = await db.insert(categories).values({
        name: node.name,
        parentId,
        level: node.level,
        path,
        glCode: node.glCode,
        description: node.description,
        isSystem: true, // AI-generated categories are system categories
        isActive: true
      }).returning();

      const category: Category = {
        id: newCategory.id,
        name: newCategory.name,
        parentId: newCategory.parentId || undefined,
        level: newCategory.level,
        path: newCategory.path,
        glCode: newCategory.glCode || undefined,
        description: newCategory.description || undefined,
        isSystem: newCategory.isSystem,
        createdAt: newCategory.createdAt,
        updatedAt: newCategory.updatedAt
      };

      savedCategories.push(category);

      // Recursively save children
      if (node.children && node.children.length > 0) {
        const childCategories = await this.saveAIStructureAsCategories(
          node.children,
          userId,
          newCategory.id,
          path
        );
        savedCategories.push(...childCategories);
      }
    }

    return savedCategories;
  }

  /**
   * Create a new category in database
   */
  async createCategory(categoryData: {
    name: string;
    parentId?: string;
    level: number;
    path: string;
    glCode?: string;
    description?: string;
    isSystem?: boolean;
  }): Promise<Category> {
    const [newCategory] = await db.insert(categories).values({
      ...categoryData,
      isSystem: categoryData.isSystem || false,
      isActive: true
    }).returning();

    return {
      id: newCategory.id,
      name: newCategory.name,
      parentId: newCategory.parentId || undefined,
      level: newCategory.level,
      path: newCategory.path,
      glCode: newCategory.glCode || undefined,
      description: newCategory.description || undefined,
      isSystem: newCategory.isSystem,
      createdAt: newCategory.createdAt,
      updatedAt: newCategory.updatedAt
    };
  }

  /**
   * Update category in database
   */
  async updateCategory(
    categoryId: string,
    updates: Partial<{
      name: string;
      description: string;
      glCode: string;
    }>
  ): Promise<Category | null> {
    const [updated] = await db.update(categories)
      .set({
        ...updates,
        updatedAt: new Date()
      })
      .where(eq(categories.id, categoryId))
      .returning();

    if (!updated) {
      return null;
    }

    return {
      id: updated.id,
      name: updated.name,
      parentId: updated.parentId || undefined,
      level: updated.level,
      path: updated.path,
      glCode: updated.glCode || undefined,
      description: updated.description || undefined,
      isSystem: updated.isSystem,
      createdAt: updated.createdAt,
      updatedAt: updated.updatedAt
    };
  }

  /**
   * Delete category from database (only non-system categories)
   */
  async deleteCategory(categoryId: string): Promise<boolean> {
    // First check if it's a system category
    const category = await db.select().from(categories)
      .where(eq(categories.id, categoryId))
      .limit(1);

    if (!category.length) {
      throw new Error('Category not found');
    }

    if (category[0].isSystem) {
      throw new Error('Cannot delete system categories');
    }

    // Soft delete by setting isActive to false
    await db.update(categories)
      .set({ 
        isActive: false,
        updatedAt: new Date()
      })
      .where(eq(categories.id, categoryId));

    return true;
  }

  /**
   * Get category drill-down data with real transaction counts
   */
  async getCategoryDrillDown(categoryId: string): Promise<{
    category: Category;
    subcategories: Category[];
    transactionCount: number;
    totalAmount: number;
  } | null> {
    // Get the main category
    const [category] = await db.select().from(categories)
      .where(eq(categories.id, categoryId))
      .limit(1);

    if (!category) {
      return null;
    }

    // Get subcategories
    const subcategories = await db.select().from(categories)
      .where(and(
        eq(categories.parentId, categoryId),
        eq(categories.isActive, true)
      ));

    // TODO: Get real transaction counts and amounts from transactions table
    // For now, we'll return basic data without the mock numbers
    const transactionCount = 0;
    const totalAmount = 0;

    return {
      category: {
        id: category.id,
        name: category.name,
        parentId: category.parentId || undefined,
        level: category.level,
        path: category.path,
        glCode: category.glCode || undefined,
        description: category.description || undefined,
        isSystem: category.isSystem,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt
      },
      subcategories: subcategories.map(cat => ({
        id: cat.id,
        name: cat.name,
        parentId: cat.parentId || undefined,
        level: cat.level,
        path: cat.path,
        glCode: cat.glCode || undefined,
        description: cat.description || undefined,
        isSystem: cat.isSystem,
        createdAt: cat.createdAt,
        updatedAt: cat.updatedAt
      })),
      transactionCount,
      totalAmount
    };
  }
}

export function getCategoriesService(): CategoriesService {
  return CategoriesService.getInstance();
}