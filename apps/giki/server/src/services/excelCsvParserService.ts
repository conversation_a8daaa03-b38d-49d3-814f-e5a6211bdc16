import * as XLSX from 'xlsx';
import { promises as fs } from 'fs';
import type { Transaction, TransactionType } from '@shared/types/index';

interface ParsedTransaction {
  date: Date;
  description: string;
  amount: number;
  type: TransactionType;
  rawData: Record<string, any>;
}

interface ParseResult {
  success: boolean;
  transactions?: ParsedTransaction[];
  totalCount?: number;
  error?: string;
  metadata?: {
    fileName: string;
    fileType: string;
    sheetsProcessed: number;
    columnsDetected: string[];
  };
}

interface ColumnMapping {
  dateColumn: string;
  descriptionColumn: string;
  amountColumn: string;
  typeColumn?: string;
}

export class ExcelCsvParserService {
  private readonly SUPPORTED_DATE_FORMATS = [
    'DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD', 
    'DD-MM-YYYY', 'MM-DD-YYYY', 'DD.MM.YYYY'
  ];

  private readonly DEBIT_KEYWORDS = [
    'debit', 'withdrawal', 'payment', 'purchase', 'deduction', 'charge'
  ];

  private readonly CREDIT_KEYWORDS = [
    'credit', 'deposit', 'salary', 'refund', 'income', 'transfer in'
  ];

  /**
   * Parse Excel or CSV file from file path
   */
  async parseFile(filePath: string, fileName: string): Promise<ParseResult> {
    try {
      // Read file
      const fileBuffer = await fs.readFile(filePath);
      const workbook = XLSX.read(fileBuffer, { 
        type: 'buffer',
        cellDates: true,
        cellText: false,
        raw: false
      });

      // Get file type
      const fileType = this.getFileType(fileName);
      
      // Parse sheets
      const allTransactions: ParsedTransaction[] = [];
      const sheetsProcessed = workbook.SheetNames.length;
      let columnsDetected: string[] = [];

      for (const sheetName of workbook.SheetNames) {
        const worksheet = workbook.Sheets[sheetName];
        const sheetResult = await this.parseWorksheet(worksheet, sheetName);
        
        if (sheetResult.success && sheetResult.transactions) {
          allTransactions.push(...sheetResult.transactions);
          if (sheetResult.metadata?.columnsDetected) {
            columnsDetected = [...new Set([...columnsDetected, ...sheetResult.metadata.columnsDetected])];
          }
        }
      }

      return {
        success: true,
        transactions: allTransactions,
        totalCount: allTransactions.length,
        metadata: {
          fileName,
          fileType,
          sheetsProcessed,
          columnsDetected
        }
      };

    } catch (error: any) {
      return {
        success: false,
        error: `Failed to parse file: ${error.message}`
      };
    }
  }

  /**
   * Parse a single worksheet
   */
  private async parseWorksheet(worksheet: XLSX.WorkSheet, sheetName: string): Promise<ParseResult> {
    try {
      // Convert sheet to JSON with header row
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1,
        raw: false,
        dateNF: 'yyyy-mm-dd'
      });

      if (!jsonData || jsonData.length < 2) {
        return {
          success: false,
          error: `Sheet ${sheetName} has insufficient data`
        };
      }

      // Get headers (first row)
      const headers = jsonData[0] as string[];
      const dataRows = jsonData.slice(1);

      // Detect column mapping
      const columnMapping = this.detectColumnMapping(headers);
      
      if (!columnMapping) {
        return {
          success: false,
          error: `Could not detect required columns (Date, Description, Amount) in sheet ${sheetName}`
        };
      }

      // Parse transactions
      const transactions: ParsedTransaction[] = [];
      
      for (let i = 0; i < dataRows.length; i++) {
        const row = dataRows[i] as any[];
        
        if (!row || row.length === 0) continue;

        try {
          const transaction = this.parseTransactionRow(row, headers, columnMapping, i + 2);
          if (transaction) {
            transactions.push(transaction);
          }
        } catch (error) {
          console.warn(`Skipping row ${i + 2} in sheet ${sheetName}:`, error);
        }
      }

      return {
        success: true,
        transactions,
        totalCount: transactions.length,
        metadata: {
          fileName: sheetName,
          fileType: 'worksheet',
          sheetsProcessed: 1,
          columnsDetected: headers
        }
      };

    } catch (error: any) {
      return {
        success: false,
        error: `Failed to parse worksheet ${sheetName}: ${error.message}`
      };
    }
  }

  /**
   * Detect column mapping from headers
   */
  private detectColumnMapping(headers: string[]): ColumnMapping | null {
    const normalizedHeaders = headers.map(h => (h || '').toString().toLowerCase().trim());
    
    // Find date column
    const dateColumn = this.findColumn(normalizedHeaders, [
      'date', 'transaction date', 'trans date', 'posting date', 'value date'
    ]);

    // Find description column  
    const descriptionColumn = this.findColumn(normalizedHeaders, [
      'description', 'transaction description', 'details', 'narration', 'memo', 'reference'
    ]);

    // Find amount column
    const amountColumn = this.findColumn(normalizedHeaders, [
      'amount', 'transaction amount', 'value', 'sum', 'total', 'debit', 'credit'
    ]);

    // Find type column (optional)
    const typeColumn = this.findColumn(normalizedHeaders, [
      'type', 'transaction type', 'dr/cr', 'debit credit', 'entry type'
    ]);

    if (!dateColumn || !descriptionColumn || !amountColumn) {
      return null;
    }

    return {
      dateColumn: headers[normalizedHeaders.indexOf(dateColumn)],
      descriptionColumn: headers[normalizedHeaders.indexOf(descriptionColumn)],
      amountColumn: headers[normalizedHeaders.indexOf(amountColumn)],
      typeColumn: typeColumn ? headers[normalizedHeaders.indexOf(typeColumn)] : undefined
    };
  }

  /**
   * Find column by keywords
   */
  private findColumn(normalizedHeaders: string[], keywords: string[]): string | null {
    for (const keyword of keywords) {
      const found = normalizedHeaders.find(header => header.includes(keyword));
      if (found) return found;
    }
    return null;
  }

  /**
   * Parse a single transaction row
   */
  private parseTransactionRow(
    row: any[], 
    headers: string[], 
    mapping: ColumnMapping,
    rowNumber: number
  ): ParsedTransaction | null {
    const dateIndex = headers.indexOf(mapping.dateColumn);
    const descriptionIndex = headers.indexOf(mapping.descriptionColumn);
    const amountIndex = headers.indexOf(mapping.amountColumn);
    const typeIndex = mapping.typeColumn ? headers.indexOf(mapping.typeColumn) : -1;

    // Extract raw values
    const rawDate = row[dateIndex];
    const rawDescription = row[descriptionIndex];
    const rawAmount = row[amountIndex];
    const rawType = typeIndex >= 0 ? row[typeIndex] : null;

    // Validate required fields
    if (!rawDate || !rawDescription || (rawAmount === undefined || rawAmount === null || rawAmount === '')) {
      return null;
    }

    // Parse date
    const date = this.parseDate(rawDate);
    if (!date) {
      throw new Error(`Invalid date format: ${rawDate}`);
    }

    // Parse description
    const description = this.parseDescription(rawDescription);

    // Parse amount
    const amount = this.parseAmount(rawAmount);
    if (amount === null) {
      throw new Error(`Invalid amount format: ${rawAmount}`);
    }

    // Determine transaction type
    const type = this.determineTransactionType(rawType, description, amount);

    // Create raw data object
    const rawData: Record<string, any> = {};
    headers.forEach((header, index) => {
      if (row[index] !== undefined) {
        rawData[header] = row[index];
      }
    });

    return {
      date,
      description,
      amount: Math.abs(amount), // Store as positive number
      type,
      rawData
    };
  }

  /**
   * Parse date from various formats
   */
  private parseDate(rawDate: any): Date | null {
    if (!rawDate) return null;

    // If it's already a Date object
    if (rawDate instanceof Date) {
      return new Date(rawDate);
    }

    // If it's a number (Excel date serial)
    if (typeof rawDate === 'number') {
      return new Date((rawDate - 25569) * 86400 * 1000);
    }

    // If it's a string
    const dateStr = rawDate.toString().trim();
    
    // Try parsing directly
    const directParse = new Date(dateStr);
    if (!isNaN(directParse.getTime())) {
      return directParse;
    }

    // Try common formats
    const formats = [
      /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/, // DD/MM/YYYY or MM/DD/YYYY
      /^(\d{4})-(\d{1,2})-(\d{1,2})$/, // YYYY-MM-DD
      /^(\d{1,2})-(\d{1,2})-(\d{4})$/, // DD-MM-YYYY or MM-DD-YYYY
      /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/ // DD.MM.YYYY
    ];

    for (const format of formats) {
      const match = dateStr.match(format);
      if (match) {
        const [, p1, p2, p3] = match;
        // Try different interpretations
        const attempts = [
          new Date(parseInt(p3), parseInt(p2) - 1, parseInt(p1)), // DD/MM/YYYY
          new Date(parseInt(p3), parseInt(p1) - 1, parseInt(p2)), // MM/DD/YYYY
          new Date(parseInt(p1), parseInt(p2) - 1, parseInt(p3))  // YYYY-MM-DD
        ];
        
        for (const attempt of attempts) {
          if (!isNaN(attempt.getTime())) {
            return attempt;
          }
        }
      }
    }

    return null;
  }

  /**
   * Parse description
   */
  private parseDescription(rawDescription: any): string {
    if (!rawDescription) return 'No description';
    return rawDescription.toString().trim().substring(0, 500); // Limit length
  }

  /**
   * Parse amount from various formats
   */
  private parseAmount(rawAmount: any): number | null {
    if (rawAmount === null || rawAmount === undefined) return null;

    // If it's already a number
    if (typeof rawAmount === 'number') {
      return rawAmount;
    }

    // If it's a string, clean it up
    let amountStr = rawAmount.toString().trim();
    
    // Remove common currency symbols and formatting
    amountStr = amountStr.replace(/[₹$€£¥,\s]/g, '');
    
    // Handle parentheses (negative amounts)
    const isNegative = amountStr.includes('(') && amountStr.includes(')');
    amountStr = amountStr.replace(/[()]/g, '');
    
    // Parse the number
    const parsed = parseFloat(amountStr);
    
    if (isNaN(parsed)) return null;
    
    return isNegative ? -Math.abs(parsed) : parsed;
  }

  /**
   * Determine transaction type
   */
  private determineTransactionType(rawType: any, description: string, amount: number): TransactionType {
    // Check explicit type column first
    if (rawType) {
      const typeStr = rawType.toString().toLowerCase().trim();
      if (this.DEBIT_KEYWORDS.some(keyword => typeStr.includes(keyword)) || typeStr === 'dr') {
        return 'debit';
      }
      if (this.CREDIT_KEYWORDS.some(keyword => typeStr.includes(keyword)) || typeStr === 'cr') {
        return 'credit';
      }
    }

    // Use amount sign
    if (amount < 0) {
      return 'debit';
    }

    // Use description keywords
    const descLower = description.toLowerCase();
    if (this.DEBIT_KEYWORDS.some(keyword => descLower.includes(keyword))) {
      return 'debit';
    }
    if (this.CREDIT_KEYWORDS.some(keyword => descLower.includes(keyword))) {
      return 'credit';
    }

    // Default to debit for positive amounts (common in bank statements)
    return 'debit';
  }

  /**
   * Get file type from extension
   */
  private getFileType(fileName: string): string {
    const extension = fileName.toLowerCase().split('.').pop();
    switch (extension) {
      case 'xlsx': return 'Excel 2007+';
      case 'xls': return 'Excel 97-2003';
      case 'csv': return 'CSV';
      case 'ods': return 'OpenDocument Spreadsheet';
      default: return 'Unknown';
    }
  }

  /**
   * Convert parsed transactions to Transaction entities
   */
  convertToTransactions(
    parsedTransactions: ParsedTransaction[], 
    userId: string, 
    fileId: string
  ): Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>[] {
    return parsedTransactions.map(parsed => ({
      userId,
      fileId,
      date: parsed.date,
      description: parsed.description,
      amount: parsed.amount,
      type: parsed.type,
      isReviewed: false,
      // Optional fields will be filled later by categorization service
      categoryId: undefined,
      confidence: undefined
    }));
  }
}

// Singleton instance
let excelCsvParserService: ExcelCsvParserService | null = null;

export function getExcelCsvParserService(): ExcelCsvParserService {
  if (!excelCsvParserService) {
    excelCsvParserService = new ExcelCsvParserService();
  }
  return excelCsvParserService;
}