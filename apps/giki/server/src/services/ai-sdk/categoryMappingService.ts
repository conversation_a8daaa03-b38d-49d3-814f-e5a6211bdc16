/**
 * Category Mapping Service using Vercel AI SDK
 * 
 * Maps transactions to categories using streaming for real-time updates.
 * Uses streamObject for responsive UI feedback during categorization.
 * 
 * Model: gemini-2.0-flash-001
 */

import { google } from '@ai-sdk/google';
import { generateObject, streamObject } from 'ai';
import { z } from 'zod';

// Load environment variables
import '../../utils/env-loader';
import { hierarchyService, type CategoryHierarchy } from '../hierarchy/hierarchyService';

// Define categorization schema with full hierarchical context
const categorizationSchema = z.object({
  categoryId: z.string().describe('The exact ID from available categories'),
  categoryName: z.string().describe('The exact name from available categories'),
  categoryPath: z.array(z.string()).describe('Full path from root to leaf: ["Expenses", "Operating Expenses", "Food & Dining"]'),
  fullPath: z.string().describe('Human-readable path: "Expenses → Operating Expenses → Food & Dining"'),
  level: z.number().describe('Hierarchy level (0=root, 1=sub, 2=leaf)'),
  parentCategories: z.array(z.object({
    id: z.string(),
    name: z.string(),
    level: z.number()
  })).describe('All parent categories for rollup calculations'),
  confidence: z.number().min(0).max(1).describe('Confidence score from 0.0 to 1.0'),
  reasoning: z.string().describe('Brief explanation for the categorization choice'),
  alternativeCategories: z.array(z.object({
    categoryId: z.string(),
    categoryName: z.string(),
    categoryPath: z.array(z.string()),
    confidence: z.number()
  })).optional().describe('Alternative category suggestions with lower confidence')
});

// Batch categorization schema with hierarchical context
const batchCategorizationSchema = z.object({
  categorizations: z.array(z.object({
    transactionId: z.string(),
    categoryId: z.string(),
    categoryName: z.string(),
    categoryPath: z.array(z.string()).describe('Full hierarchical path'),
    fullPath: z.string().describe('Human-readable path with arrows'),
    level: z.number(),
    confidence: z.number().min(0).max(1),
    reasoning: z.string()
  })),
  overallAccuracy: z.number().min(0).max(1).describe('Overall accuracy estimate for the batch'),
  processingNotes: z.string().optional().describe('Any notes about the batch processing')
});

export type CategorizationResult = z.infer<typeof categorizationSchema>;
export type BatchCategorizationResult = z.infer<typeof batchCategorizationSchema>;

export interface TransactionInput {
  id: string;
  description: string;
  amount: number;
  type: 'debit' | 'credit';
  vendor?: string;
  date?: string;
}

export interface Category {
  id: string;
  name: string;
  glCode?: string;
  description?: string;
  parentId?: string;
  // Hierarchy context for AI
  hierarchyPath?: string; // "Expenses → Operating Expenses → Food & Dining"
  level?: number; // 0, 1, 2 etc.
}

export interface BusinessContext {
  industry: string;
  businessType: string;
  size: 'small' | 'medium' | 'large';
}

export class CategoryMappingService {
  private model;

  constructor(apiKey?: string) {
    const key = apiKey || process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENERATIVE_AI_API_KEY;
    if (!key) {
      console.warn('[CategoryMappingService] No API key found, service will use fallbacks');
    }
    this.model = google('gemini-2.0-flash-001', {
      apiKey: key,
      structuredOutputs: true
    });
  }

  /**
   * Categorize transaction using predefined hierarchy (HIERARCHY-FIRST APPROACH)
   */
  async categorizeTransactionWithHierarchy(
    transaction: TransactionInput,
    businessContext: BusinessContext,
    userId?: string
  ): Promise<CategorizationResult> {
    console.log(`[CategoryMappingService] Categorizing with hierarchy-first approach`);

    // Get appropriate hierarchy for this business
    const hierarchy = await hierarchyService.getHierarchyForBusiness({
      industry: businessContext.industry,
      businessType: businessContext.businessType,
      userId: userId || 'anonymous'
    });

    console.log(`[CategoryMappingService] Using ${hierarchy.source} hierarchy: "${hierarchy.name}" with ${hierarchy.metadata.totalCategories} categories`);

    // Flatten hierarchy for AI to choose from
    const flatCategories = this.flattenHierarchy(hierarchy.categories);
    
    // Use existing categorization logic with hierarchy categories
    return this.categorizeTransaction(transaction, flatCategories, businessContext);
  }

  /**
   * Flatten hierarchical categories into a list for AI processing
   */
  private flattenHierarchy(categories: any[], path: string[] = []): Category[] {
    const flat: Category[] = [];
    
    for (const category of categories) {
      const currentPath = [...path, category.name];
      
      // Add this category
      flat.push({
        id: category.id,
        name: category.name,
        glCode: category.glCode,
        description: category.description,
        parentId: category.parentId,
        // Add hierarchy context for AI
        hierarchyPath: currentPath.join(' → '),
        level: category.level
      });
      
      // Add child categories recursively
      if (category.children?.length) {
        flat.push(...this.flattenHierarchy(category.children, currentPath));
      }
    }
    
    return flat;
  }

  /**
   * Categorize a single transaction
   */
  async categorizeTransaction(
    transaction: TransactionInput,
    categories: Category[],
    businessContext?: BusinessContext
  ): Promise<CategorizationResult> {
    const categoryList = categories.map(cat => 
      `- ${cat.name} (ID: ${cat.id}${cat.glCode ? `, GL: ${cat.glCode}` : ''}${cat.description ? `, ${cat.description}` : ''})`
    ).join('\n');

    const prompt = `Categorize this transaction with FULL HIERARCHICAL CONTEXT for a ${businessContext?.businessType || 'business'} in the ${businessContext?.industry || 'general'} industry:

Transaction Details:
- Description: "${transaction.description}"
- Amount: ${transaction.amount} (${transaction.type})
${transaction.vendor ? `- Vendor: ${transaction.vendor}` : ''}
${transaction.date ? `- Date: ${transaction.date}` : ''}

Available Categories:
${categoryList}

CRITICAL: Provide complete hierarchical context:
1. Select the MOST SPECIFIC applicable category (leaf node)
2. Provide FULL PATH from root to leaf
3. Example for a Starbucks transaction:
   - categoryPath: ["Expenses", "Operating Expenses", "Food & Dining"]
   - fullPath: "Expenses → Operating Expenses → Food & Dining"
   - level: 2 (counting from 0)
   - parentCategories: All parent categories in the hierarchy

This gives users complete context of where their transaction fits in their financial structure.

Select the most appropriate category based on:
1. Transaction description and vendor
2. Amount and transaction type
3. Business context and industry norms
4. Standard accounting practices

Provide alternative categories if multiple could apply.`;

    try {
      const { object, usage } = await generateObject({
        model: this.model,
        schema: categorizationSchema,
        prompt,
        temperature: 0.3, // Balanced for accuracy with some flexibility
        maxOutputTokens: 500,
      });

      console.log(`[CategoryMappingService] Categorized transaction "${transaction.description.substring(0, 40)}..." - Confidence: ${(object.confidence * 100).toFixed(1)}% - Tokens: ${usage.totalTokens}`);

      return object;
    } catch (error: any) {
      console.error('[CategoryMappingService] Categorization failed:', error);
      
      // Return first category as fallback
      return {
        categoryId: categories[0].id,
        categoryName: categories[0].name,
        confidence: 0.3,
        reasoning: 'Fallback categorization due to processing error',
        alternativeCategories: []
      };
    }
  }

  /**
   * Stream categorization for multiple transactions with real-time updates
   */
  async *streamCategorizeBatch(
    transactions: TransactionInput[],
    categories: Category[],
    businessContext?: BusinessContext
  ): AsyncGenerator<Partial<BatchCategorizationResult>, BatchCategorizationResult> {
    const categoryList = categories.map(cat => 
      `${cat.name} (${cat.id})`
    ).join(', ');

    const transactionList = transactions.map(txn => 
      `ID: ${txn.id}, Desc: "${txn.description}", Amount: ${txn.amount}, Type: ${txn.type}${txn.vendor ? `, Vendor: ${txn.vendor}` : ''}`
    ).join('\n');

    const prompt = `Categorize these transactions for a ${businessContext?.businessType || 'business'} in the ${businessContext?.industry || 'general'} industry:

Transactions:
${transactionList}

Available Categories: ${categoryList}

For each transaction, provide the most appropriate category with confidence score and reasoning.
Estimate overall accuracy for the batch.`;

    try {
      const { partialObjectStream, object } = await streamObject({
        model: this.model,
        schema: batchCategorizationSchema,
        prompt,
        temperature: 0.3,
        maxOutputTokens: 2000,
      });

      // Yield partial results as they stream in
      for await (const partialObject of partialObjectStream) {
        yield partialObject;
      }

      // Return final complete object
      const finalResult = await object;
      console.log(`[CategoryMappingService] Batch categorization complete - ${transactions.length} transactions - Overall accuracy: ${(finalResult.overallAccuracy * 100).toFixed(1)}%`);
      
      return finalResult;
    } catch (error: any) {
      console.error('[CategoryMappingService] Batch categorization failed:', error);
      
      // Return empty result
      return {
        categorizations: [],
        overallAccuracy: 0,
        processingNotes: 'Batch processing failed'
      };
    }
  }

  /**
   * Improve categorization with feedback
   */
  async improveCategorizationWithFeedback(
    transaction: TransactionInput,
    previousCategorization: CategorizationResult,
    correctCategoryId: string,
    categories: Category[],
    feedback?: string
  ): Promise<CategorizationResult> {
    const correctCategory = categories.find(c => c.id === correctCategoryId);
    if (!correctCategory) {
      throw new Error('Correct category not found');
    }

    const learningPrompt = `Learn from this categorization correction:

Transaction: "${transaction.description}" (Amount: ${transaction.amount})
Previous categorization: ${previousCategorization.categoryName} (Confidence: ${previousCategorization.confidence})
Previous reasoning: ${previousCategorization.reasoning}

CORRECTION: Should be categorized as "${correctCategory.name}" (ID: ${correctCategory.id})
${feedback ? `User feedback: ${feedback}` : ''}

Based on this correction, provide an improved categorization with better reasoning that explains why "${correctCategory.name}" is correct.`;

    try {
      const { object } = await generateObject({
        model: this.model,
        schema: categorizationSchema,
        prompt: learningPrompt,
        temperature: 0.2,
        maxOutputTokens: 500,
      });

      console.log(`[CategoryMappingService] Learned from feedback - New confidence: ${(object.confidence * 100).toFixed(1)}%`);

      return object;
    } catch (error) {
      console.error('[CategoryMappingService] Learning from feedback failed:', error);
      
      // Return the correct category with updated reasoning
      return {
        categoryId: correctCategory.id,
        categoryName: correctCategory.name,
        confidence: 0.9, // High confidence since it's user-corrected
        reasoning: `User-corrected categorization${feedback ? `: ${feedback}` : ''}`,
        alternativeCategories: []
      };
    }
  }

  /**
   * Analyze categorization patterns for insights
   */
  async analyzeCategorizationPatterns(
    recentCategorizations: Array<{
      transaction: TransactionInput;
      result: CategorizationResult;
    }>,
    categories: Category[]
  ) {
    const analysisSchema = z.object({
      patterns: z.array(z.object({
        pattern: z.string(),
        frequency: z.number(),
        exampleTransactions: z.array(z.string()),
        suggestedRule: z.string()
      })),
      lowConfidenceCategories: z.array(z.object({
        categoryName: z.string(),
        averageConfidence: z.number(),
        commonIssues: z.array(z.string())
      })),
      recommendations: z.array(z.string())
    });

    const categorizationSummary = recentCategorizations.slice(0, 20).map(item => 
      `"${item.transaction.description}" → ${item.result.categoryName} (${(item.result.confidence * 100).toFixed(0)}%)`
    ).join('\n');

    try {
      const { object } = await generateObject({
        model: this.model,
        schema: analysisSchema,
        prompt: `Analyze these recent categorization patterns:

${categorizationSummary}

Identify:
1. Common patterns that could be automated
2. Categories with consistently low confidence
3. Recommendations for improving accuracy`,
        temperature: 0.5,
        maxOutputTokens: 1500,
      });

      return object;
    } catch (error) {
      console.error('[CategoryMappingService] Pattern analysis failed:', error);
      return null;
    }
  }
}

// Export singleton instance
export const categoryMappingService = new CategoryMappingService();