/**
 * Category Management Service using Vercel AI SDK
 * 
 * Manages multi-level category hierarchies, custom rules,
 * and intelligent category operations with AI assistance.
 * 
 * Model: gemini-2.0-flash-001
 */

import { google } from '@ai-sdk/google';
import { generateObject, streamObject } from 'ai';
import { z } from 'zod';

// Load environment variables
import '../../utils/env-loader';

// Category hierarchy schema
const categoryNodeSchema: z.ZodType<any> = z.lazy(() => z.object({
  id: z.string(),
  name: z.string(),
  glCode: z.string().optional(),
  description: z.string().optional(),
  level: z.number(),
  parentId: z.string().nullable(),
  path: z.string().describe('Full path like "Expenses/Operating/Office Supplies"'),
  children: z.array(categoryNodeSchema).optional(),
  rules: z.array(z.object({
    pattern: z.string().describe('Regex or keyword pattern'),
    confidence: z.number().describe('Confidence boost when pattern matches')
  })).optional(),
  metadata: z.object({
    usage: z.number().default(0),
    accuracy: z.number().default(0),
    lastUsed: z.string().nullable(),
    created: z.string(),
    modified: z.string()
  }).optional()
}));

// Category operation schemas
const categoryOperationSchema = z.object({
  operation: z.enum(['add', 'update', 'delete', 'move', 'merge', 'split']),
  targetId: z.string().optional(),
  parentId: z.string().optional(),
  newData: z.object({
    name: z.string().optional(),
    glCode: z.string().optional(),
    description: z.string().optional(),
    rules: z.array(z.object({
      pattern: z.string(),
      confidence: z.number()
    })).optional()
  }).optional(),
  reason: z.string()
});

// Category suggestion schema
const categorySuggestionSchema = z.object({
  suggestedCategories: z.array(z.object({
    name: z.string(),
    parentPath: z.string().describe('Parent category path'),
    glCode: z.string().optional(),
    description: z.string(),
    reason: z.string().describe('Why this category is needed'),
    estimatedUsage: z.enum(['high', 'medium', 'low'])
  })),
  ruleSuggestions: z.array(z.object({
    categoryId: z.string(),
    pattern: z.string(),
    examples: z.array(z.string()),
    confidence: z.number()
  }))
});

export type CategoryNode = z.infer<typeof categoryNodeSchema>;
export type CategoryOperation = z.infer<typeof categoryOperationSchema>;
export type CategorySuggestion = z.infer<typeof categorySuggestionSchema>;

export interface CategoryTree {
  roots: CategoryNode[];
  totalCategories: number;
  maxDepth: number;
  flatMap: Map<string, CategoryNode>;
}

export interface CategoryAnalytics {
  mostUsed: Array<{ category: CategoryNode; count: number }>;
  leastUsed: Array<{ category: CategoryNode; count: number }>;
  lowAccuracy: Array<{ category: CategoryNode; accuracy: number }>;
  orphaned: CategoryNode[];
  duplicates: Array<{ categories: CategoryNode[]; similarity: number }>;
}

export class CategoryManagementService {
  private model;
  private categoryTree: CategoryTree | null = null;

  constructor(apiKey?: string) {
    const key = apiKey || process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENERATIVE_AI_API_KEY;
    if (!key) {
      console.warn('[CategoryManagementService] No API key found, service will use fallbacks');
    }
    this.model = google('gemini-2.0-flash-001', {
      apiKey: key,
      structuredOutputs: true
    });
  }

  /**
   * Build a multi-level category tree from flat list
   */
  buildCategoryTree(flatCategories: any[]): CategoryTree {
    const flatMap = new Map<string, CategoryNode>();
    const roots: CategoryNode[] = [];
    let maxDepth = 0;

    // First pass: Create all nodes
    flatCategories.forEach(cat => {
      const node: CategoryNode = {
        id: cat.id,
        name: cat.name,
        glCode: cat.glCode,
        description: cat.description,
        level: cat.level || 0,
        parentId: cat.parentId,
        path: cat.path || cat.name,
        children: [],
        rules: cat.rules || [],
        metadata: cat.metadata || {
          usage: 0,
          accuracy: 0,
          lastUsed: null,
          created: new Date().toISOString(),
          modified: new Date().toISOString()
        }
      };
      flatMap.set(cat.id, node);
      maxDepth = Math.max(maxDepth, node.level);
    });

    // Second pass: Build hierarchy
    flatMap.forEach(node => {
      if (node.parentId && flatMap.has(node.parentId)) {
        const parent = flatMap.get(node.parentId)!;
        if (!parent.children) parent.children = [];
        parent.children.push(node);
        node.path = `${parent.path}/${node.name}`;
      } else if (!node.parentId) {
        roots.push(node);
      }
    });

    this.categoryTree = {
      roots,
      totalCategories: flatMap.size,
      maxDepth,
      flatMap
    };

    return this.categoryTree;
  }

  /**
   * Add a new category with AI-suggested placement
   */
  async addCategory(
    name: string,
    description: string,
    suggestedParent?: string,
    existingTree?: CategoryTree
  ): Promise<CategoryOperation> {
    const tree = existingTree || this.categoryTree;
    if (!tree) throw new Error('Category tree not initialized');

    const prompt = `Given this category hierarchy, where should I add a new category?

New Category: "${name}"
Description: "${description}"
${suggestedParent ? `Suggested Parent: "${suggestedParent}"` : ''}

Existing Structure (top 2 levels):
${this.formatTreeForPrompt(tree, 2)}

Determine:
1. The best parent category (or root if top-level)
2. Appropriate GL code based on parent and siblings
3. Whether this might duplicate an existing category`;

    try {
      const { object } = await generateObject({
        model: this.model,
        schema: categoryOperationSchema,
        prompt,
        temperature: 0.3,
        maxOutputTokens: 500
      });

      console.log(`[CategoryManagementService] Adding category "${name}" under ${object.parentId || 'root'}`);
      
      return object;
    } catch (error) {
      console.error('[CategoryManagementService] Add category failed:', error);
      
      return {
        operation: 'add',
        parentId: suggestedParent || null,
        newData: { name, description },
        reason: 'Manual placement due to AI error'
      };
    }
  }

  /**
   * Suggest new categories based on uncategorized transactions
   */
  async suggestNewCategories(
    uncategorizedTransactions: Array<{ description: string; amount: number }>,
    existingTree?: CategoryTree
  ): Promise<CategorySuggestion> {
    const tree = existingTree || this.categoryTree;
    if (!tree) throw new Error('Category tree not initialized');

    const transactionSample = uncategorizedTransactions.slice(0, 20)
      .map(t => `- "${t.description}" (${t.amount})`)
      .join('\n');

    const prompt = `Analyze these uncategorized transactions and suggest new categories:

Uncategorized Transactions:
${transactionSample}

Current Category Structure:
${this.formatTreeForPrompt(tree, 2)}

Suggest:
1. New categories that would help categorize these transactions
2. Where in the hierarchy each new category should go
3. Pattern rules to automatically categorize similar transactions

Focus on practical, commonly-needed categories that are missing.`;

    try {
      const { object, usage } = await generateObject({
        model: this.model,
        schema: categorySuggestionSchema,
        prompt,
        temperature: 0.5,
        maxOutputTokens: 1500
      });

      console.log(`[CategoryManagementService] Generated ${object.suggestedCategories.length} category suggestions - Tokens: ${usage.totalTokens}`);
      
      return object;
    } catch (error) {
      console.error('[CategoryManagementService] Suggestion generation failed:', error);
      
      return {
        suggestedCategories: [],
        ruleSuggestions: []
      };
    }
  }

  /**
   * Merge similar or duplicate categories
   */
  async mergeSimilarCategories(
    category1Id: string,
    category2Id: string,
    existingTree?: CategoryTree
  ): Promise<CategoryOperation> {
    const tree = existingTree || this.categoryTree;
    if (!tree) throw new Error('Category tree not initialized');

    const cat1 = tree.flatMap.get(category1Id);
    const cat2 = tree.flatMap.get(category2Id);
    
    if (!cat1 || !cat2) {
      throw new Error('One or both categories not found');
    }

    const mergeAnalysisSchema = z.object({
      shouldMerge: z.boolean(),
      keepCategory: z.string().describe('ID of category to keep'),
      mergedName: z.string(),
      mergedDescription: z.string(),
      reason: z.string()
    });

    try {
      const { object } = await generateObject({
        model: this.model,
        schema: mergeAnalysisSchema,
        prompt: `Should these categories be merged?

Category 1: "${cat1.name}" (${cat1.path})
- Description: ${cat1.description || 'None'}
- Usage: ${cat1.metadata?.usage || 0}
- Children: ${cat1.children?.length || 0}

Category 2: "${cat2.name}" (${cat2.path})
- Description: ${cat2.description || 'None'}
- Usage: ${cat2.metadata?.usage || 0}
- Children: ${cat2.children?.length || 0}

Consider usage patterns, hierarchy position, and semantic similarity.`,
        temperature: 0.3,
        maxOutputTokens: 500
      });

      if (object.shouldMerge) {
        return {
          operation: 'merge',
          targetId: object.keepCategory === category1Id ? category2Id : category1Id,
          parentId: object.keepCategory,
          newData: {
            name: object.mergedName,
            description: object.mergedDescription
          },
          reason: object.reason
        };
      } else {
        return {
          operation: 'update',
          targetId: category1Id,
          reason: `Not merged: ${object.reason}`
        };
      }
    } catch (error) {
      console.error('[CategoryManagementService] Merge analysis failed:', error);
      throw error;
    }
  }

  /**
   * Split a category into multiple subcategories
   */
  async splitCategory(
    categoryId: string,
    recentTransactions: Array<{ description: string; amount: number }>,
    existingTree?: CategoryTree
  ): Promise<CategorySuggestion> {
    const tree = existingTree || this.categoryTree;
    if (!tree) throw new Error('Category tree not initialized');

    const category = tree.flatMap.get(categoryId);
    if (!category) throw new Error('Category not found');

    const transactionSample = recentTransactions.slice(0, 30)
      .map(t => `"${t.description}" (${t.amount})`)
      .join('\n');

    const prompt = `This category has too many diverse transactions. Suggest how to split it:

Category: "${category.name}" (${category.path})
Current Description: ${category.description || 'None'}

Recent Transactions in this Category:
${transactionSample}

Suggest 2-5 subcategories that would better organize these transactions.
Each subcategory should be distinct and meaningful.`;

    try {
      const { object } = await generateObject({
        model: this.model,
        schema: categorySuggestionSchema,
        prompt,
        temperature: 0.5,
        maxOutputTokens: 1000
      });

      // Set parent path to current category
      object.suggestedCategories.forEach(cat => {
        cat.parentPath = category.path;
      });

      console.log(`[CategoryManagementService] Suggested ${object.suggestedCategories.length} subcategories for splitting`);
      
      return object;
    } catch (error) {
      console.error('[CategoryManagementService] Split suggestion failed:', error);
      
      return {
        suggestedCategories: [],
        ruleSuggestions: []
      };
    }
  }

  /**
   * Optimize category rules based on transaction patterns
   */
  async optimizeCategoryRules(
    categoryId: string,
    correctTransactions: Array<{ description: string }>,
    incorrectTransactions: Array<{ description: string }>,
    existingTree?: CategoryTree
  ): Promise<Array<{ pattern: string; confidence: number; explanation: string }>> {
    const tree = existingTree || this.categoryTree;
    if (!tree) throw new Error('Category tree not initialized');

    const category = tree.flatMap.get(categoryId);
    if (!category) throw new Error('Category not found');

    const ruleOptimizationSchema = z.object({
      rules: z.array(z.object({
        pattern: z.string().describe('Regex or keyword pattern'),
        confidence: z.number().min(0).max(1),
        explanation: z.string().describe('Why this rule works'),
        examples: z.array(z.string()).describe('Transaction descriptions this would match')
      })),
      removedRules: z.array(z.string()).optional()
    });

    const correctSample = correctTransactions.slice(0, 15)
      .map(t => `✓ ${t.description}`)
      .join('\n');
    const incorrectSample = incorrectTransactions.slice(0, 10)
      .map(t => `✗ ${t.description}`)
      .join('\n');

    try {
      const { object } = await generateObject({
        model: this.model,
        schema: ruleOptimizationSchema,
        prompt: `Optimize categorization rules for "${category.name}":

Current Rules: ${JSON.stringify(category.rules || [])}

Correctly Categorized (should match):
${correctSample}

Incorrectly Categorized (should NOT match):
${incorrectSample}

Create patterns that:
1. Match most correct transactions
2. Exclude incorrect ones
3. Are simple and maintainable
4. Have appropriate confidence scores`,
        temperature: 0.4,
        maxOutputTokens: 1000
      });

      console.log(`[CategoryManagementService] Optimized rules for "${category.name}" - ${object.rules.length} rules generated`);
      
      return object.rules.map(r => ({
        pattern: r.pattern,
        confidence: r.confidence,
        explanation: r.explanation
      }));
    } catch (error) {
      console.error('[CategoryManagementService] Rule optimization failed:', error);
      return [];
    }
  }

  /**
   * Analyze category usage and suggest improvements
   */
  async analyzeCategoryHealth(tree?: CategoryTree): Promise<CategoryAnalytics> {
    const categoryTree = tree || this.categoryTree;
    if (!categoryTree) throw new Error('Category tree not initialized');

    const allCategories = Array.from(categoryTree.flatMap.values());
    
    // Sort by usage
    const sortedByUsage = [...allCategories].sort((a, b) => 
      (b.metadata?.usage || 0) - (a.metadata?.usage || 0)
    );

    // Find low accuracy categories
    const lowAccuracy = allCategories
      .filter(cat => (cat.metadata?.accuracy || 0) < 0.7 && (cat.metadata?.usage || 0) > 10)
      .sort((a, b) => (a.metadata?.accuracy || 0) - (b.metadata?.accuracy || 0));

    // Find orphaned categories (no parent but not root)
    const orphaned = allCategories.filter(cat => 
      cat.parentId && !categoryTree.flatMap.has(cat.parentId) && cat.level > 0
    );

    // Find potential duplicates using AI
    const duplicates = await this.findDuplicateCategories(allCategories);

    return {
      mostUsed: sortedByUsage.slice(0, 10).map(cat => ({
        category: cat,
        count: cat.metadata?.usage || 0
      })),
      leastUsed: sortedByUsage.slice(-10).filter(cat => (cat.metadata?.usage || 0) === 0).map(cat => ({
        category: cat,
        count: 0
      })),
      lowAccuracy: lowAccuracy.slice(0, 10).map(cat => ({
        category: cat,
        accuracy: cat.metadata?.accuracy || 0
      })),
      orphaned,
      duplicates
    };
  }

  /**
   * Find duplicate or highly similar categories
   */
  private async findDuplicateCategories(
    categories: CategoryNode[]
  ): Promise<Array<{ categories: CategoryNode[]; similarity: number }>> {
    if (categories.length < 2) return [];

    const duplicateSchema = z.object({
      duplicates: z.array(z.object({
        categoryIds: z.array(z.string()),
        similarity: z.number().min(0).max(1),
        reason: z.string()
      }))
    });

    const categoryList = categories.slice(0, 50) // Limit for token efficiency
      .map(cat => `${cat.id}: "${cat.name}" (${cat.path})`)
      .join('\n');

    try {
      const { object } = await generateObject({
        model: this.model,
        schema: duplicateSchema,
        prompt: `Find duplicate or highly similar categories:

${categoryList}

Identify categories that:
1. Have very similar names
2. Serve the same purpose
3. Could be merged without loss of functionality

Only report similarities above 70%.`,
        temperature: 0.3,
        maxOutputTokens: 1000
      });

      return object.duplicates
        .filter(d => d.similarity > 0.7)
        .map(d => ({
          categories: d.categoryIds.map(id => categories.find(c => c.id === id)!).filter(Boolean),
          similarity: d.similarity
        }));
    } catch (error) {
      console.error('[CategoryManagementService] Duplicate detection failed:', error);
      return [];
    }
  }

  /**
   * Generate a visual representation of the category tree
   */
  generateTreeVisualization(tree?: CategoryTree, maxDepth: number = 3): string {
    const categoryTree = tree || this.categoryTree;
    if (!categoryTree) return 'Category tree not initialized';

    let output = '📊 Category Hierarchy\n';
    output += '=' .repeat(50) + '\n\n';

    const renderNode = (node: CategoryNode, indent: string = '', isLast: boolean = true) => {
      if (node.level > maxDepth) return '';
      
      const prefix = indent + (isLast ? '└── ' : '├── ');
      const usage = node.metadata?.usage || 0;
      const accuracy = node.metadata?.accuracy || 0;
      
      let line = `${prefix}${node.name}`;
      if (node.glCode) line += ` [${node.glCode}]`;
      if (usage > 0) line += ` (${usage} uses, ${(accuracy * 100).toFixed(0)}% accuracy)`;
      line += '\n';

      if (node.children && node.children.length > 0) {
        const childIndent = indent + (isLast ? '    ' : '│   ');
        node.children.forEach((child, idx) => {
          line += renderNode(child, childIndent, idx === node.children!.length - 1);
        });
      }

      return line;
    };

    categoryTree.roots.forEach((root, idx) => {
      output += renderNode(root, '', idx === categoryTree.roots.length - 1);
    });

    output += '\n' + '=' .repeat(50) + '\n';
    output += `Total Categories: ${categoryTree.totalCategories}\n`;
    output += `Maximum Depth: ${categoryTree.maxDepth}\n`;

    return output;
  }

  /**
   * Export category tree to various formats
   */
  exportCategories(format: 'json' | 'csv' | 'yaml', tree?: CategoryTree): string {
    const categoryTree = tree || this.categoryTree;
    if (!categoryTree) throw new Error('Category tree not initialized');

    const flatList = Array.from(categoryTree.flatMap.values());

    switch (format) {
      case 'json':
        return JSON.stringify(categoryTree.roots, null, 2);
      
      case 'csv':
        let csv = 'ID,Name,Parent ID,Path,GL Code,Level,Usage,Accuracy\n';
        flatList.forEach(cat => {
          csv += `"${cat.id}","${cat.name}","${cat.parentId || ''}","${cat.path}","${cat.glCode || ''}",${cat.level},${cat.metadata?.usage || 0},${cat.metadata?.accuracy || 0}\n`;
        });
        return csv;
      
      case 'yaml':
        return this.toYAML(categoryTree.roots);
      
      default:
        return JSON.stringify(categoryTree.roots);
    }
  }

  private formatTreeForPrompt(tree: CategoryTree, maxDepth: number): string {
    let output = '';
    const renderNode = (node: CategoryNode, indent: number = 0) => {
      if (indent / 2 > maxDepth) return;
      output += '  '.repeat(indent) + `- ${node.name}`;
      if (node.glCode) output += ` (${node.glCode})`;
      output += '\n';
      if (node.children) {
        node.children.forEach(child => renderNode(child, indent + 1));
      }
    };
    tree.roots.forEach(root => renderNode(root));
    return output;
  }

  private toYAML(obj: any, indent: number = 0): string {
    // Simple YAML serialization
    let yaml = '';
    const spaces = '  '.repeat(indent);
    
    if (Array.isArray(obj)) {
      obj.forEach(item => {
        yaml += `${spaces}- `;
        if (typeof item === 'object') {
          yaml += '\n' + this.toYAML(item, indent + 1);
        } else {
          yaml += `${item}\n`;
        }
      });
    } else if (typeof obj === 'object' && obj !== null) {
      Object.entries(obj).forEach(([key, value]) => {
        if (value === null || value === undefined) return;
        yaml += `${spaces}${key}: `;
        if (typeof value === 'object') {
          yaml += '\n' + this.toYAML(value, indent + 1);
        } else {
          yaml += `${value}\n`;
        }
      });
    }
    
    return yaml;
  }
}

// Export singleton instance
export const categoryManagementService = new CategoryManagementService();