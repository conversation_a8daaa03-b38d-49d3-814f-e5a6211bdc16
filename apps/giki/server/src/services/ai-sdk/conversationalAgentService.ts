/**
 * Conversational Agent Service using Vercel AI SDK
 * 
 * Powers the right-panel agent with context-aware responses,
 * streaming support, and tool calling capabilities.
 * 
 * Model: gemini-2.0-flash-001
 */

import { google } from '@ai-sdk/google';
import { generateText, streamText, tool } from 'ai';
import { z } from 'zod';

// Load environment variables
import '../../utils/env-loader';

import { transactionAnalysisService } from './transactionAnalysisService';
import { categoryGenerationService } from './categoryGenerationService';

// Define conversation context schema
const conversationContextSchema = z.object({
  currentPage: z.string().optional(),
  userRole: z.string().optional(),
  sessionData: z.any().optional(),
  recentActions: z.array(z.string()).optional(),
  uploadedFiles: z.array(z.string()).optional()
});

// Define agent response schema
const agentResponseSchema = z.object({
  message: z.string().describe('The agent\'s response message'),
  intent: z.enum(['help', 'action', 'clarification', 'confirmation', 'error']).describe('The intent of the response'),
  suggestedActions: z.array(z.object({
    label: z.string(),
    action: z.string(),
    description: z.string().optional()
  })).optional().describe('Suggested actions for the user'),
  requiresInput: z.boolean().optional().describe('Whether the agent needs more information'),
  confidence: z.number().min(0).max(1).describe('Confidence in the response')
});

export type ConversationContext = z.infer<typeof conversationContextSchema>;
export type AgentResponse = z.infer<typeof agentResponseSchema>;

export interface ConversationMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: any;
}

export interface ConversationSession {
  id: string;
  userId: string;
  messages: ConversationMessage[];
  context: ConversationContext;
  createdAt: Date;
  updatedAt: Date;
}

export class ConversationalAgentService {
  private model;
  private sessions: Map<string, ConversationSession> = new Map();

  constructor(apiKey?: string) {
    const key = apiKey || process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENERATIVE_AI_API_KEY;
    if (!key) {
      console.warn('[ConversationalAgentService] No API key found, service will use fallbacks');
    }
    this.model = google('gemini-2.0-flash-001', {
      apiKey: key
    });
  }

  /**
   * Process a user message and generate a response
   */
  async processMessage(
    message: string,
    sessionId: string,
    userId: string,
    context?: ConversationContext
  ): Promise<AgentResponse> {
    // Get or create session
    const session = this.getOrCreateSession(sessionId, userId, context);
    
    // Add user message to history
    session.messages.push({
      role: 'user',
      content: message,
      timestamp: new Date()
    });

    // Build context-aware prompt
    const systemPrompt = this.buildSystemPrompt(session.context);
    const conversationHistory = this.formatConversationHistory(session.messages.slice(-10)); // Last 10 messages

    const prompt = `${systemPrompt}

Conversation History:
${conversationHistory}

User's Current Message: "${message}"

Based on the context and conversation history, provide a helpful response. If the user is asking for help with a specific task, provide clear guidance and suggest relevant actions they can take.`;

    try {
      const { text, usage } = await generateText({
        model: this.model,
        prompt,
        temperature: 0.7, // Higher for more conversational responses
        maxOutputTokens: 500,
// Temporarily disable tools to fix schema validation issues
        // tools: {}
      });

      // Parse response into structured format
      const response = this.parseAgentResponse(text, message, session.context);
      
      // Add assistant message to history
      session.messages.push({
        role: 'assistant',
        content: response.message,
        timestamp: new Date(),
        metadata: { intent: response.intent, confidence: response.confidence }
      });

      // Update session
      session.updatedAt = new Date();
      this.sessions.set(sessionId, session);

      console.log(`[ConversationalAgentService] Processed message - Intent: ${response.intent} - Confidence: ${(response.confidence * 100).toFixed(1)}% - Tokens: ${usage.totalTokens}`);

      return response;
    } catch (error: any) {
      console.error('[ConversationalAgentService] Message processing failed:', error);
      
      return {
        message: 'I apologize, but I encountered an error processing your request. Please try again.',
        intent: 'error',
        confidence: 0,
        suggestedActions: [
          {
            label: 'Try Again',
            action: 'retry',
            description: 'Retry your last message'
          }
        ]
      };
    }
  }

  /**
   * Stream a response for real-time updates
   */
  async *streamResponse(
    message: string,
    sessionId: string,
    userId: string,
    context?: ConversationContext
  ): AsyncGenerator<string, AgentResponse> {
    const session = this.getOrCreateSession(sessionId, userId, context);
    
    session.messages.push({
      role: 'user',
      content: message,
      timestamp: new Date()
    });

    const systemPrompt = this.buildSystemPrompt(session.context);
    const conversationHistory = this.formatConversationHistory(session.messages.slice(-10));

    try {
      const { textStream, text, usage } = await streamText({
        model: this.model,
        prompt: `${systemPrompt}\n\nConversation:\n${conversationHistory}\n\nUser: "${message}"`,
        temperature: 0.7,
        maxOutputTokens: 500
      });

      // Stream partial responses
      for await (const chunk of textStream) {
        yield chunk;
      }

      // Get final text and parse
      const finalText = await text;
      const response = this.parseAgentResponse(finalText, message, session.context);
      
      // Update session
      session.messages.push({
        role: 'assistant',
        content: response.message,
        timestamp: new Date(),
        metadata: { intent: response.intent }
      });
      session.updatedAt = new Date();
      this.sessions.set(sessionId, session);

      return response;
    } catch (error) {
      console.error('[ConversationalAgentService] Streaming failed:', error);
      
      return {
        message: 'I encountered an error. Please try again.',
        intent: 'error',
        confidence: 0
      };
    }
  }

  /**
   * Get contextual help based on current page
   */
  async getContextualHelp(
    page: string,
    specificTopic?: string
  ): Promise<AgentResponse> {
    const helpPrompts: Record<string, string> = {
      upload: 'Help with uploading financial files, supported formats, and data preparation',
      dashboard: 'Understanding dashboard metrics, charts, and financial insights',
      transactions: 'Managing transactions, categorization, and bulk operations',
      categories: 'Setting up categories, GL codes, and MIS structure',
      reports: 'Generating reports, customization options, and export formats',
      settings: 'Configuring account settings, preferences, and integrations'
    };

    const baseHelp = helpPrompts[page] || 'General help with financial management';
    const prompt = `Provide helpful guidance for: ${baseHelp}${specificTopic ? ` - Specifically about: ${specificTopic}` : ''}

Create a clear, actionable response with:
1. Brief explanation
2. Step-by-step instructions if applicable
3. Common issues and solutions
4. Suggested next actions`;

    try {
      const { text } = await generateText({
        model: this.model,
        prompt,
        temperature: 0.5,
        maxOutputTokens: 600
      });

      return {
        message: text,
        intent: 'help',
        confidence: 0.95,
        suggestedActions: this.generateHelpActions(page)
      };
    } catch (error) {
      console.error('[ConversationalAgentService] Help generation failed:', error);
      
      return {
        message: 'I can help you with that. What specific aspect would you like to know more about?',
        intent: 'clarification',
        confidence: 0.5,
        requiresInput: true
      };
    }
  }

  /**
   * Provide smart suggestions based on user activity
   */
  async getSmartSuggestions(
    context: ConversationContext,
    recentActivity?: string[]
  ): Promise<string[]> {
    const prompt = `Based on the user's context and recent activity, suggest 3-5 helpful actions:

Current Page: ${context.currentPage || 'dashboard'}
Recent Actions: ${recentActivity?.join(', ') || 'None'}
${context.uploadedFiles?.length ? `Uploaded Files: ${context.uploadedFiles.join(', ')}` : ''}

Provide smart, contextual suggestions that help the user progress with their financial management tasks.`;

    try {
      const { text } = await generateText({
        model: this.model,
        prompt,
        temperature: 0.6,
        maxOutputTokens: 200
      });

      // Parse suggestions from text
      const suggestions = text.split('\n')
        .filter(line => line.trim())
        .slice(0, 5);

      return suggestions;
    } catch (error) {
      console.error('[ConversationalAgentService] Suggestion generation failed:', error);
      
      return [
        'Upload your financial statements to get started',
        'Review your transaction categories',
        'Generate a monthly report'
      ];
    }
  }

  /**
   * Clear conversation history for a session
   */
  clearSession(sessionId: string): void {
    this.sessions.delete(sessionId);
  }

  private getOrCreateSession(
    sessionId: string,
    userId: string,
    context?: ConversationContext
  ): ConversationSession {
    let session = this.sessions.get(sessionId);
    
    if (!session) {
      session = {
        id: sessionId,
        userId,
        messages: [
          {
            role: 'system',
            content: 'You are Giki AI Assistant, helping users with financial categorization and analysis.',
            timestamp: new Date()
          }
        ],
        context: context || {},
        createdAt: new Date(),
        updatedAt: new Date()
      };
      this.sessions.set(sessionId, session);
    } else if (context) {
      // Update context if provided
      session.context = { ...session.context, ...context };
    }
    
    return session;
  }

  private buildSystemPrompt(context: ConversationContext): string {
    const pageContext = context.currentPage ? 
      `The user is currently on the ${context.currentPage} page.` : '';
    
    const fileContext = context.uploadedFiles?.length ?
      `The user has uploaded: ${context.uploadedFiles.join(', ')}.` : '';

    return `You are Giki AI Assistant, a helpful financial categorization expert. You help users:
- Upload and process financial statements
- Categorize transactions with high accuracy
- Generate MIS structures and reports
- Understand their financial data

${pageContext}
${fileContext}

Provide clear, actionable responses. Suggest relevant features and guide users through workflows.
Be concise but thorough. Use a friendly, professional tone.`;
  }

  private formatConversationHistory(messages: ConversationMessage[]): string {
    return messages
      .filter(m => m.role !== 'system')
      .map(m => `${m.role === 'user' ? 'User' : 'Assistant'}: ${m.content}`)
      .join('\n');
  }

  private parseAgentResponse(
    text: string,
    userMessage: string,
    context: ConversationContext
  ): AgentResponse {
    // Analyze intent based on message patterns
    let intent: AgentResponse['intent'] = 'help';
    let confidence = 0.8;
    const suggestedActions: AgentResponse['suggestedActions'] = [];

    const lowerMessage = userMessage.toLowerCase();
    const lowerResponse = text.toLowerCase();

    if (lowerMessage.includes('how') || lowerMessage.includes('what') || lowerMessage.includes('explain')) {
      intent = 'help';
      confidence = 0.9;
    } else if (lowerMessage.includes('upload') || lowerMessage.includes('analyze') || lowerMessage.includes('generate')) {
      intent = 'action';
      confidence = 0.85;
      
      // Add relevant action suggestions
      if (lowerMessage.includes('upload')) {
        suggestedActions.push({
          label: 'Upload File',
          action: 'upload_file',
          description: 'Upload a financial statement'
        });
      }
      if (lowerMessage.includes('analyze')) {
        suggestedActions.push({
          label: 'Start Analysis',
          action: 'start_analysis',
          description: 'Analyze your transactions'
        });
      }
      if (lowerMessage.includes('report')) {
        suggestedActions.push({
          label: 'Generate Report',
          action: 'generate_report',
          description: 'Create a financial report'
        });
      }
    } else if (lowerResponse.includes('?') || lowerResponse.includes('could you') || lowerResponse.includes('please provide')) {
      intent = 'clarification';
      confidence = 0.7;
    } else if (lowerResponse.includes('done') || lowerResponse.includes('complete') || lowerResponse.includes('success')) {
      intent = 'confirmation';
      confidence = 0.9;
    }

    // Add context-based suggestions
    if (context.currentPage === 'upload' && !suggestedActions.length) {
      suggestedActions.push({
        label: 'Select File',
        action: 'select_file',
        description: 'Choose a file to upload'
      });
    }

    return {
      message: text,
      intent,
      confidence,
      suggestedActions: suggestedActions.length > 0 ? suggestedActions : undefined,
      requiresInput: intent === 'clarification'
    };
  }

  private generateHelpActions(page: string): AgentResponse['suggestedActions'] {
    const actions: Record<string, AgentResponse['suggestedActions']> = {
      upload: [
        { label: 'View Supported Formats', action: 'view_formats' },
        { label: 'Download Sample File', action: 'download_sample' }
      ],
      dashboard: [
        { label: 'Customize Metrics', action: 'customize_dashboard' },
        { label: 'Export Dashboard', action: 'export_dashboard' }
      ],
      transactions: [
        { label: 'Filter Transactions', action: 'filter_transactions' },
        { label: 'Bulk Categorize', action: 'bulk_categorize' }
      ],
      categories: [
        { label: 'Add Category', action: 'add_category' },
        { label: 'Import Categories', action: 'import_categories' }
      ],
      reports: [
        { label: 'Create Report', action: 'create_report' },
        { label: 'Schedule Report', action: 'schedule_report' }
      ]
    };

    return actions[page] || [
      { label: 'Get Started', action: 'get_started' },
      { label: 'View Help', action: 'view_help' }
    ];
  }
}

// Export singleton instance
export const conversationalAgentService = new ConversationalAgentService();