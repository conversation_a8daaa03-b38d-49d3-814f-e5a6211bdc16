/**
 * Schema Interpretation Service using Vercel AI SDK
 * 
 * Intelligently interprets the structure of uploaded financial files
 * (CSV, Excel, bank statements) to map columns to transaction fields.
 * 
 * Model: gemini-2.0-flash-001
 */

import { google } from '@ai-sdk/google';
import { generateObject, streamObject } from 'ai';
import { z } from 'zod';

// Load environment variables
import '../../utils/env-loader';

// Column mapping schema
const columnMappingSchema = z.object({
  columnIndex: z.number().describe('0-based column index'),
  columnName: z.string().describe('Original column name from file'),
  mappedTo: z.enum([
    'date',
    'description',
    'amount',
    'debit',
    'credit',
    'balance',
    'category',
    'vendor',
    'reference',
    'notes',
    'ignore'
  ]).describe('What this column represents'),
  confidence: z.number().min(0).max(1).describe('Confidence in this mapping'),
  format: z.string().optional().describe('Format details (e.g., date format: MM/DD/YYYY)'),
  examples: z.array(z.string()).describe('Sample values from this column')
});

// File schema interpretation result
const schemaInterpretationSchema = z.object({
  fileType: z.enum(['csv', 'excel', 'pdf', 'json', 'xml']).describe('Detected file type'),
  encoding: z.string().optional().describe('File encoding (e.g., UTF-8)'),
  delimiter: z.string().optional().describe('Delimiter for CSV files'),
  hasHeaders: z.boolean().describe('Whether the file has header row'),
  headerRow: z.number().optional().describe('0-based index of header row'),
  dataStartRow: z.number().describe('0-based index where data starts'),
  dateFormat: z.string().describe('Detected date format'),
  currencySymbol: z.string().optional().describe('Detected currency symbol'),
  accountNumber: z.string().optional().describe('Detected account number if present'),
  bankName: z.string().optional().describe('Detected bank/institution name'),
  statementPeriod: z.object({
    start: z.string().optional(),
    end: z.string().optional()
  }).optional().describe('Statement period if detectable'),
  columns: z.array(columnMappingSchema).describe('Mapping for each column'),
  transactionType: z.enum(['unified', 'separate_debit_credit', 'signed_amount']).describe('How amounts are represented'),
  confidence: z.number().min(0).max(1).describe('Overall confidence in interpretation'),
  warnings: z.array(z.string()).optional().describe('Any warnings or issues detected'),
  suggestions: z.array(z.string()).optional().describe('Suggestions for better data quality')
});

// Schema validation result
const schemaValidationSchema = z.object({
  isValid: z.boolean(),
  completeness: z.number().min(0).max(1).describe('How complete the data is'),
  issues: z.array(z.object({
    severity: z.enum(['error', 'warning', 'info']),
    column: z.string().optional(),
    row: z.number().optional(),
    message: z.string()
  })),
  dataQuality: z.object({
    totalRows: z.number(),
    validRows: z.number(),
    invalidRows: z.number(),
    duplicateRows: z.number(),
    missingValues: z.record(z.string(), z.number()).describe('Missing values per column')
  }),
  recommendations: z.array(z.string())
});

export type ColumnMapping = z.infer<typeof columnMappingSchema>;
export type SchemaInterpretation = z.infer<typeof schemaInterpretationSchema>;
export type SchemaValidation = z.infer<typeof schemaValidationSchema>;

export interface FilePreview {
  headers: string[];
  rows: any[][];
  totalRows: number;
  fileSize: number;
  fileName: string;
}

export class SchemaInterpretationService {
  private model;

  constructor(apiKey?: string) {
    const key = apiKey || process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENERATIVE_AI_API_KEY;
    if (!key) {
      console.warn('[SchemaInterpretationService] No API key found, service will use fallbacks');
    }
    this.model = google('gemini-2.0-flash-001', {
      apiKey: key,
      structuredOutputs: true
    });
  }

  /**
   * Interpret the schema of an uploaded file
   */
  async interpretSchema(filePreview: FilePreview): Promise<SchemaInterpretation> {
    // Prepare sample data for analysis
    const headerRow = filePreview.headers.join(' | ');
    const sampleRows = filePreview.rows.slice(0, 10)
      .map(row => row.join(' | '))
      .join('\n');

    // Analyze each column's content
    const columnAnalysis = filePreview.headers.map((header, idx) => {
      const columnValues = filePreview.rows.slice(0, 20).map(row => row[idx]);
      const nonEmptyValues = columnValues.filter(v => v !== null && v !== undefined && v !== '');
      
      return {
        header,
        index: idx,
        samples: nonEmptyValues.slice(0, 5),
        uniqueCount: new Set(nonEmptyValues).size,
        hasNumbers: nonEmptyValues.some(v => !isNaN(parseFloat(v))),
        hasDates: nonEmptyValues.some(v => this.looksLikeDate(String(v))),
        hasCurrency: nonEmptyValues.some(v => this.looksLikeCurrency(String(v)))
      };
    });

    const prompt = `Interpret this financial file schema for transaction processing:

File: ${filePreview.fileName}
Total Rows: ${filePreview.totalRows}

Headers:
${headerRow}

Sample Data (first 10 rows):
${sampleRows}

Column Analysis:
${columnAnalysis.map(col => 
  `Column ${col.index} "${col.header}": 
   - Samples: ${col.samples.slice(0, 3).join(', ')}
   - Has numbers: ${col.hasNumbers}
   - Has dates: ${col.hasDates}
   - Has currency: ${col.hasCurrency}`
).join('\n')}

Determine:
1. What each column represents (date, description, amount, etc.)
2. The date format used
3. How amounts are represented (single column, debit/credit, signed)
4. Any bank/account information present
5. Data quality issues or warnings

Focus on accurate mapping for financial transaction processing.`;

    try {
      const { object, usage } = await generateObject({
        model: this.model,
        schema: schemaInterpretationSchema,
        prompt,
        temperature: 0.2, // Low temperature for consistent interpretation
        maxOutputTokens: 2000
      });

      // Add examples to column mappings
      object.columns = object.columns.map((col, idx) => ({
        ...col,
        examples: columnAnalysis[idx]?.samples || []
      }));

      console.log(`[SchemaInterpretationService] Interpreted schema - Confidence: ${(object.confidence * 100).toFixed(1)}% - Tokens: ${usage.totalTokens}`);
      
      return object;
    } catch (error: any) {
      console.error('[SchemaInterpretationService] Schema interpretation failed:', error);
      
      // Return basic fallback interpretation
      return this.getFallbackInterpretation(filePreview, columnAnalysis);
    }
  }

  /**
   * Stream schema interpretation with progress updates
   */
  async *streamInterpretSchema(
    filePreview: FilePreview
  ): AsyncGenerator<Partial<SchemaInterpretation>, SchemaInterpretation> {
    const headerRow = filePreview.headers.join(' | ');
    const sampleRows = filePreview.rows.slice(0, 5)
      .map(row => row.join(' | '))
      .join('\n');

    const prompt = `Interpret this file schema:
Headers: ${headerRow}
Samples:
${sampleRows}

Map each column to transaction fields and detect the file format.`;

    try {
      const { partialObjectStream, object } = await streamObject({
        model: this.model,
        schema: schemaInterpretationSchema,
        prompt,
        temperature: 0.2,
        maxOutputTokens: 1500
      });

      // Stream partial results
      for await (const partialObject of partialObjectStream) {
        yield partialObject;
      }

      // Return final result
      const finalResult = await object;
      console.log(`[SchemaInterpretationService] Streaming complete - ${finalResult.columns.length} columns mapped`);
      
      return finalResult;
    } catch (error) {
      console.error('[SchemaInterpretationService] Streaming failed:', error);
      return this.getFallbackInterpretation(filePreview, []);
    }
  }

  /**
   * Validate interpreted schema and data quality
   */
  async validateSchema(
    interpretation: SchemaInterpretation,
    filePreview: FilePreview
  ): Promise<SchemaValidation> {
    // Check for required fields
    const hasDate = interpretation.columns.some(c => c.mappedTo === 'date');
    const hasDescription = interpretation.columns.some(c => c.mappedTo === 'description');
    const hasAmount = interpretation.columns.some(c => 
      ['amount', 'debit', 'credit'].includes(c.mappedTo)
    );

    // Analyze data quality
    const dataQuality = this.analyzeDataQuality(filePreview, interpretation);

    const prompt = `Validate this schema interpretation:

File Type: ${interpretation.fileType}
Columns Mapped: ${interpretation.columns.length}
Has Required Fields: Date=${hasDate}, Description=${hasDescription}, Amount=${hasAmount}

Column Mappings:
${interpretation.columns.map(c => 
  `- ${c.columnName} → ${c.mappedTo} (${(c.confidence * 100).toFixed(0)}% confidence)`
).join('\n')}

Data Quality:
- Total Rows: ${dataQuality.totalRows}
- Empty Cells: ${Object.values(dataQuality.missingValues).reduce((a, b) => a + b, 0)}
- Potential Duplicates: ${dataQuality.duplicateRows}

Identify:
1. Any critical issues that would prevent processing
2. Data quality warnings
3. Recommendations for improvement`;

    try {
      const { object } = await generateObject({
        model: this.model,
        schema: schemaValidationSchema,
        prompt,
        temperature: 0.3,
        maxOutputTokens: 1000
      });

      // Merge with calculated data quality
      object.dataQuality = dataQuality;
      
      console.log(`[SchemaInterpretationService] Validation complete - Valid: ${object.isValid} - Completeness: ${(object.completeness * 100).toFixed(1)}%`);
      
      return object;
    } catch (error) {
      console.error('[SchemaInterpretationService] Validation failed:', error);
      
      return {
        isValid: hasDate && hasDescription && hasAmount,
        completeness: 0.5,
        issues: [],
        dataQuality,
        recommendations: ['Manual review recommended']
      };
    }
  }

  /**
   * Auto-correct common schema issues
   */
  async autoCorrectSchema(
    interpretation: SchemaInterpretation,
    filePreview: FilePreview
  ): Promise<SchemaInterpretation> {
    const correctionSchema = z.object({
      corrections: z.array(z.object({
        columnIndex: z.number(),
        originalMapping: z.string(),
        correctedMapping: z.string(),
        reason: z.string()
      })),
      dateFormatCorrection: z.string().optional(),
      transactionTypeCorrection: z.string().optional()
    });

    // Find potential issues
    const issues: string[] = [];
    
    // Check for multiple date columns
    const dateColumns = interpretation.columns.filter(c => c.mappedTo === 'date');
    if (dateColumns.length > 1) {
      issues.push(`Multiple date columns detected: ${dateColumns.map(c => c.columnName).join(', ')}`);
    }

    // Check for missing amount columns
    const amountColumns = interpretation.columns.filter(c => 
      ['amount', 'debit', 'credit'].includes(c.mappedTo)
    );
    if (amountColumns.length === 0) {
      issues.push('No amount columns detected');
    }

    // Check for ambiguous mappings
    const lowConfidence = interpretation.columns.filter(c => c.confidence < 0.5);
    if (lowConfidence.length > 0) {
      issues.push(`Low confidence mappings: ${lowConfidence.map(c => c.columnName).join(', ')}`);
    }

    if (issues.length === 0) {
      return interpretation; // No corrections needed
    }

    const prompt = `Auto-correct these schema interpretation issues:

Current Issues:
${issues.join('\n')}

Current Mappings:
${interpretation.columns.map(c => 
  `${c.columnIndex}: "${c.columnName}" → ${c.mappedTo} (${(c.confidence * 100).toFixed(0)}%)`
).join('\n')}

Sample Data:
${filePreview.rows.slice(0, 3).map(row => row.join(' | ')).join('\n')}

Suggest corrections for:
1. Ambiguous or incorrect column mappings
2. Date format detection
3. Transaction type (unified amount vs debit/credit)`;

    try {
      const { object } = await generateObject({
        model: this.model,
        schema: correctionSchema,
        prompt,
        temperature: 0.2,
        maxOutputTokens: 1000
      });

      // Apply corrections
      const corrected = { ...interpretation };
      
      object.corrections.forEach(correction => {
        const column = corrected.columns[correction.columnIndex];
        if (column) {
          column.mappedTo = correction.correctedMapping as any;
          column.confidence = Math.min(column.confidence + 0.2, 1); // Boost confidence after correction
        }
      });

      if (object.dateFormatCorrection) {
        corrected.dateFormat = object.dateFormatCorrection;
      }

      if (object.transactionTypeCorrection) {
        corrected.transactionType = object.transactionTypeCorrection as any;
      }

      console.log(`[SchemaInterpretationService] Applied ${object.corrections.length} corrections`);
      
      return corrected;
    } catch (error) {
      console.error('[SchemaInterpretationService] Auto-correction failed:', error);
      return interpretation; // Return original if correction fails
    }
  }

  /**
   * Learn from user corrections to improve future interpretations
   */
  async learnFromCorrection(
    original: SchemaInterpretation,
    corrected: SchemaInterpretation,
    feedback?: string
  ): Promise<void> {
    const changes = this.findChanges(original, corrected);
    
    if (changes.length === 0) return;

    console.log(`[SchemaInterpretationService] Learning from ${changes.length} corrections`);
    
    // In a production system, this would update a learning database
    // For now, we just log the learning
    changes.forEach(change => {
      console.log(`  - Column "${change.columnName}": ${change.from} → ${change.to}`);
    });
    
    if (feedback) {
      console.log(`  User feedback: ${feedback}`);
    }
  }

  /**
   * Generate mapping rules for common bank formats
   */
  async generateBankSpecificRules(
    bankName: string,
    sampleFile: FilePreview
  ): Promise<Record<string, any>> {
    const bankRulesSchema = z.object({
      bankName: z.string(),
      formatName: z.string().describe('Name of this format (e.g., "Chase Checking CSV")'),
      rules: z.object({
        headerRow: z.number(),
        dataStartRow: z.number(),
        delimiter: z.string().optional(),
        dateFormat: z.string(),
        columnMappings: z.record(z.string(), z.string()).describe('Column name to field mapping'),
        specialHandling: z.array(z.string()).optional()
      }),
      exampleFile: z.string().optional().describe('Example file pattern to match')
    });

    const prompt = `Generate specific parsing rules for ${bankName} bank statements:

Sample File:
Headers: ${sampleFile.headers.join(', ')}
First Row: ${sampleFile.rows[0]?.join(', ') || 'No data'}

Create rules that would consistently parse files from this bank.`;

    try {
      const { object } = await generateObject({
        model: this.model,
        schema: bankRulesSchema,
        prompt,
        temperature: 0.3,
        maxOutputTokens: 1000
      });

      console.log(`[SchemaInterpretationService] Generated rules for ${bankName}`);
      
      return object;
    } catch (error) {
      console.error('[SchemaInterpretationService] Bank rule generation failed:', error);
      return {};
    }
  }

  // Helper methods
  private looksLikeDate(value: string): boolean {
    const datePatterns = [
      /\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4}/,  // MM/DD/YYYY or MM-DD-YYYY
      /\d{4}[-\/]\d{1,2}[-\/]\d{1,2}/,     // YYYY-MM-DD
      /\d{1,2}\s+\w{3,}\s+\d{2,4}/,        // DD Mon YYYY
      /\w{3,}\s+\d{1,2},?\s+\d{4}/         // Mon DD, YYYY
    ];
    
    return datePatterns.some(pattern => pattern.test(value));
  }

  private looksLikeCurrency(value: string): boolean {
    const currencyPatterns = [
      /^[€$£¥₹]\s*[\d,]+\.?\d*/,           // Currency symbol prefix
      /^[\d,]+\.?\d*\s*[€$£¥₹]/,           // Currency symbol suffix
      /^-?\d{1,3}(,\d{3})*(\.\d{2})?$/,    // Formatted number
      /^\(\d+\.?\d*\)$/                     // Accounting negative (123.45)
    ];
    
    return currencyPatterns.some(pattern => pattern.test(value));
  }

  private analyzeDataQuality(
    filePreview: FilePreview,
    interpretation: SchemaInterpretation
  ): SchemaValidation['dataQuality'] {
    const missingValues: Record<string, number> = {};
    let duplicateRows = 0;
    const seenRows = new Set<string>();

    filePreview.rows.forEach(row => {
      const rowKey = row.join('|');
      if (seenRows.has(rowKey)) {
        duplicateRows++;
      }
      seenRows.add(rowKey);

      row.forEach((value, idx) => {
        const columnName = filePreview.headers[idx] || `Column${idx}`;
        if (value === null || value === undefined || value === '') {
          missingValues[columnName] = (missingValues[columnName] || 0) + 1;
        }
      });
    });

    const validRows = filePreview.totalRows - Object.values(missingValues).reduce((a, b) => Math.max(a, b), 0);

    return {
      totalRows: filePreview.totalRows,
      validRows,
      invalidRows: filePreview.totalRows - validRows,
      duplicateRows,
      missingValues
    };
  }

  private getFallbackInterpretation(
    filePreview: FilePreview,
    columnAnalysis: any[]
  ): SchemaInterpretation {
    // Basic heuristic-based interpretation
    const columns: ColumnMapping[] = filePreview.headers.map((header, idx) => {
      const headerLower = header.toLowerCase();
      let mappedTo: ColumnMapping['mappedTo'] = 'ignore';
      let confidence = 0.3;

      if (headerLower.includes('date') || headerLower.includes('posted')) {
        mappedTo = 'date';
        confidence = 0.8;
      } else if (headerLower.includes('description') || headerLower.includes('memo')) {
        mappedTo = 'description';
        confidence = 0.8;
      } else if (headerLower.includes('amount')) {
        mappedTo = 'amount';
        confidence = 0.7;
      } else if (headerLower.includes('debit')) {
        mappedTo = 'debit';
        confidence = 0.8;
      } else if (headerLower.includes('credit')) {
        mappedTo = 'credit';
        confidence = 0.8;
      } else if (headerLower.includes('balance')) {
        mappedTo = 'balance';
        confidence = 0.7;
      } else if (headerLower.includes('category')) {
        mappedTo = 'category';
        confidence = 0.6;
      } else if (headerLower.includes('vendor') || headerLower.includes('merchant')) {
        mappedTo = 'vendor';
        confidence = 0.7;
      } else if (headerLower.includes('reference') || headerLower.includes('ref')) {
        mappedTo = 'reference';
        confidence = 0.6;
      }

      return {
        columnIndex: idx,
        columnName: header,
        mappedTo,
        confidence,
        examples: columnAnalysis[idx]?.samples || []
      };
    });

    return {
      fileType: 'csv',
      hasHeaders: true,
      dataStartRow: 1,
      dateFormat: 'MM/DD/YYYY',
      columns,
      transactionType: columns.some(c => c.mappedTo === 'debit') ? 'separate_debit_credit' : 'unified',
      confidence: 0.5,
      warnings: ['Fallback interpretation used - please review mappings']
    };
  }

  private findChanges(
    original: SchemaInterpretation,
    corrected: SchemaInterpretation
  ): Array<{ columnName: string; from: string; to: string }> {
    const changes: Array<{ columnName: string; from: string; to: string }> = [];
    
    original.columns.forEach((col, idx) => {
      const correctedCol = corrected.columns[idx];
      if (correctedCol && col.mappedTo !== correctedCol.mappedTo) {
        changes.push({
          columnName: col.columnName,
          from: col.mappedTo,
          to: correctedCol.mappedTo
        });
      }
    });
    
    return changes;
  }
}

// Export singleton instance
export const schemaInterpretationService = new SchemaInterpretationService();