/**
 * Transaction Analysis Orchestrator Service using Vercel AI SDK
 * 
 * Main orchestrator that coordinates all AI services for the complete
 * transaction categorization pipeline using streaming and structured outputs.
 * 
 * Model: gemini-2.0-flash-001
 */

// Load environment variables
import '../../utils/env-loader';

import { vendorExtractionService, type VendorExtractionResult } from './vendorExtractionService';
import { categoryMappingService, type CategorizationResult, type TransactionInput, type Category, type BusinessContext } from './categoryMappingService';
import { categoryGenerationService, type CategoryHierarchy, type BusinessProfile } from './categoryGenerationService';

export interface Transaction {
  id: string;
  description: string;
  amount: number;
  type: 'debit' | 'credit';
  date: string;
  originalText?: string;
}

export interface AnalysisResult {
  transactionId: string;
  vendor: VendorExtractionResult;
  categorization: CategorizationResult;
  processingTime: number;
  confidence: number;
}

export interface BatchAnalysisResult {
  results: AnalysisResult[];
  categoryHierarchy: CategoryHierarchy;
  summary: {
    totalTransactions: number;
    averageConfidence: number;
    processingTime: number;
    categoriesUsed: Map<string, number>;
    vendorsExtracted: number;
  };
}

export interface AnalysisProgress {
  phase: 'vendor_extraction' | 'categorization' | 'mis_generation' | 'complete';
  progress: number; // 0-100
  message: string;
  partialResults?: Partial<BatchAnalysisResult>;
}

export class TransactionAnalysisService {
  /**
   * Analyze a single transaction through the complete pipeline
   */
  async analyzeTransaction(
    transaction: Transaction,
    categories: Category[],
    businessContext?: BusinessContext
  ): Promise<AnalysisResult> {
    const startTime = Date.now();
    
    // Step 1: Extract vendor
    const vendor = await vendorExtractionService.extractVendorWithContext(
      transaction.description,
      {
        amount: transaction.amount,
        type: transaction.type,
        date: transaction.date
      }
    );

    // Step 2: Categorize with vendor context
    const transactionInput: TransactionInput = {
      id: transaction.id,
      description: transaction.description,
      amount: transaction.amount,
      type: transaction.type,
      vendor: vendor.vendor || undefined,
      date: transaction.date
    };

    const categorization = await categoryMappingService.categorizeTransaction(
      transactionInput,
      categories,
      businessContext
    );

    const processingTime = Date.now() - startTime;
    const combinedConfidence = (vendor.confidence + categorization.confidence) / 2;

    return {
      transactionId: transaction.id,
      vendor,
      categorization,
      processingTime,
      confidence: combinedConfidence
    };
  }

  /**
   * Stream batch analysis with real-time progress updates
   */
  async *streamBatchAnalysis(
    transactions: Transaction[],
    businessProfile: BusinessProfile,
    onProgress?: (progress: AnalysisProgress) => void
  ): AsyncGenerator<AnalysisProgress, BatchAnalysisResult> {
    const startTime = Date.now();
    const results: AnalysisResult[] = [];
    
    // Phase 1: Generate Category Hierarchy (20% of progress)
    yield {
      phase: 'mis_generation',
      progress: 0,
      message: 'Generating industry-specific category hierarchy...'
    };

    let categoryHierarchy: CategoryHierarchy | undefined;
    const hierarchyGenerator = categoryGenerationService.streamGenerateCategoryHierarchy(businessProfile);
    
    for await (const partialHierarchy of hierarchyGenerator) {
      const progress = partialHierarchy.totalCategories ? 
        Math.min(20, (partialHierarchy.categories?.length || 0) / (partialHierarchy.totalCategories || 1) * 20) : 5;
      
      yield {
        phase: 'mis_generation',
        progress,
        message: `Building category hierarchy... ${partialHierarchy.categories?.length || 0} categories`,
        partialResults: { categoryHierarchy: partialHierarchy as CategoryHierarchy }
      };
    }

    // Get final category hierarchy
    categoryHierarchy = await hierarchyGenerator.next().then(r => r.value as CategoryHierarchy);
    
    yield {
      phase: 'mis_generation',
      progress: 20,
      message: `Category hierarchy complete: ${categoryHierarchy.totalCategories} categories`,
      partialResults: { categoryHierarchy }
    };

    // Convert hierarchy categories to flat list for categorization
    const flatCategories = this.flattenCategories(categoryHierarchy.categories);

    // Phase 2: Vendor Extraction (20-40% of progress)
    yield {
      phase: 'vendor_extraction',
      progress: 20,
      message: 'Extracting vendors from transactions...'
    };

    const vendorResults = new Map<string, VendorExtractionResult>();
    const batchSize = 5;
    
    for (let i = 0; i < transactions.length; i += batchSize) {
      const batch = transactions.slice(i, i + batchSize);
      const batchVendors = await Promise.all(
        batch.map(t => vendorExtractionService.extractVendor(t.description))
      );
      
      batch.forEach((t, idx) => {
        vendorResults.set(t.id, batchVendors[idx]);
      });

      const progress = 20 + (i / transactions.length) * 20;
      yield {
        phase: 'vendor_extraction',
        progress,
        message: `Extracted vendors: ${i + batch.length}/${transactions.length}`,
        partialResults: {
          summary: {
            totalTransactions: transactions.length,
            vendorsExtracted: vendorResults.size,
            averageConfidence: 0,
            processingTime: Date.now() - startTime,
            categoriesUsed: new Map()
          }
        }
      };
    }

    // Phase 3: Categorization with Streaming (40-90% of progress)
    yield {
      phase: 'categorization',
      progress: 40,
      message: 'Categorizing transactions with AI...'
    };

    // Prepare transactions for batch categorization
    const transactionInputs: TransactionInput[] = transactions.map(t => ({
      id: t.id,
      description: t.description,
      amount: t.amount,
      type: t.type,
      vendor: vendorResults.get(t.id)?.vendor || undefined,
      date: t.date
    }));

    const businessContext: BusinessContext = {
      industry: businessProfile.industry,
      businessType: businessProfile.businessType,
      size: businessProfile.size
    };

    // Stream categorization results
    const categorizationStream = categoryMappingService.streamCategorizeBatch(
      transactionInputs,
      flatCategories,
      businessContext
    );

    let categorizationResult: any;
    for await (const partialCategorization of categorizationStream) {
      const categorized = partialCategorization.categorizations?.length || 0;
      const progress = 40 + (categorized / transactions.length) * 50;
      
      yield {
        phase: 'categorization',
        progress,
        message: `Categorized: ${categorized}/${transactions.length} transactions`,
        partialResults: {
          results: this.buildPartialResults(
            transactions.slice(0, categorized),
            vendorResults,
            partialCategorization.categorizations || []
          )
        }
      };
      
      categorizationResult = partialCategorization;
    }

    // Build final results
    const finalCategorization = await categorizationStream.next().then(r => r.value);
    const categoriesUsed = new Map<string, number>();
    
    finalCategorization.categorizations.forEach((cat: any, idx: number) => {
      const vendor = vendorResults.get(transactions[idx].id)!;
      
      results.push({
        transactionId: transactions[idx].id,
        vendor,
        categorization: {
          categoryId: cat.categoryId,
          categoryName: cat.categoryName,
          confidence: cat.confidence,
          reasoning: cat.reasoning,
          alternativeCategories: []
        },
        processingTime: 0, // Will be calculated
        confidence: (vendor.confidence + cat.confidence) / 2
      });

      // Track category usage
      const count = categoriesUsed.get(cat.categoryName) || 0;
      categoriesUsed.set(cat.categoryName, count + 1);
    });

    // Phase 4: Complete
    const totalTime = Date.now() - startTime;
    const avgConfidence = results.reduce((sum, r) => sum + r.confidence, 0) / results.length;
    
    yield {
      phase: 'complete',
      progress: 100,
      message: 'Analysis complete!',
      partialResults: {
        results,
        categoryHierarchy,
        summary: {
          totalTransactions: transactions.length,
          averageConfidence: avgConfidence,
          processingTime: totalTime,
          categoriesUsed,
          vendorsExtracted: Array.from(vendorResults.values()).filter(v => v.vendor !== null).length
        }
      }
    };

    // Return final result
    return {
      results,
      categoryHierarchy,
      summary: {
        totalTransactions: transactions.length,
        averageConfidence: avgConfidence,
        processingTime: totalTime,
        categoriesUsed,
        vendorsExtracted: Array.from(vendorResults.values()).filter(v => v.vendor !== null).length
      }
    };
  }

  /**
   * Analyze transactions without streaming (simpler API)
   */
  async analyzeBatch(
    transactions: Transaction[],
    businessProfile: BusinessProfile
  ): Promise<BatchAnalysisResult> {
    const generator = this.streamBatchAnalysis(transactions, businessProfile);
    
    // Consume all progress updates
    let result: BatchAnalysisResult | undefined;
    for await (const progress of generator) {
      // Could log progress here if needed
      if (progress.phase === 'complete' && progress.partialResults) {
        result = progress.partialResults as BatchAnalysisResult;
      }
    }
    
    // Get final result
    const finalResult = await generator.next();
    return finalResult.value || result!;
  }

  /**
   * Learn from user corrections to improve future categorization
   */
  async learnFromCorrections(
    corrections: Array<{
      transaction: Transaction;
      originalResult: AnalysisResult;
      correctCategoryId: string;
      feedback?: string;
    }>,
    categories: Category[]
  ): Promise<void> {
    console.log(`[TransactionAnalysisService] Learning from ${corrections.length} corrections`);

    for (const correction of corrections) {
      // Improve categorization with feedback
      await categoryMappingService.improveCategorizationWithFeedback(
        {
          id: correction.transaction.id,
          description: correction.transaction.description,
          amount: correction.transaction.amount,
          type: correction.transaction.type,
          vendor: correction.originalResult.vendor.vendor || undefined,
          date: correction.transaction.date
        },
        correction.originalResult.categorization,
        correction.correctCategoryId,
        categories,
        correction.feedback
      );
    }
  }

  /**
   * Analyze categorization patterns for insights
   */
  async analyzePatterns(
    recentAnalyses: AnalysisResult[],
    categories: Category[]
  ) {
    const recentCategorizations = recentAnalyses.map(analysis => ({
      transaction: {
        id: analysis.transactionId,
        description: '', // Would need to be provided
        amount: 0, // Would need to be provided
        type: 'debit' as const,
        vendor: analysis.vendor.vendor || undefined
      },
      result: analysis.categorization
    }));

    return categoryMappingService.analyzeCategorizationPatterns(
      recentCategorizations,
      categories
    );
  }

  /**
   * Optimize category hierarchy based on usage
   */
  async optimizeCategoryHierarchy(
    currentStructure: CategoryHierarchy,
    analysisResults: AnalysisResult[]
  ): Promise<CategoryHierarchy> {
    // Calculate usage statistics
    const usageMap = new Map<string, { frequency: number; totalConfidence: number }>();
    
    analysisResults.forEach(result => {
      const key = result.categorization.categoryId;
      const existing = usageMap.get(key) || { frequency: 0, totalConfidence: 0 };
      existing.frequency++;
      existing.totalConfidence += result.categorization.confidence;
      usageMap.set(key, existing);
    });

    const usageData = Array.from(usageMap.entries()).map(([categoryId, stats]) => ({
      categoryId,
      frequency: stats.frequency,
      accuracy: stats.totalConfidence / stats.frequency
    }));

    return categoryGenerationService.optimizeCategoryHierarchy(
      currentStructure,
      usageData
    );
  }

  private flattenCategories(categories: any[], parentPath: string = ''): Category[] {
    const flat: Category[] = [];
    
    for (const cat of categories) {
      flat.push({
        id: cat.id,
        name: cat.name,
        glCode: cat.glCode?.code,
        description: cat.description,
        parentId: cat.parentId
      });
      
      if (cat.subcategories?.length) {
        flat.push(...this.flattenCategories(cat.subcategories, `${parentPath}/${cat.name}`));
      }
    }
    
    return flat;
  }

  private buildPartialResults(
    transactions: Transaction[],
    vendorResults: Map<string, VendorExtractionResult>,
    categorizations: any[]
  ): AnalysisResult[] {
    return transactions.slice(0, categorizations.length).map((t, idx) => ({
      transactionId: t.id,
      vendor: vendorResults.get(t.id)!,
      categorization: {
        categoryId: categorizations[idx].categoryId,
        categoryName: categorizations[idx].categoryName,
        confidence: categorizations[idx].confidence,
        reasoning: categorizations[idx].reasoning,
        alternativeCategories: []
      },
      processingTime: 0,
      confidence: (vendorResults.get(t.id)!.confidence + categorizations[idx].confidence) / 2
    }));
  }
}

// Export singleton instance
export const transactionAnalysisService = new TransactionAnalysisService();