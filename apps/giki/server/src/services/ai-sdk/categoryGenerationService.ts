/**
 * Category Generation Service using Vercel AI SDK
 * 
 * Generates industry-specific category hierarchies with nested levels,
 * GL-like codes, and rollup structures using structured outputs.
 * Categories roll up to parent categories forming the MIS structure.
 * 
 * Model: gemini-2.0-flash-001
 */

import { google } from '@ai-sdk/google';
import { generateObject, streamObject } from 'ai';
import { z } from 'zod';

// Load environment variables
import '../../utils/env-loader';

// Define GL code schema
const glCodeSchema = z.object({
  code: z.string().describe('The GL code (e.g., "1000", "2100")'),
  description: z.string().describe('Description of what this GL code represents'),
  type: z.enum(['asset', 'liability', 'equity', 'revenue', 'expense']).describe('Account type')
});

// Define category schema with proper hierarchy support
const baseCategorySchema = z.object({
  id: z.string().describe('Unique category identifier'),
  name: z.string().describe('Category name'),
  glCode: glCodeSchema.optional().describe('Associated GL code if applicable'),
  description: z.string().optional().describe('Category description'),
  level: z.number().describe('Hierarchy level (0 for root, 1 for sub, etc.)'),
  parentId: z.string().nullable().describe('Parent category ID for hierarchy'),
  examples: z.array(z.string()).optional().describe('Example transactions for this category')
});

// Add subcategories as a separate array to avoid recursive reference issues
const categorySchema = baseCategorySchema.extend({
  subcategories: z.array(baseCategorySchema).optional().describe('Direct child categories')
});

// Define complete category hierarchy schema (MIS structure)
const categoryHierarchySchema = z.object({
  industry: z.string().describe('Industry type for this category hierarchy'),
  businessType: z.enum(['retail', 'service', 'manufacturing', 'saas', 'consulting', 'other']).describe('Business type'),
  categories: z.array(categorySchema).describe('Root-level categories'),
  totalCategories: z.number().describe('Total number of categories including subcategories'),
  maxDepth: z.number().describe('Maximum hierarchy depth'),
  customizations: z.array(z.object({
    rule: z.string().describe('Custom categorization rule'),
    condition: z.string().describe('When to apply this rule')
  })).optional().describe('Industry-specific customization rules'),
  metadata: z.object({
    generatedAt: z.string().describe('ISO timestamp of generation'),
    version: z.string().describe('Structure version'),
    compliance: z.array(z.string()).optional().describe('Compliance standards met (e.g., GAAP, IFRS)')
  })
});

export type CategoryHierarchy = z.infer<typeof categoryHierarchySchema>;
export type Category = z.infer<typeof categorySchema>;

export interface BusinessProfile {
  industry: string;
  businessType: 'retail' | 'service' | 'manufacturing' | 'saas' | 'consulting' | 'other';
  size: 'small' | 'medium' | 'large';
  location?: string;
  specialRequirements?: string[];
}

export class CategoryGenerationService {
  private model;

  constructor(apiKey?: string) {
    this.model = google('gemini-2.0-flash-001', {
      structuredOutputs: true
    });
  }

  /**
   * Generate a complete category hierarchy (MIS structure) for a business
   */
  async generateCategoryHierarchy(
    profile: BusinessProfile,
    sampleTransactions?: Array<{ description: string; amount: number; type: 'debit' | 'credit' }>
  ): Promise<CategoryHierarchy> {
    const transactionSummary = sampleTransactions?.slice(0, 10)
      .map(t => `- ${t.description}: ${t.amount} (${t.type})`)
      .join('\n');

    const prompt = `Generate a comprehensive category hierarchy for a ${profile.businessType} business in ${profile.industry}:

Business Context:
- Industry: ${profile.industry}
- Type: ${profile.businessType}  
- Size: ${profile.size}
${profile.location ? `- Location: ${profile.location}` : ''}
${profile.specialRequirements?.length ? `- Requirements: ${profile.specialRequirements.join(', ')}` : ''}

${transactionSummary ? `Sample Transactions:\n${transactionSummary}\n` : ''}

Create a hierarchical category structure with 2-3 levels that includes:

**Level 0 (Major Categories):**
- Assets (GL 1000-1999) with Current/Non-Current subcategories
- Liabilities (GL 2000-2999) with Current/Long-term subcategories  
- Equity (GL 3000-3999)
- Revenue (GL 4000-4999) with industry-specific revenue streams
- Expenses (GL 5000-9999) with detailed operational categories

**Level 1 & 2 (Subcategories) - MANDATORY:**
EVERY major category MUST have subcategories in the subcategories array:
- Assets → Current Assets, Non-Current Assets (each with their own subcategories)
- Liabilities → Current Liabilities, Long-term Liabilities (with subcategories)
- Revenue → Multiple revenue streams for ${profile.industry}
- Expenses → Detailed operational expense categories

Requirements:
- Follow GAAP/IFRS standards
- MANDATORY: Each level-0 category must have 2-4 subcategories in subcategories array
- MANDATORY: Some subcategories should have their own subcategories (3-level hierarchy)
- Include appropriate GL codes at each level
- Add practical examples for leaf categories
- Total 20-30 categories across all levels
- maxDepth must be at least 2

CRITICAL: 
1. Generate clean JSON without null values in arrays
2. EVERY major category MUST populate subcategories array
3. Create actual hierarchical structure, not flat categories`;

    try {
      const { object, usage } = await generateObject({
        model: this.model,
        schema: categoryHierarchySchema,
        prompt,
        temperature: 0.4, // Balanced creativity for industry-specific structures
        maxOutputTokens: 4000, // Complex hierarchical structures need more tokens
      });

      console.log(`[CategoryGenerationService] Generated MIS for ${profile.industry}/${profile.businessType} - Categories: ${object.totalCategories} - Tokens: ${usage.totalTokens}`);

      // Add metadata
      object.metadata = {
        generatedAt: new Date().toISOString(),
        version: '1.0.0',
        compliance: ['GAAP', 'IFRS']
      };

      return object;
    } catch (error: any) {
      console.error('[CategoryGenerationService] Generation failed:', error);
      
      // Return minimal fallback structure
      return this.getFallbackStructure(profile);
    }
  }

  /**
   * Stream MIS structure generation for real-time UI updates
   */
  async *streamGenerateCategoryHierarchy(
    profile: BusinessProfile
  ): AsyncGenerator<Partial<CategoryHierarchy>, CategoryHierarchy> {
    const prompt = this.buildStreamingPrompt(profile);

    try {
      const { partialObjectStream, object } = await streamObject({
        model: this.model,
        schema: categoryHierarchySchema,
        prompt,
        temperature: 0.4,
        maxOutputTokens: 3000,
      });

      // Yield partial results as they stream in
      for await (const partialObject of partialObjectStream) {
        yield partialObject;
      }

      // Return final complete object
      const finalResult = await object;
      console.log(`[CategoryGenerationService] Streaming complete - Total categories: ${finalResult.totalCategories}`);
      
      return finalResult;
    } catch (error: any) {
      console.error('[CategoryGenerationService] Streaming failed:', error);
      return this.getFallbackStructure(profile);
    }
  }

  /**
   * Optimize existing MIS structure based on usage patterns
   */
  async optimizeCategoryHierarchy(
    currentStructure: CategoryHierarchy,
    usageData: Array<{
      categoryId: string;
      frequency: number;
      accuracy: number;
      commonMisclassifications?: string[];
    }>
  ): Promise<CategoryHierarchy> {
    const optimizationSchema = z.object({
      optimizedStructure: categoryHierarchySchema,
      changes: z.array(z.object({
        type: z.enum(['add', 'remove', 'merge', 'split', 'rename']),
        description: z.string(),
        reason: z.string()
      })),
      expectedImprovement: z.number().min(0).max(1)
    });

    const usageSummary = usageData.slice(0, 20)
      .map(u => `${u.categoryId}: ${u.frequency} uses, ${(u.accuracy * 100).toFixed(1)}% accuracy`)
      .join('\n');

    try {
      const { object } = await generateObject({
        model: this.model,
        schema: optimizationSchema,
        prompt: `Optimize this MIS structure based on usage patterns:

Current Structure:
- Industry: ${currentStructure.industry}
- Total Categories: ${currentStructure.totalCategories}
- Max Depth: ${currentStructure.maxDepth}

Usage Data:
${usageSummary}

Optimize by:
1. Removing low-usage categories (< 1% frequency)
2. Splitting high-error categories (< 70% accuracy)
3. Merging similar low-frequency categories
4. Adding missing categories based on misclassifications
5. Simplifying overly complex hierarchies`,
        temperature: 0.3,
        maxOutputTokens: 3000,
      });

      console.log(`[CategoryGenerationService] Optimized structure - Expected improvement: ${(object.expectedImprovement * 100).toFixed(1)}%`);
      
      return object.optimizedStructure;
    } catch (error) {
      console.error('[CategoryGenerationService] Optimization failed:', error);
      return currentStructure; // Return original if optimization fails
    }
  }

  /**
   * Generate GL code mapping for a simplified category list
   */
  async generateGLCodeMapping(
    categories: string[],
    businessType: string
  ): Promise<Map<string, string>> {
    const glMappingSchema = z.object({
      mappings: z.array(z.object({
        category: z.string(),
        glCode: z.string(),
        accountType: z.enum(['asset', 'liability', 'equity', 'revenue', 'expense'])
      }))
    });

    try {
      const { object } = await generateObject({
        model: this.model,
        schema: glMappingSchema,
        prompt: `Generate GL codes for these categories in a ${businessType} business:

Categories:
${categories.join('\n')}

Assign appropriate GL codes following standard accounting practices:
- 1000-1999: Assets
- 2000-2999: Liabilities
- 3000-3999: Equity
- 4000-4999: Revenue
- 5000-9999: Expenses`,
        temperature: 0.2, // Low temperature for consistent GL codes
        maxOutputTokens: 1000,
      });

      const mapping = new Map<string, string>();
      object.mappings.forEach(m => {
        mapping.set(m.category, m.glCode);
      });

      return mapping;
    } catch (error) {
      console.error('[CategoryGenerationService] GL mapping failed:', error);
      return new Map();
    }
  }

  /**
   * Validate MIS structure completeness and compliance
   */
  async validateCategoryHierarchy(structure: CategoryHierarchy | null): Promise<{
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  }> {
    const validationSchema = z.object({
      isValid: z.boolean(),
      completenessScore: z.number().min(0).max(1),
      issues: z.array(z.string()),
      suggestions: z.array(z.string()),
      complianceStatus: z.object({
        gaap: z.boolean(),
        ifrs: z.boolean(),
        industryStandards: z.boolean()
      })
    });

    // Handle null structure
    if (!structure) {
      return {
        isValid: false,
        issues: ['No category hierarchy provided for validation'],
        suggestions: ['Generate a category hierarchy first']
      };
    }

    try {
      const { object } = await generateObject({
        model: this.model,
        schema: validationSchema,
        prompt: `Validate this MIS structure for completeness and compliance:

Structure Summary:
- Industry: ${structure.industry}
- Business Type: ${structure.businessType}
- Total Categories: ${structure.totalCategories}
- Max Depth: ${structure.maxDepth}
- Root Categories: ${structure.categories.map(c => c.name).join(', ')}

Check for:
1. All major financial statement categories present
2. Appropriate hierarchy depth (not too shallow or deep)
3. Industry-specific requirements met
4. GL code consistency and correctness
5. No duplicate or overlapping categories`,
        temperature: 0.2,
        maxOutputTokens: 1000,
      });

      return {
        isValid: object.isValid,
        issues: object.issues,
        suggestions: object.suggestions
      };
    } catch (error) {
      console.error('[CategoryGenerationService] Validation failed:', error);
      return {
        isValid: false,
        issues: ['Validation service unavailable'],
        suggestions: []
      };
    }
  }

  private buildStreamingPrompt(profile: BusinessProfile): string {
    return `Generate a comprehensive MIS structure for a ${profile.size} ${profile.businessType} business in the ${profile.industry} industry.

Include standard accounting categories with GL codes and 2-3 levels of hierarchy.
Focus on practical, industry-relevant categorization.`;
  }

  private getFallbackStructure(profile: BusinessProfile): CategoryHierarchy {
    return {
      industry: profile.industry,
      businessType: profile.businessType,
      categories: [
        {
          id: 'assets',
          name: 'Assets',
          glCode: { code: '1000', description: 'Total Assets', type: 'asset' },
          level: 0,
          parentId: null,
          subcategories: []
        },
        {
          id: 'liabilities',
          name: 'Liabilities',
          glCode: { code: '2000', description: 'Total Liabilities', type: 'liability' },
          level: 0,
          parentId: null,
          subcategories: []
        },
        {
          id: 'revenue',
          name: 'Revenue',
          glCode: { code: '4000', description: 'Total Revenue', type: 'revenue' },
          level: 0,
          parentId: null,
          subcategories: []
        },
        {
          id: 'expenses',
          name: 'Expenses',
          glCode: { code: '5000', description: 'Total Expenses', type: 'expense' },
          level: 0,
          parentId: null,
          subcategories: []
        }
      ],
      totalCategories: 4,
      maxDepth: 1,
      metadata: {
        generatedAt: new Date().toISOString(),
        version: '1.0.0-fallback',
        compliance: ['Basic']
      }
    };
  }
}

// Export singleton instance
export const categoryGenerationService = new CategoryGenerationService();