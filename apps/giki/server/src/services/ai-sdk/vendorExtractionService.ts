/**
 * Vendor Extraction Service using Vercel AI SDK
 * 
 * Extracts vendor names from transaction descriptions using structured outputs
 * with Zod schemas. No primitive JSON parsing - uses AI SDK's generateObject.
 * 
 * Model: gemini-2.0-flash-001
 */

import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { z } from 'zod';

// Load environment variables
import '../../utils/env-loader';

// Define vendor extraction schema
const vendorExtractionSchema = z.object({
  vendor: z.string().nullable().describe('The vendor/merchant name extracted from the description, or null if not found'),
  vendorType: z.enum(['retail', 'service', 'subscription', 'utility', 'financial', 'other']).optional().describe('Type of vendor if identifiable'),
  confidence: z.number().min(0).max(1).describe('Confidence score for the extraction')
});

export type VendorExtractionResult = z.infer<typeof vendorExtractionSchema>;

export class VendorExtractionService {
  private model;

  constructor(apiKey?: string) {
    // Initialize Google AI provider with API key
    const key = apiKey || process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENERATIVE_AI_API_KEY;
    if (!key) {
      console.warn('[VendorExtractionService] No API key found, service will use fallbacks');
    }
    this.model = google('gemini-2.0-flash-001', {
      apiKey: key,
      structuredOutputs: true
    });
  }

  /**
   * Extract vendor from a single transaction description
   */
  async extractVendor(description: string): Promise<VendorExtractionResult> {
    try {
      const { object, usage, finishReason } = await generateObject({
        model: this.model,
        schema: vendorExtractionSchema,
        prompt: `Extract the vendor/merchant name from this transaction description:
        
"${description}"

Focus on identifying:
- Company/business names (e.g., "Amazon", "Starbucks")
- Service providers (e.g., "Netflix", "Spotify")
- Utility companies (e.g., "Electric Company", "Water Board")
- Financial institutions (e.g., "Bank of America", "Chase")

Return null for vendor if no clear vendor name can be identified.`,
        temperature: 0.1, // Low temperature for consistent extraction
        maxOutputTokens: 100, // Vendor extraction doesn't need many tokens
      });

      // Log usage for monitoring
      console.log(`[VendorExtractionService] Extracted vendor from "${description.substring(0, 50)}..." - Tokens: ${usage.totalTokens}`);

      return object;
    } catch (error: any) {
      console.error('[VendorExtractionService] Extraction failed:', error);
      
      // Return safe fallback
      return {
        vendor: null,
        vendorType: 'other',
        confidence: 0
      };
    }
  }

  /**
   * Extract vendors from multiple transaction descriptions in batch
   * Uses parallel processing for efficiency
   */
  async extractVendorsBatch(descriptions: string[]): Promise<VendorExtractionResult[]> {
    console.log(`[VendorExtractionService] Processing batch of ${descriptions.length} descriptions`);
    
    // Process in parallel with concurrency limit to avoid rate limiting
    const BATCH_SIZE = 5;
    const results: VendorExtractionResult[] = [];
    
    for (let i = 0; i < descriptions.length; i += BATCH_SIZE) {
      const batch = descriptions.slice(i, i + BATCH_SIZE);
      const batchResults = await Promise.all(
        batch.map(desc => this.extractVendor(desc))
      );
      results.push(...batchResults);
      
      // Small delay between batches to respect rate limits
      if (i + BATCH_SIZE < descriptions.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    return results;
  }

  /**
   * Extract vendor with additional context for improved accuracy
   */
  async extractVendorWithContext(
    description: string,
    context: {
      amount?: number;
      type?: 'debit' | 'credit';
      date?: string;
      previousVendors?: string[];
    }
  ): Promise<VendorExtractionResult> {
    const contextPrompt = `Extract the vendor/merchant name from this transaction:

Description: "${description}"
${context.amount ? `Amount: ${context.amount}` : ''}
${context.type ? `Type: ${context.type}` : ''}
${context.date ? `Date: ${context.date}` : ''}
${context.previousVendors?.length ? `Common vendors in this account: ${context.previousVendors.slice(0, 5).join(', ')}` : ''}

Consider the context to improve extraction accuracy. For example:
- Large amounts might indicate utility bills or rent
- Small amounts might be retail or food purchases
- Recurring amounts might be subscriptions`;

    try {
      const { object, usage } = await generateObject({
        model: this.model,
        schema: vendorExtractionSchema,
        prompt: contextPrompt,
        temperature: 0.2, // Slightly higher for context consideration
        maxOutputTokens: 150,
      });

      console.log(`[VendorExtractionService] Context-aware extraction - Tokens: ${usage.totalTokens}`);
      
      return object;
    } catch (error) {
      console.error('[VendorExtractionService] Context extraction failed:', error);
      // Fallback to simple extraction
      return this.extractVendor(description);
    }
  }

  /**
   * Validate and normalize vendor names for consistency
   */
  async normalizeVendor(vendorName: string): Promise<string> {
    const normalizationSchema = z.object({
      normalizedName: z.string().describe('The standardized vendor name'),
      isChain: z.boolean().describe('Whether this is a chain/franchise'),
      parentCompany: z.string().nullable().describe('Parent company if applicable')
    });

    try {
      const { object } = await generateObject({
        model: this.model,
        schema: normalizationSchema,
        prompt: `Normalize this vendor name to its standard form:
        
"${vendorName}"

Rules:
- Use the official company name (e.g., "AMZN" → "Amazon")
- Remove location suffixes unless it's a franchise (e.g., "Starbucks #1234" → "Starbucks")
- Standardize common abbreviations (e.g., "PYMT" → "Payment")
- Keep franchise locations if relevant`,
        temperature: 0.1,
        maxOutputTokens: 100,
      });

      return object.normalizedName;
    } catch (error) {
      console.error('[VendorExtractionService] Normalization failed:', error);
      return vendorName; // Return original if normalization fails
    }
  }
}

// Export singleton instance
export const vendorExtractionService = new VendorExtractionService();