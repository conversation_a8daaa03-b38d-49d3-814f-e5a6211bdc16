{"name": "@workspace/giki-server", "version": "0.1.0", "description": "Giki AI BHVR TypeScript backend server", "type": "module", "main": "src/index.ts", "scripts": {"dev": "PORT=8000 bun run --hot src/index.ts", "dev:strict": "PORT=8000 bun run --hot src/index.ts", "build": "bun build src/index.ts --outdir=dist --target=node", "build:strict": "tsc && bun build src/index.ts --outdir=dist --target=node", "start": "bun run dist/index.js", "preview": "bun run dist/index.js", "test": "bun test ../tests/", "test:unit": "bun test ../tests/unit/", "test:integration": "bun test ../tests/integration/", "test:e2e": "bun test ../tests/e2e/", "test:watch": "bun test --watch", "type-check": "tsc --noEmit", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "dependencies": {"@ai-sdk/google": "^2.0.5", "@effect/platform": "^0.90.0", "@effect/schema": "^0.75.5", "@google/genai": "^0.3.0", "@hono/zod-validator": "^0.4.1", "ai": "^5.0.11", "bcrypt": "^5.1.1", "dotenv": "^17.2.1", "drizzle-orm": "^0.29.3", "effect": "^3.17.6", "exceljs": "^4.4.0", "hono": "^4.6.7", "jsonwebtoken": "^9.0.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "postgres": "^3.4.3", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.25.1"}, "devDependencies": {"@types/bun": "latest", "@types/bcrypt": "^5.0.2", "drizzle-kit": "^0.20.14", "typescript": "^5.0.0"}}