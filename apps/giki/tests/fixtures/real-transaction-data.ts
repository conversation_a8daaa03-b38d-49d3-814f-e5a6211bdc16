/**
 * Real Transaction Test Data
 * 
 * Purpose: Provide realistic transaction scenarios for AI testing
 * Source: Anonymized real business transaction patterns
 * Usage: Integration tests, performance benchmarking, accuracy validation
 */

export interface TestTransactionScenario {
  businessType: string;
  industry: string;
  description: string;
  amount: number;
  type: 'debit' | 'credit';
  vendor?: string;
  expectedCategory: string;
  expectedGLCode: string;
  minConfidence: number;
  complexityLevel: 'simple' | 'medium' | 'complex';
  notes?: string;
}

export const realTransactionScenarios: TestTransactionScenario[] = [
  // Restaurant Business Scenarios
  {
    businessType: 'restaurant',
    industry: 'food_service',
    description: 'SYSCO FOOD SERVICES - WEEKLY FOOD SUPPLY',
    amount: -15670.45,
    type: 'debit',
    vendor: 'Sysco',
    expectedCategory: 'Cost of Goods Sold',
    expectedGLCode: '5000',
    minConfidence: 0.9,
    complexityLevel: 'simple',
    notes: 'Clear food supplier transaction'
  },
  {
    businessType: 'restaurant',
    industry: 'food_service',
    description: 'SQUARE POS MONTHLY SUBSCRIPTION',
    amount: -89.99,
    type: 'debit',
    vendor: 'Square',
    expectedCategory: 'Software Subscriptions',
    expectedGLCode: '5220',
    minConfidence: 0.85,
    complexityLevel: 'simple',
    notes: 'POS system subscription'
  },
  {
    businessType: 'restaurant',
    industry: 'food_service',
    description: 'ZOMATO COMMISSION - ONLINE ORDERS',
    amount: -2340.67,
    type: 'debit',
    vendor: 'Zomato',
    expectedCategory: 'Marketing & Advertising',
    expectedGLCode: '5800',
    minConfidence: 0.7,
    complexityLevel: 'medium',
    notes: 'Could be classified as commission or marketing'
  },

  // Technology Startup Scenarios
  {
    businessType: 'technology',
    industry: 'software',
    description: 'AMAZON WEB SERVICES - CLOUD HOSTING',
    amount: -1245.89,
    type: 'debit',
    vendor: 'Amazon Web Services',
    expectedCategory: 'Software Subscriptions',
    expectedGLCode: '5220',
    minConfidence: 0.95,
    complexityLevel: 'simple',
    notes: 'Clear cloud infrastructure expense'
  },
  {
    businessType: 'technology',
    industry: 'software',
    description: 'GITHUB ENTERPRISE ANNUAL SUBSCRIPTION',
    amount: -2100.00,
    type: 'debit',
    vendor: 'GitHub',
    expectedCategory: 'Software Subscriptions',
    expectedGLCode: '5220',
    minConfidence: 0.9,
    complexityLevel: 'simple',
    notes: 'Development tools subscription'
  },
  {
    businessType: 'technology',
    industry: 'software',
    description: 'FREELANCER PAYMENT - UI/UX DESIGN',
    amount: -25000.00,
    type: 'debit',
    vendor: 'Freelancer',
    expectedCategory: 'Professional Services',
    expectedGLCode: '5300',
    minConfidence: 0.8,
    complexityLevel: 'medium',
    notes: 'Could be professional services or contract labor'
  },

  // Manufacturing Business Scenarios
  {
    businessType: 'manufacturing',
    industry: 'electronics',
    description: 'ALIBABA ELECTRONIC COMPONENTS BULK ORDER',
    amount: -67890.23,
    type: 'debit',
    vendor: 'Alibaba',
    expectedCategory: 'Raw Materials',
    expectedGLCode: '5010',
    minConfidence: 0.85,
    complexityLevel: 'simple',
    notes: 'Clear raw materials purchase'
  },
  {
    businessType: 'manufacturing',
    industry: 'electronics',
    description: 'INDUSTRIAL EQUIPMENT LEASE MONTHLY',
    amount: -12500.00,
    type: 'debit',
    vendor: 'Equipment Leasing Co',
    expectedCategory: 'Equipment & Machinery',
    expectedGLCode: '1500',
    minConfidence: 0.9,
    complexityLevel: 'simple',
    notes: 'Equipment lease payment'
  },
  {
    businessType: 'manufacturing',
    industry: 'electronics',
    description: 'FACTORY ELECTRICITY BILL - HIGH VOLTAGE',
    amount: -8765.43,
    type: 'debit',
    vendor: 'State Electricity Board',
    expectedCategory: 'Utilities',
    expectedGLCode: '5500',
    minConfidence: 0.95,
    complexityLevel: 'simple',
    notes: 'Utility expense'
  },

  // Consulting Business Scenarios
  {
    businessType: 'consulting',
    industry: 'business_services',
    description: 'CLIENT RETAINER PAYMENT RECEIVED',
    amount: 50000.00,
    type: 'credit',
    vendor: 'Client ABC Corp',
    expectedCategory: 'Consulting Revenue',
    expectedGLCode: '4100',
    minConfidence: 0.9,
    complexityLevel: 'simple',
    notes: 'Revenue from consulting services'
  },
  {
    businessType: 'consulting',
    industry: 'business_services',
    description: 'BUSINESS DEVELOPMENT TRAVEL - CLIENT VISIT',
    amount: -15670.88,
    type: 'debit',
    vendor: 'Various Travel',
    expectedCategory: 'Travel Expenses',
    expectedGLCode: '5600',
    minConfidence: 0.85,
    complexityLevel: 'simple',
    notes: 'Business travel expense'
  },
  {
    businessType: 'consulting',
    industry: 'business_services',
    description: 'COWORKING SPACE MONTHLY MEMBERSHIP',
    amount: -12000.00,
    type: 'debit',
    vendor: 'WeWork',
    expectedCategory: 'Rent & Lease',
    expectedGLCode: '5200',
    minConfidence: 0.8,
    complexityLevel: 'medium',
    notes: 'Could be rent or office expenses'
  },

  // Retail Business Scenarios
  {
    businessType: 'retail',
    industry: 'clothing',
    description: 'WHOLESALE CLOTHING SUPPLIER - WINTER STOCK',
    amount: -89345.67,
    type: 'debit',
    vendor: 'Fashion Wholesale Inc',
    expectedCategory: 'Inventory Purchase',
    expectedGLCode: '5020',
    minConfidence: 0.9,
    complexityLevel: 'simple',
    notes: 'Inventory purchase for retail'
  },
  {
    businessType: 'retail',
    industry: 'clothing',
    description: 'SHOPIFY MONTHLY ECOMMERCE SUBSCRIPTION',
    amount: -299.00,
    type: 'debit',
    vendor: 'Shopify',
    expectedCategory: 'Software Subscriptions',
    expectedGLCode: '5220',
    minConfidence: 0.9,
    complexityLevel: 'simple',
    notes: 'Ecommerce platform subscription'
  },
  {
    businessType: 'retail',
    industry: 'clothing',
    description: 'INSTAGRAM ADS CAMPAIGN - SUMMER COLLECTION',
    amount: -5600.00,
    type: 'debit',
    vendor: 'Facebook/Instagram',
    expectedCategory: 'Marketing & Advertising',
    expectedGLCode: '5800',
    minConfidence: 0.95,
    complexityLevel: 'simple',
    notes: 'Social media advertising'
  },

  // Complex/Ambiguous Scenarios
  {
    businessType: 'mixed',
    industry: 'various',
    description: 'PAYMENT TO CONTRACTOR - MULTIPLE SERVICES',
    amount: -45000.00,
    type: 'debit',
    vendor: 'XYZ Contractor',
    expectedCategory: 'Professional Services',
    expectedGLCode: '5300',
    minConfidence: 0.6,
    complexityLevel: 'complex',
    notes: 'Ambiguous - could be professional services, maintenance, or other'
  },
  {
    businessType: 'mixed',
    industry: 'various',
    description: 'BANK TRANSFER - INTERNAL ACCOUNT MOVEMENT',
    amount: -10000.00,
    type: 'debit',
    vendor: 'Same Bank',
    expectedCategory: 'Bank Transfers',
    expectedGLCode: '1000',
    minConfidence: 0.7,
    complexityLevel: 'complex',
    notes: 'Internal transfer - not an expense'
  },
  {
    businessType: 'mixed',
    industry: 'various',
    description: 'REFUND PROCESSED TO CUSTOMER',
    amount: -2500.00,
    type: 'debit',
    vendor: 'Customer Return',
    expectedCategory: 'Sales Returns',
    expectedGLCode: '4150',
    minConfidence: 0.8,
    complexityLevel: 'medium',
    notes: 'Contra-revenue account'
  },

  // Indian Business Specific Scenarios
  {
    businessType: 'services',
    industry: 'indian_business',
    description: 'GST PAYMENT - QUARTERLY FILING',
    amount: -25670.89,
    type: 'debit',
    vendor: 'GSTN',
    expectedCategory: 'Taxes & Licenses',
    expectedGLCode: '5900',
    minConfidence: 0.95,
    complexityLevel: 'simple',
    notes: 'GST tax payment'
  },
  {
    businessType: 'services',
    industry: 'indian_business',
    description: 'PROVIDENT FUND CONTRIBUTION - EMPLOYEES',
    amount: -15890.45,
    type: 'debit',
    vendor: 'EPFO',
    expectedCategory: 'Employee Benefits',
    expectedGLCode: '5150',
    minConfidence: 0.9,
    complexityLevel: 'simple',
    notes: 'Statutory employee contribution'
  },
  {
    businessType: 'services',
    industry: 'indian_business',
    description: 'CHARTERED ACCOUNTANT FEES - ANNUAL AUDIT',
    amount: -35000.00,
    type: 'debit',
    vendor: 'CA Firm',
    expectedCategory: 'Professional Services',
    expectedGLCode: '5300',
    minConfidence: 0.85,
    complexityLevel: 'simple',
    notes: 'Professional accounting services'
  }
];

/**
 * Get test scenarios by business type
 */
export function getScenariosByBusinessType(businessType: string): TestTransactionScenario[] {
  return realTransactionScenarios.filter(scenario => 
    scenario.businessType === businessType || scenario.businessType === 'mixed'
  );
}

/**
 * Get test scenarios by complexity level
 */
export function getScenariosByComplexity(complexityLevel: 'simple' | 'medium' | 'complex'): TestTransactionScenario[] {
  return realTransactionScenarios.filter(scenario => scenario.complexityLevel === complexityLevel);
}

/**
 * Get high confidence scenarios (>= 0.8)
 */
export function getHighConfidenceScenarios(): TestTransactionScenario[] {
  return realTransactionScenarios.filter(scenario => scenario.minConfidence >= 0.8);
}

/**
 * Get challenging scenarios (< 0.8 confidence)
 */
export function getChallengingScenarios(): TestTransactionScenario[] {
  return realTransactionScenarios.filter(scenario => scenario.minConfidence < 0.8);
}

/**
 * Standard test categories matching our system
 */
export const standardTestCategories = [
  { id: 'cat_cogs', name: 'Cost of Goods Sold', glCode: '5000' },
  { id: 'cat_raw_materials', name: 'Raw Materials', glCode: '5010' },
  { id: 'cat_inventory', name: 'Inventory Purchase', glCode: '5020' },
  { id: 'cat_salaries', name: 'Salaries & Wages', glCode: '5100' },
  { id: 'cat_employee_benefits', name: 'Employee Benefits', glCode: '5150' },
  { id: 'cat_rent', name: 'Rent & Lease', glCode: '5200' },
  { id: 'cat_software', name: 'Software Subscriptions', glCode: '5220' },
  { id: 'cat_professional', name: 'Professional Services', glCode: '5300' },
  { id: 'cat_office_supplies', name: 'Office Supplies', glCode: '5400' },
  { id: 'cat_utilities', name: 'Utilities', glCode: '5500' },
  { id: 'cat_travel', name: 'Travel Expenses', glCode: '5600' },
  { id: 'cat_insurance', name: 'Insurance', glCode: '5650' },
  { id: 'cat_meals', name: 'Meals & Entertainment', glCode: '5700' },
  { id: 'cat_marketing', name: 'Marketing & Advertising', glCode: '5800' },
  { id: 'cat_taxes', name: 'Taxes & Licenses', glCode: '5900' },
  { id: 'cat_equipment', name: 'Equipment & Machinery', glCode: '1500' },
  { id: 'cat_consulting_revenue', name: 'Consulting Revenue', glCode: '4100' },
  { id: 'cat_sales_returns', name: 'Sales Returns', glCode: '4150' },
  { id: 'cat_bank_transfers', name: 'Bank Transfers', glCode: '1000' }
];

/**
 * Performance test scenarios - designed for batch processing
 */
export const performanceTestTransactions = [
  { description: 'OFFICE SUPPLIES - STAPLES BULK ORDER', amount: -567.89, type: 'debit' as const },
  { description: 'GOOGLE WORKSPACE BUSINESS SUBSCRIPTION', amount: -720.00, type: 'debit' as const },
  { description: 'ELECTRICITY BILL - COMMERCIAL UNIT', amount: -2340.56, type: 'debit' as const },
  { description: 'LEGAL CONSULTATION - CONTRACT REVIEW', amount: -8500.00, type: 'debit' as const },
  { description: 'MARKETING AGENCY - SOCIAL MEDIA CAMPAIGN', amount: -15000.00, type: 'debit' as const },
  { description: 'INSURANCE PREMIUM - BUSINESS LIABILITY', amount: -4560.00, type: 'debit' as const },
  { description: 'RENT PAYMENT - OFFICE SPACE MUMBAI', amount: -25000.00, type: 'debit' as const },
  { description: 'SOFTWARE LICENSE - ANNUAL RENEWAL', amount: -12000.00, type: 'debit' as const },
  { description: 'TRAVEL EXPENSES - CLIENT MEETING DELHI', amount: -3450.78, type: 'debit' as const },
  { description: 'FREELANCER PAYMENT - WEBSITE DEVELOPMENT', amount: -18000.00, type: 'debit' as const },
  { description: 'PHONE BILL - CORPORATE PLAN', amount: -890.45, type: 'debit' as const },
  { description: 'BANK CHARGES - TRANSACTION FEES', amount: -234.00, type: 'debit' as const },
  { description: 'STATIONERY SUPPLIES - MONTHLY ORDER', amount: -456.78, type: 'debit' as const },
  { description: 'INTERNET SERVICE - BROADBAND PLAN', amount: -1200.00, type: 'debit' as const },
  { description: 'AUDIT FEES - CHARTERED ACCOUNTANT', amount: -35000.00, type: 'debit' as const }
];

export default {
  realTransactionScenarios,
  standardTestCategories,
  performanceTestTransactions,
  getScenariosByBusinessType,
  getScenariosByComplexity,
  getHighConfidenceScenarios,
  getChallengingScenarios
};