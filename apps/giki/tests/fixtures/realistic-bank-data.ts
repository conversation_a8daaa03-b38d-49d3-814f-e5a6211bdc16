/**
 * Realistic Bank Statement Test Data
 * 
 * These are actual transaction formats from various banks
 * with all the messiness of real-world data
 */

export const REALISTIC_BANK_TRANSACTIONS = {
  // HDFC Bank format
  hdfc: [
    { date: "12/03/2024", description: "UPI-ZOMATO-ZOMATO@PAYTM-PYTM0123456-************-Payment for order", amount: 367.00, type: 'debit' },
    { date: "12/03/2024", description: "POS 425516XXXXXX8765 SHELL INDIA MARKETS", amount: 2500.00, type: 'debit' },
    { date: "11/03/2024", description: "NEFT CR-YESB0000123-SALARY CREDIT-ABC COMPANY LTD", amount: 75000.00, type: 'credit' },
    { date: "10/03/2024", description: "ATW-424419XXXXXX1234 S1 ATM WDL", amount: 10000.00, type: 'debit' },
    { date: "09/03/2024", description: "EMI-HDFC BANK LOAN-*********", amount: 25430.00, type: 'debit' },
    { date: "08/03/2024", description: "IB FUNDS TRANSFER CR-***********", amount: 15000.00, type: 'credit' },
    { date: "07/03/2024", description: "ACH D- AUFINANCIERSINDIALTD-LVPXXXXXXX123", amount: 18500.00, type: 'debit' }
  ],

  // ICICI Bank format
  icici: [
    { date: "2024-03-15", description: "BIL/001234/Big Basket/BigB", amount: 1847.23, type: 'debit' },
    { date: "2024-03-14", description: "MMT/IMPS/************/Rent March/JOHN DOE", amount: 35000.00, type: 'debit' },
    { date: "2024-03-13", description: "NFS/POS/********/DECATHLON SPORTS IN", amount: 4599.00, type: 'debit' },
    { date: "2024-03-12", description: "UPI/************/Gpay/swiggy/swiggy@axisbank", amount: 289.50, type: 'debit' },
    { date: "2024-03-11", description: "INB/NEFT/************/0/STATE BANK OF INDIA", amount: 50000.00, type: 'credit' },
    { date: "2024-03-10", description: "SI-TATASKY DTH ********", amount: 599.00, type: 'debit' },
    { date: "2024-03-09", description: "FD INTEREST CREDIT", amount: 2341.00, type: 'credit' }
  ],

  // Axis Bank format
  axis: [
    { date: "15-Mar-24", description: "DEBIT-XXXX1234-RAPIDO", amount: 67.00, type: 'debit' },
    { date: "14-Mar-24", description: "Credit Card Payment XXXX5678", amount: 45230.00, type: 'debit' },
    { date: "13-Mar-24", description: "BULK POSTING- 00********5678 GOOGLE CLOUD", amount: 7823.45, type: 'debit' },
    { date: "12-Mar-24", description: "BY INST 12345 : CLG", amount: 12000.00, type: 'credit' },
    { date: "11-Mar-24", description: "TO TRANSFER-UPI/CR/************/PAYTM/ELECTRICITY", amount: 1890.00, type: 'debit' },
    { date: "10-Mar-24", description: "INET BANKING CMS-MAR SALARY", amount: 125000.00, type: 'credit' },
    { date: "09-Mar-24", description: "PUR:4765XXXX1234 AMAZON RETAIL INDIA", amount: 3456.78, type: 'debit' }
  ],

  // SBI format
  sbi: [
    { date: "15/03/24", description: "UPI-DR LALS PATH LABS-DRLAL.********-@ybl-YESB0YBLUPI-************-NA", amount: 1200.00, type: 'debit' },
    { date: "14/03/24", description: "TRANSFER TO SB A/C LINKED TO MOBILE", amount: 5000.00, type: 'debit' },
    { date: "13/03/24", description: "BY TRANSFER-INB IMPS*123456*MR SHARMA*HDFC", amount: 20000.00, type: 'credit' },
    { date: "12/03/24", description: "ACH D- PHONEPE-BBPSBILPAY", amount: 899.00, type: 'debit' },
    { date: "11/03/24", description: "CREDIT INTEREST--", amount: 523.00, type: 'credit' },
    { date: "10/03/24", description: "MINIMUM BALANCE CHARGES INCL GST", amount: 295.00, type: 'debit' },
    { date: "09/03/24", description: "ATM WDL-SBI ATM CONNAUGHT PLACE NEW DELHI", amount: 5000.00, type: 'debit' }
  ],

  // Kotak Bank format
  kotak: [
    { date: "15-03-2024", description: "VPS PM CARES FUND VPA pmcares@sbi NA Ref ************", amount: 500.00, type: 'debit' },
    { date: "14-03-2024", description: "PCA MAKEMYTRIP INDIA P MAKEMYTRIP  NOIDA ********90", amount: 12450.00, type: 'debit' },
    { date: "13-03-2024", description: "IMP ************ FROM Mr John Doe Salary Credit", amount: 95000.00, type: 'credit' },
    { date: "12-03-2024", description: "BRN CHRG INC GST", amount: 236.00, type: 'debit' },
    { date: "11-03-2024", description: "VIN VPA **********@paytm BLUEDART EXPRESS Ref ************", amount: 125.00, type: 'debit' },
    { date: "10-03-2024", description: "NWD 485016******1234 + ATM CASH", amount: 10000.00, type: 'debit' },
    { date: "09-03-2024", description: "ACH BAJAJ FINANCE EMI", amount: 8976.00, type: 'debit' }
  ],

  // American Express format
  amex: [
    { date: "15-MAR-2024", description: "UBER INDIA SYSTEMS PVT HELP.UBER.COM", amount: 234.56, type: 'debit' },
    { date: "14-MAR-2024", description: "THE LEELA PALACE NEW DELHI IN", amount: 15678.90, type: 'debit' },
    { date: "13-MAR-2024", description: "AMAZON.IN ORDER 404-1234567-8901234", amount: 2345.67, type: 'debit' },
    { date: "12-MAR-2024", description: "PAYMENT RECEIVED - THANK YOU", amount: 50000.00, type: 'credit' },
    { date: "11-MAR-2024", description: "NETFLIX.COM NETFLIX.COM CA", amount: 649.00, type: 'debit' },
    { date: "10-MAR-2024", description: "FOREIGN TRANSACTION FEE", amount: 123.45, type: 'debit' },
    { date: "09-MAR-2024", description: "ANNUAL MEMBERSHIP FEE", amount: 4500.00, type: 'debit' }
  ],

  // Messy/Edge Cases
  messy: [
    { date: "15/3/24", description: "???????????? POS TXN", amount: 1234.56, type: 'debit' },
    { date: "14-03-2024", description: "UPI/P2M/************///", amount: 99.00, type: 'debit' },
    { date: "13/03/2024", description: "CHRG FOR SMS ALRTS Q1", amount: 59.00, type: 'debit' },
    { date: "12/03/2024", description: "REV-UPI/************/Payment reversal", amount: 500.00, type: 'credit' },
    { date: "11/03/2024", description: "   SPACES   EVERYWHERE   CAFE   ", amount: 150.00, type: 'debit' },
    { date: "10/03/2024", description: "********90********90 LONG REFERENCE NUMBER PAYMENT", amount: 999.99, type: 'debit' },
    { date: "09/03/2024", description: "GST@18% ON CHARGES", amount: 42.30, type: 'debit' },
    { date: "08/03/2024", description: ".COM PAYMENT", amount: 299.00, type: 'debit' },
    { date: "07/03/2024", description: "NULL", amount: 100.00, type: 'debit' },
    { date: "06/03/2024", description: "", amount: 50.00, type: 'debit' }
  ]
};

// Most challenging real-world descriptions
export const CHALLENGING_DESCRIPTIONS = [
  "UPI-ZOMATO-ZOMATO@PAYTM-PYTM0123456-************-Payment for order",
  "POS 425516XXXXXX8765 SHELL INDIA MARKETS",
  "NEFT CR-YESB0000123-SALARY CREDIT-ABC COMPANY LTD",
  "ATW-424419XXXXXX1234 S1 ATM WDL",
  "EMI-HDFC BANK LOAN-*********",
  "ACH D- AUFINANCIERSINDIALTD-LVPXXXXXXX123",
  "BIL/001234/Big Basket/BigB",
  "MMT/IMPS/************/Rent March/JOHN DOE",
  "UPI/************/Gpay/swiggy/swiggy@axisbank",
  "SI-TATASKY DTH ********",
  "BULK POSTING- 00********5678 GOOGLE CLOUD",
  "TO TRANSFER-UPI/CR/************/PAYTM/ELECTRICITY",
  "UPI-DR LALS PATH LABS-DRLAL.********-@ybl-YESB0YBLUPI-************-NA",
  "ACH D- PHONEPE-BBPSBILPAY",
  "MINIMUM BALANCE CHARGES INCL GST",
  "VPS PM CARES FUND VPA pmcares@sbi NA Ref ************",
  "VIN VPA **********@paytm BLUEDART EXPRESS Ref ************",
  "???????????? POS TXN",
  "REV-UPI/************/Payment reversal",
  "   SPACES   EVERYWHERE   CAFE   "
];

// Common Indian business categories based on real transactions
export const INDIAN_BUSINESS_CATEGORIES = [
  { id: 'food', name: 'Food & Dining', glCode: '5100', examples: ['Zomato', 'Swiggy', 'Cafe', 'Restaurant'] },
  { id: 'fuel', name: 'Fuel & Transportation', glCode: '5200', examples: ['Shell', 'Petrol', 'Diesel', 'Uber', 'Ola', 'Rapido'] },
  { id: 'salary', name: 'Salary & Income', glCode: '4000', examples: ['Salary', 'Credit', 'Interest'] },
  { id: 'utilities', name: 'Utilities & Bills', glCode: '5300', examples: ['Electricity', 'DTH', 'Tatasky', 'Internet'] },
  { id: 'emi', name: 'EMI & Loans', glCode: '2100', examples: ['EMI', 'Loan', 'Finance'] },
  { id: 'shopping', name: 'Shopping & Retail', glCode: '5400', examples: ['Amazon', 'Big Basket', 'Decathlon'] },
  { id: 'healthcare', name: 'Healthcare', glCode: '5500', examples: ['Path Labs', 'Hospital', 'Pharmacy'] },
  { id: 'travel', name: 'Travel & Hotels', glCode: '5600', examples: ['MakeMyTrip', 'Hotel', 'Flight'] },
  { id: 'transfer', name: 'Transfers', glCode: '1200', examples: ['Transfer', 'IMPS', 'NEFT'] },
  { id: 'fees', name: 'Bank Fees & Charges', glCode: '5700', examples: ['Charges', 'GST', 'Fee'] },
  { id: 'atm', name: 'ATM Withdrawals', glCode: '1100', examples: ['ATM', 'WDL', 'Cash'] },
  { id: 'subscription', name: 'Subscriptions', glCode: '5800', examples: ['Netflix', 'Google Cloud', 'AWS'] },
  { id: 'rent', name: 'Rent', glCode: '5900', examples: ['Rent', 'Lease'] },
  { id: 'donation', name: 'Donations', glCode: '6000', examples: ['PM Cares', 'Donation', 'Charity'] }
];

// Schema patterns from different banks
export const BANK_SCHEMAS = {
  hdfc: {
    columns: ['Date', 'Narration', 'Chq./Ref.No.', 'Value Dt', 'Withdrawal Amt.', 'Deposit Amt.', 'Closing Balance'],
    dateFormat: 'DD/MM/YYYY',
    hasHeaders: true,
    transactionType: 'separate_debit_credit'
  },
  icici: {
    columns: ['S No.', 'Value Date', 'Transaction Date', 'Cheque Number', 'Transaction Remarks', 'Withdrawal Amount (INR )', 'Deposit Amount (INR )', 'Balance (INR )'],
    dateFormat: 'DD/MM/YYYY',
    hasHeaders: true,
    transactionType: 'separate_debit_credit'
  },
  axis: {
    columns: ['Tran Date', 'CHQNO', 'Particulars', 'DR', 'CR', 'BAL', 'SOL'],
    dateFormat: 'DD-MMM-YY',
    hasHeaders: true,
    transactionType: 'separate_debit_credit'
  },
  sbi: {
    columns: ['Txn Date', 'Value Date', 'Description', 'Ref No./Cheque No.', 'Branch Code', 'Debit', 'Credit', 'Balance'],
    dateFormat: 'DD/MM/YY',
    hasHeaders: true,
    transactionType: 'separate_debit_credit'
  },
  kotak: {
    columns: ['Date', 'Particulars', 'Cheque', 'Amount', 'Dr/Cr', 'Balance'],
    dateFormat: 'DD-MM-YYYY',
    hasHeaders: true,
    transactionType: 'signed_amount'
  }
};

// Generate CSV-like data for testing schema interpretation
export function generateBankCSV(bank: keyof typeof BANK_SCHEMAS, transactions: any[]): string {
  const schema = BANK_SCHEMAS[bank];
  let csv = schema.columns.join(',') + '\n';
  
  transactions.forEach((txn, idx) => {
    const row = [];
    schema.columns.forEach(col => {
      if (col.includes('Date')) {
        row.push(txn.date);
      } else if (col.includes('Narration') || col.includes('Remarks') || col.includes('Particulars') || col.includes('Description')) {
        row.push(`"${txn.description}"`);
      } else if (col.includes('Withdrawal') || col.includes('DR') || col.includes('Debit')) {
        row.push(txn.type === 'debit' ? txn.amount : '');
      } else if (col.includes('Deposit') || col.includes('CR') || col.includes('Credit')) {
        row.push(txn.type === 'credit' ? txn.amount : '');
      } else if (col.includes('Amount')) {
        row.push(txn.amount);
      } else if (col.includes('Dr/Cr')) {
        row.push(txn.type === 'debit' ? 'Dr' : 'Cr');
      } else if (col.includes('Balance')) {
        row.push(Math.random() * 100000);
      } else if (col.includes('S No')) {
        row.push(idx + 1);
      } else {
        row.push('');
      }
    });
    csv += row.join(',') + '\n';
  });
  
  return csv;
}