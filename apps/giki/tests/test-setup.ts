/**
 * Test Setup for Giki AI Integration Tests
 */

// Load environment variables
import '../../../lib/env-loader.js';

// Set test environment
process.env.NODE_ENV = 'test';
process.env.GEMINI_API_KEY = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || '';

// Test utilities
export const testLogger = {
  info: (message: string, data?: any) => {
    console.log(`ℹ️  ${message}`, data ? JSON.stringify(data, null, 2) : '');
  },
  success: (message: string, data?: any) => {
    console.log(`✅ ${message}`, data ? JSON.stringify(data, null, 2) : '');
  },
  error: (message: string, error?: any) => {
    console.error(`❌ ${message}`, error);
  },
  progress: (message: string, percent: number) => {
    console.log(`⏳ ${message} [${percent}%]`);
  }
};

// Export test configuration
export const testConfig = {
  timeout: 30000, // 30 seconds for AI calls
  retries: 2,
  verbose: process.env.VERBOSE === 'true'
};