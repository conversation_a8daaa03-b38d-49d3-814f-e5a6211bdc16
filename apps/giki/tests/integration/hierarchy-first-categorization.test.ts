/**
 * Test hierarchy-first categorization approach
 * 
 * Verifies that transactions are categorized using predefined 
 * hierarchies instead of generating categories on the fly.
 */

import { describe, test, expect, beforeAll } from 'bun:test';
import { categoryMappingService } from '../../server/src/services/ai-sdk/categoryMappingService';
import { hierarchyService } from '../../server/src/services/hierarchy/hierarchyService';

describe('Hierarchy-First Categorization', () => {

  test('Should use predefined SaaS hierarchy for categorization', async () => {
    const transaction = {
      id: 'test-1',
      description: 'AMAZON WEB SERVICES - EC2 HOSTING',
      amount: 450.75,
      type: 'debit' as const,
      vendor: 'AWS'
    };

    const businessContext = {
      industry: 'Technology',
      businessType: 'saas' as const,
      size: 'medium' as const
    };

    console.log('🧪 Testing hierarchy-first categorization for SaaS business');
    
    // Test the new hierarchy-first method
    const result = await categoryMappingService.categorizeTransactionWithHierarchy(
      transaction,
      businessContext,
      'test-user'
    );

    console.log('✅ Hierarchy-first result:', {
      category: result.categoryName,
      fullPath: result.fullPath,
      categoryPath: result.categoryPath,
      level: result.level,
      confidence: `${(result.confidence * 100).toFixed(1)}%`,
      reasoning: result.reasoning
    });

    // Verify hierarchy-specific expectations
    expect(result.categoryName).toBeTruthy();
    expect(result.fullPath).toContain('→'); // Should have hierarchy arrows
    expect(result.categoryPath).toBeInstanceOf(Array);
    expect(result.categoryPath.length).toBeGreaterThan(1); // Multi-level hierarchy
    expect(result.level).toBeGreaterThanOrEqual(1); // Not root level
    expect(result.confidence).toBeGreaterThan(0.7);

    // Should be appropriate for hosting costs in SaaS business
    expect(
      result.fullPath.toLowerCase().includes('hosting') || 
      result.fullPath.toLowerCase().includes('infrastructure') ||
      result.fullPath.toLowerCase().includes('cost of revenue')
    ).toBe(true);
  });

  test('Should use predefined Retail hierarchy for different business type', async () => {
    const transaction = {
      id: 'test-2', 
      description: 'SQUARE *PAYMENT PROCESSING FEE',
      amount: 25.40,
      type: 'debit' as const,
      vendor: 'Square'
    };

    const businessContext = {
      industry: 'E-commerce',
      businessType: 'retail' as const,
      size: 'small' as const
    };

    console.log('🧪 Testing hierarchy-first categorization for Retail business');
    
    const result = await categoryMappingService.categorizeTransactionWithHierarchy(
      transaction,
      businessContext,
      'test-user-2'
    );

    console.log('✅ Hierarchy-first result:', {
      category: result.categoryName,
      fullPath: result.fullPath,
      categoryPath: result.categoryPath,
      level: result.level,
      confidence: `${(result.confidence * 100).toFixed(1)}%`
    });

    // Verify retail-specific categorization
    expect(result.categoryName).toBeTruthy();
    expect(result.fullPath).toContain('→');
    expect(result.categoryPath.length).toBeGreaterThan(1);
    
    // Should be appropriate for payment processing in retail
    expect(
      result.fullPath.toLowerCase().includes('processing') ||
      result.fullPath.toLowerCase().includes('cost of goods sold') ||
      result.fullPath.toLowerCase().includes('operating')
    ).toBe(true);
  });

  test('Should get standard template hierarchy', async () => {
    console.log('🧪 Testing standard template retrieval');
    
    const hierarchy = await hierarchyService.getStandardTemplate('SaaS', 'saas');
    
    console.log('✅ Standard SaaS template:', {
      totalCategories: hierarchy.metadata.totalCategories,
      maxDepth: hierarchy.metadata.maxDepth,
      rootCategories: hierarchy.categories.map(c => c.name),
      source: hierarchy.source
    });

    expect(hierarchy.source).toBe('standard');
    expect(hierarchy.industry).toBe('SaaS');
    expect(hierarchy.businessType).toBe('saas');
    expect(hierarchy.categories.length).toBeGreaterThan(4); // Assets, Liabilities, Equity, Revenue, Expenses
    expect(hierarchy.metadata.totalCategories).toBeGreaterThan(15); // Should have detailed subcategories
    expect(hierarchy.metadata.maxDepth).toBeGreaterThanOrEqual(2); // Multi-level hierarchy
    
    // Should have SaaS-specific categories
    const flatCategories = hierarchy.categories.flatMap(c => 
      [c.name, ...(c.children || []).map(child => child.name)]
    );
    expect(flatCategories.some(name => 
      name.toLowerCase().includes('subscription') || 
      name.toLowerCase().includes('hosting') ||
      name.toLowerCase().includes('saas')
    )).toBe(true);
  });

  test('Should show different hierarchies for different industries', async () => {
    console.log('🧪 Testing industry-specific hierarchies');
    
    const saasHierarchy = await hierarchyService.getStandardTemplate('Technology', 'saas');
    const retailHierarchy = await hierarchyService.getStandardTemplate('E-commerce', 'retail');
    const consultingHierarchy = await hierarchyService.getStandardTemplate('Professional Services', 'consulting');

    console.log('✅ Industry comparison:', {
      saas: {
        categories: saasHierarchy.metadata.totalCategories,
        sample: saasHierarchy.categories[4]?.children?.[0]?.children?.[0]?.name || 'N/A'
      },
      retail: {
        categories: retailHierarchy.metadata.totalCategories,
        sample: retailHierarchy.categories[4]?.children?.[0]?.children?.[0]?.name || 'N/A'
      },
      consulting: {
        categories: consultingHierarchy.metadata.totalCategories,
        sample: consultingHierarchy.categories[4]?.children?.[0]?.children?.[0]?.name || 'N/A'
      }
    });

    // Each industry should have different category structures
    expect(saasHierarchy.metadata.totalCategories).not.toBe(retailHierarchy.metadata.totalCategories);
    expect(retailHierarchy.metadata.totalCategories).not.toBe(consultingHierarchy.metadata.totalCategories);
    
    // Should have industry-specific categories
    const saasCategories = JSON.stringify(saasHierarchy).toLowerCase();
    const retailCategories = JSON.stringify(retailHierarchy).toLowerCase();
    const consultingCategories = JSON.stringify(consultingHierarchy).toLowerCase();

    expect(saasCategories.includes('subscription') || saasCategories.includes('hosting')).toBe(true);
    expect(retailCategories.includes('inventory') || retailCategories.includes('product')).toBe(true);
    expect(consultingCategories.includes('consulting') || consultingCategories.includes('unbilled')).toBe(true);
  });
});