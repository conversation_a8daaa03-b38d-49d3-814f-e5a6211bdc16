import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { getGenAIService, TransactionCategorizationRequest, CategorizationResult } from '../../server/src/services/genAIService';
import { realTransactionScenarios, standardTestCategories, performanceTestTransactions } from '../fixtures/real-transaction-data';

/**
 * Real AI Integration Tests - One Test Per Prompt Pattern
 * 
 * Purpose: Test actual Google GenAI SDK integration with isolated prompts
 * Pattern: Each test focuses on one specific prompt for easy optimization
 * Chaining: Tests that depend on previous outputs are clearly marked
 * Evidence: Detailed logs of each AI processing step with prompt analysis
 * 
 * Customer Journey Focus:
 * 1. Business owner uploads diverse transaction types (Prompt 1: Basic Categorization)
 * 2. AI extracts vendor information (Prompt 2: Vendor Extraction) 
 * 3. AI processes with categorization intelligence (Prompt 3: Advanced Categorization)
 * 4. System generates MIS structure (Prompt 4: MIS Generation)
 * 5. System provides confidence scoring for decision-making (Prompt 5: Confidence Calibration)
 */

describe('Real AI Integration - One Test Per Prompt', () => {
  let genAIService: ReturnType<typeof getGenAIService>;
  
  // Shared test data for chained tests
  const testContext: {
    extractedVendors: Map<string, string>;
    categorizationResults: Map<string, CategorizationResult>;
    misStructures: Map<string, any>;
    performanceMetrics: Map<string, number>;
  } = {
    extractedVendors: new Map(),
    categorizationResults: new Map(),
    misStructures: new Map(),
    performanceMetrics: new Map()
  };

  beforeAll(() => {
    genAIService = getGenAIService();
    console.log('\n🚀 Starting Real AI Integration Tests - One Test Per Prompt');
    console.log('📊 Testing Google GenAI SDK with isolated prompt validation');
    console.log(`🔧 Environment: ${process.env.NODE_ENV || 'test'}`);
    console.log(`🤖 API Key Status: ${process.env.GEMINI_API_KEY ? 'Available' : 'Missing'}`);
  });

  afterAll(() => {
    console.log('\n✅ Real AI Integration Tests Completed');
    console.log('📈 Check logs above for detailed AI processing evidence');
    console.log('\n🔗 Test Chain Summary:');
    console.log(`   Vendors Extracted: ${testContext.extractedVendors.size}`);
    console.log(`   Categorizations: ${testContext.categorizationResults.size}`);
    console.log(`   MIS Structures: ${testContext.misStructures.size}`);
  });

  // Environment Setup Tests
  describe('Setup Validation', () => {
    test('Environment Setup - API Key Validation', () => {
      expect(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY).toBeTruthy();
      console.log('✅ API key validation passed');
    });

    test('GenAI Service Initialization', () => {
      expect(genAIService).toBeDefined();
      expect(typeof genAIService.categorizeTransaction).toBe('function');
      expect(typeof genAIService.extractVendorFromDescription).toBe('function');
      expect(typeof genAIService.generateMISStructure).toBe('function');
      console.log('✅ GenAI service initialized with all required methods');
    });
  });

  // Prompt 1: Vendor Extraction (Foundation for chaining)
  describe('Prompt 1: Vendor Extraction', () => {
    const vendorExtractionTests = [
      'AMAZON WEB SERVICES AWS MONTHLY SUBSCRIPTION',
      'STARBUCKS COFFEE SHOP MUMBAI BKC',
      'PAYTM PAYMENT GATEWAY MONTHLY FEES',
      'RELIANCE JIO BROADBAND INTERNET BILL',
      'UBER EATS FOOD DELIVERY SERVICE',
      'MICROSOFT OFFICE 365 BUSINESS PREMIUM'
    ];

    vendorExtractionTests.forEach((description, index) => {
      test(`Vendor Extraction ${index + 1}: "${description.substring(0, 30)}..."`, async () => {
        console.log(`\n🔍 PROMPT 1 TEST ${index + 1}: Vendor Extraction`);
        console.log(`📝 Transaction Description: "${description}"`);
        console.log(`🎯 Goal: Extract vendor name using AI`);
        
        const startTime = Date.now();
        const vendor = await genAIService.extractVendorFromDescription(description);
        const processingTime = Date.now() - startTime;
        
        console.log(`\n🤖 AI Vendor Extraction Result:`);
        console.log(`   Extracted Vendor: ${vendor || 'None detected'}`);
        console.log(`   Processing Time: ${processingTime}ms`);
        
        // Store for chaining to next tests
        if (vendor) {
          testContext.extractedVendors.set(description, vendor);
          console.log(`   📦 Stored vendor "${vendor}" for chaining`);
        }
        
        // Validation
        expect(vendor === null || typeof vendor === 'string').toBe(true);
        if (vendor) {
          expect(vendor.length).toBeGreaterThan(0);
          expect(vendor.length).toBeLessThan(50); // Reasonable vendor name length
        }
        expect(processingTime).toBeLessThan(5000);
        
        console.log(`   ✅ Vendor extraction validated`);
        console.log(`   ✅ Performance: ${processingTime}ms < 5000ms`);
        
      }, 10000);
    });
  });

  // Prompt 2: Basic Transaction Categorization (Isolated for prompt tuning)
  describe('Prompt 2: Basic Transaction Categorization', () => {
    const basicCategorizationTests = [
      {
        scenario: 'Simple Office Supply Purchase',
        description: 'STAPLES OFFICE SUPPLIES STORE',
        amount: -234.56,
        expectedCategory: 'Office Supplies'
      },
      {
        scenario: 'Cloud Software Subscription',
        description: 'AMAZON WEB SERVICES HOSTING',
        amount: -1245.67,
        expectedCategory: 'Software Subscriptions'
      },
      {
        scenario: 'Professional Legal Services',
        description: 'LEGAL CONSULTATION SERVICES',
        amount: -5000.00,
        expectedCategory: 'Professional Services'
      },
      {
        scenario: 'Business Travel Expense',
        description: 'AIRLINE TICKET BUSINESS TRIP',
        amount: -8950.00,
        expectedCategory: 'Travel Expenses'
      }
    ];

    basicCategorizationTests.forEach(({ scenario, description, amount, expectedCategory }, index) => {
      test(`Basic Categorization ${index + 1}: ${scenario}`, async () => {
        console.log(`\n🎯 PROMPT 2 TEST ${index + 1}: Basic Categorization`);
        console.log(`📋 Scenario: ${scenario}`);
        console.log(`📝 Description: "${description}"`);
        console.log(`💰 Amount: ${amount}`);
        console.log(`🎯 Expected: ${expectedCategory}`);
        
        const transaction: TransactionCategorizationRequest = {
          description,
          amount,
          type: 'debit',
          categories: standardTestCategories
        };
        
        const startTime = Date.now();
        const result = await genAIService.categorizeTransaction(transaction);
        const processingTime = Date.now() - startTime;
        
        console.log(`\n🤖 AI Categorization Result:`);
        console.log(`   Category: ${result.categoryName}`);
        console.log(`   Confidence: ${(result.confidence * 100).toFixed(1)}%`);
        console.log(`   Reasoning: ${result.reasoning}`);
        console.log(`   Processing Time: ${processingTime}ms`);
        
        // Store for chaining
        testContext.categorizationResults.set(description, result);
        testContext.performanceMetrics.set(`basic_categorization_${index}`, processingTime);
        console.log(`   📦 Stored result for chaining to advanced tests`);
        
        // Validation
        expect(result).toBeDefined();
        expect(result.categoryName).toBeTruthy();
        expect(result.confidence).toBeGreaterThanOrEqual(0);
        expect(result.confidence).toBeLessThanOrEqual(1);
        expect(result.reasoning).toBeTruthy();
        expect(processingTime).toBeLessThan(10000);
        
        // Category should be reasonable for the transaction
        const category = standardTestCategories.find(cat => cat.name === result.categoryName);
        expect(category).toBeTruthy();
        
        console.log(`   ✅ Basic categorization validated`);
        console.log(`   ✅ Performance: ${processingTime}ms < 10000ms`);
        console.log(`   ✅ Category exists in available options`);
        
      }, 15000);
    });
  });

  // Prompt 3: Advanced Categorization with Context (Uses Prompt 1 results)
  describe('Prompt 3: Advanced Categorization with Vendor Context', () => {
    test('Advanced Categorization Chain: Using Extracted Vendors', async () => {
      console.log(`\n🔗 PROMPT 3 TEST: Advanced Categorization with Chaining`);
      console.log(`📦 Using vendors from Prompt 1 tests`);
      console.log(`🎯 Goal: Improve categorization with vendor context`);
      
      // Get a vendor from previous test
      const vendorEntries = Array.from(testContext.extractedVendors.entries());
      if (vendorEntries.length === 0) {
        console.log('⚠️  No vendors available from Prompt 1, running standalone test');
        return;
      }
      
      const [description, extractedVendor] = vendorEntries[0];
      console.log(`📝 Using transaction: "${description}"`);
      console.log(`🏪 Using extracted vendor: "${extractedVendor}"`);
      
      const transaction: TransactionCategorizationRequest = {
        description,
        amount: -1000,
        type: 'debit',
        vendor: extractedVendor, // Using chained data
        categories: standardTestCategories
      };
      
      const startTime = Date.now();
      const result = await genAIService.categorizeTransaction(transaction);
      const processingTime = Date.now() - startTime;
      
      console.log(`\n🤖 Advanced AI Categorization Result:`);
      console.log(`   Category: ${result.categoryName}`);
      console.log(`   Confidence: ${(result.confidence * 100).toFixed(1)}%`);
      console.log(`   Reasoning: ${result.reasoning}`);
      console.log(`   Processing Time: ${processingTime}ms`);
      
      // Compare with basic result if available
      const basicResult = testContext.categorizationResults.get(description);
      if (basicResult) {
        console.log(`\n📊 Comparison with Basic Categorization:`);
        console.log(`   Basic Confidence: ${(basicResult.confidence * 100).toFixed(1)}%`);
        console.log(`   Advanced Confidence: ${(result.confidence * 100).toFixed(1)}%`);
        console.log(`   Improvement: ${((result.confidence - basicResult.confidence) * 100).toFixed(1)}%`);
        
        // Advanced categorization should generally be more confident
        // (though not always, depending on the vendor context)
        console.log(`   📈 Confidence change: ${result.confidence >= basicResult.confidence ? 'Improved or maintained' : 'Slightly decreased'}`);
      }
      
      // Store advanced result
      testContext.categorizationResults.set(`advanced_${description}`, result);
      
      // Validation
      expect(result).toBeDefined();
      expect(result.categoryName).toBeTruthy();
      expect(result.confidence).toBeGreaterThan(0.3); // Should have reasonable confidence
      expect(result.reasoning).toContain(extractedVendor.toLowerCase().split(' ')[0]); // Should mention vendor
      expect(processingTime).toBeLessThan(10000);
      
      console.log(`   ✅ Advanced categorization with vendor context validated`);
      console.log(`   ✅ Reasoning includes vendor information`);
      
    }, 15000);
  });

  // Prompt 4: MIS Structure Generation (Industry-specific)
  describe('Prompt 4: MIS Structure Generation', () => {
    const industryTests = [
      { industry: 'technology', businessType: 'software startup' },
      { industry: 'retail', businessType: 'clothing store' },
      { industry: 'manufacturing', businessType: 'electronics manufacturing' },
      { industry: 'services', businessType: 'consulting firm' }
    ];

    industryTests.forEach(({ industry, businessType }, index) => {
      test(`MIS Generation ${index + 1}: ${businessType}`, async () => {
        console.log(`\n🏗️  PROMPT 4 TEST ${index + 1}: MIS Structure Generation`);
        console.log(`🏢 Industry: ${industry}`);
        console.log(`💼 Business Type: ${businessType}`);
        console.log(`🎯 Goal: Generate industry-specific MIS structure`);
        
        const startTime = Date.now();
        const misStructure = await genAIService.generateMISStructure(industry, businessType);
        const processingTime = Date.now() - startTime;
        
        console.log(`\n🤖 AI MIS Generation Result:`);
        console.log(`   Main Categories: ${misStructure.length}`);
        console.log(`   Processing Time: ${processingTime}ms`);
        
        // Log structure details
        misStructure.slice(0, 3).forEach((category, idx) => {
          console.log(`   ${idx + 1}. ${category.name} (${category.glCode})`);
          console.log(`      Description: ${category.description}`);
          console.log(`      Subcategories: ${category.children.length}`);
        });
        
        // Store for potential chaining
        testContext.misStructures.set(industry, misStructure);
        console.log(`   📦 Stored MIS structure for ${industry} industry`);
        
        // Validation
        expect(misStructure).toBeInstanceOf(Array);
        expect(misStructure.length).toBeGreaterThan(0);
        expect(misStructure.length).toBeLessThan(50); // Reasonable number of main categories
        
        misStructure.forEach(category => {
          expect(category.name).toBeTruthy();
          expect(category.level).toBeGreaterThanOrEqual(1);
          expect(category.glCode).toBeTruthy();
          expect(category.description).toBeTruthy();
          expect(category.children).toBeInstanceOf(Array);
        });
        
        expect(processingTime).toBeLessThan(30000);
        
        console.log(`   ✅ MIS structure validated for ${industry} industry`);
        console.log(`   ✅ Performance: ${processingTime}ms < 30000ms`);
        
      }, 45000); // Longer timeout for complex MIS generation
    });
  });

  // Prompt 5: Confidence Calibration Test
  describe('Prompt 5: Confidence Calibration Analysis', () => {
    test('Confidence Analysis Chain: Analyzing Previous Results', async () => {
      console.log(`\n📊 PROMPT 5 TEST: Confidence Calibration Analysis`);
      console.log(`🔗 Using results from all previous prompt tests`);
      console.log(`🎯 Goal: Analyze confidence patterns and calibration`);
      
      const allResults = Array.from(testContext.categorizationResults.values());
      if (allResults.length === 0) {
        console.log('⚠️  No categorization results available for analysis');
        return;
      }
      
      console.log(`📈 Analyzing ${allResults.length} categorization results`);
      
      // Calculate confidence statistics
      const confidences = allResults.map(r => r.confidence);
      const avgConfidence = confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;
      const minConfidence = Math.min(...confidences);
      const maxConfidence = Math.max(...confidences);
      const highConfidenceCount = confidences.filter(c => c >= 0.8).length;
      const mediumConfidenceCount = confidences.filter(c => c >= 0.6 && c < 0.8).length;
      const lowConfidenceCount = confidences.filter(c => c < 0.6).length;
      
      console.log(`\n📊 Confidence Analysis Results:`);
      console.log(`   Average Confidence: ${(avgConfidence * 100).toFixed(1)}%`);
      console.log(`   Min Confidence: ${(minConfidence * 100).toFixed(1)}%`);
      console.log(`   Max Confidence: ${(maxConfidence * 100).toFixed(1)}%`);
      console.log(`   High Confidence (≥80%): ${highConfidenceCount}/${allResults.length} (${(highConfidenceCount/allResults.length*100).toFixed(1)}%)`);
      console.log(`   Medium Confidence (60-79%): ${mediumConfidenceCount}/${allResults.length} (${(mediumConfidenceCount/allResults.length*100).toFixed(1)}%)`);
      console.log(`   Low Confidence (<60%): ${lowConfidenceCount}/${allResults.length} (${(lowConfidenceCount/allResults.length*100).toFixed(1)}%)`);
      
      // Analyze performance metrics
      const performanceValues = Array.from(testContext.performanceMetrics.values());
      if (performanceValues.length > 0) {
        const avgProcessingTime = performanceValues.reduce((sum, time) => sum + time, 0) / performanceValues.length;
        console.log(`   Average Processing Time: ${avgProcessingTime.toFixed(0)}ms`);
      }
      
      // Quality thresholds validation
      expect(avgConfidence).toBeGreaterThan(0.5); // Average confidence should be reasonable
      expect(minConfidence).toBeGreaterThan(0.0); // Should never be zero
      expect(maxConfidence).toBeLessThanOrEqual(1.0); // Should never exceed 1
      expect(highConfidenceCount).toBeGreaterThan(0); // Should have some high confidence results
      
      // Business requirements validation (from requirements doc: 87%+ accuracy target)
      const businessQualityThreshold = 0.7; // 70% of results should be high confidence
      const highConfidenceRatio = highConfidenceCount / allResults.length;
      
      console.log(`\n🎯 Business Quality Validation:`);
      console.log(`   High Confidence Ratio: ${(highConfidenceRatio * 100).toFixed(1)}%`);
      console.log(`   Business Threshold: ${(businessQualityThreshold * 100)}%`);
      console.log(`   Meets Business Standards: ${highConfidenceRatio >= businessQualityThreshold ? '✅ Yes' : '⚠️  Needs Improvement'}`);
      
      // Log recommendations for prompt improvement
      console.log(`\n💡 Prompt Improvement Recommendations:`);
      if (avgConfidence < 0.8) {
        console.log(`   • Consider enhancing categorization prompts for better accuracy`);
      }
      if (lowConfidenceCount > allResults.length * 0.3) {
        console.log(`   • Review low-confidence cases for prompt pattern improvements`);
      }
      if (performanceValues.length > 0 && avgProcessingTime > 8000) {
        console.log(`   • Consider optimizing prompts for faster processing`);
      }
      console.log(`   • Overall system performance: ${avgConfidence >= 0.7 ? 'Good' : 'Needs improvement'}`);
      
      console.log(`   ✅ Confidence calibration analysis completed`);
      
    }, 5000);
  });

  // Prompt 6: Batch Performance Test (Uses all previous patterns)
  describe('Prompt 6: Batch Processing Performance', () => {
    test('Batch Performance Chain: Processing Multiple Transactions', async () => {
      console.log(`\n⚡ PROMPT 6 TEST: Batch Processing Performance`);
      console.log(`📦 Processing ${performanceTestTransactions.length} test transactions`);
      console.log(`🎯 Goal: Validate batch processing capabilities`);
      
      const batchResults: CategorizationResult[] = [];
      const batchStartTime = Date.now();
      
      // Process transactions in batch
      for (let i = 0; i < performanceTestTransactions.length; i++) {
        const txn = performanceTestTransactions[i];
        const transaction: TransactionCategorizationRequest = {
          ...txn,
          categories: standardTestCategories
        };
        
        const startTime = Date.now();
        const result = await genAIService.categorizeTransaction(transaction);
        const processingTime = Date.now() - startTime;
        
        batchResults.push(result);
        
        console.log(`   ${i + 1}/${performanceTestTransactions.length}: ${txn.description.substring(0, 40)}... → ${result.categoryName} (${(result.confidence * 100).toFixed(1)}%, ${processingTime}ms)`);
      }
      
      const totalBatchTime = Date.now() - batchStartTime;
      const avgTimePerTransaction = totalBatchTime / performanceTestTransactions.length;
      const totalConfidence = batchResults.reduce((sum, r) => sum + r.confidence, 0);
      const avgBatchConfidence = totalConfidence / batchResults.length;
      
      console.log(`\n📊 Batch Processing Results:`);
      console.log(`   Total Processing Time: ${totalBatchTime}ms`);
      console.log(`   Average Time per Transaction: ${avgTimePerTransaction.toFixed(0)}ms`);
      console.log(`   Average Confidence: ${(avgBatchConfidence * 100).toFixed(1)}%`);
      console.log(`   Success Rate: ${batchResults.length}/${performanceTestTransactions.length} (100%)`);
      
      // Performance targets validation (from requirements: <60s for 1000 transactions)
      const targetTimeFor1000Txn = 60000; // 60 seconds for 1000 transactions
      const projectedTimeFor1000 = avgTimePerTransaction * 1000;
      
      console.log(`\n🎯 Performance Target Validation:`);
      console.log(`   Projected time for 1000 transactions: ${(projectedTimeFor1000/1000).toFixed(1)}s`);
      console.log(`   Target: <60s for 1000 transactions`);
      console.log(`   Meets Performance Target: ${projectedTimeFor1000 <= targetTimeFor1000 ? '✅ Yes' : '⚠️  Needs Optimization'}`);
      
      // Validation
      expect(batchResults.length).toBe(performanceTestTransactions.length);
      expect(avgBatchConfidence).toBeGreaterThan(0.5);
      expect(totalBatchTime).toBeLessThan(120000); // Should process batch in under 2 minutes
      
      console.log(`   ✅ Batch processing performance validated`);
      
    }, 180000); // 3 minute timeout for batch processing
  });
});