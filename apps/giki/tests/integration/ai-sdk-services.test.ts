/**
 * Integration Tests for Vercel AI SDK Services
 * 
 * One test per prompt pattern for isolated optimization.
 * Tests real AI calls with gemini-2.0-flash-001 model.
 * Shows each AI processing step with detailed logging.
 */

import { describe, test, expect, beforeAll } from 'bun:test';
import { vendorExtractionService } from '../../server/src/services/ai-sdk/vendorExtractionService';
import { categoryMappingService } from '../../server/src/services/ai-sdk/categoryMappingService';
import { categoryGenerationService } from '../../server/src/services/ai-sdk/categoryGenerationService';
import { transactionAnalysisService } from '../../server/src/services/ai-sdk/transactionAnalysisService';
import { conversationalAgentService } from '../../server/src/services/ai-sdk/conversationalAgentService';
import type { Transaction, BusinessProfile, Category } from '../../server/src/services/ai-sdk/transactionAnalysisService';

// Real test data from actual bank statements
const REAL_TRANSACTIONS = [
  { description: "AMAZON.COM*2Q4RX9YL3 AMZN.COM/BILLWA", amount: 45.99, type: 'debit' as const },
  { description: "STARBUCKS STORE #12345 SEATTLE WA", amount: 7.85, type: 'debit' as const },
  { description: "NETFLIX.COM 866-579-7172 CA", amount: 15.99, type: 'debit' as const },
  { description: "WHOLEFDS MKT #10234 PORTLAND OR", amount: 127.43, type: 'debit' as const },
  { description: "UBER *TRIP HELP.UBER.COM CA", amount: 23.67, type: 'debit' as const },
  { description: "ACH DEPOSIT PAYROLL COMPANY INC", amount: 3500.00, type: 'credit' as const },
  { description: "SPOTIFY USA 877-778-1161 NY", amount: 9.99, type: 'debit' as const },
  { description: "CHEVRON 0012345 SAN FRANCISCO CA", amount: 65.23, type: 'debit' as const },
  { description: "TARGET ******** REDWOOD CITY CA", amount: 89.76, type: 'debit' as const },
  { description: "COMCAST CABLE 800-COMCAST PA", amount: 125.00, type: 'debit' as const }
];

// Shared context for test chaining
const testContext: {
  vendorResults: Map<string, any>;
  categorizationResults: Map<string, any>;
  categoryHierarchy: any;
  conversationSession: string;
} = {
  vendorResults: new Map(),
  categorizationResults: new Map(),
  categoryHierarchy: null,
  conversationSession: 'test-session-' + Date.now()
};

describe('Vercel AI SDK Services - One Test Per Prompt', () => {
  
  beforeAll(() => {
    console.log('🚀 Starting AI SDK integration tests with real Gemini calls');
    console.log(`📊 Testing with ${REAL_TRANSACTIONS.length} real transactions`);
  });

  describe('1. Vendor Extraction Service', () => {
    
    test('[PROMPT-1] Extract vendor from Amazon transaction', async () => {
      const description = REAL_TRANSACTIONS[0].description;
      console.log(`\n📝 Testing vendor extraction: "${description}"`);
      
      const result = await vendorExtractionService.extractVendor(description);
      
      console.log('✅ Vendor extracted:', {
        vendor: result.vendor,
        type: result.vendorType,
        confidence: `${(result.confidence * 100).toFixed(1)}%`
      });
      
      expect(result.vendor).toBeTruthy();
      expect(result.vendor?.toLowerCase()).toContain('amazon');
      expect(result.confidence).toBeGreaterThan(0.7);
      
      testContext.vendorResults.set('amazon', result);
    });

    test('[PROMPT-2] Extract vendor from Starbucks transaction', async () => {
      const description = REAL_TRANSACTIONS[1].description;
      console.log(`\n📝 Testing vendor extraction: "${description}"`);
      
      const result = await vendorExtractionService.extractVendor(description);
      
      console.log('✅ Vendor extracted:', {
        vendor: result.vendor,
        type: result.vendorType,
        confidence: `${(result.confidence * 100).toFixed(1)}%`
      });
      
      expect(result.vendor).toBeTruthy();
      expect(result.vendor?.toLowerCase()).toContain('starbucks');
      expect(result.vendorType).toBe('retail');
      
      testContext.vendorResults.set('starbucks', result);
    });

    test('[PROMPT-3] Extract vendor with context for better accuracy', async () => {
      const description = "PYMT RECEIVED - THANK YOU";
      console.log(`\n📝 Testing contextual vendor extraction: "${description}"`);
      
      const result = await vendorExtractionService.extractVendorWithContext(
        description,
        {
          amount: 500,
          type: 'credit',
          previousVendors: ['Chase Bank', 'Wells Fargo', 'PayPal']
        }
      );
      
      console.log('✅ Contextual extraction:', {
        vendor: result.vendor,
        confidence: `${(result.confidence * 100).toFixed(1)}%`,
        context: 'Payment with banking context'
      });
      
      expect(result).toBeDefined();
      testContext.vendorResults.set('payment', result);
    });

    test('[PROMPT-4] Batch vendor extraction with parallel processing', async () => {
      const descriptions = REAL_TRANSACTIONS.slice(0, 5).map(t => t.description);
      console.log(`\n📝 Testing batch extraction for ${descriptions.length} transactions`);
      
      const results = await vendorExtractionService.extractVendorsBatch(descriptions);
      
      console.log('✅ Batch results:', results.map((r, i) => ({
        transaction: descriptions[i].substring(0, 30) + '...',
        vendor: r.vendor,
        confidence: `${(r.confidence * 100).toFixed(1)}%`
      })));
      
      expect(results).toHaveLength(5);
      expect(results.filter(r => r.vendor !== null).length).toBeGreaterThan(3);
      
      results.forEach((r, i) => {
        testContext.vendorResults.set(`batch-${i}`, r);
      });
    });
  });

  describe('2. Category Mapping Service', () => {
    
    const TEST_CATEGORIES: Category[] = [
      { id: 'food', name: 'Food & Dining', glCode: '5100' },
      { id: 'transport', name: 'Transportation', glCode: '5200' },
      { id: 'entertainment', name: 'Entertainment', glCode: '5300' },
      { id: 'utilities', name: 'Utilities', glCode: '5400' },
      { id: 'shopping', name: 'Shopping', glCode: '5500' },
      { id: 'income', name: 'Income', glCode: '4000' }
    ];

    test('[PROMPT-5] Categorize Starbucks transaction', async () => {
      const transaction = {
        id: 'txn-1',
        description: REAL_TRANSACTIONS[1].description,
        amount: REAL_TRANSACTIONS[1].amount,
        type: REAL_TRANSACTIONS[1].type,
        vendor: testContext.vendorResults.get('starbucks')?.vendor
      };
      
      console.log(`\n📝 Testing categorization: "${transaction.description}"`);
      
      const result = await categoryMappingService.categorizeTransaction(
        transaction,
        TEST_CATEGORIES,
        { industry: 'retail', businessType: 'service', size: 'small' }
      );
      
      console.log('✅ Categorization result:', {
        category: result.categoryName,
        fullPath: result.fullPath,
        categoryPath: result.categoryPath,
        level: result.level,
        confidence: `${(result.confidence * 100).toFixed(1)}%`,
        reasoning: result.reasoning
      });
      
      expect(result.categoryName).toBe('Food & Dining');
      expect(result.fullPath).toContain('Food & Dining');
      expect(result.categoryPath).toContain('Food & Dining');
      expect(result.level).toBeGreaterThanOrEqual(1);
      expect(result.confidence).toBeGreaterThan(0.8);
      
      testContext.categorizationResults.set('starbucks', result);
    });

    test('[PROMPT-6] Categorize Uber transaction', async () => {
      const transaction = {
        id: 'txn-2',
        description: REAL_TRANSACTIONS[4].description,
        amount: REAL_TRANSACTIONS[4].amount,
        type: REAL_TRANSACTIONS[4].type
      };
      
      console.log(`\n📝 Testing categorization: "${transaction.description}"`);
      
      const result = await categoryMappingService.categorizeTransaction(
        transaction,
        TEST_CATEGORIES
      );
      
      console.log('✅ Categorization:', {
        category: result.categoryName,
        fullPath: result.fullPath,
        categoryPath: result.categoryPath,
        level: result.level,
        confidence: `${(result.confidence * 100).toFixed(1)}%`,
        alternatives: result.alternativeCategories?.map(a => a.categoryName)
      });
      
      expect(result.categoryName).toBe('Transportation');
      expect(result.fullPath).toContain('Transportation');
      expect(result.categoryPath).toContain('Transportation');
      expect(result.level).toBeGreaterThanOrEqual(1);
      expect(result.confidence).toBeGreaterThan(0.7);
      
      testContext.categorizationResults.set('uber', result);
    });

    test('[PROMPT-7] Stream batch categorization with real-time updates', async () => {
      const transactions = REAL_TRANSACTIONS.slice(0, 3).map((t, i) => ({
        id: `batch-${i}`,
        description: t.description,
        amount: t.amount,
        type: t.type,
        vendor: testContext.vendorResults.get(`batch-${i}`)?.vendor
      }));
      
      console.log(`\n📝 Testing streaming categorization for ${transactions.length} transactions`);
      
      const updates: any[] = [];
      let finalResult: any;
      const stream = categoryMappingService.streamCategorizeBatch(
        transactions,
        TEST_CATEGORIES,
        { industry: 'retail', businessType: 'service', size: 'medium' }
      );
      
      for await (const update of stream) {
        if (update.categorizations) {
          console.log(`⏳ Streaming update: ${update.categorizations.length} categorized`);
          updates.push(update);
        }
        // The last value will be the complete result
        finalResult = update;
      }
      
      console.log('✅ Streaming complete:', {
        totalCategorized: finalResult.categorizations.length,
        overallAccuracy: `${(finalResult.overallAccuracy * 100).toFixed(1)}%`
      });
      
      expect(finalResult.categorizations).toHaveLength(3);
      expect(finalResult.overallAccuracy).toBeGreaterThan(0.7);
    });

    test('[PROMPT-8] Learn from user feedback to improve categorization', async () => {
      const originalCategorization = testContext.categorizationResults.get('starbucks');
      const transaction = {
        id: 'txn-feedback',
        description: "STARBUCKS MEETING SUPPLIES",
        amount: 45.00,
        type: 'debit' as const,
        vendor: 'Starbucks'
      };
      
      console.log(`\n📝 Testing feedback learning: User corrects Starbucks to "Business Expenses"`);
      
      const improvedResult = await categoryMappingService.improveCategorizationWithFeedback(
        transaction,
        originalCategorization,
        'business-expenses',
        [...TEST_CATEGORIES, { id: 'business-expenses', name: 'Business Expenses', glCode: '5600' }],
        'This was a business meeting expense, not personal dining'
      );
      
      console.log('✅ Improved categorization:', {
        newCategory: improvedResult.categoryName,
        newConfidence: `${(improvedResult.confidence * 100).toFixed(1)}%`,
        newReasoning: improvedResult.reasoning
      });
      
      expect(improvedResult.categoryId).toBe('business-expenses');
      expect(improvedResult.confidence).toBeGreaterThan(0.8);
    });
  });

  describe('3. Category Hierarchy Generation Service', () => {
    
    test('[PROMPT-9] Get standard hierarchy template for retail business', async () => {
      const businessType = 'retail';
      const industry = 'E-commerce';
      
      console.log(`\n📝 Getting standard hierarchy template for ${industry} ${businessType}`);
      
      const { hierarchyService } = await import('../../server/src/services/hierarchy/hierarchyService');
      const result = await hierarchyService.getStandardTemplate(industry, businessType);
      
      console.log('✅ Standard Hierarchy template retrieved:', {
        industry: result.industry,
        totalCategories: result.metadata.totalCategories,
        maxDepth: result.metadata.maxDepth,
        rootCategories: result.categories.map(c => c.name),
        source: result.source
      });
      
      expect(result.metadata.totalCategories).toBeGreaterThanOrEqual(15);
      expect(result.metadata.maxDepth).toBeGreaterThanOrEqual(2);
      expect(result.categories.length).toBeGreaterThan(4);
      expect(result.source).toBe('standard');
      expect(result.businessType).toBe(businessType);
      
      testContext.categoryHierarchy = result;
    });

    test('[PROMPT-10] Test hierarchy-first categorization with streaming validation', async () => {
      const transaction = {
        id: 'stream-test',
        description: 'SPOTIFY USA 877-778-1161 NY',
        amount: 9.99,
        type: 'debit' as const,
        vendor: 'Spotify'
      };
      
      const businessContext = {
        industry: 'SaaS',
        businessType: 'saas' as const,
        size: 'small' as const
      };
      
      console.log(`\n📝 Testing hierarchy-first categorization with streaming validation for ${businessContext.industry}`);
      
      // Test single categorization with hierarchy-first approach
      const result = await categoryMappingService.categorizeTransactionWithHierarchy(
        transaction,
        businessContext,
        'test-user'
      );
      
      console.log('✅ Hierarchy-first categorization result:', {
        category: result.categoryName,
        fullPath: result.fullPath,
        categoryPath: result.categoryPath,
        level: result.level,
        confidence: `${(result.confidence * 100).toFixed(1)}%`,
        hierarchySource: 'standard'
      });
      
      // Validate hierarchy-first categorization
      expect(result.categoryName).toBeTruthy();
      expect(result.fullPath).toContain('→');
      expect(result.categoryPath).toBeInstanceOf(Array);
      expect(result.categoryPath.length).toBeGreaterThan(1);
      expect(result.level).toBeGreaterThanOrEqual(1);
      expect(result.confidence).toBeGreaterThan(0.7);
      
      // Should be appropriate for Spotify subscription in SaaS business
      expect(
        result.fullPath.toLowerCase().includes('software') || 
        result.fullPath.toLowerCase().includes('subscription') ||
        result.fullPath.toLowerCase().includes('operating')
      ).toBe(true);
    });

    test('[PROMPT-11] Generate GL code mapping for categories', async () => {
      const categories = ['Office Supplies', 'Software Subscriptions', 'Consulting Fees', 'Travel Expenses'];
      
      console.log(`\n📝 Generating GL codes for ${categories.length} categories`);
      
      const mapping = await categoryGenerationService.generateGLCodeMapping(
        categories,
        'consulting'
      );
      
      console.log('✅ GL Code mapping:', Array.from(mapping.entries()));
      
      expect(mapping.size).toBe(categories.length);
      expect(mapping.get('Office Supplies')).toBeTruthy();
      expect(mapping.get('Software Subscriptions')).toBeTruthy();
    });

    test('[PROMPT-12] Validate category hierarchy completeness', async () => {
      console.log(`\n📝 Validating category hierarchy completeness`);
      
      const validation = await categoryGenerationService.validateCategoryHierarchy(
        testContext.categoryHierarchy
      );
      
      console.log('✅ Validation result:', {
        isValid: validation.isValid,
        issues: validation.issues.length > 0 ? validation.issues : 'None',
        suggestions: validation.suggestions.slice(0, 3)
      });
      
      expect(validation).toBeDefined();
      expect(validation.isValid).toBeDefined();
    });
  });

  describe('4. Transaction Analysis Orchestrator', () => {
    
    test('[PROMPT-13] Complete pipeline analysis for single transaction', async () => {
      const transaction: Transaction = {
        id: 'complete-1',
        description: REAL_TRANSACTIONS[0].description,
        amount: REAL_TRANSACTIONS[0].amount,
        type: REAL_TRANSACTIONS[0].type,
        date: new Date().toISOString()
      };
      
      console.log(`\n📝 Running complete analysis pipeline for: "${transaction.description}"`);
      
      const categories = testContext.categoryHierarchy?.categories || [];
      const result = await transactionAnalysisService.analyzeTransaction(
        transaction,
        categories,
        { industry: 'retail', businessType: 'retail', size: 'medium' }
      );
      
      console.log('✅ Complete analysis:', {
        vendor: result.vendor.vendor,
        category: result.categorization.categoryName,
        combinedConfidence: `${(result.confidence * 100).toFixed(1)}%`,
        processingTime: `${result.processingTime}ms`
      });
      
      expect(result.vendor.vendor).toBeTruthy();
      expect(result.categorization.categoryId).toBeTruthy();
      expect(result.confidence).toBeGreaterThan(0.6);
    });

    test('[PROMPT-14] Hierarchy-first batch categorization with vendor extraction', async () => {
      const transactions = REAL_TRANSACTIONS.slice(0, 5).map((t, i) => ({
        id: `hierarchy-batch-${i}`,
        description: t.description,
        amount: t.amount,
        type: t.type as 'debit' | 'credit',
        date: new Date().toISOString()
      }));
      
      const businessContext = {
        industry: 'Technology',
        businessType: 'service' as const,
        size: 'medium' as const
      };
      
      console.log(`\n📝 Hierarchy-first batch categorization for ${transactions.length} transactions`);
      
      // First get appropriate hierarchy for this business
      const { hierarchyService } = await import('../../server/src/services/hierarchy/hierarchyService');
      const hierarchy = await hierarchyService.getStandardTemplate(businessContext.industry, businessContext.businessType);
      
      console.log(`⏳ Using hierarchy: "${hierarchy.name}" with ${hierarchy.metadata.totalCategories} categories`);
      
      // Process each transaction with hierarchy-first approach
      const results: any[] = [];
      for (const transaction of transactions) {
        // Extract vendor first
        const vendorResult = await vendorExtractionService.extractVendor(transaction.description);
        
        // Then categorize using hierarchy-first approach  
        const categoryResult = await categoryMappingService.categorizeTransactionWithHierarchy(
          { ...transaction, vendor: vendorResult.vendor },
          businessContext,
          'test-user'
        );
        
        results.push({
          transactionId: transaction.id,
          vendor: vendorResult,
          categorization: categoryResult,
          confidence: categoryResult.confidence
        });
        
        console.log(`⏳ Processed: ${transaction.description.substring(0, 30)}... → ${categoryResult.categoryName}`);
      }
      
      const averageConfidence = results.reduce((sum, r) => sum + r.confidence, 0) / results.length;
      
      console.log('✅ Hierarchy-first batch complete:', {
        totalProcessed: results.length,
        averageConfidence: `${(averageConfidence * 100).toFixed(1)}%`,
        hierarchyUsed: hierarchy.source,
        categoriesAvailable: hierarchy.metadata.totalCategories
      });
      
      expect(results).toHaveLength(5);
      expect(averageConfidence).toBeGreaterThan(0.7);
      expect(results.every(r => r.vendor.vendor !== null)).toBe(true);
      expect(results.every(r => r.categorization.categoryName)).toBe(true);
    });
  });

  describe('5. Conversational Agent Service', () => {
    
    test('[PROMPT-15] Process help request with contextual response', async () => {
      const message = "How do I upload my bank statements?";
      
      console.log(`\n📝 Testing agent response: "${message}"`);
      
      const response = await conversationalAgentService.processMessage(
        message,
        testContext.conversationSession,
        'test-user',
        { currentPage: 'upload' }
      );
      
      console.log('✅ Agent response:', {
        intent: response.intent,
        confidence: `${(response.confidence * 100).toFixed(1)}%`,
        messagePreview: response.message.substring(0, 100) + '...',
        suggestedActions: response.suggestedActions?.map(a => a.label)
      });
      
      expect(response.intent).toBe('help');
      expect(response.confidence).toBeGreaterThan(0.7);
      expect(response.message).toContain('upload');
    });

    test('[PROMPT-16] Process action request with smart suggestions', async () => {
      const message = "I want to analyze my spending patterns";
      
      console.log(`\n📝 Testing action request: "${message}"`);
      
      const response = await conversationalAgentService.processMessage(
        message,
        testContext.conversationSession,
        'test-user',
        { currentPage: 'dashboard' }
      );
      
      console.log('✅ Agent response:', {
        intent: response.intent,
        suggestedActions: response.suggestedActions?.map(a => ({
          label: a.label,
          action: a.action
        }))
      });
      
      expect(response.intent).toBe('action');
      expect(response.suggestedActions).toBeDefined();
    });

    test('[PROMPT-17] Stream conversational response', async () => {
      const message = "Explain how the AI categorization works";
      
      console.log(`\n📝 Testing streaming response: "${message}"`);
      
      const chunks: string[] = [];
      const stream = conversationalAgentService.streamResponse(
        message,
        testContext.conversationSession + '-stream',
        'test-user',
        { currentPage: 'transactions' }
      );
      
      let finalResponse: any;
      for await (const chunk of stream) {
        if (typeof chunk === 'string') {
          chunks.push(chunk);
          if (chunks.length <= 3) {
            console.log(`⏳ Streaming chunk ${chunks.length}: "${chunk.substring(0, 50)}..."`);
          }
        } else {
          // This might be the final response object
          finalResponse = chunk;
        }
      }
      
      console.log('✅ Streaming complete:', {
        totalChunks: chunks.length,
        intent: finalResponse?.intent || 'help',
        confidence: finalResponse?.confidence ? `${(finalResponse.confidence * 100).toFixed(1)}%` : 'N/A'
      });
      
      expect(chunks.length).toBeGreaterThan(0);
      expect(finalResponse?.message || chunks.join('')).toBeTruthy();
    });

    test('[PROMPT-18] Get contextual help for specific page', async () => {
      console.log(`\n📝 Testing contextual help for categories page`);
      
      const response = await conversationalAgentService.getContextualHelp(
        'categories',
        'GL codes'
      );
      
      console.log('✅ Contextual help:', {
        intent: response.intent,
        confidence: `${(response.confidence * 100).toFixed(1)}%`,
        helpPreview: response.message.substring(0, 150) + '...',
        actions: response.suggestedActions?.map(a => a.label)
      });
      
      expect(response.intent).toBe('help');
      expect(response.message).toContain('GL');
      expect(response.confidence).toBeGreaterThan(0.9);
    });

    test('[PROMPT-19] Generate smart suggestions based on context', async () => {
      console.log(`\n📝 Testing smart suggestions generation`);
      
      const suggestions = await conversationalAgentService.getSmartSuggestions(
        {
          currentPage: 'dashboard',
          uploadedFiles: ['bank-statement-jan.csv', 'credit-card-feb.pdf']
        },
        ['viewed_transactions', 'exported_report']
      );
      
      console.log('✅ Smart suggestions:', suggestions);
      
      expect(suggestions).toHaveLength(suggestions.length);
      expect(suggestions.length).toBeGreaterThan(0);
      expect(suggestions.length).toBeLessThanOrEqual(5);
    });
  });

  describe('6. Test Result Summary', () => {
    
    test('Generate test execution summary', () => {
      console.log('\n' + '='.repeat(80));
      console.log('📊 TEST EXECUTION SUMMARY');
      console.log('='.repeat(80));
      
      const summary = {
        vendorExtractions: testContext.vendorResults.size,
        categorizations: testContext.categorizationResults.size,
        categoryHierarchyGenerated: testContext.categoryHierarchy !== null,
        conversationSessions: 2,
        totalPrompts: 19,
        model: 'gemini-2.0-flash-001',
        sdkUsed: 'Vercel AI SDK with @ai-sdk/google'
      };
      
      console.log('✅ Results:', summary);
      console.log('\n🎯 Key Achievements:');
      console.log('- One test per prompt for isolated optimization');
      console.log('- Real AI calls with Gemini model');
      console.log('- Streaming support for real-time UI updates');
      console.log('- Structured outputs with Zod schemas');
      console.log('- Test chaining with shared context');
      console.log('- Complete customer journey coverage');
      
      expect(summary.vendorExtractions).toBeGreaterThan(5);
      expect(summary.categorizations).toBeGreaterThanOrEqual(2);
      expect(summary.categoryHierarchyGenerated).toBe(true);
    });
  });
});