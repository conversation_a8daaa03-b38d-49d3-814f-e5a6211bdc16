{"name": "@workspace/giki", "version": "1.0.0", "description": "Giki AI - Financial Categorization Platform", "private": true, "type": "module", "workspaces": ["client", "server", "shared"], "scripts": {"dev": "bun run dev:client & bun run dev:server", "dev:client": "cd client && bun run dev", "dev:server": "cd server && bun run dev", "build": "bun run build:client && bun run build:server", "build:client": "cd client && bun run build", "build:server": "cd server && bun run build", "test": "bun run test:unit && bun run test:integration && bun run test:e2e", "test:unit": "cd server && bun run test:unit", "test:integration": "cd server && bun run test:integration", "test:e2e": "cd client && bun run test:e2e", "lint": "bun run lint:client && bun run lint:server", "lint:client": "cd client && bun run lint", "lint:server": "cd server && bun run lint", "type-check": "bun run type-check:client && bun run type-check:server", "type-check:client": "cd client && bun run type-check", "type-check:server": "cd server && bun run type-check"}, "keywords": ["ai", "financial", "categorization", "transactions", "ml"], "author": "Giki AI Team", "license": "MIT"}