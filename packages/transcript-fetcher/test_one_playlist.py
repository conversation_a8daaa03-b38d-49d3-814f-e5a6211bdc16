#!/usr/bin/env python3
"""Test a single playlist for geographic restrictions."""

import sys
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api.proxies import WebshareProxyConfig

# Get playlist ID from command line
if len(sys.argv) < 3:
    print("Usage: python test_one_playlist.py <playlist_id> <video_id>")
    sys.exit(1)

playlist_id = sys.argv[1]
video_id = sys.argv[2]

try:
    # Configure proxy
    proxy_config = WebshareProxyConfig(
        proxy_username="kqxhzwub",
        proxy_password="1o8lw5go7mty"
    )
    api = YouTubeTranscriptApi(proxy_config=proxy_config)
    
    # Try to fetch transcript
    transcript_list = api.list(video_id)
    transcript = transcript_list.find_transcript(['hi', 'en'])
    segments = transcript.fetch()
    
    print(f"✅ SUCCESS - {playlist_id}: {len(segments)} segments")
except Exception as e:
    if "not made this video available in your country" in str(e):
        print(f"❌ BLOCKED - {playlist_id}: Geographic restriction")
    else:
        print(f"⚠️  ERROR - {playlist_id}: {type(e).__name__}")