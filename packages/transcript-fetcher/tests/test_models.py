"""
Tests for data models.
"""

import pytest
from src.models import VideoInfo, PlaylistInfo, ExtractionResult


class TestVideoInfo:
    """Tests for VideoInfo model."""
    
    def test_video_info_creation(self):
        """Test VideoInfo creation with required fields."""
        video = VideoInfo(
            video_id="dQw4w9WgXcQ",
            season=1,
            episode=5,
            title="Test Video",
            url="https://youtube.com/watch?v=dQw4w9WgXcQ"
        )
        
        assert video.video_id == "dQw4w9WgXcQ"
        assert video.season == 1
        assert video.episode == 5
        assert video.title == "Test Video"
        assert video.url == "https://youtube.com/watch?v=dQw4w9WgXcQ"
    
    def test_video_info_defaults(self):
        """Test VideoInfo with default values."""
        video = VideoInfo(video_id="test123", season=1, episode=1)
        
        assert video.title == ""
        assert video.url == ""
        assert video.playlist_id == ""
        assert video.playlist_folder == ""
        assert video.actual_playlist_title == ""


class TestPlaylistInfo:
    """Tests for PlaylistInfo model."""
    
    def test_playlist_info_creation(self):
        """Test PlaylistInfo creation."""
        playlist = PlaylistInfo(
            playlist_id="PLtest123",
            url="https://youtube.com/playlist?list=PLtest123",
            title="Test Playlist"
        )
        
        assert playlist.playlist_id == "PLtest123"
        assert playlist.url == "https://youtube.com/playlist?list=PLtest123"
        assert playlist.title == "Test Playlist"
        assert playlist.youtube_title is None


class TestExtractionResult:
    """Tests for ExtractionResult model."""
    
    def test_success_result(self):
        """Test successful extraction result."""
        result = ExtractionResult(
            status='success',
            video_id='test123',
            season=1,
            episode=5,
            length=1500,
            filepath='/path/to/transcript.txt'
        )
        
        assert result.status == 'success'
        assert result.video_id == 'test123'
        assert result.season == 1
        assert result.episode == 5
        assert result.length == 1500
        assert result.filepath == '/path/to/transcript.txt'
        assert result.error is None
    
    def test_failed_result(self):
        """Test failed extraction result."""
        result = ExtractionResult(
            status='failed',
            video_id='test456',
            season=2,
            episode=3,
            error='Timeout occurred'
        )
        
        assert result.status == 'failed'
        assert result.video_id == 'test456'
        assert result.season == 2
        assert result.episode == 3
        assert result.error == 'Timeout occurred'
        assert result.length is None
        assert result.filepath is None