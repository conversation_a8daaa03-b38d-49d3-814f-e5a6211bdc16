"""
Tests for configuration.
"""

import pytest
from src.config import Config


class TestConfig:
    """Tests for Config class."""
    
    def test_base_settings(self):
        """Test base configuration settings."""
        assert Config.BASE_URL == "https://tactiq.io/tools/run/youtube_transcript"
        assert Config.DEFAULT_HEADLESS is True
        assert Config.DEFAULT_DELAY == 5
    
    def test_timeout_settings(self):
        """Test timeout configuration."""
        assert Config.PAGE_TIMEOUT == 60000
        assert Config.NAVIGATION_TIMEOUT == 30000
        assert Config.SCROLL_DELAY == 2000
        assert Config.PLAYLIST_DELAY == 3000
    
    def test_episode_counts(self):
        """Test official episode counts."""
        assert Config.OFFICIAL_EPISODE_COUNTS[1] == 36
        assert Config.OFFICIAL_EPISODE_COUNTS[2] == 51
        assert Config.OFFICIAL_EPISODE_COUNTS[3] == 52
        assert Config.OFFICIAL_EPISODE_COUNTS[4] == 53
    
    def test_verified_playlists(self):
        """Test verified playlists configuration."""
        # Check that all seasons have playlists
        for season in [1, 2, 3, 4]:
            assert season in Config.VERIFIED_PLAYLISTS
            assert len(Config.VERIFIED_PLAYLISTS[season]) > 0
        
        # Check playlist structure
        season_1_first_playlist = Config.VERIFIED_PLAYLISTS[1][0]
        assert hasattr(season_1_first_playlist, 'playlist_id')
        assert hasattr(season_1_first_playlist, 'url')
        assert hasattr(season_1_first_playlist, 'title')
        
        # Check URL format
        assert "youtube.com/playlist?list=" in season_1_first_playlist.url
        assert season_1_first_playlist.playlist_id in season_1_first_playlist.url