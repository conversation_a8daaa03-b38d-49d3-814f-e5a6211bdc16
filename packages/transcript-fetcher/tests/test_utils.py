"""
Tests for utility functions.
"""

import pytest
from src.utils import (
    clean_filename, 
    clean_folder_name, 
    determine_content_type, 
    extract_company_name,
    sanitize_video_id
)


class TestCleanFilename:
    """Tests for clean_filename function."""
    
    def test_basic_cleaning(self):
        """Test basic filename cleaning."""
        assert clean_filename("Hello World") == "Hello_World"
        assert clean_filename("Test-Video_Name") == "Test-Video_Name"
        assert clean_filename("Special!@#$%Characters") == "SpecialCharacters"
    
    def test_length_limit(self):
        """Test filename length limiting."""
        long_title = "a" * 150
        result = clean_filename(long_title, max_length=50)
        assert len(result) == 50
    
    def test_empty_string(self):
        """Test handling of empty strings."""
        assert clean_filename("") == "Unknown_Video"
        assert clean_filename("   ") == "Unknown_Video"


class TestCleanFolderName:
    """Tests for clean_folder_name function."""
    
    def test_folder_name_cleaning(self):
        """Test folder name cleaning."""
        assert clean_folder_name("Shark Tank India S1") == "Shark_Tank_India_S1"
        assert clean_folder_name("Full Episodes | Season 2") == "Full_Episodes__Season_2"
    
    def test_empty_folder_name(self):
        """Test handling of empty folder names."""
        assert clean_folder_name("") == "Unknown_Playlist"


class TestDetermineContentType:
    """Tests for determine_content_type function."""
    
    def test_episode_detection(self):
        """Test episode detection."""
        assert determine_content_type("Full Episode 1") == "episode"
        assert determine_content_type("Complete Episode") == "episode"
        assert determine_content_type("Shark Tank India Episode 5") == "episode"
    
    def test_pitch_detection(self):
        """Test pitch detection."""
        assert determine_content_type("Startup Pitch: TechCorp") == "pitch"
        assert determine_content_type("Entrepreneur Presentation") == "pitch"
        assert determine_content_type("Business Idea Pitch") == "pitch"
    
    def test_generic_content(self):
        """Test generic content detection."""
        assert determine_content_type("Random Video Title") == "content"
        assert determine_content_type("Behind the Scenes") == "content"


class TestExtractCompanyName:
    """Tests for extract_company_name function."""
    
    def test_quoted_company_name(self):
        """Test extraction of quoted company names."""
        assert extract_company_name("Pitch by 'TechCorp' startup") == "TechCorp"
        assert extract_company_name('Company "InnovateX" presentation') == "InnovateX"
    
    def test_fallback_extraction(self):
        """Test fallback company name extraction."""
        result = extract_company_name("Amazing Startup Idea Presentation")
        assert result == "Amazing_Startup_Idea"
    
    def test_empty_title(self):
        """Test handling of empty titles."""
        assert extract_company_name("") == "Unknown_Company"


class TestSanitizeVideoId:
    """Tests for sanitize_video_id function."""
    
    def test_valid_video_id(self):
        """Test valid video ID."""
        assert sanitize_video_id("dQw4w9WgXcQ") == "dQw4w9WgXcQ"
        assert sanitize_video_id("abc123XYZ_-") == "abc123XYZ_-"
    
    def test_invalid_video_id(self):
        """Test invalid video IDs."""
        assert sanitize_video_id("short") is None
        assert sanitize_video_id("toolongvideoid123") is None
        assert sanitize_video_id("invalid@chars") is None
        assert sanitize_video_id("") is None
        assert sanitize_video_id(None) is None