[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "transcript-fetcher"
version = "1.0.0"
description = "Shark Tank India Transcript Fetcher - CLI Tool"
authors = [
    {name = "Shark AI Team", email = "<EMAIL>"}
]
license = {text = "MIT"}
requires-python = ">=3.12"
dependencies = [
    "click>=8.1.0",
    "rich>=13.0.0",
    "loguru>=0.7.0",
    "aiofiles>=23.0.0",
    "pydantic>=2.0.0",
    "youtube-transcript-api>=0.6.0"
]

[project.scripts]
transcript-fetcher = "transcript_fetcher.__main__:main"

[tool.hatch.build.targets.wheel]
packages = ["src/transcript_fetcher"]
exclude = ["tests/"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "--verbose --cov=src --cov-report=html --cov-report=term"
asyncio_mode = "auto"
