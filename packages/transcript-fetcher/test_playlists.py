#!/usr/bin/env python3
"""Test each playlist for geographic restrictions by trying to fetch one sample video."""

import json
import sys
from pathlib import Path
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api.proxies import WebshareProxyConfig

# Proxy config
PROXY_USERNAME = "kqxhzwub"
PROXY_PASSWORD = "1o8lw5go7mty"

def test_video(video_id, playlist_info):
    """Test if a video can be fetched."""
    try:
        # Configure proxy
        proxy_config = WebshareProxyConfig(
            proxy_username=PROXY_USERNAME,
            proxy_password=PROXY_PASSWORD
        )
        api = YouTubeTranscriptApi(proxy_config=proxy_config)
        
        # Try to list transcripts
        transcript_list = api.list(video_id)
        
        # Try to fetch transcript
        transcript = transcript_list.find_transcript(['hi', 'en'])
        segments = transcript.fetch()
        
        return {
            'status': 'SUCCESS',
            'language': transcript.language_code,
            'segments': len(segments)
        }
    except Exception as e:
        error_msg = str(e)
        if "not made this video available in your country" in error_msg:
            return {'status': 'BLOCKED', 'error': 'Geographic restriction'}
        else:
            return {'status': 'ERROR', 'error': str(type(e).__name__)}

def main():
    # Sample videos for each playlist
    playlists_to_test = [
        ("PLnkwIhuXMWpe0aUH6RufkgDe5CV050L7z", "S1 Playlist 1", "_q5mDAdYF5U"),
        ("PLnkwIhuXMWpfitpYDXrWWWgTFdg0sytUg", "S1 Playlist 2", "wpGnYFz8TEk"),
        ("PLnkwIhuXMWpdGWrMBtD_3I5o8IAB6TWZs", "S2 Playlist 1", "aR9eWNAvXjo"),
        ("PLzufeTFnhupzjzzVoHt98A52tEeLkiMDz", "S2 Playlist 2", "2xM092gtD58"),
        ("PLnkwIhuXMWpfhYunJZ6t-KVQztFytwmVX", "S2 Playlist 3", "xbcoUrWIBoo"),
        ("PLzufeTFnhupzQ_JmJOIWB112cFuRY7OKZ", "S3 Playlist 1", "NDchwpBS9OU"),
        ("PLnkwIhuXMWpf1epqhLErGX0VWl1Uj01wv", "S3 Playlist 2", "ouBuj7Dqi2s"),
        ("PLnkwIhuXMWpc8jHHC8DyMEsV1bWY9I3hq", "S3 Playlist 3", "z5HMCrquwAI"),
        ("PLnkwIhuXMWpfQzCHSZn9kpwNadWHa9fWw", "S3 Playlist 4", "krOoi-25m7A"),
        ("PLnkwIhuXMWpfs2Rlt4M2SvklIsQNtZYcL", "S4 Playlist 1", "GxsR_P1bP84"),
        ("PLn5vww_8o5KvQysulSASPbQsw_HitEpOm", "S4 Playlist 2", "JgNsTfvdSpA"),
        ("PLn5vww_8o5Ks2RkBSj_872pXXaN7IBcW4", "S4 Playlist 3", "d8BClMhUg00"),
        ("PLnkwIhuXMWpfxDhxpw3SDGCSD9huMbxQo", "S4 Playlist 4", "57q3aCtqjpQ"),
        ("PLn5vww_8o5KvMf3ZWmOWiGg6zym1sBTMo", "S4 Playlist 5", "C3EXWO9HOvY"),
    ]
    
    print("Testing playlists for geographic restrictions...")
    print("=" * 60)
    
    results = {}
    for playlist_id, name, video_id in playlists_to_test:
        print(f"\nTesting {name} ({playlist_id})...")
        print(f"Sample video: {video_id}")
        
        result = test_video(video_id, {'id': playlist_id, 'name': name})
        results[playlist_id] = result
        
        if result['status'] == 'SUCCESS':
            print(f"✅ SUCCESS - {result['language']} transcript with {result['segments']} segments")
        elif result['status'] == 'BLOCKED':
            print(f"❌ BLOCKED - {result['error']}")
        else:
            print(f"⚠️  ERROR - {result['error']}")
    
    # Summary
    print("\n" + "=" * 60)
    print("SUMMARY:")
    blocked_playlists = [pid for pid, r in results.items() if r['status'] == 'BLOCKED']
    if blocked_playlists:
        print(f"\n❌ Geographically blocked playlists: {len(blocked_playlists)}")
        for pid in blocked_playlists:
            print(f"   - {pid}")
    else:
        print("\n✅ No playlists are geographically blocked!")
    
    working_playlists = [pid for pid, r in results.items() if r['status'] == 'SUCCESS']
    print(f"\n✅ Working playlists: {len(working_playlists)}")
    for pid in working_playlists:
        print(f"   - {pid}")

if __name__ == "__main__":
    main()