#!/bin/bash

# Fetch all seasons in sequence
echo "Starting transcript fetching for all seasons..."
echo "Log file: /Users/<USER>/pitch-prep/data/transcripts/.logs/transcript_fetcher.log"

# Fetch Season 4
echo "Starting Season 4..."
uv run python __main__.py fetch-youtube --season 4
echo "Season 4 complete."

# Fetch Season 3  
echo "Starting Season 3..."
uv run python __main__.py fetch-youtube --season 3
echo "Season 3 complete."

# Fetch Season 2
echo "Starting Season 2..."
uv run python __main__.py fetch-youtube --season 2
echo "Season 2 complete."

# Fetch Season 1
echo "Starting Season 1..."
uv run python __main__.py fetch-youtube --season 1
echo "Season 1 complete."

echo "All seasons fetched successfully!"