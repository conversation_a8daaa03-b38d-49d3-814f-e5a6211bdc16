{"name": "@workspace/transcript-fetcher", "version": "1.0.0", "description": "Python CLI package for Shark Tank India transcript fetching via YouTube API", "private": true, "bin": {"transcript-fetcher": "./fetch_all_seasons.sh"}, "scripts": {"setup": "uv sync", "test": "uv run python -m pytest", "lint": "uv run black src/ && uv run flake8 src/", "typecheck": "uv run mypy src/", "fetch": "uv run python __main__.py fetch-youtube --seasons all", "fetch:season": "uv run python __main__.py fetch-youtube --season", "status": "uv run python __main__.py --status"}, "keywords": ["transcript", "extraction", "shark-tank", "youtube-api", "cli"], "author": "Giki AI Team", "license": "MIT"}