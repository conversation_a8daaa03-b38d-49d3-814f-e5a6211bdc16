"""
Data models for the transcript fetcher.
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class VideoInfo:
    """Information about a YouTube video."""

    video_id: str
    season: int
    episode: int
    title: str = ""
    url: str = ""
    playlist_id: str = ""
    playlist_folder: str = ""
    actual_playlist_title: str = ""


@dataclass
class PlaylistInfo:
    """Information about a YouTube playlist."""

    playlist_id: str
    url: str
    title: str
    youtube_title: Optional[str] = None


@dataclass
class ExtractionResult:
    """Result of a transcript extraction attempt."""

    status: str  # 'success' or 'failed'
    video_id: str
    season: int
    episode: int
    length: Optional[int] = None
    error: Optional[str] = None
    filepath: Optional[str] = None
