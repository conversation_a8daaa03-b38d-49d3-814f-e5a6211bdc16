#!/usr/bin/env python3
"""
Fetch pitch videos first since we have pitch playlists for all seasons
"""

import json
from pathlib import Path
import asyncio
from youtube_fetcher import YouTubeFetcher
from config import Config

async def fetch_pitches_by_season():
    """Fetch all pitch videos, season by season"""
    
    # Load filtered video list
    filtered_path = Path(Config.OUTPUT_DIR) / "filtered_video_list.json"
    with open(filtered_path, 'r') as f:
        filtered_data = json.load(f)
    
    # Summary of what we'll fetch
    print("=== Shark Tank India Pitch Videos Fetching Plan ===\n")
    print("We have pitch playlists for ALL seasons:")
    print(f"Season 1: {filtered_data['summary']['season_1']['pitch_videos']} pitch videos")
    print(f"Season 2: {filtered_data['summary']['season_2']['pitch_videos']} pitch videos")  
    print(f"Season 3: {filtered_data['summary']['season_3']['pitch_videos']} pitch videos")
    print(f"Season 4: {filtered_data['summary']['season_4']['pitch_videos']} pitch videos")
    print(f"\nTotal Pitch Videos to Fetch: {sum(s['pitch_videos'] for s in filtered_data['summary'].values() if isinstance(s, dict))}")
    
    # Ask for confirmation
    response = input("\nReady to start fetching? (y/n): ")
    if response.lower() != 'y':
        print("Fetching cancelled.")
        return
    
    # Initialize fetcher with proxy
    fetcher = YouTubeFetcher(use_proxy=True)
    
    # Fetch season by season
    for season in [1, 2, 3, 4]:
        print(f"\n{'='*60}")
        print(f"Starting Season {season} Pitch Videos")
        print(f"{'='*60}")
        
        pitch_playlists = filtered_data['playlists_to_keep'][f'season_{season}']['pitches']
        if not pitch_playlists:
            print(f"No pitch playlists for Season {season}, skipping...")
            continue
        
        print(f"Pitch playlists to fetch: {pitch_playlists}")
        
        # Load video list for this season
        video_list_path = Path(__file__).parent.parent / "transcripts_tactiq" / f"season_{season}" / "video_list.json"
        if not video_list_path.exists():
            print(f"ERROR: Video list not found for season {season} at {video_list_path}")
            continue
            
        with open(video_list_path, 'r') as f:
            season_data = json.load(f)
        
        # Filter to only pitch playlist videos
        pitch_videos = []
        for video in season_data.get('videos', []):
            if video.get('playlist_id') in pitch_playlists:
                pitch_videos.append(video)
        
        print(f"Found {len(pitch_videos)} pitch videos to fetch")
        
        # Create output directory
        output_dir = Path(Config.OUTPUT_DIR) / f"season_{season}" / "pitch_transcripts"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Convert to VideoInfo objects and fetch
        from models import VideoInfo
        video_infos = []
        for v in pitch_videos:
            video_infos.append(VideoInfo(
                video_id=v['video_id'],
                title=v.get('title', f"Video {v['video_id']}"),
                url=v.get('url', f"https://youtube.com/watch?v={v['video_id']}")
            ))
        
        result = fetcher.fetch_multiple(video_infos, output_dir=output_dir)
        
        print(f"\nSeason {season} Summary:")
        print(f"  Attempted: {result['stats']['attempted']}")
        print(f"  Succeeded: {result['stats']['succeeded']}")
        print(f"  Failed: {result['stats']['failed']}")
        
        # Optional: pause between seasons
        if season < 4:
            print("\nPausing 30 seconds before next season...")
            import time
            time.sleep(30)
    
    print("\n" + "="*60)
    print("✅ All pitch videos fetching complete!")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(fetch_pitches_by_season())