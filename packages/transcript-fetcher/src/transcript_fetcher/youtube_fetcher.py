"""
YouTube transcript fetcher using youtube-transcript-api with Webshare proxy.
Replaces the Playwright/tactiq.io based fetching.
"""

import time
import json
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from datetime import datetime

from youtube_transcript_api import YouTubeTranscript<PERSON>pi
from youtube_transcript_api._errors import NoTranscriptFound, TranscriptsDisabled
from youtube_transcript_api.proxies import WebshareProxyConfig
from youtube_transcript_api.formatters import TextFormatter

from transcript_fetcher.models import VideoInfo, ExtractionResult
from transcript_fetcher.config import Config
from transcript_fetcher.utils import setup_logger, clean_folder_name


class YouTubeFetcher:
    """Fetches transcripts using youtube-transcript-api with proxy support."""
    
    def __init__(self, use_proxy: bool = True):
        self.logger = setup_logger(self.__class__.__name__)
        self.last_youtube_call = 0
        
        # Configure proxy if enabled
        if use_proxy and Config.PROXY_ENABLED:
            proxy_config = WebshareProxyConfig(
                proxy_username=Config.PROXY_USERNAME,
                proxy_password=Config.PROXY_PASSWORD
            )
            self.youtube_api = YouTubeTranscriptApi(proxy_config=proxy_config)
            self.logger.info("✅ Webshare proxy configured for YouTube API")
        else:
            self.youtube_api = YouTubeTranscriptApi()
            self.logger.warning("⚠️  Running without proxy - may hit rate limits")
            
        self.stats = {
            "attempted": 0,
            "succeeded": 0,
            "failed": 0,
            "errors": []
        }
    
    def _rate_limit_youtube(self):
        """Ensure delay between YouTube API calls."""
        elapsed = time.time() - self.last_youtube_call
        if elapsed < Config.YOUTUBE_API_DELAY:
            sleep_time = Config.YOUTUBE_API_DELAY - elapsed
            time.sleep(sleep_time)
        self.last_youtube_call = time.time()
    
    def fetch_transcript(self, video_info: VideoInfo) -> Optional[Dict]:
        """Fetch transcript for a single video."""
        self.stats["attempted"] += 1
        
        # Rate limit
        self._rate_limit_youtube()
        
        try:
            # List available transcripts
            transcript_list = self.youtube_api.list(video_info.video_id)
            
            # Try to find Hindi transcript first (most common for Shark Tank India)
            transcript = None
            language_found = None
            
            try:
                transcript = transcript_list.find_transcript(['hi'])
                language_found = 'hi'
                self.logger.info(f"✅ Found Hindi transcript for {video_info.video_id}")
            except:
                # Try auto-generated
                try:
                    transcript = transcript_list.find_generated_transcript(['hi', 'en'])
                    language_found = transcript.language_code
                    self.logger.info(f"✅ Found auto-generated {language_found} transcript")
                except:
                    # Try any available transcript
                    for t in transcript_list:
                        transcript = t
                        language_found = t.language_code
                        self.logger.info(f"✅ Found {language_found} transcript")
                        break
            
            if not transcript:
                raise NoTranscriptFound(f"No usable transcript found for {video_info.video_id}")
            
            # Fetch the actual transcript data
            segments = transcript.fetch()
            
            # Use TextFormatter to get clean text format
            formatter = TextFormatter()
            transcript_text = formatter.format_transcript(segments)
            
            # Calculate duration
            duration = 0
            if segments:
                last = segments[-1]
                duration = last.start + last.duration if hasattr(last, 'start') else 0
            
            # Return metadata and text
            result = {
                "video_id": video_info.video_id,
                "title": video_info.title,
                "url": video_info.url,
                "transcript_text": transcript_text,
                "language": language_found,
                "is_generated": transcript.is_generated,
                "duration": duration,
                "segment_count": len(segments),
                "extraction_timestamp": datetime.now().isoformat(),
                "extraction_method": "youtube-transcript-api"
            }
            
            self.stats["succeeded"] += 1
            self.logger.info(f"✅ Successfully fetched transcript for {video_info.title}")
            return result
            
        except Exception as e:
            self.stats["failed"] += 1
            error_info = {
                "video_id": video_info.video_id,
                "title": video_info.title,
                "error": str(e),
                "type": type(e).__name__
            }
            self.stats["errors"].append(error_info)
            self.logger.error(f"❌ Error fetching {video_info.video_id}: {type(e).__name__}: {str(e)}")
            return None
    
    def _segments_to_text(self, segments: List) -> str:
        """Convert transcript segments to plain text."""
        lines = []
        for seg in segments:
            # Handle FetchedTranscriptSnippet objects
            if hasattr(seg, 'text'):
                text = seg.text
            elif isinstance(seg, dict):
                text = seg.get('text', '')
            else:
                text = str(seg)
            
            if text.strip():
                lines.append(text.strip())
        return '\n'.join(lines)
    
    def fetch_multiple(self, videos: List[VideoInfo], output_dir: Optional[Path] = None) -> Dict:
        """Fetch transcripts for multiple videos with progress tracking and smart playlist skip logic."""
        output_dir = output_dir or Path(Config.OUTPUT_DIR) / "youtube_api"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        results = []
        
        # Smart skip logic: Track consecutive failures per playlist
        consecutive_failures = {}  # playlist_id -> failure count
        MAX_CONSECUTIVE_FAILURES = 3  # Skip playlist after 3 consecutive failures
        skipped_playlists = set()  # Track playlists we've decided to skip
        
        self.logger.info(f"🎯 Fetching {len(videos)} transcripts")
        self.logger.info("=" * 60)
        
        for i, video_info in enumerate(videos):
            # Check if we should skip this video based on playlist
            playlist_id = getattr(video_info, 'playlist_id', None)
            if playlist_id and playlist_id in skipped_playlists:
                self.logger.info(f"\n[{i+1}/{len(videos)}] Skipping (blocked playlist): {video_info.title[:50]}...")
                continue
            
            self.logger.info(f"\n[{i+1}/{len(videos)}] Processing: {video_info.title[:50]}...")
            self.logger.info(f"  Video ID: {video_info.video_id}")
            
            # Check if already exists - use simple filename: S{season}_E{episode}_{video_id}.text
            simple_filename = f"S{video_info.season}_E{video_info.episode:03d}_{video_info.video_id}.text"
            output_file = output_dir / simple_filename
            
            if output_file.exists():
                self.logger.info(f"  ⏭️  Already exists, skipping...")
                # Reset consecutive failures for this playlist since we found a working video
                if playlist_id:
                    consecutive_failures[playlist_id] = 0
                continue
            
            # Fetch transcript
            result = self.fetch_transcript(video_info)
            
            if result:
                # Save to file - TextFormatter output as .txt
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(result['transcript_text'])
                self.logger.info(f"  💾 Saved to: {output_file.name}")
                results.append(result)
                # Reset consecutive failures for this playlist
                if playlist_id:
                    consecutive_failures[playlist_id] = 0
            else:
                # Track failures for smart skip logic
                if playlist_id:
                    consecutive_failures[playlist_id] = consecutive_failures.get(playlist_id, 0) + 1
                    
                    # Check if this is a geographic block error
                    last_error = self.stats["errors"][-1] if self.stats["errors"] else None
                    is_geo_blocked = last_error and "not made this video available in your country" in str(last_error.get("error", ""))
                    
                    if is_geo_blocked and consecutive_failures[playlist_id] >= MAX_CONSECUTIVE_FAILURES:
                        skipped_playlists.add(playlist_id)
                        remaining_from_playlist = sum(1 for v in videos[i+1:] if getattr(v, 'playlist_id', None) == playlist_id)
                        self.logger.warning(f"  🚫 Playlist appears geographically blocked after {MAX_CONSECUTIVE_FAILURES} consecutive failures")
                        self.logger.warning(f"  ⏭️  Skipping {remaining_from_playlist} remaining videos from playlist: {playlist_id}")
            
            # Progress update
            success_rate = (self.stats["succeeded"] / self.stats["attempted"] * 100) if self.stats["attempted"] > 0 else 0
            self.logger.info(f"  📊 Progress: {self.stats['succeeded']}/{self.stats['attempted']} ({success_rate:.1f}% success rate)")
        
        # Final summary
        final_success_rate = (self.stats["succeeded"] / self.stats["attempted"] * 100) if self.stats["attempted"] > 0 else 0
        self.logger.info("\n" + "=" * 60)
        self.logger.info(f"✅ Fetching complete!")
        self.logger.info(f"  Total attempted: {self.stats['attempted']}")
        self.logger.info(f"  Succeeded: {self.stats['succeeded']}")
        self.logger.info(f"  Failed: {self.stats['failed']}")
        self.logger.info(f"  Success rate: {final_success_rate:.1f}%")
        
        if self.stats["errors"]:
            self.logger.warning(f"\n❌ Errors encountered:")
            for err in self.stats["errors"][:5]:  # Show first 5 errors
                self.logger.warning(f"  - {err['video_id']}: {err['type']}")
        
        return {
            "results": results,
            "stats": self.stats
        }
    
    def fetch_season(self, season: int, use_filtered: bool = True, pitch_only: bool = False) -> Dict:
        """Fetch all transcripts for a specific season.
        
        Args:
            season: Season number (1-4)
            use_filtered: Use filtered playlist list
            pitch_only: Only fetch pitch videos, skip episodes
        """
        playlists_to_fetch = None  # Initialize to None for unfiltered mode
        
        if use_filtered:
            # Load filtered playlists from data directory - use absolute path
            package_root = Path(__file__).resolve().parents[2]  # Go up to package root
            filtered_path = package_root / "data" / "filtered_video_list.json"
            if filtered_path.exists():
                with open(filtered_path, 'r') as f:
                    filtered_data = json.load(f)
                
                # Get playlists to keep for this season
                keep_playlists = filtered_data['playlists_to_keep'][f'season_{season}']
                playlists_to_fetch = []
                
                if pitch_only:
                    # Only fetch pitch playlists
                    if keep_playlists['pitches']:
                        playlists_to_fetch.extend(keep_playlists['pitches'])
                        self.logger.info(f"📺 Fetching ONLY pitch videos for Season {season}")
                else:
                    # Fetch episodes, pitches, and mixed playlists
                    if keep_playlists.get('episodes'):
                        playlists_to_fetch.extend(keep_playlists['episodes'])
                    if keep_playlists.get('pitches'):
                        playlists_to_fetch.extend(keep_playlists['pitches'])
                    if keep_playlists.get('mixed'):
                        playlists_to_fetch.extend(keep_playlists['mixed'])
                
                self.logger.info(f"🎯 Using filtered playlists for Season {season}: {playlists_to_fetch}")
            else:
                self.logger.warning("⚠️  Filtered list not found, using all playlists")
                use_filtered = False
                playlists_to_fetch = None
        else:
            self.logger.info(f"🌐 Using UNFILTERED mode for Season {season} - fetching from ALL playlists")
        
        # Load master video list - use absolute path to avoid import path issues
        package_root = Path(__file__).resolve().parents[2]  # Go up to package root
        master_video_path = package_root / "data" / "master_video_list.json"
        
        if not master_video_path.exists():
            self.logger.error(f"❌ Master video list not found at {master_video_path}")
            return {"results": [], "stats": self.stats}
        
        with open(master_video_path, 'r') as f:
            master_data = json.load(f)
            
        # Get videos for this season
        season_key = f"season_{season}"
        if season_key not in master_data["seasons"]:
            self.logger.error(f"❌ Season {season} not found in master video list")
            return {"results": [], "stats": self.stats}
            
        season_data = master_data["seasons"][season_key]
        
        # Convert to VideoInfo objects
        videos = []
        for video_data in season_data.get('videos', []):
            # Filter by playlist if using filtered mode
            if use_filtered and playlists_to_fetch is not None:
                if video_data.get('playlist_id') not in playlists_to_fetch:
                    continue
            
            video_info = VideoInfo(
                video_id=video_data['video_id'],
                season=season,
                episode=video_data.get('episode', 0),  # Use 0 if episode not specified
                title=video_data.get('title', f"Video {video_data['video_id']}"),
                url=video_data.get('url', f"https://youtube.com/watch?v={video_data['video_id']}")
            )
            videos.append(video_info)
        
        self.logger.info(f"📺 Found {len(videos)} videos for Season {season}")
        
        # Use flat directory structure - all transcripts in one location
        transcripts_dir = Path(Config.OUTPUT_DIR)
        
        return self.fetch_multiple(videos, output_dir=transcripts_dir)