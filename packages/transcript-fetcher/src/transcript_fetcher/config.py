"""
Configuration settings for the transcript fetcher.
"""

import os
from typing import Dict, List
from transcript_fetcher.models import PlaylistInfo


class Config:
    """Configuration class for the transcript fetcher."""

    # Base settings
    BASE_URL = "https://tactiq.io/tools/run/youtube_transcript"
    OUTPUT_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "data", "transcripts")

    # Extraction settings
    DEFAULT_HEADLESS = True
    DEFAULT_DELAY = 5
    MAX_SCROLLS = 50
    MAX_VIDEOS_PER_PLAYLIST = 100

    # Timeout settings - increased for full episodes
    PAGE_TIMEOUT = 60000  # 60 seconds for transcript processing
    NAVIGATION_TIMEOUT = 30000  # 30 seconds for page navigation
    SCROLL_DELAY = 1000
    PLAYLIST_DELAY = 2000

    # Retry settings
    MAX_RETRIES = 3  # Maximum number of retry attempts for transcript extraction
    RETRY_DELAY_BASE = (
        2  # Base delay in seconds between retries (multiplied by attempt number)
    )

    # Official episode counts from Wikipedia
    OFFICIAL_EPISODE_COUNTS = {
        1: 36,  # Season 1: 36 episodes
        2: 51,  # Season 2: 51 episodes
        3: 52,  # Season 3: 52 episodes
        4: 53,  # Season 4: 53 episodes
    }

    # Proxy settings (Webshare residential proxy)
    PROXY_ENABLED = True
    PROXY_HOST = "p.webshare.io"
    PROXY_PORT = 80
    PROXY_USERNAME = "kqxhzwub"
    PROXY_PASSWORD = "1o8lw5go7mty"
    
    # YouTube API settings
    YOUTUBE_API_DELAY = 0.5  # Reduced delay with proxy (500ms)
    
    # User-provided verified playlist URLs - EXACT LINKS PROVIDED
    VERIFIED_PLAYLISTS: Dict[int, List[PlaylistInfo]] = {
        1: [
            PlaylistInfo(
                playlist_id="PLnkwIhuXMWpe0aUH6RufkgDe5CV050L7z",
                url="https://www.youtube.com/playlist?list=PLnkwIhuXMWpe0aUH6RufkgDe5CV050L7z",
                title="Shark Tank India Season 1 - Playlist 1",
            ),
            PlaylistInfo(
                playlist_id="PLnkwIhuXMWpfitpYDXrWWWgTFdg0sytUg",
                url="https://www.youtube.com/playlist?list=PLnkwIhuXMWpfitpYDXrWWWgTFdg0sytUg",
                title="Shark Tank India Season 1 - Playlist 2",
            ),
        ],
        2: [
            PlaylistInfo(
                playlist_id="PLnkwIhuXMWpdGWrMBtD_3I5o8IAB6TWZs",
                url="https://www.youtube.com/playlist?list=PLnkwIhuXMWpdGWrMBtD_3I5o8IAB6TWZs",
                title="Shark Tank India Season 2 - Playlist 1",
            ),
            PlaylistInfo(
                playlist_id="PLzufeTFnhupzjzzVoHt98A52tEeLkiMDz",
                url="https://www.youtube.com/playlist?list=PLzufeTFnhupzjzzVoHt98A52tEeLkiMDz",
                title="Shark Tank India Season 2 - Playlist 2",
            ),
            PlaylistInfo(
                playlist_id="PLnkwIhuXMWpfhYunJZ6t-KVQztFytwmVX",
                url="https://www.youtube.com/playlist?list=PLnkwIhuXMWpfhYunJZ6t-KVQztFytwmVX",
                title="Shark Tank India Season 2 - Playlist 3",
            ),
        ],
        3: [
            PlaylistInfo(
                playlist_id="PLzufeTFnhupzQ_JmJOIWB112cFuRY7OKZ",
                url="https://www.youtube.com/playlist?list=PLzufeTFnhupzQ_JmJOIWB112cFuRY7OKZ",
                title="Shark Tank India Season 3 - Playlist 1",
            ),
            PlaylistInfo(
                playlist_id="PLnkwIhuXMWpf1epqhLErGX0VWl1Uj01wv",
                url="https://www.youtube.com/playlist?list=PLnkwIhuXMWpf1epqhLErGX0VWl1Uj01wv",
                title="Shark Tank India Season 3 - Playlist 2",
            ),
            PlaylistInfo(
                playlist_id="PLnkwIhuXMWpc8jHHC8DyMEsV1bWY9I3hq",
                url="https://www.youtube.com/playlist?list=PLnkwIhuXMWpc8jHHC8DyMEsV1bWY9I3hq",
                title="Shark Tank India Season 3 - Playlist 3",
            ),
            PlaylistInfo(
                playlist_id="PLnkwIhuXMWpfQzCHSZn9kpwNadWHa9fWw",
                url="https://www.youtube.com/playlist?list=PLnkwIhuXMWpfQzCHSZn9kpwNadWHa9fWw",
                title="Shark Tank India Season 3 - Playlist 4",
            ),
        ],
        4: [
            PlaylistInfo(
                playlist_id="PLnkwIhuXMWpfs2Rlt4M2SvklIsQNtZYcL",
                url="https://www.youtube.com/playlist?list=PLnkwIhuXMWpfs2Rlt4M2SvklIsQNtZYcL",
                title="Shark Tank India Season 4 - Playlist 1",
            ),
            PlaylistInfo(
                playlist_id="PLn5vww_8o5KvQysulSASPbQsw_HitEpOm",
                url="https://www.youtube.com/playlist?list=PLn5vww_8o5KvQysulSASPbQsw_HitEpOm",
                title="Shark Tank India Season 4 - Playlist 2",
            ),
            PlaylistInfo(
                playlist_id="PLn5vww_8o5Ks2RkBSj_872pXXaN7IBcW4",
                url="https://www.youtube.com/playlist?list=PLn5vww_8o5Ks2RkBSj_872pXXaN7IBcW4",
                title="Shark Tank India Season 4 - Playlist 3",
            ),
            PlaylistInfo(
                playlist_id="PLnkwIhuXMWpfxDhxpw3SDGCSD9huMbxQo",
                url="https://www.youtube.com/playlist?list=PLnkwIhuXMWpfxDhxpw3SDGCSD9huMbxQo",
                title="Shark Tank India Season 4 - Playlist 4",
            ),
            PlaylistInfo(
                playlist_id="PLn5vww_8o5KvMf3ZWmOWiGg6zym1sBTMo",
                url="https://www.youtube.com/playlist?list=PLn5vww_8o5KvMf3ZWmOWiGg6zym1sBTMo",
                title="Shark Tank India Season 4 - Playlist 5",
            ),
        ],
    }
