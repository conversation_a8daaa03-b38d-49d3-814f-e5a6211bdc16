#!/usr/bin/env python3
"""
Simplified CLI for YouTube transcript fetching with youtube-transcript-api.
"""

import json
import sys
from pathlib import Path
from typing import List, Optional

from transcript_fetcher.youtube_fetcher import YouTubeFetcher
from transcript_fetcher.models import VideoInfo
from transcript_fetcher.config import Config
from transcript_fetcher.utils import setup_logger


def load_season_videos(season: int) -> List[VideoInfo]:
    """Load video list for a specific season."""
    video_list_path = Path(Config.OUTPUT_DIR).parent / "transcripts_tactiq" / f"season_{season}" / "video_list.json"
    
    if not video_list_path.exists():
        print(f"❌ Video list not found for season {season} at {video_list_path}")
        return []
    
    with open(video_list_path, 'r') as f:
        season_data = json.load(f)
    
    # Convert to VideoInfo objects
    videos = []
    for video_data in season_data.get('videos', []):
        video_info = VideoInfo(
            video_id=video_data['video_id'],
            title=video_data.get('title', f"Video {video_data['video_id']}"),
            url=video_data.get('url', f"https://youtube.com/watch?v={video_data['video_id']}")
        )
        videos.append(video_info)
    
    return videos


def main():
    """Main entry point for YouTube transcript fetching."""
    logger = setup_logger("YouTubeCLI")
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python -m transcript-fetcher.youtube fetch-season <season_number>")
        print("  python -m transcript-fetcher.youtube test")
        print("\nExamples:")
        print("  python -m transcript-fetcher.youtube fetch-season 4")
        print("  python -m transcript-fetcher.youtube test")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "test":
        # Test with a few videos
        print("🧪 Testing YouTube transcript fetcher...")
        fetcher = YouTubeFetcher(use_proxy=True)
        
        test_videos = [
            VideoInfo("GxsR_P1bP84", "Season 4 Test Video 1", "https://youtube.com/watch?v=GxsR_P1bP84"),
            VideoInfo("szlOOHfrZoY", "Season 4 Test Video 2", "https://youtube.com/watch?v=szlOOHfrZoY"),
            VideoInfo("cVDxhD-DY_E", "Season 4 Test Video 3", "https://youtube.com/watch?v=cVDxhD-DY_E"),
        ]
        
        results = fetcher.fetch_multiple(test_videos)
        
        if results["stats"]["succeeded"] > 0:
            print(f"\n✅ Test successful! Fetched {results['stats']['succeeded']}/3 transcripts")
        else:
            print("\n❌ Test failed. Check proxy configuration.")
    
    elif command == "fetch-season":
        if len(sys.argv) < 3:
            print("❌ Please specify season number (1-4)")
            sys.exit(1)
        
        season = int(sys.argv[2])
        if season not in [1, 2, 3, 4]:
            print("❌ Invalid season. Choose 1-4")
            sys.exit(1)
        
        print(f"📺 Fetching transcripts for Season {season}...")
        
        # Load videos for the season
        videos = load_season_videos(season)
        
        if not videos:
            print(f"❌ No videos found for season {season}")
            sys.exit(1)
        
        print(f"✅ Found {len(videos)} videos")
        
        # Ask for confirmation if more than 50 videos
        if len(videos) > 50:
            response = input(f"\n⚠️  This will fetch {len(videos)} transcripts. Continue? (y/n): ")
            if response.lower() != 'y':
                print("❌ Cancelled")
                sys.exit(0)
        
        # Create fetcher and fetch
        fetcher = YouTubeFetcher(use_proxy=True)
        
        # Create season output directory
        season_dir = Path(Config.OUTPUT_DIR) / f"season_{season}" / "youtube_transcripts"
        
        results = fetcher.fetch_multiple(videos, output_dir=season_dir)
        
        # Save summary
        summary_file = season_dir / "fetch_summary.json"
        with open(summary_file, 'w') as f:
            json.dump({
                "season": season,
                "total_videos": len(videos),
                "stats": results["stats"]
            }, f, indent=2)
        
        print(f"\n📊 Summary saved to: {summary_file}")
        
        if results["stats"]["succeeded"] == len(videos):
            print(f"\n🎉 SUCCESS! All {len(videos)} transcripts fetched!")
        elif results["stats"]["succeeded"] > 0:
            print(f"\n⚠️  Partial success: {results['stats']['succeeded']}/{len(videos)} transcripts fetched.")
        else:
            print("\n❌ Failed to fetch any transcripts.")
    
    else:
        print(f"❌ Unknown command: {command}")
        sys.exit(1)


if __name__ == "__main__":
    main()