"""
Command-line interface for the transcript fetcher.
"""

import asyncio
import argparse
import sys
import os
import subprocess
import signal
from typing import Optional

from transcript_fetcher.utils import setup_logger


class CLI:
    """Command-line interface for the transcript fetcher."""

    def __init__(self):
        self.logger = setup_logger(self.__class__.__name__)

    def create_parser(self) -> argparse.ArgumentParser:
        """Create command-line argument parser."""
        parser = argparse.ArgumentParser(
            description="Shark Tank India Transcript Fetcher",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  %(prog)s collect-video-links # Collect all video links from all seasons
  %(prog)s fetch-youtube       # Fetch all transcripts using youtube-transcript-api
  %(prog)s fetch-youtube --season 4  # Fetch only season 4 transcripts
  %(prog)s fetch-pitches       # Fetch all pitch videos in background (599 videos)
  
Background mode:
  %(prog)s fetch-youtube --background  # Fetch all transcripts in background
  %(prog)s fetch-youtube --season 4 --background  # Fetch season 4 in background
  %(prog)s fetch-pitches --background  # Fetch all pitch videos in background (599 videos)
  %(prog)s --status            # Check background process status
  %(prog)s --stop              # Stop background process
            """,
        )

        parser.add_argument(
            "mode",
            nargs="?",
            choices=["collect-video-links", "fetch-youtube", "fetch-pitches"],
            help="Extraction mode: collect-video-links (collect all video links), fetch-youtube (fetch using youtube-transcript-api), fetch-pitches (fetch all 599 pitch videos)",
        )

        parser.add_argument(
            "--season",
            type=int,
            choices=[1, 2, 3, 4],
            help="Season number to extract (1-4). For multi-season mode, use --seasons instead",
        )

        parser.add_argument(
            "--seasons",
            type=str,
            help='Comma-separated season numbers (e.g., "1,2,3" or "all" for all seasons)',
        )

        parser.add_argument(
            "--limit",
            type=int,
            default=1,
            help="Number of videos to extract per playlist (default: 1)",
        )

        parser.add_argument(
            "--headful",
            action="store_true",
            help="Run browser in headful mode (visible)",
        )

        parser.add_argument(
            "--delay",
            type=int,
            default=5,
            help="Delay between requests in seconds (default: 5)",
        )

        parser.add_argument(
            "--output-dir", type=str, help="Custom output directory for transcripts"
        )

        parser.add_argument(
            "--verbose", "-v", action="store_true", help="Enable verbose logging"
        )

        parser.add_argument(
            "--background",
            "--bg",
            action="store_true",
            help="Run in background mode (detached process with file logging)",
        )

        parser.add_argument(
            "--unfiltered",
            action="store_true",
            help="Ignore geographic filtering and fetch from ALL playlists (including blocked ones)",
        )

        parser.add_argument(
            "--status", action="store_true", help="Check background process status"
        )

        parser.add_argument(
            "--stop", action="store_true", help="Stop background process"
        )

        return parser

    def get_background_files(self):
        """Get paths for background process files."""
        from transcript_fetcher.config import Config

        log_dir = os.path.join(Config.OUTPUT_DIR, ".logs")
        os.makedirs(log_dir, exist_ok=True)
        return {
            "log_file": os.path.join(log_dir, "transcript_fetcher.log"),
            "pid_file": os.path.join(log_dir, "transcript_fetcher.pid"),
            "status_file": os.path.join(log_dir, "transcript_fetcher.status"),
        }

    def start_background_process(self, args):
        """Start the process in background mode."""
        files = self.get_background_files()

        # Check if already running
        if self.is_background_running():
            print("❌ Background process is already running")
            return False

        # Prepare command without --background flag
        cmd = [
            sys.executable,
            os.path.join(os.path.dirname(__file__), "__main__.py"),
        ]
        
        # Build arguments from parsed args object, not sys.argv
        if hasattr(args, 'mode') and args.mode:
            cmd.append(args.mode)
        if hasattr(args, 'season') and args.season:
            cmd.extend(["--season", str(args.season)])
        if hasattr(args, 'seasons') and args.seasons:
            cmd.extend(["--seasons", args.seasons])
        if hasattr(args, 'limit') and args.limit != 1:
            cmd.extend(["--limit", str(args.limit)])
        if hasattr(args, 'headful') and args.headful:
            cmd.append("--headful")
        if hasattr(args, 'delay') and args.delay != 5:
            cmd.extend(["--delay", str(args.delay)])
        if hasattr(args, 'output_dir') and args.output_dir:
            cmd.extend(["--output-dir", args.output_dir])
        if hasattr(args, 'verbose') and args.verbose:
            cmd.append("--verbose")
        if hasattr(args, 'unfiltered') and args.unfiltered:
            cmd.append("--unfiltered")

        print(f"🚀 Starting background process...")
        print(f"📝 Logs will be written to: {files['log_file']}")
        print(f"📊 Check status with: python __main__.py --status")
        print(f"🛑 Stop with: python __main__.py --stop")

        # Start detached process
        try:
            with open(files["log_file"], "w") as log_file:
                log_file.write(
                    f"🦈 Shark Tank India Transcript Fetcher - Background Mode\\n"
                )
                log_file.write(f"Started at: {self.get_timestamp()}\\n")
                log_file.write(f"Command: {' '.join(cmd)}\\n")
                log_file.write("=" * 60 + "\\n\\n")
                log_file.flush()

                # Start the process
                process = subprocess.Popen(
                    cmd,
                    stdout=log_file,
                    stderr=subprocess.STDOUT,
                    stdin=subprocess.DEVNULL,
                    start_new_session=True,  # Detach from parent
                )

                # Save PID
                with open(files["pid_file"], "w") as pid_file:
                    pid_file.write(str(process.pid))

                # Initial status
                self.update_status_file(
                    "STARTING", f"Process started with PID {process.pid}"
                )

                print(f"✅ Background process started (PID: {process.pid})")
                print(f"🔍 Monitor progress: tail -f {files['log_file']}")
                return True

        except Exception as e:
            print(f"❌ Failed to start background process: {e}")
            return False

    def is_background_running(self):
        """Check if background process is running."""
        files = self.get_background_files()

        if not os.path.exists(files["pid_file"]):
            return False

        try:
            with open(files["pid_file"], "r") as f:
                pid = int(f.read().strip())

            # Check if process is still running
            os.kill(pid, 0)  # This will raise OSError if process doesn't exist
            return True
        except (OSError, ValueError):
            # Process not running, clean up
            self.cleanup_background_files()
            return False

    def get_background_status(self):
        """Get background process status."""
        files = self.get_background_files()

        if not self.is_background_running():
            return {"running": False, "message": "No background process running"}

        status = {"running": True}

        # Get PID
        try:
            with open(files["pid_file"], "r") as f:
                status["pid"] = int(f.read().strip())
        except:
            status["pid"] = None

        # Get current status
        try:
            with open(files["status_file"], "r") as f:
                lines = f.readlines()
                if lines:
                    status["last_update"] = lines[-1].strip()
        except:
            status["last_update"] = "No status available"

        # Get log tail
        try:
            with open(files["log_file"], "r") as f:
                lines = f.readlines()
                status["log_tail"] = lines[-10:] if len(lines) > 10 else lines
        except:
            status["log_tail"] = []

        return status

    def stop_background_process(self):
        """Stop the background process."""
        files = self.get_background_files()

        if not self.is_background_running():
            print("❌ No background process running")
            return False

        try:
            with open(files["pid_file"], "r") as f:
                pid = int(f.read().strip())

            print(f"🛑 Stopping background process (PID: {pid})...")

            # Try graceful shutdown first
            os.kill(pid, signal.SIGTERM)

            # Wait a bit for graceful shutdown
            import time

            time.sleep(2)

            # Check if still running
            try:
                os.kill(pid, 0)
                # Still running, force kill
                print("🔥 Force killing process...")
                os.kill(pid, signal.SIGKILL)
            except OSError:
                pass  # Process already dead

            self.cleanup_background_files()
            self.update_status_file("STOPPED", "Process stopped by user")

            print("✅ Background process stopped")
            return True

        except Exception as e:
            print(f"❌ Failed to stop background process: {e}")
            return False

    def cleanup_background_files(self):
        """Clean up background process files."""
        files = self.get_background_files()
        for file_path in [files["pid_file"]]:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except:
                pass

    def update_status_file(self, status, message):
        """Update the status file."""
        files = self.get_background_files()
        try:
            with open(files["status_file"], "a") as f:
                f.write(f"{self.get_timestamp()}: {status} - {message}\\n")
        except:
            pass

    def get_timestamp(self):
        """Get current timestamp."""
        from datetime import datetime

        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    async def collect_all_video_links(self):
        """Collect all video links from all playlists for all seasons."""
        import json
        from pathlib import Path
        from transcript_fetcher.config import Config
        from transcript_fetcher.models import PlaylistInfo
        
        self.logger.info("🔍 Collecting all video links from all seasons...")
        
        # Create master video list
        master_list = {
            "collection_date": self.get_timestamp(),
            "total_seasons": 4,
            "seasons": {}
        }
        
        # Check existing video lists
        for season in [1, 2, 3, 4]:
            # Try multiple paths
            paths_to_check = [
                Path(__file__).parent.parent / "transcripts_tactiq" / f"season_{season}" / "video_list.json",
                Path(Config.OUTPUT_DIR).parent / "transcripts_tactiq" / f"season_{season}" / "video_list.json",
                Path("/Users/<USER>/pitch-prep/transcript-fetcher/transcripts_tactiq") / f"season_{season}" / "video_list.json"
            ]
            
            video_list_path = None
            for path in paths_to_check:
                if path.exists():
                    video_list_path = path
                    break
            
            if video_list_path and video_list_path.exists():
                with open(video_list_path, 'r') as f:
                    season_data = json.load(f)
                    master_list["seasons"][f"season_{season}"] = season_data
                    self.logger.info(f"✅ Season {season}: {season_data.get('total_videos', 0)} videos from {len(season_data.get('playlists', []))} playlists")
            else:
                self.logger.error(f"❌ Season {season}: video_list.json not found! Run playlist extraction first.")
        
        # Calculate totals
        total_videos = sum(s.get('total_videos', 0) for s in master_list["seasons"].values())
        master_list["total_videos_all_seasons"] = total_videos
        
        # Save master list
        output_path = Path(Config.OUTPUT_DIR) / "master_video_list.json"
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(master_list, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"\n📊 Summary:")
        self.logger.info(f"  Total videos across all seasons: {total_videos}")
        self.logger.info(f"  Master list saved to: {output_path}")
        
        return {
            "total": total_videos,
            "successful": total_videos,
            "failed": 0
        }

    async def run_extraction(
        self,
        mode: str,
        headless: bool = True,
        delay: int = 5,
        output_dir: Optional[str] = None,
        season: Optional[int] = None,
        seasons: Optional[str] = None,
        limit: int = 1,
        unfiltered: bool = False,
    ) -> dict:
        """Run the transcript extraction."""
        # Update output directory if provided
        if output_dir:
            from transcript_fetcher.config import Config

            Config.OUTPUT_DIR = output_dir

        # Run extraction based on mode
        if mode == "collect-video-links":
            # Collect all video links from all seasons
            from transcript_fetcher.youtube_fetcher import YouTubeFetcher
            yt_fetcher = YouTubeFetcher(use_proxy=True)
            return await self.collect_all_video_links()
        elif mode == "fetch-youtube":
            # Fetch transcripts using youtube-transcript-api
            from transcript_fetcher.youtube_fetcher import YouTubeFetcher
            yt_fetcher = YouTubeFetcher(use_proxy=True)
            use_filtering = not unfiltered  # Use filtering unless --unfiltered flag is set
            if season:
                return yt_fetcher.fetch_season(season, use_filtered=use_filtering)
            else:
                # Fetch all seasons
                results = {"total": 0, "successful": 0, "failed": 0}
                for s in [1, 2, 3, 4]:
                    season_result = yt_fetcher.fetch_season(s, use_filtered=use_filtering)
                    results["total"] += season_result["stats"]["attempted"]
                    results["successful"] += season_result["stats"]["succeeded"]
                    results["failed"] += season_result["stats"]["failed"]
                return results
        elif mode == "fetch-pitches":
            # Fetch all pitch videos across all seasons
            from transcript_fetcher.youtube_fetcher import YouTubeFetcher
            import time
            
            print("\n=== Fetching All Pitch Videos (599 total) ===")
            print("This will fetch pitch videos from all 4 seasons")
            print("Expected: S1:100, S2:99, S3:200, S4:200\n")
            
            yt_fetcher = YouTubeFetcher(use_proxy=True)
            total_results = {"total": 0, "successful": 0, "failed": 0}
            
            # Fetch each season's pitch videos
            for s in [1, 2, 3, 4]:
                print(f"\n{'='*60}")
                print(f"Starting Season {s} Pitch Videos")
                print(f"{'='*60}")
                
                # Load filtered video list to get only pitch playlists
                import json
                from pathlib import Path
                filtered_path = Path(__file__).parent.parent / "data" / "filtered_video_list.json"
                
                with open(filtered_path, 'r') as f:
                    filtered_data = json.load(f)
                
                pitch_playlists = filtered_data['playlists_to_keep'][f'season_{s}']['pitches']
                if not pitch_playlists:
                    print(f"No pitch playlists for Season {s}, skipping...")
                    continue
                
                # Fetch only pitch videos for this season
                use_filtering = not unfiltered  # Use filtering unless --unfiltered flag is set
                season_result = yt_fetcher.fetch_season(s, use_filtered=use_filtering, pitch_only=True)
                total_results["total"] += season_result["stats"]["attempted"]
                total_results["successful"] += season_result["stats"]["succeeded"]
                total_results["failed"] += season_result["stats"]["failed"]
                
                # Sleep between seasons
                if s < 4:
                    print("\nSleeping 30 seconds before next season...")
                    time.sleep(30)
            
            print("\n" + "="*60)
            print("✅ All pitch videos fetching complete!")
            print(f"Total: {total_results['successful']}/{total_results['total']} successful")
            print("="*60)
            
            return total_results
        else:
            raise ValueError(f"Unknown mode: {mode}")

    async def main(self, args: Optional[list] = None) -> int:
        """Main CLI entry point."""
        parser = self.create_parser()
        parsed_args = parser.parse_args(args)

        # Handle background process management
        if parsed_args.status:
            status = self.get_background_status()
            if status["running"]:
                print(f"✅ Background process is running (PID: {status['pid']})")
                print(f"📝 Last update: {status['last_update']}")
                print(f"📊 Recent log entries:")
                for line in status["log_tail"][-5:]:
                    print(f"   {line.rstrip()}")
                files = self.get_background_files()
                print(f"🔍 Full log: {files['log_file']}")
            else:
                print(status["message"])
            return 0

        if parsed_args.stop:
            return 0 if self.stop_background_process() else 1

        if parsed_args.background:
            return 0 if self.start_background_process(parsed_args) else 1

        # Set up logging level
        if parsed_args.verbose:
            import logging

            logging.getLogger().setLevel(logging.DEBUG)

        # Validate that mode is provided if not using management commands
        if not parsed_args.mode and not any([parsed_args.status, parsed_args.stop]):
            parser.error("Mode is required unless using --status or --stop")


        try:
            print("🦈 Shark Tank India Transcript Fetcher")
            print("=" * 60)
            print(f"Mode: {parsed_args.mode}")
            if parsed_args.season:
                print(f"Season: {parsed_args.season}")
            if parsed_args.output_dir:
                print(f"Output: {parsed_args.output_dir}")
            print()

            # Run extraction
            results = await self.run_extraction(
                mode=parsed_args.mode,
                headless=not parsed_args.headful,
                delay=parsed_args.delay,
                output_dir=parsed_args.output_dir,
                season=parsed_args.season,
                seasons=parsed_args.seasons,
                limit=parsed_args.limit,
                unfiltered=parsed_args.unfiltered,
            )

            # Print final summary
            total = results["total"]
            successful = results["successful"]
            failed = results["failed"]

            print()
            print("🎯 Extraction Complete!")
            percentage = (successful/total*100) if total > 0 else 0
            print(
                f"📊 Results: {successful}/{total} successful ({percentage:.1f}%)"
            )

            if failed > 0:
                print(f"⚠️  {failed} videos failed to extract")
                return 1
            else:
                print("✅ All videos extracted successfully!")
                return 0

        except KeyboardInterrupt:
            print("\\n⚠️  Extraction interrupted by user")
            return 130
        except Exception as e:
            self.logger.error(f"Extraction failed: {e}")
            print(f"❌ Error: {e}")
            return 1


def main(args: Optional[list] = None) -> int:
    """Main entry point for the CLI."""
    cli = CLI()
    return asyncio.run(cli.main(args))


if __name__ == "__main__":
    sys.exit(main())
