"""
Transcript extraction functionality using tactiq.io service.
"""

import os
import time
import json
import asyncio
import aiohttp
from typing import Dict, Optional
from playwright.async_api import async_playwright, Page

from transcript_fetcher.models import VideoInfo, ExtractionResult
from transcript_fetcher.config import Config
from transcript_fetcher.utils import (
    setup_logger,
    clean_filename,
    determine_content_type,
    extract_company_name,
    get_current_timestamp,
)


class TranscriptExtractor:
    """Extracts transcripts from YouTube videos using tactiq.io."""

    def __init__(self, headless: bool = True, delay_between_requests: int = 5):
        self.headless = headless
        self.delay = delay_between_requests
        self.logger = setup_logger(self.__class__.__name__)

    async def get_video_title_fast(self, video_id: str) -> str:
        """Get video title using fast methods (oembed API, then yt-dlp fallback)."""

        # Method 1: YouTube oEmbed API (fastest)
        title = await self._get_title_from_oembed(video_id)
        if title and title != f"Video {video_id}":
            return title

        # Method 2: yt-dlp (fast, reliable)
        title = await self._get_title_from_ytdlp(video_id)
        if title and title != f"Video {video_id}":
            return title

        # Method 3: Fallback to browser (slow)
        self.logger.warning(f"Using slow browser method for {video_id}")
        return await self._get_title_from_browser(video_id)

    async def _get_title_from_oembed(self, video_id: str) -> str:
        """Get title from YouTube oEmbed API (fastest method)."""
        try:
            video_url = f"https://www.youtube.com/watch?v={video_id}"
            oembed_url = f"https://www.youtube.com/oembed?url={video_url}&format=json"

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    oembed_url, timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        title = data.get("title", "")
                        if title:
                            self.logger.info(f"📺 oEmbed: {video_id} -> {title}")
                            return title
        except Exception as e:
            self.logger.debug(f"oEmbed failed for {video_id}: {e}")

        return f"Video {video_id}"

    async def _get_title_from_ytdlp(self, video_id: str) -> str:
        """Get title using yt-dlp (fast and reliable)."""
        try:
            import subprocess
            import tempfile

            # Use yt-dlp to extract only metadata (no download)
            cmd = [
                "yt-dlp",
                "--dump-json",
                "--no-download",
                "--quiet",
                f"https://www.youtube.com/watch?v={video_id}",
            ]

            # Run with timeout
            result = await asyncio.create_subprocess_exec(
                *cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await asyncio.wait_for(result.communicate(), timeout=15.0)

            if result.returncode == 0 and stdout:
                data = json.loads(stdout.decode("utf-8"))
                title = data.get("title", "")
                if title:
                    self.logger.info(f"📺 yt-dlp: {video_id} -> {title}")
                    return title

        except asyncio.TimeoutError:
            self.logger.debug(f"yt-dlp timeout for {video_id}")
        except FileNotFoundError:
            self.logger.debug("yt-dlp not installed - skipping this method")
        except Exception as e:
            self.logger.debug(f"yt-dlp failed for {video_id}: {e}")

        return f"Video {video_id}"

    async def _get_title_from_browser(self, video_id: str) -> str:
        """Fallback: Get title from browser (slow but reliable)."""
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=self.headless)
                page = await browser.new_page()

                video_url = f"https://www.youtube.com/watch?v={video_id}"
                await page.goto(
                    video_url,
                    wait_until="networkidle",
                    timeout=Config.NAVIGATION_TIMEOUT,
                )

                # Wait for title element and extract
                await page.wait_for_selector("h1", timeout=10000)

                title = await page.evaluate(
                    """
                    () => {
                        // Use CSS selectors (not XPath)
                        const selectors = [
                            'h1.ytd-watch-metadata yt-formatted-string',
                            'ytd-watch-metadata h1 yt-formatted-string', 
                            'h1 yt-formatted-string',
                            '.ytd-watch-metadata h1',
                            'h1[class*="ytd"]',
                            'h1'
                        ];
                        
                        for (const selector of selectors) {
                            const element = document.querySelector(selector);
                            if (element && element.textContent.trim()) {
                                return element.textContent.trim();
                            }
                        }
                        
                        return document.title.replace(' - YouTube', '');
                    }
                """
                )

                await browser.close()

                if title:
                    self.logger.info(f"📺 Browser: {video_id} -> {title}")
                    return title

        except Exception as e:
            self.logger.warning(f"Browser title extraction failed for {video_id}: {e}")

        return f"Video {video_id}"

    # Keep old method for backward compatibility
    async def get_video_title(self, page: Page, video_id: str) -> str:
        """Legacy method - use get_video_title_fast instead."""
        return await self.get_video_title_fast(video_id)

    async def extract_single_transcript(
        self, video: VideoInfo, browser
    ) -> ExtractionResult:
        """Extract transcript for a single video with retry logic."""

        # Fix tactiq.io URL format - needs full YouTube URL as query parameter
        youtube_url = f"https://www.youtube.com/watch?v={video.video_id}"
        import urllib.parse

        encoded_url = urllib.parse.quote(youtube_url)
        transcript_url = f"{Config.BASE_URL}?yt={encoded_url}"

        self.logger.info(
            f"Processing S{video.season:02d}E{video.episode:02d}: {video.video_id} - {video.title}"
        )

        max_retries = Config.MAX_RETRIES
        for attempt in range(max_retries):
            page = await browser.new_page()

            try:
                if attempt > 0:
                    self.logger.info(
                        f"🔄 Retry {attempt + 1}/{max_retries} for S{video.season:02d}E{video.episode:02d}: {video.video_id}"
                    )

                # Navigate to tactiq transcript page
                await page.goto(
                    transcript_url,
                    wait_until="networkidle",
                    timeout=Config.PAGE_TIMEOUT,
                )

                # Wait for transcript container
                await page.wait_for_selector("#transcript", timeout=Config.PAGE_TIMEOUT)

                # Wait for content to load (transcript should be populated)
                await page.wait_for_function(
                    """
                    () => {
                        const transcript = document.querySelector('#transcript');
                        return transcript && transcript.textContent.trim().length > 100;
                    }
                """,
                    timeout=Config.PAGE_TIMEOUT,
                )

                # Extract transcript text
                transcript_content = await page.evaluate(
                    """
                    () => {
                        const transcript = document.querySelector('#transcript');
                        return transcript ? transcript.textContent.trim() : '';
                    }
                """
                )

                if transcript_content and len(transcript_content) > 100:
                    # Save transcript to file
                    filepath = await self._save_transcript(video, transcript_content)

                    self.logger.info(
                        f"✓ S{video.season:02d}E{video.episode:02d}: {len(transcript_content)} chars"
                    )
                    self.logger.info(f"Saved: {filepath}")

                    return ExtractionResult(
                        status="success",
                        video_id=video.video_id,
                        season=video.season,
                        episode=video.episode,
                        length=len(transcript_content),
                        filepath=filepath,
                    )
                else:
                    # If no content but no error, retry
                    if attempt < max_retries - 1:
                        self.logger.warning(
                            f"⚠️  S{video.season:02d}E{video.episode:02d}: No transcript content (attempt {attempt + 1}/{max_retries})"
                        )
                        await page.close()
                        await asyncio.sleep(
                            Config.RETRY_DELAY_BASE * (attempt + 1)
                        )  # Increasing delay between retries
                        continue
                    else:
                        self.logger.warning(
                            f"✗ S{video.season:02d}E{video.episode:02d}: No transcript content after {max_retries} attempts"
                        )
                        return ExtractionResult(
                            status="failed",
                            video_id=video.video_id,
                            season=video.season,
                            episode=video.episode,
                            error="No transcript content after retries",
                        )

            except Exception as e:
                if attempt < max_retries - 1:
                    self.logger.warning(
                        f"⚠️  S{video.season:02d}E{video.episode:02d}: {str(e)} (attempt {attempt + 1}/{max_retries})"
                    )
                    await page.close()
                    await asyncio.sleep(
                        Config.RETRY_DELAY_BASE * (attempt + 1)
                    )  # Increasing delay between retries
                    continue
                else:
                    self.logger.error(
                        f"✗ S{video.season:02d}E{video.episode:02d}: {str(e)} (failed after {max_retries} attempts)"
                    )
                    return ExtractionResult(
                        status="failed",
                        video_id=video.video_id,
                        season=video.season,
                        episode=video.episode,
                        error=f"{str(e)} (after {max_retries} retries)",
                    )
            finally:
                await page.close()

    async def _save_transcript(self, video: VideoInfo, transcript_content: str) -> str:
        """Save transcript to file with proper naming and metadata."""
        # Determine content type and generate appropriate filename
        content_type = determine_content_type(video.title)

        # Use YouTube video title as filename (cleaned for filesystem)
        clean_title = clean_filename(video.title)
        filename = f"S{video.season:02d}_{clean_title}_{video.video_id}_tactiq.txt"

        # Save to raw/ subdirectory
        filepath = os.path.join(
            Config.OUTPUT_DIR,
            f"season_{video.season}",
            "raw",
            filename,
        )
        # Ensure directory exists
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # Check if file already exists
        if os.path.exists(filepath):
            self.logger.info(f"⏭️  Skipping {video.video_id}: File already exists")
            return filepath

        # Write transcript with metadata
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(f"Video ID: {video.video_id}\\n")
            f.write(f"Season: {video.season}\\n")
            f.write(f"Episode: {video.episode}\\n")
            f.write(f"Content Type: {content_type}\\n")
            f.write(f"Title (English): {video.title}\\n")
            if hasattr(video, "original_title") and video.original_title:
                f.write(f"Title (Original): {video.original_title}\\n")
            f.write(f"URL: {video.url}\\n")
            f.write(f"Playlist ID: {video.playlist_id}\\n")
            if hasattr(video, "actual_playlist_title") and video.actual_playlist_title:
                f.write(f"Playlist Title: {video.actual_playlist_title}\\n")
            f.write(f"Extracted: {get_current_timestamp()}\\n")
            f.write(f"Method: tactiq.io\\n")
            f.write(
                f"Official Episode Count (Season {video.season}): {Config.OFFICIAL_EPISODE_COUNTS.get(video.season, 'Unknown')}\\n"
            )
            f.write("-" * 50 + "\\n\\n")
            f.write(transcript_content)

        return filepath
