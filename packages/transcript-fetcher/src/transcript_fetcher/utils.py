"""
Utility functions for the transcript fetcher.
"""

import re
import time
import logging
from typing import Optional


def setup_logger(
    name: str = "transcript_fetcher", level: int = logging.INFO
) -> logging.Logger:
    """Set up logger with consistent formatting."""
    logger = logging.getLogger(name)
    logger.setLevel(level)

    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
        handler.setFormatter(formatter)
        logger.addHandler(handler)

    return logger


def clean_filename(title: str, max_length: int = 100) -> str:
    """Clean title for use as filename."""
    # Remove invalid characters for filenames
    cleaned = "".join(c for c in title if c.isalnum() or c in (" ", "-", "_")).rstrip()
    # Replace spaces with underscores and limit length
    cleaned = cleaned.replace(" ", "_")[:max_length]
    return cleaned if cleaned else "Unknown_Video"


def clean_folder_name(name: str, max_length: int = 100) -> str:
    """Clean playlist name for use as folder name."""
    # Remove invalid characters for folder names
    cleaned = "".join(c for c in name if c.isalnum() or c in (" ", "-", "_")).strip()
    # Replace spaces with underscores and limit length
    cleaned = cleaned.replace(" ", "_")[:max_length]
    return cleaned if cleaned else "Unknown_Playlist"


def determine_content_type(title: str) -> str:
    """Determine if video is a full episode or individual pitch."""
    title_lower = title.lower()

    # Check for full episode indicators
    episode_keywords = [
        "full episode",
        "complete episode",
        "episode",
        "ep",
        "full show",
        "complete show",
        "entire episode",
    ]

    # Check for individual pitch indicators
    pitch_keywords = [
        "pitch",
        "presentation",
        "startup",
        "business idea",
        "entrepreneur",
        "founder",
        "company",
    ]

    # If title contains episode indicators
    for keyword in episode_keywords:
        if keyword in title_lower:
            return "episode"

    # If title contains pitch indicators
    for keyword in pitch_keywords:
        if keyword in title_lower:
            return "pitch"

    # Default to generic content
    return "content"


def extract_company_name(title: str) -> str:
    """Extract company name from pitch video title."""
    # Look for company name in quotes or parentheses
    quoted_match = re.search(r"'([^']+)'|\"([^\"]+)\"", title)
    if quoted_match:
        return quoted_match.group(1) or quoted_match.group(2)

    # Extract first few words as company name
    words = title.strip().split()[:3]  # Take first 3 words max
    company_name = "_".join(words)

    # Clean up company name for filename
    company_name = re.sub(r"[^\w\s-]", "", company_name)
    company_name = re.sub(r"\s+", "_", company_name)

    return company_name if company_name else "Unknown_Company"


def get_current_timestamp() -> str:
    """Get current timestamp as formatted string."""
    return time.strftime("%Y-%m-%d %H:%M:%S")


def format_duration(seconds: float) -> str:
    """Format duration in seconds to human-readable string."""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"


def sanitize_video_id(video_id: str) -> Optional[str]:
    """Validate and sanitize YouTube video ID."""
    if not video_id or len(video_id) != 11:
        return None

    # YouTube video IDs are 11 characters long and contain only alphanumeric, - and _
    if re.match(r"^[A-Za-z0-9_-]{11}$", video_id):
        return video_id

    return None
