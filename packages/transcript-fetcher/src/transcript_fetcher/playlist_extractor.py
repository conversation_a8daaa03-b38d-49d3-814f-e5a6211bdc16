"""
YouTube playlist video extraction functionality.
"""

import async<PERSON>
from typing import List, <PERSON><PERSON>
from playwright.async_api import async_playwright, <PERSON>

from transcript_fetcher.models import VideoInfo
from transcript_fetcher.config import Config
from transcript_fetcher.utils import setup_logger, sanitize_video_id
from transcript_fetcher.cache import PlaylistCache


class PlaylistExtractor:
    """Extracts video information from YouTube playlists."""

    def __init__(self, headless: bool = True):
        self.headless = headless
        self.logger = setup_logger(self.__class__.__name__)
        self.cache = PlaylistCache()

    async def get_playlist_title(self, page: Page, playlist_url: str) -> str:
        """Extract the actual playlist title from YouTube."""
        try:
            await page.goto(
                playlist_url,
                wait_until="networkidle",
                timeout=Config.NAVIGATION_TIMEOUT,
            )

            # Get playlist title
            playlist_title = await page.evaluate(
                """
                () => {
                    // Try multiple selectors for playlist title
                    const selectors = [
                        'h1.style-scope.ytd-playlist-header-renderer',
                        '.ytd-playlist-header-renderer h1',
                        'h1[class*="playlist"]',
                        '.playlist-header h1',
                        'h1'
                    ];
                    
                    for (const selector of selectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim()) {
                            return element.textContent.trim();
                        }
                    }
                    
                    // Fallback to page title
                    return document.title.replace(' - YouTube', '');
                }
            """
            )

            return playlist_title if playlist_title else "Unknown Playlist"
        except Exception as e:
            self.logger.warning(f"Could not extract playlist title: {e}")
            return "Unknown Playlist"

    async def extract_videos_from_playlist(
        self, season: int, playlist_url: str, test_mode: bool = False
    ) -> Tuple[List[VideoInfo], str]:
        """Extract videos from a specific playlist and return playlist title."""
        playlist_id = playlist_url.split("list=")[1] if "list=" in playlist_url else ""

        # Check cache first (use cache even in test mode, but filter results)
        cached_videos = self.cache.get_playlist_videos(playlist_url)
        if cached_videos:
            # Get playlist title from first video's cached data
            playlist_title = (
                cached_videos[0].actual_playlist_title
                if cached_videos and hasattr(cached_videos[0], "actual_playlist_title")
                else "Cached Playlist"
            )

            # In test mode, return only the first video
            if test_mode:
                test_videos = cached_videos[:1]
                self.logger.info(
                    f"📦 Using cached data for playlist {playlist_url}: {len(test_videos)} videos (test mode)"
                )
                return test_videos, playlist_title
            else:
                self.logger.info(
                    f"📦 Using cached data for playlist {playlist_url}: {len(cached_videos)} videos"
                )
                return cached_videos, playlist_title

        self.logger.info(f"Loading Season {season} playlist...")

        videos = []
        playlist_title = ""

        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=self.headless)
            page = await browser.new_page()

            try:
                # Get playlist title first
                playlist_title = await self.get_playlist_title(page, playlist_url)
                self.logger.info(f"Loading Season {season} playlist: {playlist_title}")

                await page.goto(
                    playlist_url, wait_until="networkidle", timeout=Config.PAGE_TIMEOUT
                )

                # Wait for initial content
                await page.wait_for_timeout(3000)

                # Scroll to load videos (skip scrolling in test mode)
                if not test_mode:
                    await self._scroll_to_load_all_videos(page, season)
                else:
                    self.logger.info(
                        f"Test mode: Loading only first video from playlist"
                    )
                    current_count = await page.evaluate(
                        """
                        () => document.querySelectorAll('a[href*="/watch?v="]').length
                    """
                    )

                # Extract video information
                video_data = await self._extract_video_data(page, season, test_mode)

                # Convert to VideoInfo objects
                for i, video_data_item in enumerate(video_data):
                    video_id = sanitize_video_id(video_data_item["video_id"])
                    if video_id:
                        video = VideoInfo(
                            video_id=video_id,
                            season=season,
                            episode=i + 1,
                            title=video_data_item["title"],
                            url=video_data_item["url"],
                            playlist_id=playlist_id,
                        )
                        videos.append(video)

                self.logger.info(
                    f"Successfully extracted {len(videos)} videos for Season {season}"
                )

                # Cache the results (skip cache for test mode)
                if not test_mode and videos:
                    # Add playlist title to video objects for caching
                    for video in videos:
                        video.actual_playlist_title = playlist_title

                    self.cache.save_playlist_videos(playlist_id, videos, playlist_title)
                    self.logger.info(
                        f"💾 Cached {len(videos)} videos for playlist {playlist_id}"
                    )

            except Exception as e:
                self.logger.error(
                    f"Error extracting Season {season} playlist videos: {e}"
                )
            finally:
                await browser.close()

        return videos, playlist_title

    async def _scroll_to_load_all_videos(self, page: Page, season: int):
        """Scroll to load all videos in the playlist."""
        self.logger.info(f"Scrolling to load all Season {season} videos...")
        last_count = 0
        scroll_count = 0

        while scroll_count < Config.MAX_SCROLLS:
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await page.wait_for_timeout(Config.SCROLL_DELAY)

            current_count = await page.evaluate(
                """
                () => document.querySelectorAll('a[href*="/watch?v="]').length
            """
            )

            if current_count == last_count:
                break

            last_count = current_count
            scroll_count += 1

            # Continue scrolling until no more videos load
            if current_count >= Config.MAX_VIDEOS_PER_PLAYLIST:
                break

    async def _extract_video_data(
        self, page: Page, season: int, test_mode: bool
    ) -> List[dict]:
        """Extract video information using JavaScript evaluation."""
        video_data = await page.evaluate(
            """
            (args) => {
                const { season, testMode } = args;
                const videos = [];
                const links = document.querySelectorAll('a[href*="/watch?v="]');
                const seen_ids = new Set();
                
                for (let i = 0; i < links.length; i++) {
                    // In test mode, only process first video
                    if (testMode && videos.length >= 1) {
                        break;
                    }
                    
                    const link = links[i];
                    const href = link.href;
                    const videoIdMatch = href.match(/[?&]v=([^&]+)/);
                    
                    if (videoIdMatch) {
                        const videoId = videoIdMatch[1];
                        
                        if (!seen_ids.has(videoId) && videoId.length === 11) {
                            seen_ids.add(videoId);
                            
                            // Playlist is just for getting video IDs - title will be fetched from individual video page
                            const title = `Season ${season} Video ${videos.length + 1}`;
                            
                            videos.push({
                                video_id: videoId,
                                title: title,
                                url: `https://www.youtube.com/watch?v=${videoId}`
                            });
                        }
                    }
                }
                
                console.log(`Found ${links.length} links, extracted ${videos.length} videos`);
                return videos;
            }
        """,
            {"season": season, "testMode": test_mode},
        )

        return video_data

    async def extract_videos_from_playlist_with_limit(
        self, season: int, playlist_url: str, limit: int = 1
    ) -> Tuple[List[VideoInfo], str]:
        """Extract limited number of videos from a specific playlist."""
        playlist_id = playlist_url.split("list=")[1] if "list=" in playlist_url else ""

        # Check cache first for the full playlist
        cached_videos = self.cache.get_playlist_videos(playlist_url)
        if cached_videos:
            # Get playlist title from first video's cached data
            playlist_title = (
                cached_videos[0].actual_playlist_title
                if cached_videos and hasattr(cached_videos[0], "actual_playlist_title")
                else "Cached Playlist"
            )

            # Return only the limited number of videos
            limited_videos = cached_videos[:limit]
            self.logger.info(
                f"📦 Using cached data for playlist {playlist_url}: {len(limited_videos)} videos (limited from {len(cached_videos)} total)"
            )
            return limited_videos, playlist_title

        self.logger.info(f"Loading Season {season} playlist with limit {limit}...")

        videos = []
        playlist_title = ""

        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=self.headless)
            page = await browser.new_page()

            try:
                # Get playlist title first
                playlist_title = await self.get_playlist_title(page, playlist_url)
                self.logger.info(f"Loading Season {season} playlist: {playlist_title}")

                await page.goto(
                    playlist_url, wait_until="networkidle", timeout=Config.PAGE_TIMEOUT
                )

                # Wait for initial content
                await page.wait_for_timeout(3000)

                # Extract video information with limit
                video_data = await self._extract_video_data_with_limit(
                    page, season, limit
                )

                # Convert to VideoInfo objects
                playlist_id = (
                    playlist_url.split("list=")[1] if "list=" in playlist_url else ""
                )

                for i, video_data_item in enumerate(video_data):
                    video_id = sanitize_video_id(video_data_item["video_id"])
                    if video_id:
                        video = VideoInfo(
                            video_id=video_id,
                            season=season,
                            episode=i + 1,
                            title=video_data_item["title"],
                            url=video_data_item["url"],
                            playlist_id=playlist_id,
                        )
                        videos.append(video)

                self.logger.info(
                    f"Successfully extracted {len(videos)} videos for Season {season}"
                )

                # Cache the results - but we need to extract ALL videos first for proper caching
                # For limit mode, we'll extract all but return only limited number
                if len(videos) == limit:
                    # This was a limited extraction, we should extract all videos for proper caching
                    self.logger.info(
                        f"Limited extraction completed, extracting full playlist for caching..."
                    )

                    # Get all videos for caching
                    all_video_data = await self._extract_video_data(page, season, False)
                    all_videos = []

                    for i, video_data_item in enumerate(all_video_data):
                        video_id = sanitize_video_id(video_data_item["video_id"])
                        if video_id:
                            video = VideoInfo(
                                video_id=video_id,
                                season=season,
                                episode=i + 1,
                                title=video_data_item["title"],
                                url=video_data_item["url"],
                                playlist_id=playlist_id,
                            )
                            # Add playlist title to video objects for caching
                            video.actual_playlist_title = playlist_title
                            all_videos.append(video)

                    # Cache all videos
                    if all_videos:
                        self.cache.save_playlist_videos(
                            playlist_id, all_videos, playlist_title
                        )
                        self.logger.info(
                            f"💾 Cached {len(all_videos)} videos for playlist {playlist_id} (returned {limit})"
                        )

            except Exception as e:
                self.logger.error(
                    f"Error extracting Season {season} playlist videos: {e}"
                )
            finally:
                await browser.close()

        return videos, playlist_title

    async def _extract_video_data_with_limit(
        self, page: Page, season: int, limit: int
    ) -> List[dict]:
        """Extract limited number of video information using JavaScript evaluation."""
        video_data = await page.evaluate(
            """
            (args) => {
                const { season, limit } = args;
                const videos = [];
                const links = document.querySelectorAll('a[href*="/watch?v="]');
                const seen_ids = new Set();
                
                for (let i = 0; i < links.length; i++) {
                    // Stop when we reach the limit
                    if (videos.length >= limit) {
                        break;
                    }
                    
                    const link = links[i];
                    const href = link.href;
                    const videoIdMatch = href.match(/[?&]v=([^&]+)/);
                    
                    if (videoIdMatch) {
                        const videoId = videoIdMatch[1];
                        
                        if (!seen_ids.has(videoId) && videoId.length === 11) {
                            seen_ids.add(videoId);
                            
                            // Playlist is just for getting video IDs - title will be fetched from individual video page
                            const title = `Season ${season} Video ${videos.length + 1}`;
                            
                            videos.push({
                                video_id: videoId,
                                title: title,
                                url: `https://www.youtube.com/watch?v=${videoId}`
                            });
                        }
                    }
                }
                
                console.log(`Found ${links.length} links, extracted ${videos.length} videos (limit: ${limit})`);
                return videos;
            }
        """,
            {"season": season, "limit": limit},
        )

        return video_data
