"""
SQLite-based caching system for playlist data.
"""

import os
import sqlite3
import json
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from transcript_fetcher.models import VideoInfo, PlaylistInfo
from transcript_fetcher.config import Config
from transcript_fetcher.utils import get_current_timestamp


class PlaylistCache:
    """SQLite-based cache for playlist video data."""

    def __init__(self, cache_dir: Optional[str] = None):
        self.cache_dir = cache_dir or os.path.join(Config.OUTPUT_DIR, ".cache")
        os.makedirs(self.cache_dir, exist_ok=True)

        self.db_path = os.path.join(self.cache_dir, "playlist_cache.db")

        # Cache is permanent for stable datasets like Shark Tank episodes
        self.cache_expiry_hours = None  # No expiration

        # Initialize database
        self._init_db()

    def _init_db(self):
        """Initialize SQLite database with required tables."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                """
                CREATE TABLE IF NOT EXISTS playlists (
                    playlist_id TEXT PRIMARY KEY,
                    playlist_title TEXT NOT NULL,
                    video_count INTEGER NOT NULL,
                    cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    season INTEGER NOT NULL
                )
            """
            )

            conn.execute(
                """
                CREATE TABLE IF NOT EXISTS videos (
                    video_id TEXT PRIMARY KEY,
                    playlist_id TEXT NOT NULL,
                    title TEXT NOT NULL,
                    url TEXT NOT NULL,
                    season INTEGER NOT NULL,
                    episode INTEGER DEFAULT 0,
                    playlist_folder TEXT,
                    actual_playlist_title TEXT,
                    cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (playlist_id) REFERENCES playlists (playlist_id)
                )
            """
            )

            # Create indexes for faster queries
            conn.execute(
                "CREATE INDEX IF NOT EXISTS idx_videos_playlist_id ON videos (playlist_id)"
            )
            conn.execute(
                "CREATE INDEX IF NOT EXISTS idx_videos_season ON videos (season)"
            )
            conn.execute(
                "CREATE INDEX IF NOT EXISTS idx_playlists_cached_at ON playlists (cached_at)"
            )

            conn.commit()

    def _is_cache_valid(self, cached_at: str) -> bool:
        """Check if cache entry is valid (not expired)."""
        # Cache is permanent for stable datasets
        if self.cache_expiry_hours is None:
            return True
        
        try:
            cache_time = datetime.fromisoformat(cached_at.replace("Z", "+00:00"))
            expiry_time = cache_time + timedelta(hours=self.cache_expiry_hours)
            return datetime.now() < expiry_time
        except Exception:
            return False

    def get_playlist_videos(self, playlist_url: str) -> Optional[List[VideoInfo]]:
        """Get cached playlist videos if available and valid."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row

                # Extract playlist_id from URL for backward compatibility
                playlist_id = (
                    playlist_url.split("list=")[1]
                    if "list=" in playlist_url
                    else playlist_url
                )

                # Check if playlist exists and is valid
                playlist_row = conn.execute(
                    """
                    SELECT playlist_id, playlist_title, cached_at, season
                    FROM playlists 
                    WHERE playlist_id = ?
                """,
                    (playlist_id,),
                ).fetchone()

                if not playlist_row or not self._is_cache_valid(
                    playlist_row["cached_at"]
                ):
                    return None

                # Get videos for this playlist
                video_rows = conn.execute(
                    """
                    SELECT video_id, title, url, season, episode, 
                           playlist_folder, actual_playlist_title
                    FROM videos 
                    WHERE playlist_id = ?
                    ORDER BY episode
                """,
                    (playlist_id,),
                ).fetchall()

                if not video_rows:
                    return None

                # Convert to VideoInfo objects
                videos = []
                for row in video_rows:
                    video = VideoInfo(
                        video_id=row["video_id"],
                        title=row["title"],
                        url=row["url"],
                        season=row["season"],
                        episode=row["episode"] or 0,  # Provide default episode
                        playlist_id=playlist_id,
                    )
                    if row["playlist_folder"]:
                        video.playlist_folder = row["playlist_folder"]
                    if row["actual_playlist_title"]:
                        video.actual_playlist_title = row["actual_playlist_title"]
                    videos.append(video)

                print(f"📦 Cache hit: {playlist_url} -> {len(videos)} videos")
                return videos

        except Exception as e:
            print(f"⚠️  Cache read error for {playlist_id}: {e}")
            return None

    def save_playlist_videos(
        self, playlist_id: str, videos: List[VideoInfo], playlist_title: str
    ):
        """Save playlist videos to cache."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                season = videos[0].season if videos else 1

                # Insert or update playlist
                conn.execute(
                    """
                    INSERT OR REPLACE INTO playlists 
                    (playlist_id, playlist_title, video_count, season, cached_at)
                    VALUES (?, ?, ?, ?, ?)
                """,
                    (
                        playlist_id,
                        playlist_title,
                        len(videos),
                        season,
                        get_current_timestamp(),
                    ),
                )

                # Clear existing videos for this playlist
                conn.execute("DELETE FROM videos WHERE playlist_id = ?", (playlist_id,))

                # Insert videos
                for video in videos:
                    conn.execute(
                        """
                        INSERT INTO videos 
                        (video_id, playlist_id, title, url, season, episode, 
                         playlist_folder, actual_playlist_title, cached_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                        (
                            video.video_id,
                            playlist_id,
                            video.title,
                            video.url,
                            video.season,
                            getattr(video, "episode", 0),
                            getattr(video, "playlist_folder", None),
                            getattr(video, "actual_playlist_title", None),
                            get_current_timestamp(),
                        ),
                    )

                conn.commit()
                print(f"💾 Cached: {playlist_id} -> {len(videos)} videos")

        except Exception as e:
            print(f"⚠️  Cache write error for {playlist_id}: {e}")

    def clear_cache(self):
        """Clear all cached playlist data."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("DELETE FROM videos")
                conn.execute("DELETE FROM playlists")
                conn.commit()
            print("🗑️  Cache cleared")
        except Exception as e:
            print(f"⚠️  Cache clear error: {e}")

    def clear_expired_cache(self):
        """Clear only expired cache entries."""
        # No-op when cache is permanent
        if self.cache_expiry_hours is None:
            print("📦 Cache is permanent - no expiration cleanup needed")
            return
            
        try:
            cutoff_time = datetime.now() - timedelta(hours=self.cache_expiry_hours)
            cutoff_str = cutoff_time.isoformat()

            with sqlite3.connect(self.db_path) as conn:
                # Get expired playlist IDs
                expired_playlists = conn.execute(
                    """
                    SELECT playlist_id FROM playlists 
                    WHERE cached_at < ?
                """,
                    (cutoff_str,),
                ).fetchall()

                # Delete expired videos and playlists
                conn.execute("DELETE FROM videos WHERE cached_at < ?", (cutoff_str,))
                conn.execute("DELETE FROM playlists WHERE cached_at < ?", (cutoff_str,))

                conn.commit()

                if expired_playlists:
                    print(f"🗑️  Cleared {len(expired_playlists)} expired cache entries")

        except Exception as e:
            print(f"⚠️  Cache cleanup error: {e}")

    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about cached playlists."""
        cache_info = {
            "cached_playlists": 0,
            "total_cached_videos": 0,
            "cache_files": [],
        }

        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row

                # Get playlist info
                playlist_rows = conn.execute(
                    """
                    SELECT playlist_id, playlist_title, video_count, cached_at, season
                    FROM playlists 
                    ORDER BY cached_at DESC
                """
                ).fetchall()

                valid_playlists = 0
                total_videos = 0

                for row in playlist_rows:
                    if self._is_cache_valid(row["cached_at"]):
                        valid_playlists += 1
                        total_videos += row["video_count"]
                        cache_info["cache_files"].append(
                            {
                                "playlist_id": row["playlist_id"],
                                "title": row["playlist_title"],
                                "video_count": row["video_count"],
                                "season": row["season"],
                                "cached_at": row["cached_at"],
                            }
                        )

                cache_info["cached_playlists"] = valid_playlists
                cache_info["total_cached_videos"] = total_videos

        except Exception as e:
            print(f"⚠️  Cache info error: {e}")

        return cache_info
