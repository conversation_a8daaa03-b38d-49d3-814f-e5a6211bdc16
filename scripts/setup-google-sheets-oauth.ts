#!/usr/bin/env bun
import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import readline from 'readline';
import fs from 'fs/promises';
import path from 'path';

const SCOPES = ['https://www.googleapis.com/auth/spreadsheets'];
const TOKEN_PATH = path.join(process.cwd(), '.credentials/sheets-token.json');
const CREDENTIALS_PATH = path.join(process.cwd(), '.credentials/oauth2-credentials.json');

/**
 * Setup OAuth2 authentication for Google Sheets
 */
class GoogleSheetsOAuthSetup {
  private oAuth2Client?: OAuth2Client;
  
  /**
   * Load or request authorization
   */
  async authorize(): Promise<OAuth2Client> {
    // Check if we have credentials file
    const hasCredentials = await this.fileExists(CREDENTIALS_PATH);
    if (!hasCredentials) {
      console.log('❌ OAuth2 credentials file not found.');
      console.log('\n📋 To set up OAuth2 authentication:');
      console.log('1. Go to https://console.cloud.google.com/apis/credentials');
      console.log('2. Create a new OAuth 2.0 Client ID (Desktop application)');
      console.log('3. Download the credentials JSON');
      console.log(`4. Save it as: ${CREDENTIALS_PATH}`);
      console.log('\nAlternatively, you can use a service account:');
      console.log('1. Create a service account in Google Cloud Console');
      console.log('2. Share your Google Sheet with the service account email');
      process.exit(1);
    }
    
    // Load client credentials
    const credentials = JSON.parse(await fs.readFile(CREDENTIALS_PATH, 'utf-8'));
    const { client_secret, client_id, redirect_uris } = credentials.installed || credentials.web;
    
    this.oAuth2Client = new google.auth.OAuth2(
      client_id,
      client_secret,
      redirect_uris[0]
    );
    
    // Check if we have a token
    const hasToken = await this.fileExists(TOKEN_PATH);
    if (hasToken) {
      const token = JSON.parse(await fs.readFile(TOKEN_PATH, 'utf-8'));
      this.oAuth2Client.setCredentials(token);
    } else {
      await this.getNewToken();
    }
    
    return this.oAuth2Client;
  }
  
  /**
   * Get new token through OAuth2 flow
   */
  private async getNewToken(): Promise<void> {
    if (!this.oAuth2Client) throw new Error('OAuth2 client not initialized');
    
    const authUrl = this.oAuth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: SCOPES,
    });
    
    console.log('🔗 Authorize this app by visiting this URL:');
    console.log(authUrl);
    
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
    
    const code = await new Promise<string>((resolve) => {
      rl.question('\n📝 Enter the authorization code from the URL: ', (code) => {
        rl.close();
        resolve(code);
      });
    });
    
    const { tokens } = await this.oAuth2Client.getToken(code);
    this.oAuth2Client.setCredentials(tokens);
    
    // Save the token
    await fs.mkdir(path.dirname(TOKEN_PATH), { recursive: true });
    await fs.writeFile(TOKEN_PATH, JSON.stringify(tokens));
    
    console.log('✅ Token stored successfully!');
  }
  
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * Alternative: Use Service Account
 */
async function setupServiceAccount() {
  console.log('\n🔐 Service Account Setup Instructions:\n');
  console.log('1. Create a service account:');
  console.log('   gcloud iam service-accounts create sheets-sync \\');
  console.log('     --display-name="Google Sheets Sync Service"');
  console.log('');
  console.log('2. Create and download key:');
  console.log('   gcloud iam service-accounts keys create \\');
  console.log('     .credentials/service-account.json \\');
  console.log('     --iam-account=sheets-sync@$(gcloud config get-value project).iam.gserviceaccount.com');
  console.log('');
  console.log('3. Share your Google Sheet with the service account email:');
  console.log('   sheets-sync@$(gcloud config get-value project).iam.gserviceaccount.com');
  console.log('');
  console.log('4. Update your code to use service account:');
  console.log(`
import { GoogleAuth } from 'google-auth-library';

const auth = new GoogleAuth({
  keyFile: '.credentials/service-account.json',
  scopes: ['https://www.googleapis.com/auth/spreadsheets'],
});
`);
}

/**
 * Quick setup using existing gcloud auth (simplest approach)
 */
async function quickSetupWithGcloud() {
  console.log('\n⚡ Quick Setup with gcloud (Recommended):\n');
  console.log('Since you are already logged in with gcloud, the easiest approach is:');
  console.log('');
  console.log('1. Make sure your Google Sheet is shared with: <EMAIL>');
  console.log('   (Go to your sheet → Share → Add <EMAIL> with at least Viewer access)');
  console.log('');
  console.log('2. The sheet URL you provided:');
  console.log('   https://docs.google.com/spreadsheets/d/1Yr7uShp6y1pUxwaNjzdOjV0VH18zsHM-a7gBnpdzGDY/');
  console.log('');
  console.log('3. Check sharing settings by clicking "Share" button in the spreadsheet');
  console.log('');
  console.log('Once shared, run: bun run scripts/test-google-sheets.ts');
}

// Main
async function main() {
  console.log('🔧 Google Sheets Authentication Setup\n');
  console.log('Choose your authentication method:\n');
  console.log('1. OAuth2 (Interactive - for development)');
  console.log('2. Service Account (Non-interactive - for production)');
  console.log('3. Use existing gcloud auth (Simplest - already set up)\n');
  
  await quickSetupWithGcloud();
  
  console.log('\n---\n');
  await setupServiceAccount();
  
  console.log('\n---\n');
  console.log('For OAuth2 setup, run:');
  console.log('bun run scripts/setup-google-sheets-oauth.ts --oauth');
}

// Handle OAuth setup if requested
if (process.argv.includes('--oauth')) {
  const setup = new GoogleSheetsOAuthSetup();
  setup.authorize()
    .then(() => console.log('✅ OAuth2 setup complete!'))
    .catch(console.error);
} else {
  main();
}