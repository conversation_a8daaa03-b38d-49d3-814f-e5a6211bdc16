#!/usr/bin/env bun
import { GoogleSheetsService } from '../packages/google-sheets/src';
import { GoogleAuth } from 'google-auth-library';
import fs from 'fs/promises';
import path from 'path';

interface SyncConfig {
  spreadsheetUrl: string;
  sheetName?: string;
  localPath?: string;
  syncDirection?: 'pull' | 'push' | 'bidirectional';
}

class GoogleSheetsSync {
  private service: GoogleSheetsService;
  private spreadsheetId: string;
  
  constructor(spreadsheetUrl: string) {
    const auth = new GoogleAuth({
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
    });
    
    this.service = new GoogleSheetsService(auth);
    this.spreadsheetId = GoogleSheetsService.extractSpreadsheetId(spreadsheetUrl);
  }
  
  /**
   * Pull data from Google Sheets to local JSON file
   */
  async pullToJson(sheetName: string, outputPath: string): Promise<void> {
    console.log(`📥 Pulling data from sheet: ${sheetName}`);
    
    // Read all data from the sheet
    const range = `'${sheetName}'!A:Z`;
    const data = await this.service.readRange(this.spreadsheetId, range);
    
    if (data.values.length === 0) {
      console.log('⚠️  No data found in sheet');
      return;
    }
    
    // Convert to JSON format (assuming first row is headers)
    const headers = data.values[0];
    const rows = data.values.slice(1);
    
    const jsonData = rows.map(row => {
      const obj: Record<string, any> = {};
      headers.forEach((header, index) => {
        obj[header] = row[index] || '';
      });
      return obj;
    });
    
    // Save to file
    await fs.mkdir(path.dirname(outputPath), { recursive: true });
    await fs.writeFile(outputPath, JSON.stringify(jsonData, null, 2));
    
    console.log(`✅ Saved ${jsonData.length} records to ${outputPath}`);
  }
  
  /**
   * Push data from local JSON file to Google Sheets
   */
  async pushFromJson(jsonPath: string, sheetName: string): Promise<void> {
    console.log(`📤 Pushing data to sheet: ${sheetName}`);
    
    // Read JSON file
    const jsonContent = await fs.readFile(jsonPath, 'utf-8');
    const jsonData = JSON.parse(jsonContent);
    
    if (!Array.isArray(jsonData) || jsonData.length === 0) {
      console.log('⚠️  No valid data found in JSON file');
      return;
    }
    
    // Convert JSON to sheet format
    const headers = Object.keys(jsonData[0]);
    const values = [
      headers,
      ...jsonData.map(obj => headers.map(h => obj[h] || ''))
    ];
    
    // Clear existing data and write new data
    const range = `'${sheetName}'!A:Z`;
    await this.service.clearRange(this.spreadsheetId, range);
    await this.service.writeRange(this.spreadsheetId, `'${sheetName}'!A1`, values);
    
    console.log(`✅ Uploaded ${jsonData.length} records to Google Sheets`);
  }
  
  /**
   * Sync with TypeScript interfaces
   */
  async generateTypeScriptInterface(sheetName: string, outputPath: string): Promise<void> {
    console.log(`🔧 Generating TypeScript interface from sheet: ${sheetName}`);
    
    // Read headers and sample data
    const range = `'${sheetName}'!A1:Z2`;
    const data = await this.service.readRange(this.spreadsheetId, range);
    
    if (data.values.length === 0) {
      console.log('⚠️  No data found in sheet');
      return;
    }
    
    const headers = data.values[0];
    const sampleRow = data.values[1] || [];
    
    // Generate interface
    let interfaceContent = `// Generated from Google Sheets: ${sheetName}\n`;
    interfaceContent += `// Last synced: ${new Date().toISOString()}\n\n`;
    interfaceContent += `export interface ${this.toPascalCase(sheetName)} {\n`;
    
    headers.forEach((header, index) => {
      const fieldName = this.toCamelCase(header);
      const sampleValue = sampleRow[index];
      const fieldType = this.inferType(sampleValue);
      interfaceContent += `  ${fieldName}: ${fieldType};\n`;
    });
    
    interfaceContent += '}\n';
    
    // Save interface file
    await fs.mkdir(path.dirname(outputPath), { recursive: true });
    await fs.writeFile(outputPath, interfaceContent);
    
    console.log(`✅ Generated TypeScript interface at ${outputPath}`);
  }
  
  /**
   * Create a new tracking sheet
   */
  async createTrackingSheet(sheetName: string, headers: string[]): Promise<void> {
    console.log(`📝 Creating new sheet: ${sheetName}`);
    
    // Create the sheet
    await this.service.createSheet(this.spreadsheetId, sheetName);
    
    // Add headers
    const values = [headers];
    await this.service.writeRange(this.spreadsheetId, `'${sheetName}'!A1`, values);
    
    console.log(`✅ Created sheet with ${headers.length} columns`);
  }
  
  /**
   * Append tracking data
   */
  async appendTrackingData(sheetName: string, data: Record<string, any>[]): Promise<void> {
    if (data.length === 0) return;
    
    console.log(`📝 Appending ${data.length} records to sheet: ${sheetName}`);
    
    // Get headers from the sheet
    const headerRange = `'${sheetName}'!A1:Z1`;
    const headerData = await this.service.readRange(this.spreadsheetId, headerRange);
    const headers = headerData.values[0] || [];
    
    // Convert data to values array
    const values = data.map(record => 
      headers.map(header => record[this.toCamelCase(header)] || '')
    );
    
    // Append to sheet
    await this.service.appendData(this.spreadsheetId, `'${sheetName}'!A:Z`, values);
    
    console.log(`✅ Appended ${data.length} records`);
  }
  
  // Helper methods
  private toCamelCase(str: string): string {
    return str.replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match, index) => {
      if (+match === 0) return '';
      return index === 0 ? match.toLowerCase() : match.toUpperCase();
    }).replace(/\s+/g, '');
  }
  
  private toPascalCase(str: string): string {
    const camel = this.toCamelCase(str);
    return camel.charAt(0).toUpperCase() + camel.slice(1);
  }
  
  private inferType(value: any): string {
    if (value === null || value === undefined || value === '') {
      return 'string | null';
    }
    if (!isNaN(Number(value))) {
      return 'number';
    }
    if (value === 'true' || value === 'false') {
      return 'boolean';
    }
    if (value.match(/^\d{4}-\d{2}-\d{2}/)) {
      return 'string'; // or Date
    }
    return 'string';
  }
}

// Example usage
async function main() {
  const spreadsheetUrl = 'https://docs.google.com/spreadsheets/d/1Yr7uShp6y1pUxwaNjzdOjV0VH18zsHM-a7gBnpdzGDY/edit?gid=0#gid=0';
  const sync = new GoogleSheetsSync(spreadsheetUrl);
  
  // Get sheet names to see what's available
  const service = new GoogleSheetsService(new GoogleAuth({
    scopes: ['https://www.googleapis.com/auth/spreadsheets'],
  }));
  const spreadsheetId = GoogleSheetsService.extractSpreadsheetId(spreadsheetUrl);
  const info = await service.getSpreadsheetInfo(spreadsheetId);
  
  console.log('📊 Available operations:\n');
  console.log('1. Pull sheet data to JSON');
  console.log('2. Push JSON data to sheet');
  console.log('3. Generate TypeScript interfaces');
  console.log('4. Create tracking sheet');
  console.log('5. Append tracking data\n');
  
  console.log('Available sheets:');
  const sheetNames = await service.getSheetNames(spreadsheetId);
  sheetNames.forEach((name, index) => {
    console.log(`  - ${name}`);
  });
  
  // Example: Pull first sheet to JSON
  if (sheetNames.length > 0) {
    console.log('\n📋 Example: Pulling first sheet to JSON...');
    await sync.pullToJson(
      sheetNames[0], 
      './data/google-sheets/sheet-data.json'
    );
    
    console.log('\n🔧 Example: Generating TypeScript interface...');
    await sync.generateTypeScriptInterface(
      sheetNames[0],
      './packages/google-sheets/types/sheet-types.ts'
    );
  }
}

// Run if called directly
if (import.meta.main) {
  main().catch(console.error);
}

export { GoogleSheetsSync };