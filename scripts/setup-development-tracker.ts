#!/usr/bin/env bun
import { GoogleSheetsService } from '../packages/google-sheets/src';
import { GoogleAuth } from 'google-auth-library';

const SPREADSHEET_URL = 'https://docs.google.com/spreadsheets/d/1Yr7uShp6y1pUxwaNjzdOjV0VH18zsHM-a7gBnpdzGDY/edit';
const SHEET_NAME = 'Development Tracker';

interface TrackerItem {
  id: string;
  type: 'EPIC' | 'STORY' | 'TASK' | 'SUBTASK';
  parent: string;
  project: 'Giki' | 'Pitch' | 'Shared';
  title: string;
  priority: 'P0' | 'P1' | 'P2' | 'P3';
  status: 'pending' | 'in_progress' | 'completed' | 'blocked';
  progress: number;
  todoWriteId: string;
  evidence: string;
  blockers: string;
  dueDate: string;
  worklog: string;
}

class DevelopmentTrackerSetup {
  private service: GoogleSheetsService;
  private spreadsheetId: string;
  
  constructor() {
    const auth = new GoogleAuth({
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
      projectId: 'rezolve-poc',
    });
    
    this.service = new GoogleSheetsService(auth);
    this.spreadsheetId = GoogleSheetsService.extractSpreadsheetId(SPREADSHEET_URL);
  }
  
  /**
   * Create the Development Tracker sheet with proper structure
   */
  async createTrackerSheet() {
    console.log('📊 Creating Development Tracker sheet...');
    
    try {
      // Check if sheet already exists
      const sheets = await this.service.getSheetNames(this.spreadsheetId);
      if (sheets.includes(SHEET_NAME)) {
        console.log('⚠️  Sheet already exists. Clearing existing data...');
        await this.service.clearRange(this.spreadsheetId, `'${SHEET_NAME}'!A:Z`);
      } else {
        // Create new sheet
        await this.service.createSheet(this.spreadsheetId, SHEET_NAME, 1000, 20);
      }
      
      // Add headers
      const headers = [
        'ID', 'Type', 'Parent', 'Project', 'Title', 'Priority', 
        'Status', 'Progress', 'TodoWrite_ID', 'Evidence', 
        'Blockers', 'Due_Date', 'Worklog'
      ];
      
      await this.service.writeRange(
        this.spreadsheetId,
        `'${SHEET_NAME}'!A1`,
        [headers]
      );
      
      console.log('✅ Sheet structure created');
    } catch (error) {
      console.error('❌ Error creating sheet:', error);
      throw error;
    }
  }
  
  /**
   * Populate with Aug 15 critical path items
   */
  async populateCriticalPath() {
    console.log('🎯 Populating Aug 15 critical path items...');
    
    const aug15 = '2025-08-15';
    const today = new Date().toISOString().split('T')[0];
    
    const items: TrackerItem[] = [
      // EPICS
      {
        id: 'E1',
        type: 'EPIC',
        parent: '',
        project: 'Giki',
        title: 'Giki AI - Aug 15 MIS Demo',
        priority: 'P0',
        status: 'in_progress',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        dueDate: aug15,
        worklog: 'Critical demo for investor meeting'
      },
      {
        id: 'E2',
        type: 'EPIC',
        parent: '',
        project: 'Pitch',
        title: 'Pitch Prep - Aug 15 Report Launch',
        priority: 'P0',
        status: 'in_progress',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        dueDate: aug15,
        worklog: 'Revenue generation launch'
      },
      {
        id: 'E3',
        type: 'EPIC',
        parent: '',
        project: 'Shared',
        title: 'Production Deployment Infrastructure',
        priority: 'P0',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        dueDate: aug15,
        worklog: 'Both projects need production env'
      },
      {
        id: 'E4',
        type: 'EPIC',
        parent: '',
        project: 'Shared',
        title: 'Testing & Quality Assurance',
        priority: 'P1',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        dueDate: aug15,
        worklog: 'Ensure production readiness'
      },
      
      // STORIES for Giki AI
      {
        id: 'S1',
        type: 'STORY',
        parent: 'E1',
        project: 'Giki',
        title: 'Complete E2E Upload to MIS Flow',
        priority: 'P0',
        status: 'in_progress',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        dueDate: '2025-08-14',
        worklog: 'Core demo functionality'
      },
      
      // TASKS for Giki AI E2E Flow
      {
        id: 'T1',
        type: 'TASK',
        parent: 'S1',
        project: 'Giki',
        title: 'Fix schema interpretation integration tests',
        priority: 'P0',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: 'Test failures in apps/giki/server/src/services',
        dueDate: today,
        worklog: 'Integration tests for upload and schema interpretation'
      },
      {
        id: 'T2',
        type: 'TASK',
        parent: 'S1',
        project: 'Giki',
        title: 'Test categorization with default MIS',
        priority: 'P0',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        dueDate: '2025-08-13',
        worklog: 'Verify 87% accuracy target'
      },
      {
        id: 'T3',
        type: 'TASK',
        parent: 'S1',
        project: 'Giki',
        title: 'Test customer-specified MIS structure',
        priority: 'P0',
        owner: '@qa-tester',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        estHours: 3,
        actualHours: 0,
        dueDate: '2025-08-13',
        comments: 'Custom category hierarchies'
      },
      {
        id: 'T4',
        type: 'TASK',
        parent: 'S1',
        project: 'Giki',
        title: 'Fix historical data categorization',
        priority: 'P1',
        owner: '@ai-integration-developer',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: 'TODO comment in code',
        estHours: 4,
        actualHours: 0,
        dueDate: '2025-08-14',
        comments: 'Customer-uploaded historical data'
      },
      {
        id: 'T5',
        type: 'TASK',
        parent: 'S1',
        project: 'Giki',
        title: 'Demo preparation and rehearsal',
        priority: 'P0',
        owner: '@frontend-developer',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        estHours: 2,
        actualHours: 0,
        dueDate: '2025-08-14',
        comments: 'Smooth demo flow, sample data ready'
      },
      
      // STORIES for Pitch Prep
      {
        id: 'S2',
        type: 'STORY',
        parent: 'E2',
        project: 'Pitch',
        title: 'Complete Report Generation Pipeline',
        priority: 'P0',
        owner: '@fullstack-developer',
        status: 'in_progress',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        estHours: 20,
        actualHours: 0,
        dueDate: '2025-08-14',
        comments: 'End-to-end report generation'
      },
      
      // TASKS for Pitch Prep Pipeline
      {
        id: 'T6',
        type: 'TASK',
        parent: 'S2',
        project: 'Pitch',
        title: 'Fix report generation database schema',
        priority: 'P0',
        owner: '@backend-developer',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: 'TODO: Add generated_reports table',
        estHours: 3,
        actualHours: 0,
        dueDate: today,
        comments: 'reportGenerationService.ts:1289'
      },
      {
        id: 'T7',
        type: 'TASK',
        parent: 'S2',
        project: 'Pitch',
        title: 'Fix payment integration test email',
        priority: 'P0',
        owner: '@backend-developer',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: 'Hardcoded test email',
        estHours: 2,
        actualHours: 0,
        dueDate: today,
        comments: 'enhancedPaymentService.ts:143'
      },
      {
        id: 'T8',
        type: 'TASK',
        parent: 'S2',
        project: 'Pitch',
        title: 'Complete E2E website to report test',
        priority: 'P0',
        owner: '@qa-tester',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        estHours: 4,
        actualHours: 0,
        dueDate: '2025-08-13',
        comments: 'Dr Paws veterinary test case'
      },
      {
        id: 'T9',
        type: 'TASK',
        parent: 'S2',
        project: 'Pitch',
        title: 'Deploy to production environment',
        priority: 'P0',
        owner: '@devops-engineer',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: 'Waiting for env config',
        estHours: 6,
        actualHours: 0,
        dueDate: '2025-08-14',
        comments: 'Vercel + Cloud Run'
      },
      {
        id: 'T10',
        type: 'TASK',
        parent: 'S2',
        project: 'Pitch',
        title: 'Test with real Dr Paws case',
        priority: 'P0',
        owner: '@qa-tester',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        estHours: 2,
        actualHours: 0,
        dueDate: '2025-08-14',
        comments: 'Validate report quality'
      },
      
      // STORIES for Infrastructure
      {
        id: 'S3',
        type: 'STORY',
        parent: 'E3',
        project: 'Shared',
        title: 'Production Environment Setup',
        priority: 'P0',
        owner: '@devops-engineer',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        estHours: 12,
        actualHours: 0,
        dueDate: '2025-08-13',
        comments: 'Both projects need this'
      },
      
      // TASKS for Infrastructure
      {
        id: 'T11',
        type: 'TASK',
        parent: 'S3',
        project: 'Shared',
        title: 'Configure environment variables',
        priority: 'P0',
        owner: '@devops-engineer',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        estHours: 2,
        actualHours: 0,
        dueDate: today,
        comments: '.env.production setup'
      },
      {
        id: 'T12',
        type: 'TASK',
        parent: 'S3',
        project: 'Shared',
        title: 'Set up monitoring and logging',
        priority: 'P1',
        owner: '@devops-engineer',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        estHours: 4,
        actualHours: 0,
        dueDate: '2025-08-13',
        comments: 'Error tracking, analytics'
      },
      {
        id: 'T13',
        type: 'TASK',
        parent: 'S3',
        project: 'Shared',
        title: 'Configure Vercel/Cloud Run deployment',
        priority: 'P0',
        owner: '@devops-engineer',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        estHours: 4,
        actualHours: 0,
        dueDate: '2025-08-13',
        comments: 'CI/CD pipeline'
      },
      {
        id: 'T14',
        type: 'TASK',
        parent: 'S3',
        project: 'Shared',
        title: 'Database migration to production',
        priority: 'P0',
        owner: '@backend-developer',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        estHours: 2,
        actualHours: 0,
        dueDate: '2025-08-13',
        comments: 'PostgreSQL setup'
      },
      
      // STORIES for Quality
      {
        id: 'S4',
        type: 'STORY',
        parent: 'E4',
        project: 'Shared',
        title: 'Critical Bug Fixes',
        priority: 'P1',
        owner: '@qa-tester',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        estHours: 12,
        actualHours: 0,
        dueDate: '2025-08-14',
        comments: 'Fix before launch'
      },
      
      // TASKS for Quality
      {
        id: 'T15',
        type: 'TASK',
        parent: 'S4',
        project: 'Shared',
        title: 'Fix 20 TODO comments in code',
        priority: 'P2',
        owner: '@backend-developer',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        estHours: 4,
        actualHours: 0,
        dueDate: '2025-08-14',
        comments: 'Clean up technical debt'
      },
      {
        id: 'T16',
        type: 'TASK',
        parent: 'S4',
        project: 'Shared',
        title: 'Fix TypeScript errors',
        priority: 'P1',
        owner: '@frontend-developer',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        estHours: 3,
        actualHours: 0,
        dueDate: '2025-08-13',
        comments: 'Type safety'
      },
      {
        id: 'T17',
        type: 'TASK',
        parent: 'S4',
        project: 'Shared',
        title: 'Achieve 80% test coverage',
        priority: 'P2',
        owner: '@qa-tester',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        estHours: 4,
        actualHours: 0,
        dueDate: '2025-08-14',
        comments: 'Production quality gate'
      },
      {
        id: 'T18',
        type: 'TASK',
        parent: 'S4',
        project: 'Shared',
        title: 'Performance validation',
        priority: 'P1',
        owner: '@performance-validator',
        status: 'pending',
        progress: 0,
        todoWriteId: '',
        evidence: '',
        blockers: '',
        estHours: 3,
        actualHours: 0,
        dueDate: '2025-08-14',
        comments: '<200ms API, <3s load'
      }
    ];
    
    // Convert to sheet format
    const rows = items.map(item => [
      item.id,
      item.type,
      item.parent,
      item.project,
      item.title,
      item.priority,
      item.status,
      item.progress,
      item.todoWriteId,
      item.evidence,
      item.blockers,
      item.dueDate,
      item.worklog
    ]);
    
    // Write to sheet
    await this.service.writeRange(
      this.spreadsheetId,
      `'${SHEET_NAME}'!A2`,
      rows
    );
    
    console.log(`✅ Added ${items.length} critical path items`);
    
    // Add progress formulas for EPICs
    console.log('📊 Adding progress rollup formulas...');
    
    // Formula to calculate EPIC progress based on child stories
    const epicFormulas = [
      [`=IFERROR(AVERAGE(FILTER(I:I, C:C=A2, B:B="STORY")), 0)`], // E1 progress
      [`=IFERROR(AVERAGE(FILTER(I:I, C:C=A3, B:B="STORY")), 0)`], // E2 progress
      [`=IFERROR(AVERAGE(FILTER(I:I, C:C=A4, B:B="STORY")), 0)`], // E3 progress
      [`=IFERROR(AVERAGE(FILTER(I:I, C:C=A5, B:B="STORY")), 0)`], // E4 progress
    ];
    
    await this.service.writeRange(
      this.spreadsheetId,
      `'${SHEET_NAME}'!I2`,
      epicFormulas,
      'USER_ENTERED'
    );
    
    // Formula to calculate STORY progress based on child tasks
    const storyFormulas = [
      [`=IFERROR(AVERAGE(FILTER(I:I, C:C=A6, B:B="TASK")), 0)`], // S1 progress
      [`=IFERROR(AVERAGE(FILTER(I:I, C:C=A12, B:B="TASK")), 0)`], // S2 progress
      [`=IFERROR(AVERAGE(FILTER(I:I, C:C=A18, B:B="TASK")), 0)`], // S3 progress
      [`=IFERROR(AVERAGE(FILTER(I:I, C:C=A23, B:B="TASK")), 0)`], // S4 progress
    ];
    
    await this.service.writeRange(
      this.spreadsheetId,
      `'${SHEET_NAME}'!I6`,
      [[storyFormulas[0][0]]],
      'USER_ENTERED'
    );
    await this.service.writeRange(
      this.spreadsheetId,
      `'${SHEET_NAME}'!I12`,
      [[storyFormulas[1][0]]],
      'USER_ENTERED'
    );
    await this.service.writeRange(
      this.spreadsheetId,
      `'${SHEET_NAME}'!I18`,
      [[storyFormulas[2][0]]],
      'USER_ENTERED'
    );
    await this.service.writeRange(
      this.spreadsheetId,
      `'${SHEET_NAME}'!I23`,
      [[storyFormulas[3][0]]],
      'USER_ENTERED'
    );
    
    console.log('✅ Progress formulas added');
    
    return items;
  }
  
  /**
   * Create a summary dashboard
   */
  async createDashboard() {
    console.log('📈 Creating Dashboard sheet...');
    
    const DASHBOARD_NAME = 'Dashboard';
    
    // Check if dashboard exists
    const sheets = await this.service.getSheetNames(this.spreadsheetId);
    if (!sheets.includes(DASHBOARD_NAME)) {
      await this.service.createSheet(this.spreadsheetId, DASHBOARD_NAME, 100, 10);
    } else {
      await this.service.clearRange(this.spreadsheetId, `'${DASHBOARD_NAME}'!A:Z`);
    }
    
    // Create dashboard summary
    const dashboardData = [
      ['Aug 15 Critical Path Dashboard'],
      [''],
      ['Project', 'Epic', 'Progress', 'Status', 'Blockers'],
      [`Giki AI`, `=FILTER('${SHEET_NAME}'!E:E, '${SHEET_NAME}'!A:A="E1")`, `=FILTER('${SHEET_NAME}'!I:I, '${SHEET_NAME}'!A:A="E1")`, `=FILTER('${SHEET_NAME}'!H:H, '${SHEET_NAME}'!A:A="E1")`, `=COUNTIF('${SHEET_NAME}'!L:L, "<>")`],
      [`Pitch Prep`, `=FILTER('${SHEET_NAME}'!E:E, '${SHEET_NAME}'!A:A="E2")`, `=FILTER('${SHEET_NAME}'!I:I, '${SHEET_NAME}'!A:A="E2")`, `=FILTER('${SHEET_NAME}'!H:H, '${SHEET_NAME}'!A:A="E2")`, ``],
      [''],
      ['Critical Tasks for Today'],
      ['ID', 'Project', 'Task', 'Owner', 'Blockers'],
    ];
    
    await this.service.writeRange(
      this.spreadsheetId,
      `'${DASHBOARD_NAME}'!A1`,
      dashboardData,
      'USER_ENTERED'
    );
    
    // Add critical tasks query
    const criticalQuery = `=QUERY('${SHEET_NAME}'!A:P, "SELECT A, D, E, G, L WHERE F='P0' AND H='pending' AND O='${new Date().toISOString().split('T')[0]}' ORDER BY F", 1)`;
    
    await this.service.writeRange(
      this.spreadsheetId,
      `'${DASHBOARD_NAME}'!A9`,
      [[criticalQuery]],
      'USER_ENTERED'
    );
    
    console.log('✅ Dashboard created');
  }
}

// Main execution
async function main() {
  console.log('🚀 Setting up Development Tracker in Google Sheets\n');
  
  const setup = new DevelopmentTrackerSetup();
  
  try {
    // Set up environment variables
    process.env.GOOGLE_APPLICATION_CREDENTIALS = process.env.HOME + '/.config/gcloud/application_default_credentials.json';
    process.env.GCLOUD_PROJECT = 'rezolve-poc';
    
    // Create sheet structure
    await setup.createTrackerSheet();
    
    // Populate critical path
    const items = await setup.populateCriticalPath();
    
    // Create dashboard
    await setup.createDashboard();
    
    console.log('\n✅ Development Tracker setup complete!');
    console.log(`📊 ${items.length} items added for Aug 15 deadline`);
    console.log('\n📱 Next steps:');
    console.log('1. Open your Google Sheet to see the Development Tracker');
    console.log('2. Review the critical path items');
    console.log('3. Check the Dashboard for today\'s priorities');
    console.log('4. Run sync-development-tracker.ts to sync with TodoWrite');
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.main) {
  main();
}

export { DevelopmentTrackerSetup };