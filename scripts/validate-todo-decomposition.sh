#!/bin/bash

# Validate Todo Decomposition System Integrity
# Ensures all memory files exist and decomposition rules are properly configured

echo "🚨 VALIDATING TODO DECOMPOSITION SYSTEM 🚨"
echo "============================================"

ERRORS=0
WORKSPACE_ROOT="/Users/<USER>/giki-ai-workspace"
MEMORY_ROOT="$WORKSPACE_ROOT/.claude/memory"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to report errors
report_error() {
    echo -e "${RED}❌ ERROR: $1${NC}"
    ((ERRORS++))
}

# Function to report success
report_success() {
    echo -e "${GREEN}✅ SUCCESS: $1${NC}"
}

# Function to report warning
report_warning() {
    echo -e "${YELLOW}⚠️  WARNING: $1${NC}"
}

echo -e "${BLUE}1. CHECKING CORE DECOMPOSITION FILES${NC}"
echo "-----------------------------------"

# Check if enhanced-todo-system.md exists and has mandatory sections
ENHANCED_TODO_FILE="$MEMORY_ROOT/global/enhanced-todo-system.md"
if [ ! -f "$ENHANCED_TODO_FILE" ]; then
    report_error "enhanced-todo-system.md not found at $ENHANCED_TODO_FILE"
else
    report_success "enhanced-todo-system.md found"
    
    # Check for mandatory sections
    if grep -q "AUTOMATIC TODO DECOMPOSITION MANDATE" "$ENHANCED_TODO_FILE"; then
        report_success "AUTOMATIC TODO DECOMPOSITION MANDATE section found"
    else
        report_error "Missing AUTOMATIC TODO DECOMPOSITION MANDATE section"
    fi
    
    if grep -q "HIERARCHICAL AUTO-EXPANSION RULES" "$ENHANCED_TODO_FILE"; then
        report_success "HIERARCHICAL AUTO-EXPANSION RULES section found"
    else
        report_error "Missing HIERARCHICAL AUTO-EXPANSION RULES section"
    fi
    
    if grep -q "BEHAVIORAL ENFORCEMENT CHECKLIST" "$ENHANCED_TODO_FILE"; then
        report_success "BEHAVIORAL ENFORCEMENT CHECKLIST section found"
    else
        report_error "Missing BEHAVIORAL ENFORCEMENT CHECKLIST section"
    fi
fi

# Check if QUICK-TODO-DECOMPOSITION.md exists
QUICK_REF_FILE="$MEMORY_ROOT/global/QUICK-TODO-DECOMPOSITION.md"
if [ ! -f "$QUICK_REF_FILE" ]; then
    report_error "QUICK-TODO-DECOMPOSITION.md not found at $QUICK_REF_FILE"
else
    report_success "QUICK-TODO-DECOMPOSITION.md found"
    
    # Check for mandatory sections
    if grep -q "INSTANT DECISION TREE" "$QUICK_REF_FILE"; then
        report_success "INSTANT DECISION TREE section found"
    else
        report_error "Missing INSTANT DECISION TREE section"
    fi
fi

echo ""
echo -e "${BLUE}2. CHECKING WORKFLOW INTEGRATION${NC}"
echo "-------------------------------"

# Check development-workflows.md for decomposition integration
WORKFLOWS_FILE="$MEMORY_ROOT/global/development-workflows.md"
if [ ! -f "$WORKFLOWS_FILE" ]; then
    report_error "development-workflows.md not found at $WORKFLOWS_FILE"
else
    report_success "development-workflows.md found"
    
    if grep -q "TODO DECOMPOSITION WORKFLOW INTEGRATION" "$WORKFLOWS_FILE"; then
        report_success "TODO DECOMPOSITION WORKFLOW INTEGRATION section found"
    else
        report_error "Missing TODO DECOMPOSITION WORKFLOW INTEGRATION section"
    fi
    
    if grep -q "MANDATORY TODO DECOMPOSITION CHECKPOINTS" "$WORKFLOWS_FILE"; then
        report_success "MANDATORY TODO DECOMPOSITION CHECKPOINTS section found"
    else
        report_error "Missing MANDATORY TODO DECOMPOSITION CHECKPOINTS section"
    fi
fi

echo ""
echo -e "${BLUE}3. CHECKING CLAUDE.md INTEGRATION${NC}"
echo "--------------------------------"

# Check if CLAUDE.md loads the necessary memory files
CLAUDE_MD="$WORKSPACE_ROOT/CLAUDE.md"
if [ ! -f "$CLAUDE_MD" ]; then
    report_warning "CLAUDE.md not found - memory loading may not be configured"
else
    report_success "CLAUDE.md found"
    
    if grep -q "enhanced-todo-system.md" "$CLAUDE_MD"; then
        report_success "enhanced-todo-system.md referenced in CLAUDE.md"
    else
        report_warning "enhanced-todo-system.md not referenced in CLAUDE.md"
    fi
    
    if grep -q "QUICK-TODO-DECOMPOSITION.md" "$CLAUDE_MD"; then
        report_success "QUICK-TODO-DECOMPOSITION.md referenced in CLAUDE.md"
    else
        report_warning "QUICK-TODO-DECOMPOSITION.md not referenced in CLAUDE.md - should be in CRITICAL CORE section"
    fi
fi

echo ""
echo -e "${BLUE}4. CHECKING DECOMPOSITION RULE COMPLETENESS${NC}"
echo "-------------------------------------------"

# Check for all hierarchy levels in enhanced-todo-system.md
if [ -f "$ENHANCED_TODO_FILE" ]; then
    if grep -q "\[EPIC:" "$ENHANCED_TODO_FILE"; then
        report_success "EPIC decomposition rules found"
    else
        report_error "Missing EPIC decomposition rules"
    fi
    
    if grep -q "\[STORY:" "$ENHANCED_TODO_FILE"; then
        report_success "STORY decomposition rules found"
    else
        report_error "Missing STORY decomposition rules"
    fi
    
    if grep -q "\[TASK:" "$ENHANCED_TODO_FILE"; then
        report_success "TASK decomposition rules found"
    else
        report_error "Missing TASK decomposition rules"
    fi
    
    if grep -q "\[SUBTASK:" "$ENHANCED_TODO_FILE"; then
        report_success "SUBTASK decomposition rules found"
    else
        report_error "Missing SUBTASK decomposition rules"
    fi
    
    if grep -q "30min" "$ENHANCED_TODO_FILE"; then
        report_success "Size-based decomposition triggers found (30min rule)"
    else
        report_error "Missing size-based decomposition triggers"
    fi
fi

echo ""
echo -e "${BLUE}5. CHECKING SUB-AGENT INTEGRATION${NC}"
echo "--------------------------------"

# Check for sub-agent assignment patterns
if [ -f "$ENHANCED_TODO_FILE" ]; then
    if grep -q "@frontend-developer\|@backend-developer\|@qa-tester" "$ENHANCED_TODO_FILE"; then
        report_success "Sub-agent assignment patterns found"
    else
        report_error "Missing sub-agent assignment patterns"
    fi
    
    if grep -q "SUB-AGENT ORCHESTRATION INTEGRATION" "$ENHANCED_TODO_FILE"; then
        report_success "Sub-agent orchestration integration found"
    else
        report_error "Missing sub-agent orchestration integration"
    fi
fi

echo ""
echo "============================================"
if [ $ERRORS -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL VALIDATION CHECKS PASSED!${NC}"
    echo -e "${GREEN}Todo decomposition system is properly configured.${NC}"
    exit 0
else
    echo -e "${RED}❌ VALIDATION FAILED WITH $ERRORS ERRORS${NC}"
    echo -e "${RED}Please fix the issues above before proceeding.${NC}"
    exit 1
fi