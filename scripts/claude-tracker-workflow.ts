#!/usr/bin/env bun
/**
 * <PERSON>'s Real-Time Development Tracker Workflow
 * 
 * This script demonstrates how <PERSON> will interact with the Google Sheets
 * Development Tracker while maintaining the TodoWrite system for task decomposition.
 */

import { DevelopmentTrackerSync } from './sync-development-tracker';
import { GoogleSheetsService } from '../packages/google-sheets/src';
import { GoogleAuth } from 'google-auth-library';
import fs from 'fs/promises';
import path from 'path';

const SPREADSHEET_URL = 'https://docs.google.com/spreadsheets/d/1Yr7uShp6y1pUxwaNjzdOjV0VH18zsHM-a7gBnpdzGDY/edit';

class ClaudeTrackerWorkflow {
  private sync: DevelopmentTrackerSync;
  private service: GoogleSheetsService;
  private spreadsheetId: string;
  
  constructor() {
    this.sync = new DevelopmentTrackerSync();
    
    const auth = new GoogleAuth({
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
      projectId: 'rezolve-poc',
    });
    
    this.service = new GoogleSheetsService(auth);
    this.spreadsheetId = GoogleSheetsService.extractSpreadsheetId(SPREADSHEET_URL);
  }
  
  /**
   * Claude's morning routine - check priorities and blockers
   */
  async morningCheck() {
    console.log('🌅 Claude\'s Morning Check\n');
    console.log('Reading Development Tracker for today\'s priorities...\n');
    
    // Get status report
    await this.sync.generateStatusReport();
    
    // Get next task
    const nextTask = await this.sync.getNextTask();
    if (nextTask) {
      console.log('📝 Claude will start with:');
      console.log(`   ${nextTask.id}: ${nextTask.title}`);
      console.log(`   Estimated time: ${nextTask.estHours} hours`);
      
      // Create TodoWrite decomposition
      console.log('\n🔨 Decomposing into TodoWrite tasks:');
      const todos = this.decomposeTask(nextTask);
      for (const todo of todos) {
        console.log(`   ${todo.status === 'in_progress' ? '🔄' : '⏳'} ${todo.content}`);
      }
    }
    
    return nextTask;
  }
  
  /**
   * Decompose a sheet task into P/I/V todos
   */
  decomposeTask(task: any) {
    const todos = [];
    
    // Planning phase
    todos.push({
      id: `${task.id}-P`,
      content: `[TASK: ${task.id}-P-@research-planner] Plan ${task.title}`,
      status: 'pending'
    });
    
    // Implementation phase
    todos.push({
      id: `${task.id}-I`,
      content: `[TASK: ${task.id}-I-${task.owner}] Implement ${task.title}`,
      status: 'pending'
    });
    
    // Validation phase
    todos.push({
      id: `${task.id}-V`,
      content: `[TASK: ${task.id}-V-@qa-tester] Validate ${task.title}`,
      status: 'pending'
    });
    
    // If task is complex (>4 hours), add subtasks
    if (task.estHours > 4) {
      const subtaskCount = Math.ceil(task.estHours / 2);
      for (let i = 1; i <= subtaskCount; i++) {
        todos.push({
          id: `${task.id}-ST${i}`,
          content: `[SUBTASK: ${task.id}-ST${i}] ${task.title} - Part ${i}`,
          status: 'pending'
        });
      }
    }
    
    return todos;
  }
  
  /**
   * Real-time task selection based on context
   */
  async selectNextTask(context: string) {
    console.log(`\n🤔 Claude selecting next task based on: ${context}\n`);
    
    // Read current sheet state
    const sheetData = await this.service.readRange(
      this.spreadsheetId,
      `'Development Tracker'!A:P`
    );
    
    const rows = sheetData.values.slice(1); // Skip header
    const tasks = rows.filter(r => r[1] === 'TASK' && r[7] === 'pending');
    
    // Apply context-based filtering
    let selectedTask = null;
    
    if (context.includes('blocker')) {
      // Prioritize unblocking tasks
      selectedTask = tasks.find(t => !t[11]); // No blockers
    } else if (context.includes('quick')) {
      // Find quick wins (<2 hours)
      selectedTask = tasks.find(t => parseFloat(t[12]) <= 2);
    } else if (context.includes('critical')) {
      // P0 tasks only
      selectedTask = tasks.find(t => t[5] === 'P0');
    } else {
      // Default to priority order
      selectedTask = tasks[0];
    }
    
    if (selectedTask) {
      console.log(`✅ Selected: ${selectedTask[0]}: ${selectedTask[4]}`);
      console.log(`   Priority: ${selectedTask[5]} | Est: ${selectedTask[12]}h`);
      
      // Update sheet to mark as in_progress
      const rowIndex = rows.indexOf(selectedTask) + 2;
      await this.service.writeRange(
        this.spreadsheetId,
        `'Development Tracker'!H${rowIndex}`,
        [['in_progress']]
      );
    } else {
      console.log('❌ No suitable task found for context');
    }
    
    return selectedTask;
  }
  
  /**
   * Update progress in real-time
   */
  async updateProgress(taskId: string, progress: number, notes?: string) {
    console.log(`\n📊 Updating progress for ${taskId}: ${progress}%\n`);
    
    // Find the task row
    const sheetData = await this.service.readRange(
      this.spreadsheetId,
      `'Development Tracker'!A:P`
    );
    
    const rowIndex = sheetData.values.findIndex(r => r[0] === taskId);
    
    if (rowIndex > 0) {
      const actualRow = rowIndex + 1;
      
      // Update progress and actual hours
      const updates = [[
        progress === 100 ? 'completed' : 'in_progress', // Status
        progress, // Progress
      ]];
      
      await this.service.writeRange(
        this.spreadsheetId,
        `'Development Tracker'!H${actualRow}:I${actualRow}`,
        updates
      );
      
      // Add notes if provided
      if (notes) {
        await this.service.writeRange(
          this.spreadsheetId,
          `'Development Tracker'!P${actualRow}`,
          [[notes]]
        );
      }
      
      console.log(`✅ Updated ${taskId} to ${progress}% complete`);
      
      // If task is complete, update evidence
      if (progress === 100) {
        const evidence = `Completed at ${new Date().toISOString()}`;
        await this.service.writeRange(
          this.spreadsheetId,
          `'Development Tracker'!K${actualRow}`,
          [[evidence]]
        );
      }
    }
  }
  
  /**
   * Handle blocker escalation
   */
  async escalateBlocker(taskId: string, blocker: string) {
    console.log(`\n🚨 Escalating blocker for ${taskId}\n`);
    console.log(`   Blocker: ${blocker}\n`);
    
    // Update sheet with blocker
    const sheetData = await this.service.readRange(
      this.spreadsheetId,
      `'Development Tracker'!A:P`
    );
    
    const rowIndex = sheetData.values.findIndex(r => r[0] === taskId);
    
    if (rowIndex > 0) {
      await this.service.writeRange(
        this.spreadsheetId,
        `'Development Tracker'!L${rowIndex + 1}`,
        [[blocker]]
      );
      
      // Change status to blocked
      await this.service.writeRange(
        this.spreadsheetId,
        `'Development Tracker'!H${rowIndex + 1}`,
        [['blocked']]
      );
      
      console.log('✅ Blocker recorded in tracker');
      console.log('📧 Human attention required!');
    }
  }
  
  /**
   * End of day summary
   */
  async endOfDayReport() {
    console.log('\n🌙 Claude\'s End of Day Report\n');
    
    // Get today's completed tasks
    const sheetData = await this.service.readRange(
      this.spreadsheetId,
      `'Development Tracker'!A:P`
    );
    
    const today = new Date().toISOString().split('T')[0];
    const completedToday = sheetData.values.filter(r => 
      r[7] === 'completed' && 
      r[10]?.includes(today)
    );
    
    console.log(`✅ Completed ${completedToday.length} tasks today:`);
    for (const task of completedToday) {
      console.log(`   - ${task[0]}: ${task[4]}`);
    }
    
    // Calculate overall progress
    const epics = sheetData.values.filter(r => r[1] === 'EPIC');
    const avgProgress = epics.reduce((sum, e) => sum + (parseFloat(e[8]) || 0), 0) / epics.length;
    
    console.log(`\n📊 Overall Aug 15 Progress: ${Math.round(avgProgress)}%`);
    
    // Identify tomorrow's priorities
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    
    const tomorrowTasks = sheetData.values.filter(r => 
      r[1] === 'TASK' && 
      r[7] === 'pending' && 
      r[14] === tomorrowStr
    );
    
    console.log(`\n📅 Tomorrow's priorities (${tomorrowTasks.length} tasks):`);
    for (const task of tomorrowTasks.slice(0, 3)) {
      console.log(`   - ${task[0]}: ${task[4]} (${task[5]})`);
    }
    
    return {
      completedToday: completedToday.length,
      overallProgress: Math.round(avgProgress),
      tomorrowCount: tomorrowTasks.length
    };
  }
}

// Demonstration of Claude's workflow
async function demonstrateWorkflow() {
  console.log('🤖 Claude\'s Development Tracker Workflow Demo\n');
  console.log('=' .repeat(50) + '\n');
  
  const claude = new ClaudeTrackerWorkflow();
  
  // Set up environment
  process.env.GOOGLE_APPLICATION_CREDENTIALS = process.env.HOME + '/.config/gcloud/application_default_credentials.json';
  process.env.GCLOUD_PROJECT = 'rezolve-poc';
  
  // Morning check
  const firstTask = await claude.morningCheck();
  
  // Simulate working on a task
  if (firstTask) {
    console.log('\n⏰ Starting work on first task...');
    
    // Update progress at intervals
    await new Promise(resolve => setTimeout(resolve, 1000));
    await claude.updateProgress(firstTask.id, 25, 'Research completed');
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    await claude.updateProgress(firstTask.id, 50, 'Implementation in progress');
    
    // Simulate finding a blocker
    if (firstTask.id === 'T1') {
      await claude.escalateBlocker(firstTask.id, 'Missing test fixtures');
    } else {
      // Complete the task
      await new Promise(resolve => setTimeout(resolve, 1000));
      await claude.updateProgress(firstTask.id, 100, 'Task completed successfully');
    }
  }
  
  // Select next task based on context
  await claude.selectNextTask('quick win needed');
  
  // End of day report
  await claude.endOfDayReport();
  
  console.log('\n' + '=' .repeat(50));
  console.log('🎯 This is how Claude will work with your spreadsheet!');
  console.log('\nKey benefits:');
  console.log('  ✅ Real-time visibility into progress');
  console.log('  ✅ Automatic priority selection');
  console.log('  ✅ TodoWrite decomposition preserved');
  console.log('  ✅ Blocker escalation to humans');
  console.log('  ✅ Progress rollup to EPICs');
}

// Run demo if called directly
if (import.meta.main) {
  demonstrateWorkflow().catch(console.error);
}

export { ClaudeTrackerWorkflow };