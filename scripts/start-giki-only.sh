#!/bin/bash

# Start Giki AI Only Script
set -e

WORKSPACE_ROOT="/Users/<USER>/giki-ai-workspace"
LOG_DIR="$WORKSPACE_ROOT/logs"

# Create logs directory
mkdir -p "$LOG_DIR"

echo "🚀 Starting Giki AI services only..."

# Clear Giki AI ports
echo "  🧹 Clearing Giki AI ports..."
lsof -ti:3000,8000 | xargs kill -9 2>/dev/null || echo "  ✅ Ports 3000,8000 cleared"

# Start Giki AI services in background
echo "  → Starting Giki AI Client (port 3000)..."
nohup bash -c "cd '$WORKSPACE_ROOT/apps/giki/client' && bun run dev:strict" >> "$LOG_DIR/giki-client.log" 2>&1 &
disown

echo "  → Starting Giki AI Server (port 8000)..."
nohup bash -c "cd '$WORKSPACE_ROOT/apps/giki/server' && bun run dev:strict" >> "$LOG_DIR/giki-server.log" 2>&1 &
disown

echo ""
echo "🎉 Giki AI services started!"
echo "📝 To stop services: bun run stop:giki"
echo "📄 To view logs: bun run logs:giki"
echo "💻 Access your application: http://localhost:3000"