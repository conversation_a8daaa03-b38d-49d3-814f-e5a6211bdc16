#!/usr/bin/env bun
import { GoogleSheetsService } from '../packages/google-sheets/src';
import { GoogleAuth } from 'google-auth-library';

async function testGoogleSheetsConnection() {
  try {
    console.log('🔗 Testing Google Sheets connection...\n');
    
    // Your spreadsheet URL
    const spreadsheetUrl = 'https://docs.google.com/spreadsheets/d/1Yr7uShp6y1pUxwaNjzdOjV0VH18zsHM-a7gBnpdzGDY/edit?gid=0#gid=0';
    const spreadsheetId = GoogleSheetsService.extractSpreadsheetId(spreadsheetUrl);
    
    console.log(`📊 Spreadsheet ID: ${spreadsheetId}`);
    
    // Initialize auth with Application Default Credentials
    // This will use your gcloud auth (since you're logged <NAME_EMAIL>)
    const auth = new GoogleAuth({
      scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'],
      projectId: 'rezolve-poc', // Required for quota
    });
    
    // Create service instance
    const sheetsService = new GoogleSheetsService(auth);
    
    // Get spreadsheet info
    console.log('\n📋 Getting spreadsheet information...');
    const info = await sheetsService.getSpreadsheetInfo(spreadsheetId);
    console.log(`✅ Spreadsheet Title: ${info.properties?.title}`);
    console.log(`✅ Locale: ${info.properties?.locale}`);
    console.log(`✅ Time Zone: ${info.properties?.timeZone}`);
    
    // Get sheet names
    console.log('\n📑 Available sheets:');
    const sheetNames = await sheetsService.getSheetNames(spreadsheetId);
    sheetNames.forEach((name, index) => {
      console.log(`  ${index + 1}. ${name}`);
    });
    
    // Read data from the first sheet (A1:Z100 as example)
    if (sheetNames.length > 0) {
      const firstSheet = sheetNames[0];
      console.log(`\n📖 Reading data from sheet: ${firstSheet}`);
      
      // Read first 10 rows and 10 columns
      const range = `'${firstSheet}'!A1:J10`;
      const data = await sheetsService.readRange(spreadsheetId, range);
      
      console.log(`✅ Data range: ${data.range}`);
      console.log(`✅ Rows found: ${data.values.length}`);
      
      if (data.values.length > 0) {
        console.log('\n📊 Sample data (first 5 rows):');
        data.values.slice(0, 5).forEach((row, index) => {
          console.log(`  Row ${index + 1}: ${JSON.stringify(row)}`);
        });
      }
    }
    
    console.log('\n✅ Google Sheets connection test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error connecting to Google Sheets:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('invalid_grant')) {
        console.log('\n💡 Authentication issue detected. Try these steps:');
        console.log('1. Run: gcloud auth application-default login');
        console.log('2. Make sure you have the Google Sheets API enabled:');
        console.log('   gcloud services enable sheets.googleapis.com');
      } else if (error.message.includes('Insufficient Permission')) {
        console.log('\n💡 Permission issue detected. Make sure:');
        console.log('1. The spreadsheet is shared with your Google account (<EMAIL>)');
        console.log('2. You have at least viewer access to the spreadsheet');
      }
    }
    
    process.exit(1);
  }
}

// Run the test
testGoogleSheetsConnection();