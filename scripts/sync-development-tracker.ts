#!/usr/bin/env bun
import { GoogleSheetsService } from '../packages/google-sheets/src';
import { GoogleAuth } from 'google-auth-library';
import fs from 'fs/promises';
import path from 'path';

const SPREADSHEET_URL = 'https://docs.google.com/spreadsheets/d/1Yr7uShp6y1pUxwaNjzdOjV0VH18zsHM-a7gBnpdzGDY/edit';
const SHEET_NAME = 'Development Tracker';
const TODO_CACHE_FILE = path.join(process.cwd(), '.cache/sheet-todo-mapping.json');

interface SheetRow {
  id: string;
  type: string;
  parent: string;
  project: string;
  title: string;
  priority: string;
  owner: string;
  status: string;
  progress: number;
  todoWriteId: string;
  evidence: string;
  blockers: string;
  estHours: number;
  actualHours: number;
  dueDate: string;
  comments: string;
}

interface TodoItem {
  id: string;
  content: string;
  status: 'pending' | 'in_progress' | 'completed';
}

class DevelopmentTrackerSync {
  private service: GoogleSheetsService;
  private spreadsheetId: string;
  private todoMapping: Map<string, string> = new Map(); // Sheet ID -> TodoWrite ID
  
  constructor() {
    const auth = new GoogleAuth({
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
      projectId: 'rezolve-poc',
    });
    
    this.service = new GoogleSheetsService(auth);
    this.spreadsheetId = GoogleSheetsService.extractSpreadsheetId(SPREADSHEET_URL);
  }
  
  /**
   * Load cached todo mappings
   */
  async loadMappings() {
    try {
      const cacheDir = path.dirname(TODO_CACHE_FILE);
      await fs.mkdir(cacheDir, { recursive: true });
      
      const data = await fs.readFile(TODO_CACHE_FILE, 'utf-8');
      const mappings = JSON.parse(data);
      this.todoMapping = new Map(Object.entries(mappings));
      console.log(`📂 Loaded ${this.todoMapping.size} todo mappings from cache`);
    } catch {
      console.log('📂 No cached mappings found, starting fresh');
    }
  }
  
  /**
   * Save todo mappings to cache
   */
  async saveMappings() {
    const mappings = Object.fromEntries(this.todoMapping);
    const cacheDir = path.dirname(TODO_CACHE_FILE);
    await fs.mkdir(cacheDir, { recursive: true });
    await fs.writeFile(TODO_CACHE_FILE, JSON.stringify(mappings, null, 2));
    console.log(`💾 Saved ${this.todoMapping.size} todo mappings to cache`);
  }
  
  /**
   * Read current state from Google Sheets
   */
  async readSheetData(): Promise<SheetRow[]> {
    console.log('📖 Reading Development Tracker from Google Sheets...');
    
    const data = await this.service.readRange(
      this.spreadsheetId,
      `'${SHEET_NAME}'!A:P`
    );
    
    if (data.values.length <= 1) {
      return [];
    }
    
    // Skip header row
    const rows = data.values.slice(1);
    
    return rows.map(row => ({
      id: row[0] || '',
      type: row[1] || '',
      parent: row[2] || '',
      project: row[3] || '',
      title: row[4] || '',
      priority: row[5] || '',
      owner: row[6] || '',
      status: row[7] || '',
      progress: parseFloat(row[8]) || 0,
      todoWriteId: row[9] || '',
      evidence: row[10] || '',
      blockers: row[11] || '',
      estHours: parseFloat(row[12]) || 0,
      actualHours: parseFloat(row[13]) || 0,
      dueDate: row[14] || '',
      comments: row[15] || ''
    }));
  }
  
  /**
   * Generate TodoWrite format from sheet row
   */
  generateTodoContent(row: SheetRow): string {
    const prefix = row.type === 'EPIC' ? '[EPIC' :
                  row.type === 'STORY' ? '[STORY' :
                  row.type === 'TASK' ? '[TASK' :
                  '[SUBTASK';
    
    const projectTag = row.project ? `-${row.project.toUpperCase()}` : '';
    const ownerTag = row.owner.startsWith('@') ? `-${row.owner}` : '';
    
    return `${prefix}: ${row.id}${projectTag}${ownerTag}] ${row.title}`;
  }
  
  /**
   * Convert sheet status to TodoWrite status
   */
  convertStatus(sheetStatus: string): 'pending' | 'in_progress' | 'completed' {
    switch (sheetStatus.toLowerCase()) {
      case 'completed':
      case 'done':
        return 'completed';
      case 'in_progress':
      case 'active':
      case 'working':
        return 'in_progress';
      default:
        return 'pending';
    }
  }
  
  /**
   * Sync sheet data to TodoWrite format
   */
  async syncToTodos(dryRun: boolean = false): Promise<TodoItem[]> {
    console.log('🔄 Syncing Google Sheets → TodoWrite...');
    
    await this.loadMappings();
    const sheetData = await this.readSheetData();
    const todos: TodoItem[] = [];
    
    // Group by priority for better organization
    const p0Items = sheetData.filter(r => r.priority === 'P0');
    const p1Items = sheetData.filter(r => r.priority === 'P1');
    const otherItems = sheetData.filter(r => !['P0', 'P1'].includes(r.priority));
    
    const orderedItems = [...p0Items, ...p1Items, ...otherItems];
    
    for (const row of orderedItems) {
      // Skip items without IDs
      if (!row.id) continue;
      
      // Check if we already have a TodoWrite ID
      let todoId = row.todoWriteId || this.todoMapping.get(row.id);
      
      if (!todoId) {
        // Generate new TodoWrite ID
        todoId = `${row.project.toLowerCase()}-${row.id.toLowerCase()}-${Date.now()}`;
        this.todoMapping.set(row.id, todoId);
        
        if (!dryRun) {
          // Update sheet with TodoWrite ID
          const rowIndex = sheetData.indexOf(row) + 2; // +2 for header and 0-index
          await this.service.writeRange(
            this.spreadsheetId,
            `'${SHEET_NAME}'!J${rowIndex}`,
            [[todoId]]
          );
        }
      }
      
      todos.push({
        id: todoId,
        content: this.generateTodoContent(row),
        status: this.convertStatus(row.status)
      });
    }
    
    if (!dryRun) {
      await this.saveMappings();
    }
    
    console.log(`✅ Generated ${todos.length} todos from sheet`);
    
    // Save todos to file for TodoWrite to read
    const todoFile = path.join(process.cwd(), '.cache/sheet-todos.json');
    await fs.writeFile(todoFile, JSON.stringify(todos, null, 2));
    console.log(`💾 Saved todos to ${todoFile}`);
    
    return todos;
  }
  
  /**
   * Update sheet with TodoWrite status
   */
  async updateSheetStatus(todoId: string, status: string, evidence?: string) {
    console.log(`📝 Updating sheet for todo: ${todoId}`);
    
    // Find sheet row with this TodoWrite ID
    const sheetData = await this.readSheetData();
    const rowIndex = sheetData.findIndex(r => 
      r.todoWriteId === todoId || this.todoMapping.get(r.id) === todoId
    );
    
    if (rowIndex === -1) {
      console.log(`⚠️  No sheet row found for todo: ${todoId}`);
      return;
    }
    
    const actualRowIndex = rowIndex + 2; // +2 for header and 0-index
    
    // Update status
    let sheetStatus = status;
    let progress = 0;
    
    switch (status) {
      case 'completed':
        sheetStatus = 'completed';
        progress = 100;
        break;
      case 'in_progress':
        sheetStatus = 'in_progress';
        progress = 50; // Default to 50% for in progress
        break;
      default:
        sheetStatus = 'pending';
        progress = 0;
    }
    
    // Update multiple columns at once
    const updates = [[sheetStatus, progress]];
    
    await this.service.writeRange(
      this.spreadsheetId,
      `'${SHEET_NAME}'!H${actualRowIndex}:I${actualRowIndex}`,
      updates
    );
    
    // Update evidence if provided
    if (evidence) {
      await this.service.writeRange(
        this.spreadsheetId,
        `'${SHEET_NAME}'!K${actualRowIndex}`,
        [[evidence]]
      );
    }
    
    console.log(`✅ Updated sheet row ${actualRowIndex}`);
  }
  
  /**
   * Get next priority task from sheet
   */
  async getNextTask(): Promise<SheetRow | null> {
    const sheetData = await this.readSheetData();
    
    // Filter for pending tasks
    const pendingTasks = sheetData.filter(r => 
      r.type === 'TASK' && 
      r.status === 'pending' &&
      !r.blockers // No blockers
    );
    
    // Sort by priority and due date
    pendingTasks.sort((a, b) => {
      // P0 > P1 > P2 > P3
      const priorityOrder = { 'P0': 0, 'P1': 1, 'P2': 2, 'P3': 3 };
      const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] ?? 99;
      const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] ?? 99;
      
      if (aPriority !== bPriority) {
        return aPriority - bPriority;
      }
      
      // Then by due date
      return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
    });
    
    return pendingTasks[0] || null;
  }
  
  /**
   * Generate status report
   */
  async generateStatusReport() {
    console.log('\n📊 Generating Status Report...\n');
    
    const sheetData = await this.readSheetData();
    
    // Calculate metrics
    const epics = sheetData.filter(r => r.type === 'EPIC');
    const stories = sheetData.filter(r => r.type === 'STORY');
    const tasks = sheetData.filter(r => r.type === 'TASK');
    
    const p0Tasks = tasks.filter(t => t.priority === 'P0');
    const completedP0 = p0Tasks.filter(t => t.status === 'completed');
    const blockedTasks = tasks.filter(t => t.blockers);
    
    console.log('=== Aug 15 Critical Path Status ===\n');
    
    // Epic status
    console.log('📌 EPICS:');
    for (const epic of epics) {
      const progress = Math.round(epic.progress);
      const bar = '█'.repeat(Math.floor(progress / 10)) + '░'.repeat(10 - Math.floor(progress / 10));
      console.log(`  ${epic.id}: ${epic.title}`);
      console.log(`    Progress: ${bar} ${progress}%`);
    }
    
    console.log('\n📊 METRICS:');
    console.log(`  P0 Tasks: ${completedP0.length}/${p0Tasks.length} completed`);
    console.log(`  Blocked Tasks: ${blockedTasks.length}`);
    console.log(`  Overall Progress: ${Math.round(epics.reduce((sum, e) => sum + e.progress, 0) / epics.length)}%`);
    
    // Critical blockers
    if (blockedTasks.length > 0) {
      console.log('\n🚨 BLOCKERS:');
      for (const task of blockedTasks.slice(0, 5)) {
        console.log(`  ${task.id}: ${task.title}`);
        console.log(`    Blocker: ${task.blockers}`);
      }
    }
    
    // Next actions
    const nextTask = await this.getNextTask();
    if (nextTask) {
      console.log('\n➡️  NEXT PRIORITY TASK:');
      console.log(`  ${nextTask.id}: ${nextTask.title}`);
      console.log(`  Priority: ${nextTask.priority} | Due: ${nextTask.dueDate}`);
      console.log(`  Owner: ${nextTask.owner}`);
    }
    
    console.log('\n===================================\n');
  }
}

// Command line interface
async function main() {
  const sync = new DevelopmentTrackerSync();
  
  // Set up environment
  process.env.GOOGLE_APPLICATION_CREDENTIALS = process.env.HOME + '/.config/gcloud/application_default_credentials.json';
  process.env.GCLOUD_PROJECT = 'rezolve-poc';
  
  const command = process.argv[2];
  
  switch (command) {
    case 'sync':
      await sync.syncToTodos(false);
      break;
      
    case 'dry-run':
      await sync.syncToTodos(true);
      break;
      
    case 'status':
      await sync.generateStatusReport();
      break;
      
    case 'next':
      const next = await sync.getNextTask();
      if (next) {
        console.log('Next priority task:');
        console.log(`${next.id}: ${next.title}`);
        console.log(`Priority: ${next.priority} | Due: ${next.dueDate}`);
      } else {
        console.log('No pending tasks available');
      }
      break;
      
    case 'update':
      const todoId = process.argv[3];
      const status = process.argv[4];
      const evidence = process.argv[5];
      if (todoId && status) {
        await sync.updateSheetStatus(todoId, status, evidence);
      } else {
        console.log('Usage: sync-development-tracker.ts update <todoId> <status> [evidence]');
      }
      break;
      
    default:
      console.log('📋 Development Tracker Sync Tool\n');
      console.log('Commands:');
      console.log('  sync      - Sync sheet to TodoWrite format');
      console.log('  dry-run   - Preview sync without making changes');
      console.log('  status    - Generate status report');
      console.log('  next      - Get next priority task');
      console.log('  update    - Update task status from TodoWrite');
      console.log('\nExample:');
      console.log('  bun run scripts/sync-development-tracker.ts sync');
      console.log('  bun run scripts/sync-development-tracker.ts update giki-t1-123 completed "PR#123"');
  }
}

// Run if called directly
if (import.meta.main) {
  main().catch(console.error);
}

export { DevelopmentTrackerSync };