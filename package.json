{"name": "giki-ai-workspace", "version": "0.1.0", "description": "Monorepo containing Giki AI financial categorization platform and Pitch Prep Shark Tank preparation tool", "private": true, "type": "module", "packageManager": "bun@1.2.19", "workspaces": ["apps/*", "apps/*/client", "apps/*/server", "apps/*/shared", "packages/*", "services/*"], "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-table": "^8.21.3", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cross-fetch": "^4.1.0", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dotenv": "^17.2.1", "eventsource": "^4.0.0", "exceljs": "^4.4.0", "form-data": "^4.0.4", "framer-motion": "^12.19.2", "google-auth-library": "^10.2.1", "googleapis": "^155.0.1", "js-yaml": "^4.1.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.510.0", "node-fetch": "^2.7.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-resizable-panels": "^3.0.2", "react-router-dom": "^6.28.0", "recharts": "^2.15.3", "serve": "^14.2.4", "tailwind-merge": "^3.3.0", "tslib": "^2.8.1", "uuid": "^11.1.0", "vite-plugin-pwa": "^1.0.2", "zod": "^3.25.1", "zustand": "^5.0.4"}, "devDependencies": {"@axe-core/cli": "^4.10.1", "@axe-core/playwright": "^4.10.2", "@eslint/js": "^9.27.0", "@happy-dom/global-registrator": "^18.0.1", "@playwright/test": "^1.54.2", "@tanstack/react-query": "^5.82.0", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/crypto-js": "^4.2.2", "@types/js-yaml": "^4.0.9", "@types/jsdom": "^21.1.7", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.6.0", "axe-core": "^4.10.3", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "hono": "^4.6.7", "jsdom": "^26.1.0", "playwright": "^1.54.2", "prettier": "^3.5.3", "tailwindcss": "^4.1.7", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "typescript-eslint": "^8.33.0", "vite": "^5.4.19"}, "scripts": {"_comment": "=== SIMPLIFIED DEVELOPMENT COMMANDS ===", "clean:ports": "lsof -ti:3000,3001,8000,8001 | xargs kill -9 2>/dev/null || true", "dev": "bun run dev:pitch-prep", "dev:giki": "bash scripts/start-giki-only.sh", "dev:pitch-prep": "bash scripts/start-pitch-prep-only.sh", "dev:all": "bash scripts/start-all-services.sh", "dev:giki:client": "cd apps/giki/client && bun run dev", "dev:giki:server": "cd apps/giki/server && bun run dev", "dev:pitch-prep:client": "cd apps/pitch-prep/client && bun run dev", "dev:pitch-prep:server": "cd apps/pitch-prep/server && bun run dev", "stop:all": "bash scripts/stop-all-services.sh", "stop:giki": "bash scripts/stop-project-services.sh giki", "stop:pitch-prep": "bash scripts/stop-project-services.sh pitch-prep", "status": "echo '=== Service Status ===' && (lsof -ti:3000,3001,8000,8001 | wc -l | xargs -I {} echo '{} services running on development ports') && echo '=== Port Details ===' && lsof -i:3000,3001,8000,8001 2>/dev/null || echo 'No services running'", "logs:giki": "tail -20 logs/giki-client.log logs/giki-server.log 2>/dev/null || echo 'No Giki AI logs yet'", "logs:pitch-prep": "tail -20 logs/pitch-prep-client.log logs/pitch-prep-server.log 2>/dev/null || echo 'No Pitch Prep logs yet'", "logs:all": "tail -20 logs/*.log 2>/dev/null || echo 'No logs yet'", "logs:live:giki": "tail -f logs/giki-client.log logs/giki-server.log", "logs:live:pitch-prep": "tail -f logs/pitch-prep-client.log logs/pitch-prep-server.log", "logs:live:all": "tail -f logs/*.log", "build": "bun run --filter '*' build", "test": "bash scripts/test-with-services.sh", "test:giki": "bun run --filter '@workspace/giki-*' test", "test:pitch-prep": "bun run --filter '@workspace/pitch-prep-*' test", "test:unit": "bun run --filter '*' test:unit", "test:integration": "bash scripts/test-with-services.sh --integration-only", "test:e2e": "bash scripts/test-with-services.sh --e2e-only", "test:e2e:direct": "bun x playwright test --config=playwright.config.ts", "test:all": "bash scripts/test-with-services.sh --all", "test:unit-only": "bun run test:unit", "test:watch": "bun run --filter '*' test:watch", "test:coverage": "bun run --filter '*' test:coverage", "lint": "bun run --filter '*' lint", "type-check": "bun run --filter '*' type-check", "format": "bun run --filter '*' format", "clean": "bun run --filter '*' clean", "_comment2": "=== PROTOTYPE DEVELOPMENT COMMANDS ===", "serve:prototypes": "bun run scripts/serve-prototypes.ts", "serve:prototypes:watch": "bun run scripts/serve-prototypes.ts --watch", "serve:prototypes:port": "bun run scripts/serve-prototypes.ts --port=3002", "dev:prototypes": "bun run scripts/serve-prototypes.ts --watch --port=3002", "validate:html": "bun run scripts/validate-html-prototypes.ts", "validate:html:verbose": "bun run scripts/validate-html-prototypes.ts --verbose", "validate:tokens": "bun run scripts/validate-html-prototypes.ts --focus=tokens", "validate:responsive": "bun run scripts/validate-html-prototypes.ts --focus=responsive", "_comment3": "=== TRANSCRIPT FETCHER COMMANDS (Background Only) ===", "transcript:fetch": "cd packages/transcript-fetcher && uv run python -c \"import sys; sys.path.insert(0, 'src'); from transcript_fetcher.cli import main; main(['fetch-youtube', '--background', '--unfiltered'])\"", "transcript:fetch:filtered": "cd packages/transcript-fetcher && uv run python -c \"import sys; sys.path.insert(0, 'src'); from transcript_fetcher.cli import main; main(['fetch-youtube', '--background'])\"", "transcript:fetch:pitches": "cd packages/transcript-fetcher && uv run python -c \"import sys; sys.path.insert(0, 'src'); from transcript_fetcher.cli import main; main(['fetch-pitches', '--background'])\"", "transcript:status": "cd packages/transcript-fetcher && uv run python -c \"import sys; sys.path.insert(0, 'src'); from transcript_fetcher.cli import main; main(['--status'])\"", "transcript:stop": "cd packages/transcript-fetcher && uv run python -c \"import sys; sys.path.insert(0, 'src'); from transcript_fetcher.cli import main; main(['--stop'])\""}, "keywords": ["giki-ai", "financial-categorization", "mis-platform", "ai", "vertex-ai", "accounting", "shark-tank", "pitch-prep", "business-analysis", "entrepreneur", "india"], "author": "Development Team", "license": "MIT"}